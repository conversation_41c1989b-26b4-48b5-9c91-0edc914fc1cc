[package]
description = "Work with Windows network interfaces and their configuration"
edition = "2021"
name = "windows-net"
version = "0.0.0"

[target.'cfg(windows)'.dependencies]
futures = "0.3.15"
libc = "0.2"
socket2 = { version = "0.4.2", features = ["all"] }
thiserror = "2.0.4"
winapi = { version = "0.3.6", features = ["ws2def"] }
windows-sys = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_Globalization",
    "Win32_System_Com",
    "Win32_System_IO",
    "Win32_Networking_WinSock",
    "Win32_NetworkManagement_IpHelper",
    "Win32_NetworkManagement_Ndis",
] }
