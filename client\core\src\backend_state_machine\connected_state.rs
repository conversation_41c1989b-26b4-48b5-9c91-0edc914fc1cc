use std::net::IpAddr;

use futures::{
    channel::{mpsc, oneshot},
    StreamExt,
};
use tokio::net::TcpStream;
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use base::packet::{Message, MessageCodec, MessageType};
use tlv::Serialize;
use tlv_types::{AccessResource, MfaCompleteAuth};
use types::backend::BackendStateTransition;

use crate::{
    backend::Backend,
    backend_state_machine::{
        disconnected_state::DisconnectedState, AfterDisconnect, BackendCommand,
        BackendCommandReceiver, BackendState, BackendStateWrapper, EventResult,
        SharedBackendStateValues,
    },
    mpsc::Sender,
    tunnel_state_machine::TunnelCommand,
    CoreEventSender, EventListener, InternalCoreEvent,
};

use super::EventConsequence;

pub struct ConnectedState {
    core_tx: CoreEventSender,
    backend_close_tx: oneshot::Sender<()>,
    close_rx: oneshot::Receiver<Option<i8>>,

    packet_tx: mpsc::UnboundedSender<Message>,
    auth_packet_tx: mpsc::UnboundedSender<(Message, oneshot::Sender<bool>)>,
}

impl ConnectedState {
    async fn disconnect<L>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
        _after_disconnect: AfterDisconnect,
        reason: Option<i8>,
        callback_tx: Option<oneshot::Sender<()>>,
    ) -> EventConsequence
    where
        L: EventListener + Send + Clone + 'static,
    {
        let (result_tx, result_rx) = oneshot::channel();
        _ = self
            .core_tx
            .send(InternalCoreEvent::TunnelCommand(TunnelCommand::ResetDns(
                Some(result_tx),
            )));
        _ = result_rx.await;
        _ = self
            .core_tx
            .send(InternalCoreEvent::TunnelCommand(TunnelCommand::Routes((
                None,
                callback_tx,
            ))));

        _ = self.backend_close_tx.send(());

        EventConsequence::NewState(DisconnectedState::enter(shared_values, reason).await)
    }

    async fn handle_commands<L: EventListener + Send>(
        self,
        command: Option<BackendCommand>,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match command {
            None => SameState(self.into()),
            Some(command) => match command {
                BackendCommand::Disconnect(tx) => {
                    self.disconnect(shared_values, AfterDisconnect::Nothing, None, tx)
                        .await
                }
                BackendCommand::Login { .. } => {
                    self.disconnect(shared_values, AfterDisconnect::Nothing, None, None)
                        .await
                }

                BackendCommand::GetTicket => {
                    let message = Message::new(MessageType::GenerateSSOTicket, &[]);
                    self.send_message(shared_values, message).await
                }
                BackendCommand::CompleteAuth(auth_complete, tx) => {
                    if auth_complete.ip_addr.is_none() {
                        log::warn!(
                            "missing access target. id = {}",
                            auth_complete.auth_result_id
                        );
                        return SameState(self.into());
                    }

                    let complete_auth = MfaCompleteAuth {
                        auth_result_id: auth_complete.auth_result_id,
                        resource: match auth_complete.ip_addr.unwrap() {
                            IpAddr::V4(ip) => AccessResource::Ipv4(ip),
                            IpAddr::V6(ip) => AccessResource::Ipv6(ip),
                        },
                    };
                    let message =
                        Message::new(MessageType::MfaAuthCompleted, &complete_auth.serialize());
                    self.send_auth_message(shared_values, message, tx).await
                }
                BackendCommand::CancelAuth(mfa, tx) => {
                    if mfa.ip_addr.is_none() {
                        log::warn!("missing access target.");
                        return SameState(self.into());
                    }

                    let access_resource = match mfa.ip_addr.unwrap() {
                        IpAddr::V4(ip) => AccessResource::Ipv4(ip),
                        IpAddr::V6(ip) => AccessResource::Ipv6(ip),
                    };
                    let message =
                        Message::new(MessageType::CancelMfaAuth, &access_resource.serialize());
                    self.send_auth_message(shared_values, message, tx).await
                }
                BackendCommand::ChangePwdByOldPwd(payload) => {
                    let payload = serde_json::json!({
                        "username": payload.username,
                        "old": payload.old_password,
                        "password": payload.new_password,
                    });

                    let payload = tlv::Serialize::serialize(&payload);
                    let message = Message::new(MessageType::ChangePwd, &payload);
                    self.send_message(shared_values, message).await
                }
                BackendCommand::HttpRequest(message) => {
                    self.send_message(shared_values, message).await
                }
                BackendCommand::Packet(message) => self.send_message(shared_values, message).await,
                _ => SameState(self.into()),
            },
        }
    }

    async fn send_message<L: EventListener + Send>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
        message: Message,
    ) -> EventConsequence {
        if self.packet_tx.unbounded_send(message).is_err() {
            log::error!("Backend channel close");
            self.disconnect(shared_values, AfterDisconnect::Nothing, Some(-1), None)
                .await
        } else {
            EventConsequence::SameState(self.into())
        }
    }

    async fn send_auth_message<L: EventListener + Send>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
        message: Message,
        tx: oneshot::Sender<bool>,
    ) -> EventConsequence {
        if self.auth_packet_tx.unbounded_send((message, tx)).is_err() {
            log::error!("Backend controller channel close");
            self.disconnect(shared_values, AfterDisconnect::Nothing, Some(-1), None)
                .await
        } else {
            EventConsequence::SameState(self.into())
        }
    }

    async fn handle_backend_close_event<L: EventListener + Send>(
        self,
        shared_values: &mut SharedBackendStateValues<L>,
        reason: Option<i8>,
    ) -> EventConsequence {
        let (result_tx, result_rx) = oneshot::channel();
        _ = self
            .core_tx
            .send(InternalCoreEvent::TunnelCommand(TunnelCommand::ResetDns(
                Some(result_tx),
            )));
        _ = result_rx.await;
        let (result_tx, result_rx) = oneshot::channel();
        _ = self
            .core_tx
            .send(InternalCoreEvent::TunnelCommand(TunnelCommand::Routes((
                None,
                Some(result_tx),
            ))));
        _ = result_rx.await;

        EventConsequence::NewState(DisconnectedState::enter(shared_values, reason).await)
    }
}

#[async_trait::async_trait]
impl<L> BackendState<L> for ConnectedState
where
    L: EventListener + Send + Clone + 'static,
{
    type Bootstrap = (
        Framed<TlsStream<TcpStream>, MessageCodec>,
        Option<Vec<IpAddr>>,
        IpAddr,
    );

    async fn enter(
        shared_values: &mut SharedBackendStateValues<L>,
        bootstrap: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition) {
        let (close_tx, close_rx) = oneshot::channel();
        let (internal_close_tx, internal_close_rx) = oneshot::channel();
        let (packet_tx, packet_rx) = mpsc::unbounded();
        let (auth_packet_tx, auth_packet_rx) = mpsc::unbounded();

        let connected_state = ConnectedState {
            core_tx: shared_values.core_tx.clone(),
            backend_close_tx: internal_close_tx,
            close_rx,
            packet_tx,
            auth_packet_tx,
        };

        let backend = Backend {
            core_tx: connected_state.core_tx.clone(),
            event_listener: shared_values.event_listener.clone(),
            route_manager_handle: shared_values.route_manager_handle.clone(),
            interface_name: shared_values.interface_name.clone(),
            ctl_framed: bootstrap.0,
            backend_close_rx: internal_close_rx,
            backend_close_tx: close_tx,
            ctl_packet_rx: packet_rx,
            ctl_auth_packet_rx: auth_packet_rx,
        };
        tokio::spawn(async move {
            backend.run().await;
        });
        (
            BackendStateWrapper::from(connected_state),
            BackendStateTransition::Connected(bootstrap.2, bootstrap.1),
        )
    }

    async fn handle_event(
        mut self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        let result = futures::select! {
            command = commands.next() => EventResult::Command(command),
            reason = &mut self.close_rx => EventResult::Close(reason.unwrap_or_default()),
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Close(reason) => {
                self.handle_backend_close_event(shared_values, reason).await
            }
        }
    }
}
