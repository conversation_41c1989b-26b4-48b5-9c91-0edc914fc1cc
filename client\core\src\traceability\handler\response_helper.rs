use crate::traceability::{
    constants::RESPONSE_HEADER_SERVER, utils::headers::add_header_entry_overwrite_if_exist,
    ProxyError,
};
use hyper::{Response, StatusCode};
use reqwest::Body;

pub(super) fn http_error(status_code: StatusCode) -> Result<Response<Body>, ProxyError> {
    let mut response = Response::builder()
        .status(status_code)
        .body(Body::default())
        .unwrap();
    let headers = response.headers_mut();
    _ = add_header_entry_overwrite_if_exist(
        headers,
        "server",
        format!("{}/{}", RESPONSE_HEADER_SERVER, version::PRODUCT_VERSION),
    );
    Ok(response)
}
