use crate::plugins::Result;
use communicate_interface::ControlServiceClient;
use tauri::{async_runtime::Mutex, State};

/// 发送获取ticket事件
#[tauri::command]
pub(in crate::plugins) async fn ticket(rpc: State<'_, Mutex<ControlServiceClient>>) -> Result<()> {
    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);
    match rpc.get_ticket(tonic::Request::new(())).await {
        Ok(_) => {}
        Err(status) => {
            log::error!("Failed to get ticket. {status}");
        }
    }
    Ok(())
}
