use once_cell::sync::Lazy;
use types::net::Connectivity;

/// 在线用户信息
#[derive(Clone)]
pub struct User {
    /// 单位
    pub unit: String,
    /// 用户名
    pub username: String,
    /// 绑定码
    pub secret: String,
}

/// 电源状态
#[allow(dead_code)]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PowerState {
    Suspend,
    SuspendResume,
}

/// APP 状态
pub struct AppState {
    /// 当前网络状态
    pub connectivity: Connectivity,
    /// 电源状态
    pub power_state: Option<PowerState>,
    /// 设备ID
    pub device_id: Option<String>,
    /// 当前登录用户
    pub user: Option<User>,
    /// 是否以执行过登录, 不管登录是否成功
    pub already_executed_login: bool,
    /// 最近登录时间
    pub last_login_time: Option<u64>,
    #[cfg(feature = "sdp")]
    pub proxy_ready: bool,
    /// 单点登录最后的结果
    #[cfg(all(feature = "iam", feature = "sdp"))]
    pub sso_result: Option<String>,
    /// 系统信息
    pub system_info: serde_json::Value,
    /// 重连令牌
    #[cfg(feature = "sdp")]
    pub reconnect_token: Option<(String, i64)>,
    /// 开机自启状态
    #[cfg(target_os = "linux")]
    pub tray_auto_start_state: bool,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            connectivity: Connectivity::Status {
                ipv4: true,
                ipv6: true,
            },
            power_state: None,
            device_id: Default::default(),
            user: Default::default(),
            already_executed_login: Default::default(),
            last_login_time: Default::default(),
            #[cfg(feature = "sdp")]
            proxy_ready: Default::default(),
            #[cfg(all(feature = "iam", feature = "sdp"))]
            sso_result: Default::default(),
            system_info: Default::default(),
            #[cfg(feature = "sdp")]
            reconnect_token: Default::default(),
            #[cfg(target_os = "linux")]
            tray_auto_start_state: Default::default(),
        }
    }
}

impl AppState {
    #[cfg(windows)]
    pub fn reonnect_token_is_valid(&self) -> bool {
        if let Some((_token, expire)) = self.reconnect_token.as_ref() {
            let timestamp = chrono::Local::now().timestamp_millis();
            return timestamp < *expire;
        }
        false
    }

    pub fn reset(&mut self) {
        log::debug!(target: "app", "Reset app state");
        self.user.take();
        self.power_state.take();
        #[cfg(feature = "sdp")]
        {
            self.proxy_ready = false;
        }

        #[cfg(all(feature = "sdp", feature = "iam"))]
        {
            self.sso_result = None;
        }
    }
}

/// 同一时间, 只有一次登录
pub static LOGIN_LOCK: Lazy<tokio::sync::Mutex<u32>> = Lazy::new(|| tokio::sync::Mutex::new(0));
