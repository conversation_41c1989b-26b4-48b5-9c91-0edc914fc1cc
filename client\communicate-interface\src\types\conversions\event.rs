use crate::types::proto;

use super::FromProtobufTypeError;

impl From<sdptypes::net::Connectivity> for proto::Connectivity {
    fn from(message: sdptypes::net::Connectivity) -> Self {
        match message {
            sdptypes::net::Connectivity::Status { ipv4, ipv6 } => proto::Connectivity {
                state: Some(proto::connectivity::State::Status(
                    proto::ConnectivityStatus { ipv4, ipv6 },
                )),
            },
            sdptypes::net::Connectivity::PresumeOnline => proto::Connectivity {
                state: Some(proto::connectivity::State::PresumeOnline(
                    proto::ConnectivityPresumeOnline {},
                )),
            },
        }
    }
}

impl TryFrom<proto::Connectivity> for sdptypes::net::Connectivity {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::Connectivity) -> Result<Self, Self::Error> {
        match message.state {
            Some(state) => match state {
                proto::connectivity::State::Status(proto::ConnectivityStatus { ipv4, ipv6 }) => {
                    Ok(sdptypes::net::Connectivity::Status { ipv4, ipv6 })
                }
                proto::connectivity::State::PresumeOnline(_connectivity_presume_online) => {
                    Ok(sdptypes::net::Connectivity::PresumeOnline)
                }
            },
            None => Err(FromProtobufTypeError::InvalidArgument(
                "Invalid connectivity",
            )),
        }
    }
}
