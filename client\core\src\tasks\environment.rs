use std::{collections::HashMap, time::Duration};

use serde_json::{json, Value};

use crate::{
    environment, http,
    strategy::{self, Condition},
    EventListener,
};

pub(crate) const TASK_NAME: &str = "environment";

/// 定时发送环境数据
pub(crate) async fn environment_task<L>(
    device_id: String,
    event_listener: L,
    conditions: Option<HashMap<String, Vec<Condition>>>,
) where
    L: EventListener + Send + Clone + 'static,
{
    let mut tick = tokio::time::interval(Duration::from_secs(30));

    log::debug!("Start scan environment task");

    log::trace!("Execute local strategy. {:?}", &conditions);

    let mut last_env = Value::Null;
    let mut flag = false;

    #[cfg(windows)]
    let port = crate::windows::get_gui_port();
    #[cfg(not(windows))]
    let port = 50031u16;

    let system_info = environment::system::info(&device_id);

    loop {
        let mut system_info = system_info.clone();
        let client = http::client().await;

        // 本地策略执行结果
        if let Some(conditions) = &conditions {
            system_info["strategyExecResults"] = strategy::exec(conditions).await;
        }

        // 获取WIFI名称
        #[cfg(target_os = "macos")]
        let response = client
            .get(format!("http://localhost:{port}/wifi"))
            .send()
            .await;
        #[cfg(target_os = "macos")]
        let names = if let Ok(response) = response {
            response.json::<Vec<String>>().await.unwrap_or_default()
        } else {
            vec![]
        };
        #[cfg(not(target_os = "macos"))]
        let names = environment::wifi::network_ssid();
        system_info["wifiName"] = Value::String(names.join(","));

        // 获取网卡数据列表
        if let Some(interfaces) = environment::interface::list() {
            system_info["networks"] = Value::Array(interfaces);
        }

        system_info["softwareDtlParams"] = Value::Array(
            environment::software::list()
                .iter()
                .map(|x| serde_json::to_value(x).unwrap())
                .collect::<Vec<Value>>(),
        );

        let response = client
            .get(format!("http://localhost:{port}/screen_saver"))
            .send()
            .await;
        let screen_saver = if let Ok(response) = response {
            response.json::<bool>().await.unwrap_or_default()
        } else {
            false
        };

        #[allow(unused_mut)]
        let mut dtl_param = json!({
            "screenProtector": screen_saver,
            "firewallEnabled": serde_json::to_string(&environment::firewall::is_enable()).unwrap(),
        });
        #[cfg(windows)]
        {
            let domain_name = environment::domain::get_domain_name()
                .map(|(name, is_domain_user)| format!("{name}:{}", is_domain_user as i32))
                .unwrap_or_default();
            dtl_param["domainName"] = Value::String(domain_name);
            dtl_param["terminalFileSharing"] =
                Value::Bool(!environment::share_folders::share_folders().is_empty());
        }

        system_info["dtlParam"] = dtl_param;

        if let Some((ip, net_operator, location)) = environment::ip::public_ip().await {
            system_info["netOperator"] = Value::String(net_operator);
            system_info["externalNetworkIp"] = Value::String(ip.clone());
            system_info["location"] = Value::String(location);
        }

        // 经纬度
        system_info["coordinate"] = json!({});
        // 环境数据有变化时, 才发送
        if !flag || last_env != system_info {
            if last_env.is_null() {
                last_env = system_info;
                let env_data = serde_json::to_vec(&last_env).unwrap();
                event_listener.notify_environment(env_data);
            } else {
                // 发送数据转移
                let mut send_env = last_env;
                // 转移环境信息
                last_env = system_info;
                // 修改发送数据
                if let Some(value) = last_env.as_object() {
                    let filter_key = [
                        "deviceId",
                        "name",
                        "systemVersion",
                        "recentlyLoginUser",
                        "operatingSystem",
                        "clientVersion",
                    ];
                    value
                        .into_iter()
                        .filter(|(k, _)| !filter_key.contains(&k.as_str()))
                        .for_each(|(k, v)| {
                            // 发送JSON
                            if let Some(value) = send_env.get(k) {
                                if value != v {
                                    send_env[k] = v.clone();
                                } else {
                                    send_env[k] = Value::Null
                                }
                            }
                        });
                }
                let env_data = serde_json::to_vec(&send_env).unwrap();
                event_listener.notify_environment(env_data);
            }

            flag = true;
        }

        _ = tick.tick().await;
    }
}
