// Use of a mod or pub mod is not actually necessary.
mod built_info {
    // The file has been placed there by the build script.
    include!(concat!(env!("OUT_DIR"), "/built.rs"));
}

pub fn version_info() -> String {
    let fallback = "Unknown (env var does not exist when building)";
    format!(
        "\nRelease Version:   {}\
         \nGit Commit Hash:   {}\
         \nGit Commit Branch: {}\
         \nUTC Build Time:    {}\
         \nProfile:           {}",
        version::PRODUCT_VERSION,
        built_info::GIT_COMMIT_HASH_SHORT.unwrap_or(fallback),
        built_info::GIT_HEAD_REF
            .map(|head| head.replace("refs/heads/", ""))
            .unwrap_or(fallback.to_string()),
        option_env!("BUILD_TIME").unwrap_or(fallback),
        built_info::PROFILE,
    )
}
