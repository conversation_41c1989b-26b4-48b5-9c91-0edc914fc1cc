use core::{
    future::Future,
    pin::Pin,
    task::{Context, Poll},
};

use pin_project_lite::pin_project;

pin_project!(
    /// The main event loop future for selecting readiness of sub-tasks
    ///
    /// This future ensures all sub-tasks are polled fairly by yielding once
    /// after completing any of the sub-tasks. This is especially important when the TX queue is
    /// flushed quickly and we never get notified of the RX socket having packets to read.
    pub struct Select<Rx, Tx>
    where
        Rx: Future,
        Tx: Future,
    {
        #[pin]
        rx: Rx,
        #[pin]
        tx: Tx,
    }
);

impl<Rx, Tx> Select<Rx, Tx>
where
    Rx: Future,
    Tx: Future,
{
    #[inline(always)]
    pub fn new(rx: Rx, tx: Tx) -> Self {
        Self { rx, tx }
    }
}

#[derive(Debug)]
pub struct Outcome<Rx, Tx> {
    pub rx_result: core::option::Option<Rx>,
    pub tx_result: core::option::Option<Tx>,
}

pub type Option<Rx, Tx> = core::option::Option<Outcome<Rx, Tx>>;

impl<Rx, Tx> Future for Select<Rx, Tx>
where
    Rx: Future,
    Tx: Future,
{
    type Output = Option<Rx::Output, Tx::Output>;

    fn poll(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Self::Output> {
        let this = self.project();

        let mut should_wake = false;

        let mut rx_result = None;
        if let Poll::Ready(v) = this.rx.poll(cx) {
            should_wake = true;
            rx_result = Some(v);
        }

        let mut tx_result = None;
        if let Poll::Ready(v) = this.tx.poll(cx) {
            should_wake = true;
            tx_result = Some(v);
        }

        // if none of the subtasks are ready, return
        if !should_wake {
            return Poll::Pending;
        }

        Poll::Ready(Some(Outcome {
            rx_result,
            tx_result,
        }))
    }
}
