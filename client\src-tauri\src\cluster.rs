use crate::http;
use config::UrlWrapper;
use err_ext::ErrorExt;
use rand::{thread_rng, Rng};
use reqwest::Url;
use std::{net::IpAddr, time::Duration};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Runtime};
use tokio::sync::Mutex;

#[derive(Debug, serde::Serialize, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
struct RemoteNode {
    host: String,
    ip: Option<IpAddr>,
    port: u16,
    spa_port: u16,
}

pub async fn reload<R: Runtime>(app_handle: AppHandle<R>) {
    let config_state = app_handle.state::<Mutex<config::AppConfig>>();
    let config_guard = config_state.lock().await;
    let mut config = config_guard.clone();
    drop(config_guard);
    let status = load_nodes_and_update_cluster_config_url(
        &app_handle,
        config.cluster_config_url.clone().map(Into::into),
        config.cluster_external_config_url.clone().map(Into::into),
        &mut config,
    )
    .await;

    if status {
        // 通知前端更新CONFIG
        _ = app_handle.emit_to("main", "config_updated", config.clone());
    }

    log::info!(target: "app", "Reload nodes state: {}", status);
}

pub async fn load_nodes_and_update_cluster_config_url<R: Runtime>(
    app_handle: &AppHandle<R>,
    cluster_config_url: Option<Url>,
    cluster_external_config_url: Option<Url>,
    config: &mut config::AppConfig,
) -> bool {
    let cluster_config_url: Option<UrlWrapper> = cluster_config_url.map(Into::into);
    let cluster_external_config_url: Option<UrlWrapper> =
        cluster_external_config_url.map(Into::into);
    let changed = config.cluster_config_url != cluster_config_url
        || config.cluster_external_config_url != cluster_external_config_url;

    let client = http::client().await;

    let mut nodes = vec![];

    let urls = vec![
        cluster_config_url.clone(),
        cluster_external_config_url.clone(),
    ];

    let mut future_list = vec![];
    for url in urls.into_iter().flatten() {
        future_list.push(tokio::spawn(async move {
            log::trace!(target: "app", "Get the node list: {}", url.as_str());

            let response = client
                .get(url.as_ref())
                .timeout(Duration::from_secs(2))
                .send()
                .await;

            match response {
                Ok(response) => {
                    if let Ok(cluster_nodes) = response.json::<Vec<RemoteNode>>().await {
                        log::debug!(target: "app", "Addr: {}, nodes: {:?}", url.as_str(), cluster_nodes);
                        return Some(cluster_nodes);
                    }
                }
                Err(error) => {
                    log::error!(target: "app", "Addr: {}, {}", url.as_str(), error.display_chain());
                }
            }
            None
        }));
    }

    let mut count_of_internal = 0;
    let mut first = true;
    let results = futures::future::join_all(future_list).await;
    results.into_iter().for_each(|result| {
        if let Ok(Some(cluster_nodes)) = result {
            if first {
                count_of_internal = cluster_nodes.len();
            }
            nodes.extend(cluster_nodes);
        }
        if first {
            first = false;
        }
    });

    if nodes.is_empty() {
        return false;
    }

    let cluster_nodes = nodes
        .iter()
        .map(|node| config::Node {
            host: node.host.clone(),
            ip: node.ip,
            port: node.port,
            spa_port: node.spa_port,
        })
        .collect::<Vec<config::Node>>();

    let (mut index, mut node) = config
        .node
        .as_ref()
        .and_then(|node| {
            cluster_nodes.iter().enumerate().find(|(_index, new_node)| {
                node.host == new_node.host
                    && node.ip == new_node.ip
                    && node.port == new_node.port
                    && node.spa_port == new_node.spa_port
            })
        })
        .unwrap_or_else(|| {
            let index = thread_rng().gen_range(0..cluster_nodes.len());
            (index, &cluster_nodes[index])
        });

    // 优先选择内网节点
    if count_of_internal > 0 {
        // 选择的节点不是内网节点, 重新选择
        if index + 1 > count_of_internal {
            index = thread_rng().gen_range(0..count_of_internal);
            node = &cluster_nodes[index]
        }
    }

    log::debug!(target: "app", "Use node: {node:?}");

    config.node = Some(node.clone());
    config.cluster_nodes = Some(cluster_nodes);
    config.cluster_starting_index = index;
    config.cluster_node_changed_times = 0;
    config.cluster_config_url = cluster_config_url;
    config.cluster_external_config_url = cluster_external_config_url;

    // 若地址改变, 则清楚之前缓存的数据
    if changed {
        config.selected_tenant = None;
        config.tenants = None;
        config.users = None;
        config.secret_key = None;
    }

    if let Err(error) = config.save_file() {
        log::error!(target: "app", "Failed to save config: {error}");
        return false;
    }

    // 更新全局状态
    let state = app_handle.state::<Mutex<config::AppConfig>>();
    let mut state = state.lock().await;
    *state = config.clone();
    drop(state);

    true
}
