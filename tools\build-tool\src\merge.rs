use anyhow::{bail, Context};
use clap::Parser;
use serde_json::Value as JsonValue;
use std::{fs, path::PathBuf};

#[derive(Debug, Parser)]
pub struct Options {
    /// Source json content.
    #[clap(long)]
    pub source: Option<String>,
    /// Source file path.
    #[clap(long)]
    pub source_path: Option<PathBuf>,
    /// Target json content.
    #[clap(long)]
    pub target: Option<String>,
    /// Target file path.
    #[clap(long)]
    pub target_path: Option<PathBuf>,
}

pub fn run(opts: Options) -> anyhow::Result<()> {
    let merge = if let Some(source) = opts.source {
        let merge: JsonValue =
            serde_json::from_str(&source).with_context(|| "failed to parse source to merge")?;
        merge
    } else if let Some(source_path) = opts.source_path {
        let content =
            fs::read_to_string(source_path).with_context(|| "failed to read source content")?;
        let merge: JsonValue =
            serde_json::from_str(&content).with_context(|| "failed to parse source to merge")?;
        merge
    } else {
        bail!("Choose one of `source` and `source_path`");
    };

    let mut target_content = if let Some(target) = opts.target {
        let target_content: JsonValue =
            serde_json::from_str(&target).with_context(|| "failed to parse target to merge")?;
        target_content
    } else if let Some(target_path) = opts.target_path {
        let content =
            fs::read_to_string(target_path).with_context(|| "failed to read target content")?;
        let target_content: JsonValue =
            serde_json::from_str(&content).with_context(|| "failed to parse target to merge")?;
        target_content
    } else {
        bail!("Choose one of `target` and `target_path`");
    };

    json_patch::merge(&mut target_content, &merge);
    let output = serde_json::to_string(&target_content).unwrap();

    println!("{}", output);
    Ok(())
}
