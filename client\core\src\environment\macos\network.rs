use std::ffi::CString;

use anyhow::Context;
use system_configuration::{
    core_foundation::runloop::{kCFRunLoopCommonModes, CFRunLoop},
    network_reachability::{ReachabilityFlags, SCNetworkReachability},
};
// use system_configuration_sys::{
//     core_foundation_sys::base::{kCFAllocatorDefault, CFRelease},
//     network_reachability::{
//         kSCNetworkReachabilityFlagsConnectionRequired, kSCNetworkReachabilityFlagsReachable,
//         SCNetworkReachabilityCreateWithAddress, SCNetworkReachabilityGetFlags,
//         SCNetworkReachabilityRef, SCNetworkReachabilityScheduleWithRunLoop,
//     },
// };

// pub fn connected_internet() -> bool {
//     unsafe {
//         let mut addr = MaybeUninit::<sockaddr_in>::zeroed().assume_init();
//         addr.sin_len = size_of::<sockaddr_in>() as _;
//         addr.sin_family = libc::AF_INET as _;

//         // let addr = libc::sockaddr_in {
//         //     sin_len: std::mem::size_of::<libc::sockaddr_in>() as u8,
//         //     sin_family: libc::AF_INET as u8,
//         //     sin_port: 0,
//         //     sin_addr: libc::in_addr { s_addr: 0 },
//         //     sin_zero: [0i8; 8],
//         // };

//         let reachability = SCNetworkReachabilityCreateWithAddress(
//             kCFAllocatorDefault,
//             &addr as *const sockaddr_in as _,
//         );

//         if reachability.is_null() {
//             eprintln!("Failed to obtain network status.");
//             return false;
//         }

//         let mut flags = 0;
//         let flag = SCNetworkReachabilityGetFlags(reachability, &mut flags);

//         if flag == 0 {
//             return false;
//         }

//         let reachable = flags & kSCNetworkReachabilityFlagsReachable;
//         let needs_connection = flags & kSCNetworkReachabilityFlagsConnectionRequired;

//         CFRelease(reachability);

//         reachable == kSCNetworkReachabilityFlagsReachable
//             && needs_connection != kSCNetworkReachabilityFlagsConnectionRequired
//     }
// }

pub struct Monitor {
    network_reachability: SCNetworkReachability,
}

impl Monitor {
    pub fn new() -> Self {
        let host = CString::new("0.0.0.0").unwrap();
        let network_reachability =
            SCNetworkReachability::from_host(&host).expect("Failed to create network reachability");
        Self {
            network_reachability,
        }
    }

    #[allow(dead_code)]
    pub fn reachable(&self) -> bool {
        self.network_reachability
            .reachability()
            .map(|flag| flag == ReachabilityFlags::REACHABLE)
            .unwrap_or_default()
    }

    pub fn run_monitor(&mut self, callback: fn(flags: ReachabilityFlags)) -> anyhow::Result<()> {
        self.network_reachability
            .set_callback(callback)
            .context("set callback failed")?;
        self.network_reachability
            .schedule_with_runloop(&CFRunLoop::get_current(), unsafe { kCFRunLoopCommonModes })
            .context("monitor start failed")
    }

    pub fn stop_monitor(&mut self) {
        self.network_reachability
            .unschedule_from_runloop(&CFRunLoop::get_current(), unsafe { kCFRunLoopCommonModes })
            .unwrap();
    }
}
