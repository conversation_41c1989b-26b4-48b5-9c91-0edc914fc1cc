use std::net::{Ip<PERSON>ddr, Ipv4Addr, Ipv<PERSON><PERSON>ddr, <PERSON>cketAddr, SocketAddrV4, SocketAddrV6};

use ipnet::{IpAddrRange, IpNet, Ipv4AddrRange, Ipv4Net, Ipv6AddrRange, Ipv6Net};
use serde_json::Value;

use crate::{
    utils::{except_tag_and_get_value, read_tag_and_get_value},
    Deserialize, Tag, BAD_MESSAGE,
};

impl Deserialize for u8 {
    const FIXED_TAG: Tag = Tag::Uint8;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<u8, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        Ok(inner.as_slice_less_safe()[0])
    }
}

impl Deserialize for u16 {
    const FIXED_TAG: Tag = Tag::Uint16;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<u16, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        Ok(u16::from_be_bytes([bytes[0], bytes[1]]))
    }
}

impl Deserialize for u32 {
    const FIXED_TAG: Tag = Tag::Uint32;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<u32, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        Ok(u32::from_be_bytes(
            <&[u8] as TryInto<[u8; 4]>>::try_into(bytes).map_err(|_| BAD_MESSAGE)?,
        ))
    }
}

impl Deserialize for u64 {
    const FIXED_TAG: Tag = Tag::Uint64;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<u64, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        Ok(u64::from_be_bytes(
            <&[u8] as TryInto<[u8; 8]>>::try_into(bytes).map_err(|_| BAD_MESSAGE)?,
        ))
    }
}

impl Deserialize for IpAddr {
    const FIXED_TAG: Tag = Tag::Unknown;
    const MAYBE_TAGS: &'static [Tag] = &[Tag::Ipv4, Tag::Ipv6];

    fn deserialize(reader: &mut untrusted::Reader) -> Result<IpAddr, &'static str> {
        let (tag, inner) = read_tag_and_get_value(reader)?;
        let bytes = inner.as_slice_less_safe();
        let tag: Tag = tag.into();
        match tag {
            Tag::Ipv4 => Ok(IpAddr::V4(Ipv4Addr::from(
                <&[u8] as TryInto<[u8; 4]>>::try_into(bytes).map_err(|_| BAD_MESSAGE)?,
            ))),
            Tag::Ipv6 => Ok(IpAddr::V6(Ipv6Addr::from(
                <&[u8] as TryInto<[u8; 16]>>::try_into(bytes).map_err(|_| BAD_MESSAGE)?,
            ))),
            _ => Err(BAD_MESSAGE),
        }
    }
}

impl Deserialize for Ipv4Addr {
    const FIXED_TAG: Tag = Tag::Ipv4;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Ipv4Addr, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();
        Ok(Ipv4Addr::from(
            <&[u8] as TryInto<[u8; 4]>>::try_into(bytes).map_err(|_| BAD_MESSAGE)?,
        ))
    }
}

impl Deserialize for Ipv6Addr {
    const FIXED_TAG: Tag = Tag::Ipv6;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Ipv6Addr, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();
        Ok(Ipv6Addr::from(
            <&[u8] as TryInto<[u8; 16]>>::try_into(bytes).map_err(|_| BAD_MESSAGE)?,
        ))
    }
}

impl Deserialize for SocketAddr {
    const FIXED_TAG: Tag = Tag::Unknown;
    const MAYBE_TAGS: &'static [Tag] = &[Tag::SocketAddrV4, Tag::SocketAddrV6];

    fn deserialize(reader: &mut untrusted::Reader) -> Result<SocketAddr, &'static str> {
        let (tag, inner) = read_tag_and_get_value(reader)?;
        let bytes = inner.as_slice_less_safe();
        let tag: Tag = tag.into();
        match tag {
            Tag::SocketAddrV4 => {
                let ip = IpAddr::V4(Ipv4Addr::from(
                    <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[..4]).map_err(|_| BAD_MESSAGE)?,
                ));

                let port = u16::from_be_bytes(
                    <&[u8] as TryInto<[u8; 2]>>::try_into(&bytes[4..]).map_err(|_| BAD_MESSAGE)?,
                );

                Ok(SocketAddr::new(ip, port))
            }
            Tag::SocketAddrV6 => {
                let ip = IpAddr::V6(Ipv6Addr::from(
                    <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[..16])
                        .map_err(|_| BAD_MESSAGE)?,
                ));

                let port = u16::from_be_bytes(
                    <&[u8] as TryInto<[u8; 2]>>::try_into(&bytes[16..]).map_err(|_| BAD_MESSAGE)?,
                );

                Ok(SocketAddr::new(ip, port))
            }
            _ => Err(BAD_MESSAGE),
        }
    }
}

impl Deserialize for SocketAddrV4 {
    const FIXED_TAG: Tag = Tag::SocketAddrV4;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<SocketAddrV4, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        let ip = Ipv4Addr::from(
            <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[..4]).map_err(|_| BAD_MESSAGE)?,
        );

        let port = u16::from_be_bytes(
            <&[u8] as TryInto<[u8; 2]>>::try_into(&bytes[4..]).map_err(|_| BAD_MESSAGE)?,
        );

        Ok(SocketAddrV4::new(ip, port))
    }
}

impl Deserialize for SocketAddrV6 {
    const FIXED_TAG: Tag = Tag::SocketAddrV6;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<SocketAddrV6, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        let ip = Ipv6Addr::from(
            <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[..16]).map_err(|_| BAD_MESSAGE)?,
        );

        let port = u16::from_be_bytes(
            <&[u8] as TryInto<[u8; 2]>>::try_into(&bytes[16..]).map_err(|_| BAD_MESSAGE)?,
        );

        Ok(SocketAddrV6::new(ip, port, 0, 0))
    }
}

impl Deserialize for bool {
    const FIXED_TAG: Tag = Tag::Boolean;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        Ok(bytes[0] != 0)
    }
}

impl Deserialize for Vec<u8> {
    const FIXED_TAG: Tag = Tag::RawBytes;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Vec<u8>, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        Ok(inner.as_slice_less_safe().to_vec())
    }
}

impl Deserialize for IpNet {
    const FIXED_TAG: Tag = Tag::Unknown;
    const MAYBE_TAGS: &'static [Tag] = &[Tag::Ipv4Net, Tag::Ipv6Net];

    fn deserialize(reader: &mut untrusted::Reader) -> Result<IpNet, &'static str> {
        let (tag, inner) = read_tag_and_get_value(reader)?;
        let bytes = inner.as_slice_less_safe();
        let tag: Tag = tag.into();
        match tag {
            Tag::Ipv4Net => {
                let ip = Ipv4Addr::from(
                    <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[1..]).map_err(|_| BAD_MESSAGE)?,
                );

                let prefix_len = bytes[0];

                Ok(IpNet::V4(
                    Ipv4Net::new(ip, prefix_len).map_err(|_| BAD_MESSAGE)?,
                ))
            }
            Tag::Ipv6Net => {
                let ip = Ipv6Addr::from(
                    <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[1..]).map_err(|_| BAD_MESSAGE)?,
                );

                let prefix_len = bytes[0];

                Ok(IpNet::V6(
                    Ipv6Net::new(ip, prefix_len).map_err(|_| BAD_MESSAGE)?,
                ))
            }
            _ => Err(BAD_MESSAGE),
        }
    }
}

impl Deserialize for Ipv4Net {
    const FIXED_TAG: Tag = Tag::Ipv4Net;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Ipv4Net, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        let prefix_len = bytes[0];

        let ip = Ipv4Addr::from(
            <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[1..]).map_err(|_| BAD_MESSAGE)?,
        );

        Ipv4Net::new(ip, prefix_len).map_err(|_| BAD_MESSAGE)
    }
}

impl Deserialize for Ipv6Net {
    const FIXED_TAG: Tag = Tag::Ipv6Net;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Ipv6Net, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        let prefix_len = bytes[0];

        let ip = Ipv6Addr::from(
            <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[1..]).map_err(|_| BAD_MESSAGE)?,
        );

        Ipv6Net::new(ip, prefix_len).map_err(|_| BAD_MESSAGE)
    }
}

impl Deserialize for String {
    const FIXED_TAG: Tag = Tag::UTF8String;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<String, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;

        String::from_utf8(inner.as_slice_less_safe().to_vec()).map_err(|_| BAD_MESSAGE)
    }
}

impl Deserialize for IpAddrRange {
    const FIXED_TAG: Tag = Tag::Unknown;
    const MAYBE_TAGS: &'static [Tag] = &[Tag::Ipv4Range, Tag::Ipv6Range];

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
        let (tag, inner) = read_tag_and_get_value(reader)?;
        let bytes = inner.as_slice_less_safe();
        let tag: Tag = tag.into();
        match tag {
            Tag::Ipv4Range => {
                let start = Ipv4Addr::from(
                    <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[..4]).map_err(|_| BAD_MESSAGE)?,
                );

                let end = Ipv4Addr::from(
                    <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[4..]).map_err(|_| BAD_MESSAGE)?,
                );

                Ok(IpAddrRange::V4(Ipv4AddrRange::new(start, end)))
            }
            Tag::Ipv6Range => {
                let start = Ipv6Addr::from(
                    <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[..16])
                        .map_err(|_| BAD_MESSAGE)?,
                );

                let end = Ipv6Addr::from(
                    <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[16..])
                        .map_err(|_| BAD_MESSAGE)?,
                );

                Ok(IpAddrRange::V6(Ipv6AddrRange::new(start, end)))
            }
            _ => Err(BAD_MESSAGE),
        }
    }
}

impl Deserialize for Ipv4AddrRange {
    const FIXED_TAG: Tag = Tag::Ipv4Range;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        let start = Ipv4Addr::from(
            <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[..4]).map_err(|_| BAD_MESSAGE)?,
        );
        let end = Ipv4Addr::from(
            <&[u8] as TryInto<[u8; 4]>>::try_into(&bytes[4..]).map_err(|_| BAD_MESSAGE)?,
        );

        Ok(Ipv4AddrRange::new(start, end))
    }
}

impl Deserialize for Ipv6AddrRange {
    const FIXED_TAG: Tag = Tag::Ipv6Range;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        let bytes = inner.as_slice_less_safe();

        let start = Ipv6Addr::from(
            <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[..16]).map_err(|_| BAD_MESSAGE)?,
        );
        let end = Ipv6Addr::from(
            <&[u8] as TryInto<[u8; 16]>>::try_into(&bytes[16..]).map_err(|_| BAD_MESSAGE)?,
        );

        Ok(Ipv6AddrRange::new(start, end))
    }
}

impl Deserialize for Value {
    const FIXED_TAG: Tag = Tag::RawBytes;

    fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
        let inner = except_tag_and_get_value(reader, Self::FIXED_TAG)?;
        serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| BAD_MESSAGE)
    }
}
