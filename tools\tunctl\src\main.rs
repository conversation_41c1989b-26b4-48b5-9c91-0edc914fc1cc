use std::net::Ipv4Addr;

use build_info::version_info;
use clap::Parser;

mod build_info;

/// tunctl
#[derive(Parser, Debug)]
#[clap(name = ABOUT, about = ABOUT, long_about = None, version = version_info())]
pub struct AppArgs {
    /// TUN name
    #[clap(short, long)]
    pub name: String,

    /// TUN IP address
    #[clap(short, long)]
    pub ip: Ipv4Addr,
}

static ABOUT: &str = "create persistent TUN interfaces";

fn main() {
    let args = AppArgs::parse();

    let mut config = tun2::Configuration::default();
    config
        .tun_name(args.name)
        .address(args.ip)
        .netmask((255, 255, 255, 0))
        .up();

    let mut dev = tun2::create(&config).unwrap();
    dev.persist().unwrap();
}
