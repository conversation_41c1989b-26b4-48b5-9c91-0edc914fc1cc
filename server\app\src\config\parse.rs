use gm_sm2::key::{Sm2Private<PERSON><PERSON>, Sm2<PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::path::PathBuf;

use percent_encoding::{AsciiSet, CONTROLS};
use pkcs8::{DecodePrivate<PERSON><PERSON>, DecodePublicKey};
use serde::{Deserialize, Deserializer};

const QUERY: &AsciiSet = &CONTROLS.add(b' ').add(b'"').add(b'#').add(b'<').add(b'>');

#[derive(Deserialize, PartialEq)]
struct WrappedString(String);

pub fn pub_key<'de, D>(deserializer: D) -> Result<Sm2PublicKey, D::Error>
where
    D: Deserializer<'de>,
{
    let value = String::deserialize(deserializer)?;

    Ok(Sm2PublicKey::from_public_key_pem(&value).unwrap())
}

pub fn sec_key<'de, D>(deserializer: D) -> Result<Sm2Private<PERSON><PERSON>, D::Error>
where
    D: Deserializer<'de>,
{
    let value = String::deserialize(deserializer)?;
    Ok(Sm2PrivateKey::from_pkcs8_pem(&value).unwrap())
}

pub fn hex_str<'de, D>(deserializer: D) -> Result<Vec<u8>, D::Error>
where
    D: Deserializer<'de>,
{
    let value = String::deserialize(deserializer)?;
    Ok(hex::decode!(value.as_bytes()))
}

pub fn hex_str_opt<'de, D>(deserializer: D) -> Result<Option<Vec<u8>>, D::Error>
where
    D: Deserializer<'de>,
{
    let value = Option::<String>::deserialize(deserializer)?;
    if let Some(value) = value {
        if value.is_empty() || value == "None" {
            return Ok(None);
        }
        return Ok(Some(hex::decode!(value.as_bytes())));
    }
    Ok(None)
}

pub fn redis_pwd<'de, D>(deserializer: D) -> Result<Option<String>, D::Error>
where
    D: Deserializer<'de>,
{
    Option::<WrappedString>::deserialize(deserializer).map(|option: Option<WrappedString>| {
        option.map(|wrapped: WrappedString| {
            let value = wrapped.0;
            percent_encoding::utf8_percent_encode(&value, QUERY).to_string()
        })
    })
}

pub fn path_buf<'de, D>(deserializer: D) -> Result<PathBuf, D::Error>
where
    D: Deserializer<'de>,
{
    let value = String::deserialize(deserializer)?;

    Ok(PathBuf::from(value))
}
