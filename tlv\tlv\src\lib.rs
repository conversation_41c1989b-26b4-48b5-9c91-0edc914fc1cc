mod de;
mod ser;
pub mod utils;

use std::collections::HashSet;

pub use de::Deserialize;
pub use ser::Serialize;

pub const BAD_MESSAGE: &str = "bad message";

/// 数据格式
#[derive(PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>, Debug)]
#[repr(u8)]
pub enum Tag {
    /// 字节序列
    RawBytes = 0x00,
    /// 布尔值
    Boolean = 0x01,
    /// u8
    Uint8 = 0x02,
    /// u16
    Uint16 = 0x03,
    /// u32
    Uint32 = 0x04,
    /// u64
    Uint64 = 0x05,
    /// UTF8 字符串
    UTF8String = 0x06,
    /// 一个或多个字段队列
    Sequence = 0x07,
    /// Ipv4地址
    Ipv4 = 0x08,
    /// Ipv6地址
    Ipv6 = 0x09,
    /// Ipv4范围
    Ipv4Range = 0x0a,
    /// Ipv6范围
    Ipv6Range = 0x0b,
    /// Ipv4 Cidr
    Ipv4Net = 0x0c,
    /// Ipv6 Cidr
    Ipv6Net = 0x0d,
    /// SocketV4Addr
    SocketAddrV4 = 0x0e,
    /// SocketV6Addr
    SocketAddrV6 = 0x0f,
    /// 未定义
    Unknown = u8::MAX,
}

impl From<u8> for Tag {
    fn from(value: u8) -> Self {
        match value {
            0x00 => Tag::RawBytes,
            0x01 => Tag::Boolean,
            0x02 => Tag::Uint8,
            0x03 => Tag::Uint16,
            0x04 => Tag::Uint32,
            0x05 => Tag::Uint64,
            0x06 => Tag::UTF8String,
            0x07 => Tag::Sequence,
            0x08 => Tag::Ipv4,
            0x09 => Tag::Ipv6,
            0x0a => Tag::Ipv4Range,
            0x0b => Tag::Ipv6Range,
            0x0c => Tag::Ipv4Net,
            0x0d => Tag::Ipv6Net,
            0x0e => Tag::SocketAddrV4,
            0x0f => Tag::SocketAddrV6,
            _ => Tag::Unknown,
        }
    }
}

impl From<Tag> for usize {
    fn from(tag: Tag) -> Self {
        tag as Self
    }
}

pub trait Sequence<T> {
    fn push(&mut self, value: T);
}

impl<T> Sequence<T> for Vec<T> {
    fn push(&mut self, value: T) {
        self.push(value);
    }
}

impl<T> Sequence<T> for HashSet<T>
where
    T: Eq + core::hash::Hash,
{
    fn push(&mut self, value: T) {
        self.insert(value);
    }
}

#[cfg(feature = "derive")]
pub use tlv_derive::{Deserialize, Serialize};

#[macro_export]
macro_rules! read_to_struct {
    ($input: expr) => {
        $input.read_all(tlv::BAD_MESSAGE, |reader| {
            tlv::Deserialize::deserialize(reader)
        })
    };
}
