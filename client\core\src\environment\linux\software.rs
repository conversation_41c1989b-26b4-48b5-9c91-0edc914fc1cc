use std::{
    collections::HashMap,
    fs,
    path::{Path, PathBuf},
    str::FromStr,
};

use freedesktop_desktop_entry::DesktopEntry;

use crate::{
    environment::{running, Software},
    linux::detect_package_ext,
};

pub fn list() -> Vec<Software> {
    let mut processes = running::processes(true);
    processes.sort();
    processes.dedup();

    let mut software_list = vec![];

    let ext = detect_package_ext();

    let locales = ["zh_CN"];
    match ext {
        "deb" => {
            let package_version = load_dpkg_status("/var/lib/dpkg/status");

            if let Ok(entries) = fs::read_dir("/var/lib/dpkg/info") {
                for entry in entries.flatten() {
                    let filename = entry.file_name();
                    let Some(filename) = filename.to_str() else {
                        continue;
                    };
                    // 只处理以 `.list` 结尾的文件
                    if filename.ends_with(".list") {
                        let package_name = filename.strip_suffix(".list").unwrap_or("").to_string();
                        let Ok(content) = fs::read_to_string(entry.path()) else {
                            continue;
                        };
                        for line in content.lines() {
                            if line.ends_with(".desktop") {
                                if let Ok(entry) = DesktopEntry::from_path(
                                    PathBuf::from_str(line).unwrap(),
                                    Some(&locales),
                                ) {
                                    let Some(software_name) =
                                        entry.name(&locales).or(entry.generic_name(&locales))
                                    else {
                                        continue;
                                    };

                                    let Some(path) = entry.exec() else {
                                        continue;
                                    };

                                    let executable_file_location =
                                        path.split_whitespace().next().unwrap();
                                    let path = Path::new(executable_file_location);
                                    let exe = path.file_name().unwrap_or_default();
                                    let exe = exe.to_string_lossy().to_string();

                                    let version = package_version.get(&package_name);

                                    software_list.push(Software {
                                        run: processes.contains(&exe),
                                        software_name: software_name.to_string(),
                                        system_version: version.map(ToOwned::to_owned),
                                        executable_file_location: Some(
                                            executable_file_location.to_string(),
                                        ),
                                        exe: Some(exe),
                                    });
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
        _ => (),
    }

    software_list.sort_by(|x, y| x.software_name.cmp(&y.software_name));
    software_list
}

/// 加载 dpkg 的 status 文件，返回一个 HashMap<PackageName, Version>
fn load_dpkg_status(file_path: &str) -> HashMap<String, String> {
    let mut package_data = HashMap::new();
    if let Ok(content) = fs::read_to_string(file_path) {
        let mut current_package = None;
        let mut current_version = None;

        for line in content.lines() {
            if line.starts_with("Package: ") {
                current_package = Some(line.replace("Package: ", "").trim().to_string());
            } else if line.starts_with("Version: ") {
                current_version = Some(line.replace("Version: ", "").trim().to_string());
            } else if line.is_empty() {
                // 到达一个包的结尾，保存 Package 和 Version 信息
                if let (Some(package), Some(version)) =
                    (current_package.take(), current_version.take())
                {
                    package_data.insert(package, version);
                }
            }
        }
    }
    package_data
}
