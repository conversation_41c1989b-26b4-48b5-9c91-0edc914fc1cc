use serde::Serialize;
use winreg::{enums::HKEY_LOCAL_MACHINE, RegKey};

#[allow(dead_code)]
#[derive(Debug, Default, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct Firewall {
    pub private_network_firewall: bool,
    pub public_network_firewall: bool,
    pub domain_firewall: bool,
}

pub fn is_enable() -> Firewall {
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let Ok(policy) = hklm.open_subkey(
        "SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy",
    ) else {
        return Default::default();
    };

    macro_rules! network_profile {
        ($profile: literal) => {
            policy
                .open_subkey($profile)
                .map(|profile| {
                    let enabled = profile
                        .get_value::<u32, &str>("EnableFirewall")
                        .unwrap_or_default();
                    enabled == 1
                })
                .unwrap_or_default()
        };
    }

    let domain = network_profile!("DomainProfile");
    let standard = network_profile!("StandardProfile");
    let public = network_profile!("PublicProfile");

    Firewall {
        domain_firewall: domain,
        private_network_firewall: standard,
        public_network_firewall: public,
    }
}
