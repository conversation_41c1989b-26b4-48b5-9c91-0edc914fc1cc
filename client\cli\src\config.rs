use serde::Deserialize;
use std::{
    fs,
    path::{Path, PathBuf},
};
use url::Url;

/// 配置
#[derive(Deserialize)]
pub struct Config {
    /// 日志
    pub log: Log,

    /// 网关配置获取地址
    pub url: Url,

    /// 证书目录
    pub cert_dir: PathBuf,

    /// 用户
    pub user: User,
}

#[derive(Deserialize)]
pub struct Log {
    /// 日志等级
    pub level: String,
    /// 日志目录
    pub dir: String,
    /// 日志文件名称
    pub name: String,
}

impl Default for Log {
    fn default() -> Self {
        Self {
            level: "error,oneid_cli=info".to_string(),
            dir: ".".to_string(),
            name: "oneid".to_string(),
        }
    }
}

#[derive(Clone, Deserialize)]
pub struct User {
    /// 单位
    pub unit: String,

    /// 用户名
    pub username: String,

    /// 密码
    pub password: String,
}

pub fn load(path: impl AsRef<Path>) -> anyhow::Result<Config> {
    let content = fs::read_to_string(path)?;
    let config = toml::from_str(&content)?;
    Ok(config)
}
