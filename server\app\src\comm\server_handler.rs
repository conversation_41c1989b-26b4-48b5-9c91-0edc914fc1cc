use std::{
    collections::HashSet,
    net::IpAddr,
    sync::{atomic::Ordering, Arc},
};

use base::packet::{Message, MessageCodec, MessageType};
use err_ext::ErrorExt;
use futures::{SinkExt, StreamExt};
use http::HeaderValue;
use proxy_request::HttpRequest;
use tlv::Serialize;
use tlv_types::{AccessResource, MfaCompleteAuth, ProxyRequest};
use tokio::{net::TcpStream, sync::broadcast};
use tokio_rustls::server::TlsStream;
use tokio_util::codec::Framed;
use tracing::{debug, info, warn, Instrument};

use crate::{
    backend::{client::BackendClient, proxy},
    cache::CacheManager,
    constants::{ONLINE_DEVICES, ONLINE_USERS},
    enums::{CloseReason, DeviceType},
    message::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    packet::client::Client<PERSON><PERSON>etHandle,
    traceablitity::WEBAPPS_VIRTUAL_IP_MAPPING,
    traffic::{AccessKey, SESSION_RESOURCE_TRAFFIC, SESSION_TRAFFIC},
    EventSender, InternalEvent, CLIENTS,
};

use super::*;

struct HttpProxySender(flume::Sender<Message>);

impl proxy::Sender for HttpProxySender {
    async fn send(&self, message: Message) -> Result<(), proxy_request::Error> {
        if self.0.send_async(message).await.is_err() {
            error!("client already disconnect or thread panicked.");
        }
        Ok(())
    }
}

/// 客户端
struct MixClient {
    authorization_interval: u64,

    session: Session,
    stream: Option<Framed<TlsStream<TcpStream>, MessageCodec>>,

    cache_manager: CacheManager,
    event_sender: EventSender,
    client_packet_handle: ClientPacketHandle,
    mq_handle: MqHandle,
    resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,

    rx: Option<flume::Receiver<CommCommand>>,

    /// 已认证的资源列表
    authed_servers: HashSet<IpAddr>,
    /// 已发送需要认证消息的资源列表
    sent_servers: HashSet<IpAddr>,
    /// 访问服务器信息
    access_list: Vec<AccessInfo>,
    /// 后端服务
    backend_client: Arc<BackendClient>,
    /// 本次连接已访问过的目标资源
    accessed: HashSet<(IpAddr, Option<u16>)>,
}

pub struct MixHandleArgs {
    pub authorization_interval: u64,
    pub session: Session,
    pub stream: Framed<TlsStream<TcpStream>, MessageCodec>,
    pub cache_manager: CacheManager,
    pub event_sender: EventSender,
    pub backend_client: Arc<BackendClient>,
    pub client_packet_handle: ClientPacketHandle,
    pub mq_handle: MqHandle,
    pub resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,
}

pub async fn handle(args: MixHandleArgs) {
    let device_id = args.session.device_id.clone();

    let tenant = args.session.tenant.clone();
    let username = args.session.username.clone();
    let env = args.session.env.clone();

    let (tx, rx) = flume::unbounded();

    args.event_sender
        .send(InternalEvent::PostClientOnline {
            ip: args.session.peer_addr.ip(),
            device_id,
            tenant,
            username: username.clone(),
            env,
            handle: ClientHandle {
                username,
                command_tx: tx,
            },
            peer_addr: args.session.peer_addr,
        })
        .await;

    let client = MixClient {
        authorization_interval: args.authorization_interval,
        session: args.session,
        stream: Some(args.stream),
        cache_manager: args.cache_manager,
        event_sender: args.event_sender,
        client_packet_handle: args.client_packet_handle,
        mq_handle: args.mq_handle,
        resource_whitelist: args.resource_whitelist,
        rx: Some(rx),
        authed_servers: Default::default(),
        sent_servers: Default::default(),
        access_list: Default::default(),
        backend_client: args.backend_client,
        accessed: Default::default(),
    };

    client.online().await;
}

impl MixClient {
    #[tracing::instrument(name = "CommunicationHandler", parent = None, skip_all, fields(peer_addr = %self.session.peer_addr, device.id = self.session.device_id, username = self.session.username))]
    async fn online(mut self) {
        info!("online");
        // 记录设备在线
        let online_device_key = format!(
            "{}|{}{}",
            &self.session.tenant, ONLINE_DEVICES, &self.session.device_id
        );
        // 同时在线的其他设备
        let online_devices = format!(
            "{}|{}{}",
            &self.session.tenant, ONLINE_USERS, &self.session.username
        );

        let device_id = self.session.device_id.clone();
        _ = self.cache_manager.set(&online_device_key, "", None).await;

        // 插入到列表头部返回列表数量
        if let Some(count) = self.cache_manager.lpush(&online_devices, &device_id).await {
            // 如果有多个设备
            if count >= 2 {
                // 获取列表从第二个数据开始
                if let Some(devices) = self
                    .cache_manager
                    .lrange::<String>(&online_devices, 1, count as i64)
                    .await
                {
                    let local_ip = self.session.local_addr.ip();

                    for device in devices {
                        if device_id.as_str() == device.as_str()
                            || !device.starts_with(&device_id[0..2])
                        {
                            continue;
                        }
                        let clients = CLIENTS.read().await;
                        if let Some(client) = clients.get(&device) {
                            let message = Message::new(
                                MessageType::CloseReason,
                                &(CloseReason::LoginElseWhere as u8).serialize(),
                            );
                            let command = CommCommand::Message(message);
                            client.handle.send(command).await;

                            warn!(
                                previous.device = device,
                                "user is logged in on another device"
                            );

                            let command = CommCommand::Kill;
                            client.handle.send(command).await;
                        }
                        self.mq_handle.online((local_ip, device)).await;
                    }
                }
            }
        }

        self.access_list = super::common::load_authorize(
            &self.session.tenant,
            &self.session.username,
            &self.session.device_id,
            &self.cache_manager,
        )
        .await;
        debug!("authorization: {:#?}", &self.access_list);
        self.run().await;
    }

    async fn run(mut self) {
        let stream = self.stream.take();
        if stream.is_none() {
            warn!("missing stream");
            return;
        }
        let stream = stream.unwrap();
        let (mut sink, mut stream) = stream.split();

        let local_ip = self.session.local_addr.ip();

        let (tx, access_info_rx) = flume::bounded(1);

        let (internal_tx, internal_rx) = flume::unbounded();
        let (close_tx, mut close_rx) = broadcast::channel::<Option<CloseReason>>(1);

        super::common::spawn_client_strategy(
            self.session.username.clone(),
            self.authorization_interval,
            internal_tx.clone(),
            self.backend_client.clone(),
        )
        .await;

        super::common::spawn_authorize(
            &self.session.tenant,
            &self.session.username,
            &self.session.device_id,
            self.authorization_interval,
            self.cache_manager.clone(),
            tx,
        )
        .await;

        let command_rx = self.rx.take().unwrap();
        let session_id = self.session.id.clone();
        tokio::spawn({
            let whitelist = self.resource_whitelist.clone();
            async move {
                loop {
                    tokio::select! {
                        reason = close_rx.recv() => {
                            if let Ok(Some(reason)) = reason {
                                if reason != CloseReason::Exceptions {
                                    let message = Message::new(MessageType::CloseReason, &(reason as u8).serialize());
                                    if let Err(err) = sink.send(message).await {
                                        error!("write. {err}");
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                        command = command_rx.recv_async() => match command {
                            Err(_) => break,
                            Ok(command) => match command {
                                CommCommand::Kill => break,
                                CommCommand::Close(ip) => {
                                    if local_ip != ip {
                                        let message = Message::new(MessageType::CloseReason, &(CloseReason::LoginElseWhere as u8).serialize());
                                        if let Err(err) = sink.send(message).await {
                                            error!("write. {err}");
                                            break;
                                        }
                                        break;
                                    }
                                }
                                CommCommand::Message(message) => {
                                    if let Err(err) = sink.send(message).await {
                                        error!("write. {err}");
                                        break;
                                    }
                                }
                                CommCommand::Packet(packet) => {
                                    let src_port = packet.ports.map(|(src_port, _dst_port)| src_port);
                                    if !Self::in_whitelist(whitelist.clone(), packet.src, src_port) {
                                        let (_up, down) = SESSION_TRAFFIC
                                            .entry(session_id.clone())
                                            .or_default()
                                            .await
                                            .into_value();
                                        down.fetch_add(packet.data.len() as u64, Ordering::Relaxed);
                                        let key = AccessKey {
                                            session_id: session_id.clone(),
                                            protocol: packet.next_protocol,
                                            ip: packet.src,
                                            port: src_port,
                                        };
                                        let (_up, down) = SESSION_RESOURCE_TRAFFIC
                                            .entry(key)
                                            .or_default()
                                            .await
                                            .into_value();
                                        down.fetch_add(packet.data.len() as u64, Ordering::Relaxed);
                                    }
                                    let message = Message::new(MessageType::ServerPacket, &packet.data);
                                    if let Err(err) = sink.send(message).await {
                                        error!("write. {err}");
                                        break;
                                    }
                                }
                            }
                        },
                        message = internal_rx.recv_async() => match message {
                            Err(_) => break,
                            Ok(message) => {
                                if let Err(err) = sink.send(message).await {
                                    error!("write. {err}");
                                    break;
                                }
                            }
                        }
                    }
                }
                if let Err(err) = sink.close().await {
                    error!("{err}");
                }
            }
        }.in_current_span());

        let (auth_tx, auth_rx) = flume::bounded(1);

        tokio::spawn({
            async move {
                loop {
                    tokio::select! {
                        Ok(access_info) = access_info_rx.recv_async() => {
                            self.access_list = access_info;
                            debug!(authorization = ?self.access_list);
                        }
                        command = auth_rx.recv_async() => match command {
                            Err(_) => break,
                            Ok(command) => match command {
                                InternalAuthCommand::CompleteAuth(access_resource) => {
                                    self.auth_success_resource(access_resource.ip());
                                }
                                InternalAuthCommand::CancelAuth(access_resource) => {
                                    self.cancel_resource(access_resource.ip());
                                }
                            }
                        },
                        message = stream.next() => match message {
                            None => break,
                            Some(Err(err)) => {
                                error!("{err}");
                                break;
                            }
                            Some(Ok(message)) => match message.r#type() {
                                // 修改密码
                                MessageType::ChangePwd => {
                                    debug!("change password");

                                    tokio::spawn({
                                        let backend_client = self.backend_client.clone();
                                        let tx = internal_tx.clone();
                                        let close_tx = close_tx.clone();
                                        async move {
                                            let input = message.payload();
                                            match input.read_all(tlv::BAD_MESSAGE, |reader| {
                                                let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;

                                                Ok(inner.as_slice_less_safe())
                                            }) {
                                                Ok(bytes) => {
                                                    let result = match backend_client.change_pwd(bytes).await {
                                                        Ok(result) => result,
                                                        Err(result) => result,
                                                    };
                                                    let message = Message::new(MessageType::ChangePwdResult, &result.serialize());

                                                    if tx.send_async(message).await.is_err() {
                                                        _ = close_tx.send(Some(CloseReason::UserPwdChanged));
                                                    }
                                                }
                                                Err(_) => {
                                                    error!(message_type = ?MessageType::ChangePwd, "bad message");
                                                }
                                            }
                                        }
                                    }.in_current_span());
                                }
                                // 认证完成
                                MessageType::MfaAuthCompleted => {
                                    debug!("mfa auth completed");

                                    let input = message.payload();
                                    let result: Result<MfaCompleteAuth, &'static str> =
                                        tlv::read_to_struct!(input);
                                    let Ok(auth) = result else {
                                        error!(message_type = ?MessageType::MfaAuthCompleted, "bad message");
                                        continue;
                                    };

                                    let device_type = DeviceType::from(self.session.device_id.as_str());
                                    match device_type {
                                        DeviceType::MOBILE => {
                                            self.auth_success_resource(auth.resource.ip());
                                        }
                                        DeviceType::DESKTOP => {
                                            tokio::spawn({
                                                let backend_client = self.backend_client.clone();
                                                let tx = auth_tx.clone();
                                                async move {
                                                    match backend_client.ask_auth(&auth.auth_result_id).await {
                                                        Ok(result) => {
                                                            let value = serde_json::from_slice::<Value>(&result).unwrap();
                                                            if value["data"]["auth"].as_bool().unwrap_or_default() {
                                                                _ = tx.send_async(InternalAuthCommand::CompleteAuth(auth.resource)).await;
                                                            } else {
                                                                _ = tx.send_async(InternalAuthCommand::CancelAuth(auth.resource)).await;
                                                            }
                                                        }
                                                        Err(err) => {
                                                            let parsed_value = serde_json::from_slice::<Value>(&err);
                                                            warn!("query auth result failed. {:?}", parsed_value);
                                                        }
                                                    }
                                                }
                                            }.in_current_span());
                                        }
                                        _ => {
                                            self.cancel_resource(auth.resource.ip());
                                        }
                                    }
                                }
                                // 取消认证
                                MessageType::CancelMfaAuth => {
                                    debug!("cancel mfa auth");
                                    let input = message.payload();
                                    let result: Result<AccessResource, &'static str> =
                                        tlv::read_to_struct!(input);
                                    let Ok(resource) = result else {
                                        error!(message_type = ?MessageType::MfaAuthCompleted, "bad message");
                                        continue;
                                    };
                                    self.cancel_resource(resource.ip());
                                }
                                // 生成票据
                                MessageType::GenerateSSOTicket => {
                                    debug!("generate sso ticket");
                                    tokio::spawn({
                                        let backend_client = self.backend_client.clone();
                                        let tx = internal_tx.clone();
                                        let username = self.session.username.clone();
                                        let close_tx = close_tx.clone();
                                        async move {
                                            let result = backend_client.generate_ticket(&username).await;
                                            let message = Message::new(MessageType::SSOTicketResult, &result.serialize());
                                            if tx.send_async(message).await.is_err() {
                                                _ = close_tx.send(Some(CloseReason::Normal));
                                            }
                                        }
                                    }.in_current_span());
                                }
                               // 重置票据
                                MessageType::RefreshToken => {
                                    debug!("refresh reconnect ticket");
                                    let input = message.payload();
                                    let result: Result<String, &'static str> =
                                            tlv::read_to_struct!(input);
                                    let Ok(reconnect_token) = result else {
                                        error!(message_type = ?MessageType::RefreshToken, "bad message");
                                        continue;
                                    };

                                    tokio::spawn({
                                        let backend_client = self.backend_client.clone();
                                        let tx = internal_tx.clone();
                                        let close_tx = close_tx.clone();
                                        async move {
                                            let result = backend_client.refresh_ticket(&reconnect_token).await.unwrap_or_else(|result| result);
                                            let message = Message::new(
                                                MessageType::RefreshTokenResult,
                                                &result.serialize(),
                                            );
                                            if tx.send_async(message).await.is_err() {
                                                _ = close_tx.send(Some(CloseReason::Normal));
                                            }
                                        }
                                    }.in_current_span());
                                }
                                // 转发数据包
                                MessageType::ForwardPacket => {
                                    let input = message.payload();
                                    let (next_action, message) = self.process_forward_msg(input.as_slice_less_safe()).await;
                                    if let Some(message) = message {
                                        _ = internal_tx.send_async(message).await;
                                    }
                                    if let NextAction::Break = next_action {
                                        break;
                                    }
                                }
                                // 代理请求接口
                                MessageType::ProxyRequest => {
                                    let input = message.payload();
                                    let result: Result<ProxyRequest, &'static str> =
                                        tlv::read_to_struct!(input);
                                    let Ok(request) = result else {
                                        error!(message_type = ?MessageType::ProxyRequest, "bad message");
                                        continue;
                                    };
                                    let Ok(mut http_request) =
                                        serde_json::from_slice::<HttpRequest>(&request.data)
                                    else {
                                        error!(message_type = ?MessageType::ProxyRequest, "bad message");
                                        continue;
                                    };

                                    tokio::spawn({
                                        let tx = internal_tx.clone();
                                        let base_url = self.backend_client.base_url();
                                        _ = http_request.url.set_scheme(base_url.scheme());
                                        _ = http_request.url.set_host(base_url.host_str());
                                        _ = http_request.url.set_port(base_url.port());
                                        let device_id = self.session.device_id.clone();

                                        if let Some(headers) = http_request.headers.as_mut() {
                                            let ip = self.session.peer_addr.ip().to_string();
                                            _ = headers.0.insert("x-forwarded-for", HeaderValue::from_str(ip.as_str()).unwrap());
                                        }

                                        async move {
                                            let response_sender = HttpProxySender(tx);
                                            let url = http_request.url.clone();
                                            if let Err(err) = proxy::execute(&device_id, request.seq, http_request, response_sender).await {
                                                error!(%url, "http proxy fail. {}", err.display_chain());
                                            }
                                        }
                                    }.in_current_span());
                                }
                                message_type => {
                                    error!(
                                        ?message_type,
                                        "received unexpected message",
                                    );
                                }
                            }
                        }
                    }
                }

                self.shutdown().await;
            }
        }.in_current_span());
    }

    async fn shutdown(self) {
        self.event_sender
            .send(InternalEvent::PostClientOffline {
                device_id: self.session.device_id.clone(),
                peer_addr: self.session.peer_addr,
            })
            .await;

        self.backend_client
            .logout(
                &self.session.username,
                &self.session.device_id,
                &self.session.peer_addr.to_string(),
            )
            .await;

        let key = format!(
            "{}|{}{}",
            &self.session.tenant, ONLINE_DEVICES, &self.session.device_id
        );

        _ = self.cache_manager.del(&key).await;

        let key = format!(
            "{}|{}{}",
            &self.session.tenant, ONLINE_USERS, &self.session.username
        );
        _ = self
            .cache_manager
            .lrem(&key, 0, &self.session.device_id)
            .await;

        info!("offline");
    }

    /// 处理IP包
    async fn process_forward_msg(&mut self, payload: &[u8]) -> (NextAction, Option<Message>) {
        let mut packet = match base::utils::net::parse_ip_packet(payload.to_vec()) {
            Some(packet) => packet,
            None => {
                warn!("failed to parse client packet.");
                return (NextAction::Continue, None);
            }
        };

        let dst_port = packet.ports.map(|(_src_port, dst_port)| dst_port);
        // web资源虚拟IP地址映射
        let trace = match (dst_port, packet.next_protocol) {
            (Some(dst_port), 6) => {
                let mapping = WEBAPPS_VIRTUAL_IP_MAPPING.read().await;
                if let Some(real_ip) = mapping.get(&SocketAddr::new(packet.dest, dst_port)) {
                    packet.set_dest(*real_ip);
                    true
                } else {
                    false
                }
            }
            _ => false,
        };

        // 资源白名单不统计信息
        if Self::in_whitelist(self.resource_whitelist.clone(), packet.dest, dst_port) {
            self.client_packet_handle
                .send((self.session.device_id.clone(), packet, trace))
                .await;
            return (NextAction::Continue, None);
        }

        let (up, _down) = SESSION_TRAFFIC
            .entry(self.session.id.clone())
            .or_default()
            .await
            .into_value();
        up.fetch_add(payload.len() as u64, Ordering::Relaxed);

        let key = AccessKey {
            session_id: self.session.id.clone(),
            protocol: packet.next_protocol,
            ip: packet.dest,
            port: dst_port,
        };

        let (up, _down) = SESSION_RESOURCE_TRAFFIC
            .entry(key)
            .or_default()
            .await
            .into_value();
        up.fetch_add(payload.len() as u64, Ordering::Relaxed);

        let (permitted, action) = self.has_permission_by_socket_addr(packet.dest, dst_port);
        if !permitted {
            info!(resource = %packet.dest, port = ?dst_port, action);
            match action {
                DENY_AND_DISABLE_ACCOUNT | DENY_AND_QUIT => {
                    info!("does not satisfy the policy.");
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::PolicyNotMatch as u8).serialize(),
                    );
                    if action == DENY_AND_DISABLE_ACCOUNT {
                        let username = self.session.username.clone();
                        let client = self.backend_client.clone();
                        tokio::spawn(async move {
                            client.disable_user(&username).await;
                        });
                    }
                    return (NextAction::Break, Some(message));
                }
                AUTH_REQUIRED => {
                    if self.sent_servers.contains(&packet.dest) {
                        debug!(resource = %packet.dest, "mfa request is sended.");
                        return (NextAction::Continue, None);
                    }

                    let message = Message::new(MessageType::RequiredMfa, &packet.dest.serialize());
                    self.request_resource(packet.dest);
                    return (NextAction::Continue, Some(message));
                }
                _ => {
                    return (NextAction::Continue, None);
                }
            }
        }

        super::common::handle_virtual_ip_and_log(
            self.session.peer_addr.ip(),
            &self.session.device_id,
            &self.session.username,
            &self.session.tenant,
            &mut packet,
            &mut self.accessed,
            self.mq_handle.clone(),
        )
        .await;

        self.client_packet_handle
            .send((self.session.device_id.clone(), packet, trace))
            .await;

        (NextAction::Continue, None)
    }

    fn in_whitelist(
        whitelist: Option<Arc<HashSet<SocketAddr>>>,
        target: IpAddr,
        port: Option<u16>,
    ) -> bool {
        match whitelist {
            Some(whitelist) => whitelist.iter().any(|addr| {
                let mut r#match = addr.ip() == target;
                if r#match && addr.port() != 0 {
                    if let Some(port) = port {
                        r#match = addr.port() == port;
                    }
                }
                r#match
            }),
            None => false,
        }
    }

    fn in_rules(resource: IpAddr, rules: &[AccessRule], port: Option<u16>) -> bool {
        rules.iter().any(|rule| rule.r#match(resource, port))
    }

    /// 判断是否有目标的访问权限
    fn has_permission_by_socket_addr(
        &mut self,
        ip: IpAddr,
        port: Option<u16>,
    ) -> (bool, &'static str) {
        for access_info in self.access_list.iter() {
            if Self::in_rules(ip, &access_info.rules, port) {
                match access_info.action.as_str() {
                    ALLOW => {
                        return (true, ALLOW);
                    }
                    // 需要认证
                    AUTH_REQUIRED => {
                        if self.authed_servers.contains(&ip) {
                            return (true, AUTH_REQUIRED);
                        }

                        return (false, AUTH_REQUIRED);
                    }
                    // 拒绝访问并禁用账号
                    DENY_AND_DISABLE_ACCOUNT => {
                        return (false, DENY_AND_DISABLE_ACCOUNT);
                    }
                    // 拒绝访问并退出
                    DENY_AND_QUIT => {
                        return (false, DENY_AND_QUIT);
                    }
                    // 拒绝访问
                    _ => {
                        return (false, DENY);
                    }
                }
            }
        }

        (false, DENY)
    }

    fn request_resource(&mut self, resource: IpAddr) {
        self.sent_servers.insert(resource);
    }

    fn auth_success_resource(&mut self, resource: IpAddr) {
        self.sent_servers.remove(&resource);
        self.authed_servers.insert(resource);
    }

    fn cancel_resource(&mut self, resource: IpAddr) {
        self.sent_servers.remove(&resource);
    }
}
