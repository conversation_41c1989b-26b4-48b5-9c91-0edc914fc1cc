use crate::{Tag, BAD_MESSAGE};
use ipnet::IpNet;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

pub fn len_to_bytes(len: usize) -> Vec<u8> {
    let len = len as u64;
    match len.leading_zeros() {
        0 => {
            panic!("max length 7FFFFFFFFFFFFFFF");
        }
        1..=8 => {
            let mut bytes = vec![0x80 | 8];
            bytes.extend(len.to_be_bytes().iter());
            bytes
        }
        9..=16 => {
            let mut bytes = vec![0x80 | 7];
            bytes.extend(len.to_be_bytes().iter().skip(1));
            bytes
        }
        17..=24 => {
            let mut bytes = vec![0x80 | 6];
            bytes.extend(len.to_be_bytes().iter().skip(2));
            bytes
        }
        25..=32 => {
            let mut bytes = vec![0x80 | 5];
            bytes.extend(len.to_be_bytes().iter().skip(3));
            bytes
        }
        33..=40 => {
            let mut bytes = vec![0x80 | 4];
            bytes.extend(len.to_be_bytes().iter().skip(4));
            bytes
        }
        41..=48 => {
            let mut bytes = vec![0x80 | 3];
            bytes.extend(len.to_be_bytes().iter().skip(5));
            bytes
        }
        49..=56 => {
            let mut bytes = vec![0x80 | 2];
            bytes.extend(len.to_be_bytes().iter().skip(6));
            bytes
        }
        _ => {
            vec![len as u8]
        }
    }
}

pub fn insert_tag(buf: &mut Vec<u8>, tag: Tag) {
    let len = buf.len();
    buf.insert(0, tag as u8);
    let mut len_bytes = len_to_bytes(len);
    len_bytes.reverse();
    len_bytes.iter().for_each(|b| buf.insert(1, *b));
}

pub fn except_tag_and_get_value<'a>(
    reader: &mut untrusted::Reader<'a>,
    tag: Tag,
) -> Result<untrusted::Input<'a>, &'static str> {
    let (actual_tag, inner) = read_tag_and_get_value(reader)?;
    if usize::from(tag) != usize::from(actual_tag) {
        return Err(BAD_MESSAGE);
    }
    Ok(inner)
}

pub fn read_tag_and_get_value<'a>(
    reader: &mut untrusted::Reader<'a>,
) -> Result<(u8, untrusted::Input<'a>), &'static str> {
    let tag = reader.read_byte().map_err(|_| BAD_MESSAGE)?;

    let length = reader.read_byte().map_err(|_| BAD_MESSAGE)?;
    let length = if length & 0x80 == 0 {
        usize::from(length)
    } else {
        let length_len = (length ^ 0x80) as usize;
        let mut len_bytes = [0; 8];

        let bytes = reader.read_bytes(length_len).map_err(|_| BAD_MESSAGE)?;

        len_bytes[8 - length_len..].copy_from_slice(bytes.as_slice_less_safe());
        u64::from_be_bytes(len_bytes) as usize
    };

    let inner = reader.read_bytes(length).map_err(|_| BAD_MESSAGE)?;

    Ok((tag, inner))
}

// 公共trait定义IP转换行为
pub trait ToInt {
    fn to_int(&self) -> u128;
    fn from_int(value: u128) -> Self;
    fn max_bits() -> u8;
    fn to_ip(self) -> IpAddr;
}

// IPv4实现
impl ToInt for Ipv4Addr {
    fn to_int(&self) -> u128 {
        u128::from(u32::from_be_bytes(self.octets()))
    }

    fn from_int(value: u128) -> Self {
        Ipv4Addr::from((value as u32).to_be_bytes())
    }

    fn max_bits() -> u8 {
        32
    }

    fn to_ip(self) -> IpAddr {
        IpAddr::V4(self)
    }
}

// IPv6实现
impl ToInt for Ipv6Addr {
    fn to_int(&self) -> u128 {
        u128::from_be_bytes(self.octets())
    }

    fn from_int(value: u128) -> Self {
        Ipv6Addr::from(value.to_be_bytes())
    }

    fn max_bits() -> u8 {
        128
    }

    fn to_ip(self) -> IpAddr {
        IpAddr::V6(self)
    }
}

// 核心CIDR生成算法（泛型实现）
pub fn range_to_cidr<T: ToInt>(start: T, end: T) -> Vec<IpNet> {
    let start = start.to_int();
    let end = end.to_int();

    if start > end {
        return vec![];
    }

    let mut current = start;
    let end_ip = end;
    let max_bits = T::max_bits();
    let mut cidrs = Vec::new();

    while current <= end_ip {
        let remaining = end_ip - current + 1;
        let trailing_zeros = current.trailing_zeros().min(max_bits.into());
        let mut block_size = 1u128 << trailing_zeros;

        // 调整块大小以适应剩余范围
        while block_size > remaining || (current & (block_size - 1)) != 0 {
            block_size >>= 1;
            if block_size == 0 {
                block_size = 1;
                break;
            }
        }

        let mask = max_bits - block_size.trailing_zeros() as u8;
        cidrs.push(IpNet::new(T::from_int(current).to_ip(), mask).unwrap());

        current += block_size;
    }

    cidrs
}

#[test]
fn test_ip_range_to_cidr() {
    // ip_range_to_cidr(IpAddr::V4(Ipv4Addr::new(2, 2, 2, 2)), IpAddr::V4(Ipv4Addr::new(3, 3, 3,
    // 3))).unwrap();

    let cidrs = range_to_cidr(Ipv4Addr::new(2, 2, 2, 2), Ipv4Addr::new(3, 3, 3, 3));
    for cidr in cidrs {
        println!("{}", cidr);
    }

    // IPv6测试
    let v6_result = range_to_cidr(
        "::1".parse::<Ipv6Addr>().unwrap(),
        "::8".parse::<Ipv6Addr>().unwrap(),
    );
    println!("\nIPv6 CIDRs:");
    for cidr in v6_result {
        println!("{}", cidr);
    }
}
