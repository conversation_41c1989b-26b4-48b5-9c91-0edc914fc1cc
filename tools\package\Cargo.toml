[package]
description = "客户端打包工具"
edition = "2021"
name = "package"
version = "0.0.0"

[dependencies]
anyhow = "1.0.75"
bus = "2.4.1"
chrono = "0.4.31"
clap = { version = "4.4.10", features = ["derive"] }
colored = "2.0.4"
ctrlc = "3.4.2"
indicatif = "0.17.7"
json-patch = "1.2.0"
rand = "0.8.5"
range = "1.0.0"
rpassword = "7.3.1"
rustc-host = "0.1.7"
serde = { version = "1.0.193", features = ["derive"] }
serde_json = "1.0.108"
tokio = { version = "1.35.1", features = ["full"] }
version = { version = "0.0.0", path = "../../version" }

[target.'cfg(target_os = "linux")'.dependencies]
elf = "0.7.2"
glob = "0.3.2"
rbpf = "0.2.0"
