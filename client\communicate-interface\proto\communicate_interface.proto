syntax = "proto3";

package oneidcore.communicate_interface;

import "google/protobuf/empty.proto";
// import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
// import "google/protobuf/duration.proto";
// import "google/protobuf/any.proto";

service CommunicateService {
  // 获取设备ID
  rpc GetDeviceId(google.protobuf.Empty) returns(google.protobuf.StringValue) {};

  // 获取系统信息
  rpc GetSystemInfo(google.protobuf.Empty) returns(google.protobuf.StringValue) {};

  rpc Exit(google.protobuf.Empty) returns(google.protobuf.Empty) {};

  // 登录
  rpc login(Login) returns(google.protobuf.StringValue) {};
  // 通过验证码修改密码
  rpc ChangePwdByCode(ChangePwdCode) returns(google.protobuf.StringValue) {};
  // 退出
  rpc Disconnect(google.protobuf.Empty) returns(google.protobuf.BoolValue) {};

  // 监听事件
  rpc EventsListen(google.protobuf.Empty) returns (stream CoreEvent) {};
  // 获取下一个需要认证的资源
  rpc NextAuthEvent(google.protobuf.Empty) returns (AuthEvent) {};
  // 认证通过
  rpc CompleteAuthEvent(CompleteAuth) returns(google.protobuf.BoolValue) {};
  // 取消认证
  rpc CancelAuthEvent(CancelAuth) returns(google.protobuf.BoolValue) {};

  // 获取票据(通过事件发送返回结果)
  rpc GetTicket(google.protobuf.Empty) returns(google.protobuf.Empty) {};
  // 通过旧密码修改密码
  rpc ChangePwdByOldPwd(ChangePwdOldPwd) returns(google.protobuf.Empty) {};

  // 代理请求
  rpc HttpRequest(ProxyRequest) returns(ProxyResponse) {};

  // 检查更新
  rpc CheckUpdate(CheckUpdateParam) returns(UpdateResponse) {};
  // 下载
  rpc Download(google.protobuf.Empty) returns(google.protobuf.Empty) {};
  // 取消更新
  rpc CancelUpdate(google.protobuf.Empty) returns(google.protobuf.Empty) {};
  // 安装
  rpc Install(google.protobuf.Empty) returns(google.protobuf.Empty) {};
}

message ConnectionConfig {
  string host = 1;
  optional string ip = 2;
  uint32 port = 3;
  uint32 spaPort = 4;

  optional string tenant = 5;
  optional string secret = 6;
  optional string username = 7;
}

message Login {
  ConnectionConfig connection_config = 1;
  string payload = 2;
}

message ChangePwdCode {
  ConnectionConfig connection_config = 1;
  string payload = 2;
}

message ChangePwdOldPwd {
  string username = 1;
  string old_password = 2;
  string new_password = 3;
}

enum AfterDisconnect {
  NOTHING = 0;
  RECONNECT = 1;
}

message ErrorState {
  enum Cause {
    IPV6_UNAVAILABLE = 0;
    SET_DNS_ERROR = 1;
    SET_ROUTE_ERROR = 2;
    START_TUNNEL_ERROR = 3;
    TUNNEL_PARAMETER_MISSING = 4;
    IS_OFFLINE = 5;
  }

  Cause cause = 1;
}

message TunnelState {
  message Disconnected {}
  message Connecting {}
  message Connected {}
  message Disconnecting {AfterDisconnect after_disconnect = 1;}
  message Error {ErrorState error_state = 1;}

  oneof state {
    Disconnected disconnected = 1;
    Connecting connecting = 2;
    Connected connected = 3;
    Disconnecting disconnecting = 4;
    Error error = 5;
  }
}

message BackendErrorState {
  enum Cause {
    AUTH_FAILED = 0;
    CONNECT_TIMEOUT = 1;
    NEED_CHANGE_PWD = 2;
  }

  Cause cause = 1;
}

message BackendState {
  message Disconnected {
    optional int32 reason = 1;
  }
  message ProxyConnecting {}
  message ProxyConnected {}
  message Connecting {}
  message Connected {}
  message Disconnecting {}
  message Error {BackendErrorState error_state = 1;}

  oneof state {
    Disconnected disconnected = 1;
    ProxyConnecting proxy_connecting = 2;
    ProxyConnected proxy_connected = 3;
    Connecting connecting = 4;
    Connected connected = 5;
    Disconnecting disconnecting = 6;
    Error error = 7;
  }
}

message Ticket {
  bytes ticket = 1;
  TicketType ticketType = 2;

  enum TicketType {
    SSO = 0;
    Reconnect = 1;
  }
}

message ProxyReady {}

message Connectivity {
  oneof state {
    ConnectivityStatus status = 1;
    ConnectivityPresumeOnline presume_online = 2;
  }
}

message ConnectivityStatus {
  bool ipv4 = 1;
  bool ipv6 = 2;
}

message ConnectivityPresumeOnline {}

// 服务端主动断开连接时的原因
message CloseReason {
  uint32 reason = 1;
}

// 服务端通知消息
message Notification {
  uint32 type = 1;
}

message ChangePwdResult {
  bytes result = 1;
}

message NeedAuth {}

message AccessDenied {
  bytes ip_addr = 1;
}

message Environment {
  bytes data = 1;
}

message UpdateStatus {
  string status = 1;
  optional string msg = 2;
}

message DownloadProgress {
  uint64 chunk_size = 1;
  uint64 content_length = 2;
}

message CoreEvent {
  oneof event {
    TunnelState tunnel_state = 1;
    BackendState backend_state = 2;
    Ticket ticket = 3;
    ProxyReady proxy_ready = 4;
    Connectivity connectivity = 5;
    CloseReason close_reason = 6;
    ChangePwdResult change_pwd_result = 7;
    NeedAuth need_auth = 8;
    AccessDenied access_denied = 9;
    Environment environment = 10;
    Notification notification_type = 11;
    UpdateStatus update_status = 12;
    DownloadProgress download_progress = 13;
  }
}

message AuthEvent {
  optional bytes ip_addr = 1;
}

message CompleteAuth {
  string auth_result_id = 1;
  optional bytes ip_addr = 2;
}

message CancelAuth {
  optional bytes ip_addr = 1;
}

message ProxyRequest {
  bytes request = 1;
  ConnectionConfig connection_config = 2;
}

message ResponseData {
  bytes response = 1;
  optional bytes data = 2;
}

message ProxyResponse {
  oneof response {
    ResponseData ok = 1;
    google.protobuf.Empty timeout = 2;
    google.protobuf.Empty connection_failed = 3;
    string err = 4;
  }
}

message CheckUpdateParam {
  string model = 1;
  oneof config {
    ConnectionConfig connection_config = 2;
    string endpoint = 3;
  }
}

message UpdateResponse {
  bool should_update = 1;
  optional UpdateResponseInner payload = 2;
}

message UpdateResponseInner {
  // 是否强制更新
  bool force = 1;
  // 版本
  string version = 2;
  // 更新日志
  string notes = 3;
}
