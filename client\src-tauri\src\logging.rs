use chrono::Local;
use log::LevelFilter;
use log4rs::{
    append::{console::ConsoleAppender, file::FileAppender},
    config::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>},
    encode::pattern::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    Config,
};
use std::{env, str::FromStr};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Event, Manager, Wry};

use err_ext::ErrorExt;

/// initialize this instance's log file
pub fn init_logger() -> Result<(), String> {
    let log_dir = paths::log_dir().map_err(|error| error.display_chain())?;

    let local_time = Local::now().format("%Y-%m-%d").to_string();
    let log_file = format!("oneid_{local_time}.log");
    let log_file = log_dir.join(log_file);

    #[cfg(debug_assertions)]
    let time_format = "{d(%Y-%m-%d %H:%M:%S%.3f)} {l:5} [{t:5}] - {M}#{L} {m}{n}";
    #[cfg(not(debug_assertions))]
    let time_format = "{d(%Y-%m-%d %H:%M:%S%.3f)} {l:5} [{t:5}] - {m}{n}";

    let encode = Box::new(PatternEncoder::new(time_format));

    let stdout = ConsoleAppender::builder().encoder(encode.clone()).build();
    let tofile = FileAppender::builder()
        .encoder(encode)
        .build(log_file)
        .map_err(|error| error.display_chain_with_msg("Failed to build log appender"))?;

    #[cfg(debug_assertions)]
    let level =
        LevelFilter::from_str(&env::var("ONEID_LOG_LEVEL").unwrap_or(String::from("trace")))
            .unwrap_or(LevelFilter::Trace);
    #[cfg(not(debug_assertions))]
    let level = LevelFilter::from_str(&env::var("ONEID_LOG_LEVEL").unwrap_or(String::from("info")))
        .unwrap_or(LevelFilter::Info);

    let config = Config::builder()
        .appender(Appender::builder().build("stdout", Box::new(stdout)))
        .appender(Appender::builder().build("file", Box::new(tofile)))
        .logger(
            Logger::builder()
                .appenders(["file", "stdout"])
                .additive(false)
                .build("front", level),
        )
        .logger(
            Logger::builder()
                .appenders(["file", "stdout"])
                .additive(false)
                .build("app", level),
        )
        .logger(
            Logger::builder()
                .appenders(["file", "stdout"])
                .additive(false)
                .build("core", level),
        )
        .logger(
            Logger::builder()
                .appenders(["file", "stdout"])
                .additive(false)
                .build("biometric", level),
        )
        .build(Root::builder().appender("stdout").build(LevelFilter::Error))
        .map_err(|error| error.display_chain_with_msg("Failed to build log config"))?;

    log4rs::init_config(config).map_err(|error| error.display_chain())?;

    log_panics::init();
    exception_logging::enable();
    crate::version::log_version();
    Ok(())
}

pub fn front_log(app_handle: &AppHandle<Wry>) {
    app_handle.listen_global("log", log);
}

#[derive(serde::Deserialize, Debug)]
enum Level {
    /// The "error" level.
    ///
    /// Designates very serious errors.
    // This way these line up with the discriminants for LevelFilter below
    // This works because Rust treats field-less enums the same way as C does:
    // https://doc.rust-lang.org/reference/items/enumerations.html#custom-discriminant-values-for-field-less-enumerations
    Error = 1,
    /// The "warn" level.
    ///
    /// Designates hazardous situations.
    Warn,
    /// The "info" level.
    ///
    /// Designates useful information.
    Info,
    /// The "debug" level.
    ///
    /// Designates lower priority information.
    Debug,
    /// The "trace" level.
    ///
    /// Designates very low priority, often extremely verbose, information.
    Trace,
}

#[derive(serde::Deserialize, Debug)]
struct LogEvent {
    level: Level,
    message: String,
}

fn log(event: Event) {
    if let Some(payload) = event.payload() {
        if let Ok(event) = serde_json::from_str::<LogEvent>(payload) {
            match event.level {
                Level::Error => log::error!(target: "front", "{}", event.message),
                Level::Warn => log::warn!(target: "front", "{}", event.message),
                Level::Info => log::info!(target: "front", "{}", event.message),
                Level::Debug => log::debug!(target: "front", "{}", event.message),
                Level::Trace => log::trace!(target: "front", "{}", event.message),
            }
        }
    }
}
