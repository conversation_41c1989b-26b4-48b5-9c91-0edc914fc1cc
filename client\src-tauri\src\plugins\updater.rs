use communicate_interface::{
    types::{check_update_param::Config, CheckUpdateParam},
    ControlServiceClient,
};
use config::{AppConfig, MODEL};
use tauri::{async_runtime::Mutex, command, State};
use types::{backend::ConnectionConfig, updater::UpdateResponse};

use super::{ErrorResponse, Result};

/// check update
#[command(async)]
pub(super) async fn check_update(
    config: State<'_, Mutex<AppConfig>>,
    rpc: State<'_, Mutex<ControlServiceClient>>,
) -> Result<UpdateResponse> {
    log::debug!(target: "app", "Receive check update event");

    // 获取配置信息
    let config = config.lock().await;
    let config = config.clone();

    if config.node.is_none() {
        return Ok(UpdateResponse {
            should_update: false,
            payload: None,
        });
    }

    let check_update_param = CheckUpdateParam {
        model: MODEL.to_owned(),
        config: Some(
            #[cfg(feature = "sdp")]
            {
                // unwrap is safe
                let node = config.node.unwrap();
                Config::ConnectionConfig(
                    ConnectionConfig {
                        host: node.host,
                        ip: node.ip,
                        port: node.port,
                        spa_port: node.spa_port,
                        authorization: None,
                    }
                    .into(),
                )
            },
            #[cfg(not(feature = "sdp"))]
            {
                // unwrap is safe
                Config::Endpoint(config.endpoint.unwrap())
            },
        ),
    };

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    match rpc
        .check_update(tonic::Request::new(check_update_param))
        .await
    {
        Ok(response) => Ok(response.into_inner().try_into().unwrap()),
        Err(status) => {
            log::error!(target: "app", "Check update failed. {status}");
            Err(ErrorResponse::new(600, None))
        }
    }
}

/// download updater file
#[command(async)]
pub(super) async fn download(rpc: State<'_, Mutex<ControlServiceClient>>) -> Result<()> {
    log::debug!(target: "app", "Receive download event");

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    tauri::async_runtime::spawn(async move {
        if let Err(status) = rpc.download(tonic::Request::new(())).await {
            log::error!(target: "app", "Download update failed. {status}");
        }
    });

    Ok(())
}

/// install the updater file
#[command(async)]
pub(super) async fn install(rpc: State<'_, Mutex<ControlServiceClient>>) -> Result<()> {
    log::debug!(target: "app", "Receive install event");

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    rpc.install(tonic::Request::new(()))
        .await
        .map(|_| ())
        .map_err(|status| {
            log::error!(target: "app", "Install update failed. {status}");
            ErrorResponse::new(600, None)
        })
}

#[command(async)]
pub(super) async fn cancel_update(rpc: State<'_, Mutex<ControlServiceClient>>) -> Result<()> {
    log::debug!(target: "app", "Receive cancel update event");

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    rpc.cancel_update(tonic::Request::new(()))
        .await
        .map(|_| ())
        .map_err(|status| {
            log::error!(target: "app", "Cancel update failed. {status}");
            ErrorResponse::new(600, None)
        })
}
