use std::{
    io::{self, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Write},
    os::unix::net::UnixStream,
};

use build_info::version_info;
use clap::Parser;
use serde::Serialize;

/// SDP Server Cli.
#[derive(Parse<PERSON>, Debug, Serialize)]
#[clap(name = ABOUT, about = ABOUT, long_about = None, version = version_info())]
pub struct Cli {
    #[clap(subcommand)]
    pub command: server_cli::Command,
}

static ABOUT: &str = "SDP Server Cli.";

mod build_info;

fn main() -> io::Result<()> {
    let cli = Cli::parse();

    let socket_path = "/tmp/sdp_server.sock";
    let mut stream = UnixStream::connect(socket_path)?;

    let cmd = serde_json::to_string(&cli.command).unwrap();
    writeln!(stream, "{}", cmd)?;

    let mut reader = BufReader::new(stream);
    let mut response = String::new();

    match cli.command {
        server_cli::Command::SetLogFilter { .. } => {
            reader.read_line(&mut response)?;
            println!("{}", response);
        }
        server_cli::Command::ViewClients => loop {
            reader.read_line(&mut response)?;

            let ending = response.trim().ends_with("END");
            if ending {
                response = response.replace("END", "");
            }
            let online_clients = serde_json::from_str::<Vec<server_cli::OnlineClient>>(&response)
                .unwrap_or_default();

            println!("{}", serde_json::to_string_pretty(&online_clients).unwrap());

            if ending {
                break;
            }
        },
        server_cli::Command::ViewTraceMapping => {
            reader.read_line(&mut response)?;
            let mapping = serde_json::from_str::<serde_json::Value>(&response).unwrap_or_default();
            println!("{}", serde_json::to_string_pretty(&mapping).unwrap());
        }
    }

    Ok(())
}
