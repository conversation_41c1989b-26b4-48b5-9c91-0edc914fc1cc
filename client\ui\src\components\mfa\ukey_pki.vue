<template>
  <div
    v-loading.fullscreen.lock="loading"
    class="content"
    element-loading-text="登录中..."
    element-loading-background="rgba(0, 0, 0, 0.5)"
  >
    <h3 v-if="type">Ukey登录</h3>
    <div v-if="ukeyStart" style="margin: 30px 0">
      <!-- 证书 -->
      <div class="inputItem" :style="!type && 'width: 60%; margin: 15px auto;'">
        <div class="inputContent">
          <el-input
            ref="username"
            v-model="user.username"
            readonly
            placeholder="请插入证书"
          />
        </div>
      </div>
      <!-- 密码 -->
      <div class="inputItem" :style="!type && 'width: 60%; margin: 15px auto;'">
        <div class="inputContent">
          <el-input
            ref="password"
            v-model="user.password"
            placeholder="请输入密码"
            show-password
            @keyup.enter="search"
          />
        </div>
      </div>
      <!-- 登录 -->
      <div style="text-align: center">
        <el-button
          ref="confirmBtn"
          type="primary"
          :class="{
            loginBtn: type === 'login',
            'mfa-confirm-btn': type !== 'login',
          }"
          :disabled="
            (type === 'login' && !user.username) || !user.password || submitting
          "
          @click="submitAuth"
        >
          {{ type === "login" ? "登录" : "确定" }}
        </el-button>
      </div>
      <userAgreement v-if="type" />
    </div>
    <div v-else :class="type == 'login' ? 'ukeystart' : 'ukeystyle'">
      <img src="/assets/usbkey.png" width="265" height="200" alt="" />
      <p style="font-size: 14px">未找到可用的ukey证书</p>
      <p style="font-size: 14px; color: #fda43d">请插入ukey证书</p>
    </div>
  </div>
</template>

<script>
import {
  ukeycard,
  ukeyauthentication,
  getrandom,
  ukeyverify,
} from "../../api/service.js";
import { useStore } from "vuex";
import { computed } from "vue";
import userAgreement from "../../views/userAgreement.vue";

export default {
  components: {
    userAgreement,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
  },
  setup() {
    const store = useStore();
    store.commit("updateIsLogin", false);
    return {};
  },
  data() {
    return {
      user: {
        username: "",
        password: "",
      },
      loading: false,
      address: "",
      lessee: "",
      certID: "",
      polling: null,
      code: "ukey",
      ukeyStart: true,
      submitting: false,
    };
  },
  watch: {},
  created() {
    this.polling = setInterval(() => {
      this.ukeycard();
    }, 2000);
  },
  methods: {
    ukeycard() {
      ukeycard(this.code)
        .then((res) => {
          if (!res.data.length) {
            // this.alert.error("获取证书失败")
            this.ukeyStart = false;
            return;
          }
          this.ukeyStart = true;
          this.stopPolling();
          if (res && res.code === 0) {
            this.user.username = res.data[0].name;
            this.certID = res.data[0].id;
          }
        })
        .catch(() => {
          this.alert.error("获取证书失败");
        });
    },

    // 停止轮询
    stopPolling() {
      this.polling && clearInterval(this.polling);
    },
    /**
     * 提交认证
     */
    async submitAuth() {
      try {
        this.subbmitting = true;
        await this.clickregister();
      } finally {
        this.subbmitting = false;
      }
    },
    // 登录
    clickregister() {
      if (!this.user.username) {
        this.alert.error("请插入证书");
        return;
      }
      if (!this.user.password || this.user.password.length < 6) {
        this.alert.error("请输入六位密码");
        return;
      }
      this.loading = true;
      let data = {
        participantTypes: this.type === "login" ? "03" : "01",
        participantKeyword: this.userId || this.user.username.split("(")[0],
      };
      return getrandom(
        this.participantGroupId,
        data.participantTypes,
        data.participantKeyword,
        this.code
      )
        .then((result) => {
          const data = {
            id: this.certID,
            password: this.user.password,
            random_str: result.data.requestId,
            der_format: "false",
            ukey_type: this.code,
          };
          return ukeyauthentication(data)
            .then((res) => {
              if (res.data.retry_count == 0) {
                this.loading = false;
                this.alert.error("您的证书密码已被锁死,请联系管理员进行解锁!");
                return;
              }
              if (!res.data.result) {
                this.loading = false;
                this.alert.error(
                  "校验证书密码失败!您还有" +
                    res.data.retry_count +
                    "次机会重试!"
                );
                return;
              }
              ukeyverify(
                result.data.requestId,
                res.data.signature,
                res.data.certificate
              )
                .then(() => {
                  this.$emit("mfaCallbackFunc", {
                    requestId: result.data.requestId,
                    type: this.code,
                  });
                })
                .catch((err) => {
                  this.loading = false;
                  this.$emit("childEventCancel", result.data.requestId);
                  let msg = JSON.parse(err.message);
                  this.alert.error(msg.data.errorMsg);
                });
            })
            .catch((err) => {
              console.log("err: ", err);
              this.loading = false;
            });
        })
        .catch((err) => {
          let msg = JSON.parse(err.message);
          this.alert.error(msg.data.errorMsg);
          this.loading = false;
          this.$emit("generateRequestIdFailCallbackFunc", error);
        });
    },
    // 回车登录
    search() {
      this.clickregister();
    },
    cancel() {
      this.stopPolling();
    },
  },
};
</script>

<style lang="less" scoped>
@mainColor: #f9780c;

.inputItem {
  text-indent: 0.6em;
  position: relative;
  margin-top: 0.8rem;
  border-bottom: 1px solid #d1d3db;
  // border-radius: 30px;
  margin-bottom: 10px;
  &:hover {
    border-bottom: 1px solid #f9780c;
  }

  .clud {
    display: flex;
    align-items: center;

    span {
      width: 40px;
      cursor: pointer;
      color: @mainColor;
      text-align: right;
      font-size: 14px;
    }
  }
}

.login_icon {
  color: #bfbfbf;
  font-size: 22px;
}

.inputContent {
  padding: 0.4rem;
  width: 240px;
}

.input-pwd {
  width: 300px;
}

:deep(.el-input__wrapper) {
  box-shadow: none;
  padding: 1px;
}

.login-form-bottom {
  // clear: both;
  // overflow: visible;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;

  :deep(.el-checkbox) {
    margin-right: 0;
  }
}

.login-bottom-item {
  // vertical-align: middle;
  margin-bottom: 2px;
  font-size: 14px;
  color: @mainColor;
}

.ukeystyle {
  text-align: center;
  padding: 10px 0;
}

.ukeystart {
  margin: 10px 0 28px;
}
</style>