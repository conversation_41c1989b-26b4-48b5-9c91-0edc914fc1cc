use fred::{
    error::Error,
    prelude::*,
    types::{config::Config, MultipleValues},
};
use tracing::{error, info};

use crate::config::RedisConf;

macro_rules! redis_error {
    ($error: expr, $($arg:tt)+) => {
        match $error.kind() {
            fred::error::ErrorKind::NotFound => {}
            _ => error!($($arg)+),
        }
    }
}

#[derive(Clone)]
pub struct CacheManager {
    pool: DynamicPool,
}

impl CacheManager {
    pub async fn new(config: RedisConf) -> Self {
        let config: Config = config.into();

        let hosts = config.server.hosts();
        let mut builder = Builder::from_config(config);
        let pool = builder
            .set_policy(ReconnectPolicy::default())
            .build_dynamic_pool()
            .unwrap();

        pool.init().await.unwrap();

        info!("connected to {:?}", hosts);

        Self { pool }
    }

    pub async fn exists(&self, key: &str) -> Option<bool> {
        let result = self.pool.next().exists::<bool, _>(key).await;
        match result {
            Ok(exists) => return Some(exists),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `EXISTS {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn get<T>(&self, key: &str) -> Option<T>
    where
        T: FromValue,
    {
        let result: FredResult<Option<T>> = self.pool.next().get(key).await;

        match result {
            Ok(result) => return result,
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `GET {}`: {}",
                    key,
                    err
                )
            }
        }

        None
    }

    pub async fn get_or_error<T>(&self, key: &str) -> FredResult<Option<T>>
    where
        T: FromValue,
    {
        let result: FredResult<Option<T>> = self.pool.next().get(key).await;

        match result {
            Ok(result) => Ok(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `GET {}`: {}",
                    key,
                    err
                );
                Err(err)
            }
        }
    }

    pub async fn set(&self, key: &str, value: &str, expire: Option<Expiration>) -> Option<bool> {
        let result: FredResult<()> = self.pool.next().set(key, value, expire, None, false).await;
        match result {
            Ok(_) => return Some(true),
            Err(err) => redis_error!(
                err,
                "failed to execute redis command `SET {} {}`: {}",
                key,
                value,
                err
            ),
        }
        None
    }

    pub async fn del(&self, key: &str) -> Option<u32> {
        let result = self.pool.next().del::<u32, _>(key).await;

        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `DEL {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn hkeys(&self, key: &str) -> Option<Vec<String>> {
        let result = self.pool.next().hkeys::<Vec<String>, _>(key).await;

        match result {
            Ok(keys) => {
                if !keys.is_empty() {
                    let keys = keys
                        .into_iter()
                        .map(|key| {
                            if let Some((_, key)) = key.split_once('|') {
                                key.to_owned()
                            } else {
                                key
                            }
                        })
                        .collect();
                    return Some(keys);
                }
            }
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `HKEYS {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn hgetall<T>(&self, key: &str) -> Option<Box<T>>
    where
        T: FromValue,
    {
        let result: FredResult<T> = self.pool.next().hgetall(key).await;

        match result {
            Ok(result) => return Some(Box::new(result)),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `HGETALL {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn hset<T>(&self, key: &str, field: &str, value: T) -> Option<u32>
    where
        T: FromValue + Send,
        T: TryInto<Value>,
        T::Error: Into<Error>,
    {
        let result: FredResult<u32> = self.pool.next().hset(key, (field, value)).await;
        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `HSET {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn hget<F, T>(&self, key: &str, field: F) -> Option<T>
    where
        F: Into<Key> + Send,
        T: FromValue,
    {
        let result: FredResult<T> = self.pool.next().hget(key, field).await;
        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `HGET {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn hdel<F>(&self, key: &str, field: F) -> Option<u32>
    where
        F: Into<Key> + Send,
    {
        let result: FredResult<u32> = self.pool.next().hdel(key, field).await;
        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `HDEL {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn lpush<F>(&self, key: &str, value: F) -> Option<u32>
    where
        F: TryInto<MultipleValues> + Send,
        F::Error: Into<Error> + Send,
    {
        let result: FredResult<u32> = self.pool.next().lpush(key, value).await;
        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `LPUSH {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn lrange<T>(&self, key: &str, start: i64, stop: i64) -> Option<Vec<T>>
    where
        T: FromValue,
    {
        let result: FredResult<Vec<T>> = self.pool.next().lrange(key, start, stop).await;
        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `LRANGE {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn lrem<V>(&self, key: &str, count: i64, value: V) -> Option<u32>
    where
        V: TryInto<Value> + Send,
        V::Error: Into<Error> + Send,
    {
        let result = self
            .pool
            .next()
            .lrem::<u32, &str, V>(key, count, value)
            .await;
        match result {
            Ok(result) => return Some(result),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `LREM {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }

    pub async fn ltrim(&self, key: &str, start: i64, stop: i64) -> Option<bool> {
        let result: FredResult<u32> = self.pool.next().ltrim(key, start, stop).await;
        match result {
            Ok(result) => return Some(result > 0),
            Err(err) => {
                redis_error!(
                    err,
                    "failed to execute redis command `LREM {}`: {}",
                    key,
                    err
                )
            }
        }
        None
    }
}
