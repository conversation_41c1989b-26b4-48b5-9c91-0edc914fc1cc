#[cfg(target_os = "macos")]
use std::time::Duration;
#[cfg(target_os = "macos")]
use std::time::Instant;
use std::{
    collections::HashMap,
    env::{consts::EXE_SUFFIX, current_dir},
    fs, io,
    io::Write,
    path::Path,
    process::Stdio,
};

use std::path::PathBuf;

#[cfg(target_os = "macos")]
use anyhow::bail;

use anyhow::Context;
use chrono::{FixedOffset, Utc};
use clap::{Parser, ValueEnum};
use colored::Colorize;
#[cfg(target_os = "macos")]
use indicatif::{ProgressBar, ProgressStyle};
use serde_json::{json, Value};
use tokio::{
    io::{AsyncBufReadExt, BufReader},
    process::Command,
    sync::broadcast::Sender,
};

use crate::{print_command_output, utils};

#[derive(Debug, Parser, Clone)]
pub struct Options {
    /// 输出名称
    #[clap(long)]
    pub output: Option<String>,
    /// 打包所有产品的包
    #[clap(long)]
    pub all: bool,
    /// Verbose
    #[clap(long, short)]
    pub verbose: bool,
    /// 运行
    #[clap(long)]
    pub run: bool,
    /// Release
    #[clap(long, short)]
    pub release: bool,
    /// 编译模块
    #[clap(value_enum, default_values_t = [Module::SDP, Module::IAM, Module::TLCP, Module::CLUSTER, ])]
    pub modules: Vec<Module>,
    /// 目标
    #[clap(long)]
    #[cfg(not(windows))]
    pub target: Option<String>,
    /// 公证
    #[clap(long)]
    #[cfg(target_os = "macos")]
    pub notarize: bool,
    /// 跳过编译阶段
    #[clap(long, short)]
    pub skip_build: bool,
    /// 使用环境变量设置的参数, 使用该参数时, 可跳过编译时设置参数的阶段.
    #[clap(long)]
    pub use_env: bool,
    /// 溯源
    #[clap(long, default_value_t = true)]
    pub traceability: std::primitive::bool,
    /// DNS服务
    #[clap(long, default_value_t = false)]
    pub dns_server: std::primitive::bool,
}

#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, ValueEnum, Hash)]
#[repr(u16)]
pub enum Module {
    SDP = 0x01,
    IAM = 0x02,

    TLCP = 65534,
    CLUSTER = 65535,
}

fn model(modules: &[Module]) -> String {
    let mut features = modules.to_vec();
    features.retain(|feature| feature != &Module::CLUSTER && feature != &Module::TLCP);

    let mut model = 0;
    for feature in features {
        model |= feature as u16;
    }

    format!(
        "{}{}",
        model,
        if modules.contains(&Module::CLUSTER) {
            "C"
        } else {
            "S"
        }
    )
}

pub fn get_arch(target: &str, ext: &str) -> Option<&'static str> {
    let (arch, _) = target.split_once('-').unwrap();
    if ext.ends_with("deb") {
        match arch {
            "x86" => Some("i386"),
            "x86_64" => Some("amd64"),
            // ARM64 is detected differently, armel isn't supported, so armhf is the only reasonable
            // choice here.
            "arm" => Some("armhf"),
            "aarch64" => Some("arm64"),
            _ => None,
        }
    } else if ext.ends_with("rpm") {
        match arch {
            "x86" => Some("i386"),
            "x86_64" => Some("x86_64"),
            "arm" => Some("armhfp"),
            "aarch64" => Some("aarch64"),
            _ => None,
        }
    } else {
        match arch {
            "x86_64" => Some("x64"),
            "x86" => Some("i686"),
            "arm" => Some("armv7"),
            "aarch64" => Some("aarch64"),
            _ => {
                if cfg!(target_arch = "x86") {
                    Some("i686")
                } else if cfg!(target_arch = "x86_64") {
                    Some("x64")
                } else if cfg!(target_arch = "arm") {
                    Some("armv7")
                } else if cfg!(target_arch = "aarch64") {
                    Some("aarch64")
                } else {
                    None
                }
            }
        }
    }
}

pub async fn run(
    tx: Sender<()>,
    opts: Options,
    target: &str,
    build_envs: &HashMap<String, String>,
) -> anyhow::Result<()> {
    let mut front_features = opts.modules.clone();
    front_features.retain(|v| v != &Module::TLCP);

    let mut features = String::new();
    for feature in front_features {
        features.push_str(&format!("{feature:?}"));
        features.push(' ');
    }

    let mut additional_config = json!({
          "build": {
            "beforeBuildCommand": {
              "script": format!("vite build -- {}", features.to_lowercase())
            },
            "beforeDevCommand": {
              "script": format!("vite -- {}", features.to_lowercase())
            }
          }
    });

    if opts.modules.contains(&Module::TLCP) {
        let content = fs::read_to_string("./client/src-tauri/tlcp.conf.json")?;
        let mut config_value = serde_json::from_str::<Value>(&content)?;
        utils::merge_tauri_config(&mut additional_config, &mut config_value);
    }

    // 平台配置文件
    let config_file_name = if cfg!(target_os = "macos") {
        "tauri.macos.conf.json"
    } else if cfg!(windows) {
        "tauri.windows.conf.json"
    } else {
        "tauri.linux.conf.json"
    };
    let platform_config = format!("./client/src-tauri/{}", config_file_name);
    if fs::File::open(&platform_config).is_ok() {
        let content = fs::read_to_string(platform_config)?;
        let mut config_value = serde_json::from_str::<Value>(&content)?;
        utils::merge_tauri_config(&mut additional_config, &mut config_value);
    }

    let content = fs::read_to_string("./client/src-tauri/base.conf.json")?;
    let mut config_value = serde_json::from_str::<Value>(&content)?;
    utils::merge_tauri_config(&mut additional_config, &mut config_value);

    // 产品名称
    let tauri_config =
        fs::read_to_string("client/src-tauri/tauri.conf.json").context("Tauri config")?;
    let tauri_config = serde_json::from_str::<Value>(&tauri_config)?;
    let product_name = tauri_config["package"]["productName"].as_str().unwrap();

    let product_name = if let Some(name) = opts.output.clone() {
        name
    } else if let Some(_product_name) = additional_config["package"]["productName"].as_str() {
        _product_name.to_owned()
    } else {
        product_name.to_owned()
    };

    let build_product_name = format!(
        "{}_{}_{}",
        product_name,
        model(&opts.modules),
        version::FILE_VERSION,
    );

    println!(
        "{}",
        format!(
            "{} {}{}",
            if opts.run { "Run" } else { "Build" },
            build_product_name,
            EXE_SUFFIX
        )
        .bright_purple()
    );

    if opts.verbose {
        println!("additional_config: {:#?}", additional_config);
    }

    // 编译目录
    let dir = current_dir()?;

    // 编译模式
    let mode = if opts.release { "release" } else { "debug" };

    let mut step = 1;

    if !opts.skip_build {
        // 守护进程
        build_daemon(step, tx.clone(), &opts, target, build_envs, &dir).await?;
        step += 1;

        // 配置程序
        build_config(step, tx.clone(), &opts, target, build_envs, &dir).await?;
        step += 1;

        // 核心
        build_core(step, tx.clone(), &opts, target, build_envs, &dir).await?;
        step += 1;

        // 复制文件
        copy_files(step, &opts, &dir, mode, target, &product_name).await?;
        step += 1;
    }

    #[cfg(windows)]
    {
        // GUI
        build_gui(
            step,
            tx.clone(),
            &opts,
            target,
            build_envs,
            &additional_config,
            &dir,
        )
        .await?;
    }
    #[cfg(not(windows))]
    {
        if !opts.skip_build {
            // GUI
            build_gui(
                step,
                tx.clone(),
                &opts,
                target,
                build_envs,
                &additional_config,
                &dir,
            )
            .await?;
        }
    }

    if !opts.run {
        // 重命名
        #[cfg(any(windows, target_os = "linux"))]
        let (final_dir, exts) = rename(&opts, target, mode, &product_name)?;

        #[cfg(target_os = "macos")]
        {
            if !opts.skip_build {
                step += 1;
            }
        }
        // 构建DMG文件
        #[cfg(target_os = "macos")]
        let (final_dir, final_name) =
            build_dmg(step, tx.clone(), &opts, &dir, target, mode, &product_name).await?;

        #[cfg(target_os = "macos")]
        let exts = vec![("dmg".to_owned(), final_name)];

        // 构建更新包
        if opts.release {
            for (ext, final_name) in exts {
                build_updater(
                    tx.clone(),
                    &opts,
                    final_dir.clone(),
                    &final_name,
                    target,
                    &ext,
                    version::FILE_VERSION,
                )
                .await?;
            }
        }

        // 刪除临时文件
        #[cfg(target_os = "linux")]
        {
            let pattern = "client/src-tauri/deb_*";
            for entry in glob::glob(pattern).expect("读取模式失败") {
                if let Ok(entry) = entry {
                    _ = fs::remove_file(entry);
                }
            }

            let pattern = "client/src-tauri/rpm_*";
            for entry in glob::glob(pattern).expect("读取模式失败") {
                if let Ok(entry) = entry {
                    _ = fs::remove_file(entry);
                }
            }

            _ = fs::remove_file("client/src-tauri/com.jingantech.oneid.policy");
        }

        println!("=== {} ===", "Build successfully".green());
    }
    Ok(())
}

pub fn prompt(modules: &[Module]) -> anyhow::Result<HashMap<String, String>> {
    let mut build_envs = HashMap::new();
    // 集群提示输入地址
    if modules.contains(&Module::CLUSTER) {
        let mut cluster_config_url = String::new();
        println!("输入服务器内网连接地址(eg: http://192.168.1.216:11083/cluster_inside)");

        io::stdin().read_line(&mut cluster_config_url)?;
        let cluster_config_url = cluster_config_url.trim();
        if !cluster_config_url.is_empty() {
            build_envs.insert(
                "SDP_CLUSTER_CONFIG_URL".to_owned(),
                cluster_config_url.to_owned(),
            );
        }

        let mut cluster_external_config_url = String::new();
        println!("输入服务器外网连接地址(eg: https://cdimp.jinganiam.com:21008/cluster)");
        io::stdin().read_line(&mut cluster_external_config_url)?;
        let cluster_external_config_url = cluster_external_config_url.trim();
        if !cluster_external_config_url.is_empty() {
            build_envs.insert(
                "SDP_CLUSTER_EXTERNAL_CONFIG_URL".to_owned(),
                cluster_external_config_url.to_owned(),
            );
        }
    } else if modules.contains(&Module::SDP) {
        let mut domain = String::new();
        let mut ip = String::new();
        let mut port = String::new();
        let mut spa_port = String::new();

        println!("输入控制器域名: (ctrl-215.sdp.jingantech.com)");
        io::stdin().read_line(&mut domain)?;
        let mut domain = domain.trim();
        if domain.is_empty() {
            domain = "ctrl-215.sdp.jingantech.com";
        }

        println!("输入控制器IP: ");
        io::stdin().read_line(&mut ip)?;
        let ip = ip.trim();

        println!("输入控制器端口: (3031)");
        io::stdin().read_line(&mut port)?;
        let mut port = port.trim();
        if port.is_empty() {
            port = "3031";
        }

        println!("输入控制器SPA端口: (65321)");
        io::stdin().read_line(&mut spa_port)?;
        let mut spa_port = spa_port.trim();
        if spa_port.is_empty() {
            spa_port = "65321";
        }

        // 验证端口数量是否匹配
        let ips = ip.split(",");
        let ports = port.split(",");
        let spa_ports = spa_port.split(",");
        let port_num = ports.count();
        if port_num != spa_ports.count() {
            return Err(anyhow::anyhow!("端口与SPA端口数量不匹配"));
        }

        let ip_num = ips.clone().count();
        if ip_num > 1 && ip_num != port_num {
            return Err(anyhow::anyhow!("端口与IP地址数量不匹配"));
        }

        build_envs.insert("SDP_DOMAIN".to_owned(), domain.to_owned());
        build_envs.insert("SDP_IP".to_owned(), ip.to_owned());
        build_envs.insert("SDP_PORT".to_owned(), port.to_owned());
        build_envs.insert("SDP_SPA_PORT".to_owned(), spa_port.to_owned());
    }
    Ok(build_envs)
}

async fn build_daemon(
    step: u32,
    tx: Sender<()>,
    opts: &Options,
    target: &str,
    build_envs: &HashMap<String, String>,
    base_dir: &Path,
) -> anyhow::Result<()> {
    println!(
        "{}{}{}",
        "\n========================= ".red(),
        step.to_string().red(),
        ". 守护进程 =========================\n".red()
    );
    if opts.run {
        println!("开发模式, 忽略守护进程")
    } else {
        let mut command = Command::new("cargo");
        command
            .arg("build")
            .arg("--target")
            .arg(target)
            .current_dir(base_dir.join("client/daemon"));
        if opts.release {
            command.arg("--release");
        }

        // 不包含IAM模块
        if !opts.modules.contains(&Module::IAM) {
            command
                .arg("--no-default-features")
                .arg("--features")
                .arg("pki,sdp");
        }

        let mut rx = tx.subscribe();
        print_command_output!(opts.verbose, command, build_envs, rx);
    }

    Ok(())
}

async fn build_config(
    step: u32,
    tx: Sender<()>,
    opts: &Options,
    target: &str,
    build_envs: &HashMap<String, String>,
    base_dir: &Path,
) -> anyhow::Result<()> {
    println!(
        "{}{}{}",
        "\n========================= ".red(),
        step.to_string().red(),
        ". 配置程序 =========================\n".red()
    );
    let mut command = Command::new("cargo");
    command
        .arg("build")
        .current_dir(base_dir.join("client/config"));
    if opts.release {
        command.arg("--release");
    }
    if !opts.run {
        command.arg("--target").arg(target);
    }

    let mut core_features = opts.modules.clone();
    core_features.retain(|v| v != &Module::TLCP);
    let mut features = String::new();
    for feature in core_features {
        features.push_str(&format!("{feature:?}"));
        features.push(',');
    }

    if !opts.modules.is_empty() {
        command
            .arg("--no-default-features")
            .arg("--features")
            .arg(features.to_lowercase());
    }

    let mut rx = tx.subscribe();
    print_command_output!(opts.verbose, command, build_envs, rx);
    Ok(())
}

async fn build_core(
    step: u32,
    tx: Sender<()>,
    opts: &Options,
    target: &str,
    build_envs: &HashMap<String, String>,
    base_dir: &Path,
) -> anyhow::Result<()> {
    println!(
        "{}{}{}",
        "\n========================= ".red(),
        step.to_string().red(),
        ". 核心服务 =========================\n".red()
    );
    let mut command = Command::new("cargo");
    command
        .arg("build")
        .current_dir(base_dir.join("client/core"));
    if opts.release {
        command.arg("--release");
    }
    if !opts.run {
        command.arg("--target").arg(target);
    }

    let mut core_features = opts.modules.clone();
    core_features.retain(|v| v != &Module::CLUSTER);
    let mut features: Vec<String> = core_features
        .iter()
        .map(|module| format!("{module:?}"))
        .collect();

    if opts.traceability {
        features.push("traceability".to_owned());
    }

    if opts.dns_server {
        features.push("dns_server".to_owned());
    }

    let features = features.join(",");

    if !opts.modules.is_empty() {
        command
            .arg("--no-default-features")
            .arg("--features")
            .arg(features.to_lowercase());
    }

    let mut rx = tx.subscribe();
    print_command_output!(opts.verbose, command, build_envs, rx);
    Ok(())
}

async fn copy_files(
    step: u32,
    opts: &Options,
    _base_dir: &Path,
    mode: &str,
    target: &str,
    _product_name: &str,
) -> anyhow::Result<()> {
    println!(
        "{}{}{}",
        "\n========================= ".red(),
        step.to_string().red(),
        ". 复制文件 =========================\n".red()
    );

    _ = fs::remove_dir_all("client/src-tauri/src/bin").context("remove");
    fs::create_dir_all("client/src-tauri/src/bin")?;

    #[cfg(target_os = "linux")]
    let config_name = "oneidconfig";
    #[cfg(not(target_os = "linux"))]
    let config_name = "config";
    if !opts.run {
        fs::copy(
            format!("target/{}/{}/config{}", target, mode, EXE_SUFFIX),
            format!("client/src-tauri/bin/{config_name}-{target}{}", EXE_SUFFIX),
        )
        .context("Copy config")?;
    } else {
        fs::copy(
            format!("target/{}/config{}", mode, EXE_SUFFIX),
            format!("client/src-tauri/bin/{config_name}-{target}{}", EXE_SUFFIX),
        )
        .context("Copy config")?;
    }

    if !opts.run {
        fs::copy(
            format!("target/{}/{}/oneidcore{}", target, mode, EXE_SUFFIX),
            format!("client/src-tauri/bin/oneidcore-{target}{}", EXE_SUFFIX),
        )
        .context("Copy core")?;
    } else {
        fs::copy(
            format!("target/{}/oneidcore{}", mode, EXE_SUFFIX),
            format!("client/src-tauri/bin/oneidcore-{target}{}", EXE_SUFFIX),
        )
        .context("Copy core")?;
    }

    if !opts.run {
        fs::copy(
            format!("target/{}/{}/oneidservice{}", target, mode, EXE_SUFFIX),
            format!("client/src-tauri/src/bin/oneidservice{}", EXE_SUFFIX),
        )
        .context("Copy daemon")?;
    }

    #[cfg(target_os = "linux")]
    {
        if !opts.run {
            fs::copy(
                format!("client/attachments/linux/oneid.desktop"),
                format!("client/src-tauri/oneid.desktop"),
            )
            .context("Copy entry file")?;
            utils::replace_text_in_file(
                _base_dir
                    .join("client")
                    .join("src-tauri")
                    .join("oneid.desktop"),
                "${PRODUCT_NAME}",
                _product_name,
            )
            .context("Replace placeholder")?;
        } else {
            // 麒麟. 将当前程序加入到ksc-defender的白名单中
            let limit_file = "/etc/dbus-1/conf/com.ksc.defender.limit";
            if fs::exists(limit_file).unwrap_or_default() {
                let core_path = _base_dir.join("target").join(mode).join("oneidcore");
                let core_path = format!("{}", core_path.display());
                let contents = fs::read_to_string(limit_file)?;
                let mut lines = contents.lines().skip(1);
                let mut serial = 1;
                let mut exists = false;
                while let Some(line) = lines.next() {
                    if let Some((key, value)) = line.split_once("=") {
                        if value.trim() == core_path {
                            exists = true;
                            break;
                        }
                        serial = key.replace("key", "").parse::<u32>().unwrap_or_default();
                    }
                }
                if !exists {
                    let line = format!("key{}={}", serial + 1, core_path);
                    let command = format!("echo \"{}\" >> {}", line, limit_file);

                    _ = Command::new("sudo")
                        .args(["sh", "-c", &command])
                        .output()
                        .await?;
                }
            }
        }

        for ext in ["deb", "rpm"] {
            let policy_path = if opts.run {
                format!("target/{mode}/com.jingantech.oneid.policy")
            } else {
                "client/src-tauri/com.jingantech.oneid.policy".to_owned()
            };
            fs::copy(
                "client/attachments/linux/com.jingantech.oneid.policy",
                &policy_path,
            )
            .context("Copy linux policy")?;

            if opts.run {
                let core_path = _base_dir.join("target").join(mode).join("oneidcore");
                let real_path = _base_dir.join("target").join(mode).join("oneid_auth");

                fs::copy(
                    format!("client/attachments/linux/{ext}/auth_dev"),
                    real_path.as_path(),
                )
                .context("Copy auth shell script")?;

                _ = Command::new("sudo")
                    .args(["chmod", "+x", &real_path.display().to_string()])
                    .output()
                    .await?;

                utils::replace_text_in_file(
                    real_path.clone(),
                    "${CORE_PATH}",
                    core_path.to_str().unwrap(),
                )
                .context("Replace placeholder")?;

                _ = Command::new("sed")
                    .args([
                        "-i",
                        &format!(
                            "s#/usr/bin/oneid_auth#{}#g",
                            real_path.display().to_string()
                        ),
                        &policy_path,
                    ])
                    .output()
                    .await?;

                _ = Command::new("sudo")
                    .args([
                        "cp",
                        &policy_path,
                        "/usr/share/polkit-1/actions/com.jingantech.oneid.policy",
                    ])
                    .output()
                    .await?;
            } else {
                fs::copy(
                    format!("client/attachments/linux/{ext}/postinst"),
                    format!("client/src-tauri/{ext}_postinst"),
                )
                .context("Copy post install script")?;
                utils::replace_text_in_file(
                    _base_dir
                        .join("client")
                        .join("src-tauri")
                        .join(format!("{ext}_postinst")),
                    "${PRODUCT_NAME}",
                    _product_name,
                )
                .context("Replace placeholder")?;

                fs::copy(
                    format!("client/attachments/linux/{ext}/preinst"),
                    format!("client/src-tauri/{ext}_preinst"),
                )
                .context("Copy pre install script")?;
                utils::replace_text_in_file(
                    _base_dir
                        .join("client")
                        .join("src-tauri")
                        .join(format!("{ext}_preinst")),
                    "${PRODUCT_NAME}",
                    _product_name,
                )
                .context("Replace placeholder")?;

                fs::copy(
                    format!("client/attachments/linux/{ext}/prerm"),
                    format!("client/src-tauri/{ext}_prerm"),
                )
                .context("Copy pre rm script")?;
                utils::replace_text_in_file(
                    _base_dir
                        .join("client")
                        .join("src-tauri")
                        .join(format!("{ext}_prerm")),
                    "${PRODUCT_NAME}",
                    _product_name,
                )
                .context("Replace placeholder")?;
            }
        }
    }
    Ok(())
}

async fn build_gui(
    step: u32,
    tx: Sender<()>,
    opts: &Options,
    target: &str,
    build_envs: &HashMap<String, String>,
    front_config: &Value,
    base_dir: &Path,
) -> anyhow::Result<()> {
    println!(
        "{}{}{}",
        "\n========================= ".red(),
        step.to_string().red(),
        ". GUI =========================\n".red()
    );

    let mut command = Command::new("cargo");
    command
        .arg("tauri")
        .current_dir(base_dir.join("client/src-tauri"));

    if opts.run {
        command.arg("dev");
        if opts.release {
            command.arg("--release");
        }
    } else {
        command.arg("build");
        if !opts.release {
            command.arg("--debug");
        }
    }

    if !opts.run {
        command.arg("--target").arg(target);
    }

    // bundles
    #[cfg(target_os = "macos")]
    if !opts.run {
        command.arg("-b").arg("app");
    }

    if opts.verbose {
        command.arg("--verbose");
    }

    command.arg("-c").arg(serde_json::to_string(&front_config)?);

    let mut features: Vec<String> = opts
        .modules
        .iter()
        .map(|module| format!("{module:?}"))
        .collect();

    if opts.traceability {
        features.push("traceability".to_owned());
    }

    let features = features.join(",");
    if !opts.modules.is_empty() {
        command
            .arg("--")
            .arg("--no-default-features")
            .arg("--features")
            .arg(features.to_lowercase());
    }

    let mut rx = tx.subscribe();
    print_command_output!(opts.verbose, command, build_envs, rx);
    Ok(())
}

#[cfg(any(windows, target_os = "linux"))]
fn rename(
    opts: &Options,
    target: &str,
    mode: &str,
    product_name: &str,
) -> anyhow::Result<(PathBuf, Vec<(String, String)>)> {
    let now = chrono::Local::now();
    let tmp_dir = now.format("%Y_%m_%d_%H_%M");
    let build_dir = PathBuf::from("build");

    // 创建目录
    let final_dir = build_dir
        .join(target)
        .join(product_name)
        .join(mode)
        .join(tmp_dir.to_string());
    fs::create_dir_all(&final_dir).context("Create final dir")?;

    let mut exts = vec![];

    #[cfg(windows)]
    {
        let before_name = format!(
            "{}_{}_{}-setup",
            product_name,
            version::PRODUCT_VERSION,
            get_arch(target, "exe").unwrap()
        );

        let final_name = format!(
            "{}_{}_{}_{}",
            product_name,
            get_arch(target, "exe").unwrap(),
            model(&opts.modules),
            version::FILE_VERSION,
        );

        // productName_version_arch-setup.exe
        fs::copy(
            format!("target/{target}/{mode}/bundle/nsis/{before_name}.exe"),
            final_dir.join(format!("{}{}", final_name, EXE_SUFFIX)),
        )
        .context("Copy exe")?;
        exts.push(("exe".to_owned(), final_name));
    }

    #[cfg(target_os = "linux")]
    {
        // productName_version_arch.deb
        let before_name = format!(
            "{}_{}_{}",
            product_name,
            version::PRODUCT_VERSION,
            get_arch(target, "deb").unwrap()
        );
        let final_name = format!(
            "{}_{}_{}_{}",
            product_name,
            get_arch(target, "deb").unwrap(),
            model(&opts.modules),
            version::FILE_VERSION,
        );
        let deb_path = format!("target/{target}/{mode}/bundle/deb/{before_name}.deb");
        let ext = ".deb";
        if fs::exists(&deb_path).unwrap_or_default() {
            fs::copy(deb_path, final_dir.join(format!("{}{}", final_name, ext)))
                .context("Copy deb")?;
            exts.push(("deb".to_owned(), final_name));
        }

        // productName-version-release.arch.rpm
        let before_name = format!(
            "{}-{}-1.{}",
            product_name,
            version::PRODUCT_VERSION,
            get_arch(target, "rpm").unwrap()
        );
        let final_name = format!(
            "{}_{}_{}_{}",
            product_name,
            get_arch(target, "rpm").unwrap(),
            model(&opts.modules),
            version::FILE_VERSION,
        );
        let deb_path = format!("target/{target}/{mode}/bundle/rpm/{before_name}.rpm");
        let ext = ".rpm";
        if fs::exists(&deb_path).unwrap_or_default() {
            fs::copy(deb_path, final_dir.join(format!("{}{}", final_name, ext)))
                .context("Copy rpm")?;
            exts.push(("rpm".to_owned(), final_name));
        }
    }

    Ok((final_dir, exts))
}

#[cfg(target_os = "macos")]
async fn build_dmg(
    step: u32,
    tx: Sender<()>,
    opts: &Options,
    base_dir: &Path,
    target: &str,
    mode: &str,
    product_name: &str,
) -> anyhow::Result<(PathBuf, String)> {
    let final_name = format!(
        "{}_{}_{}_{}",
        product_name,
        get_arch(target, "dmg").unwrap(),
        model(&opts.modules),
        version::FILE_VERSION,
    );

    let app_path = base_dir
        .join("target")
        .join(target)
        .join(mode)
        .join("bundle")
        .join("macos")
        .join(format!("{}.app", product_name));

    // 签名
    let mut command = Command::new("xattr");
    command.args(["-cr", &app_path.display().to_string()]);

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // codesign -f -s "7B965FCB612E9CD9AE78E1CBF59B15A5E9A2549A" --options runtime --deep
    // ${target_dir}bundle/macos/${PRODUCT_NAME}.app
    let mut command = Command::new("codesign");
    command.args([
        "-f",
        "-s",
        "7B965FCB612E9CD9AE78E1CBF59B15A5E9A2549A",
        "--options",
        "runtime",
        "--deep",
        &app_path.display().to_string(),
    ]);

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    println!(
        "{}{}{}",
        "\n========================= ".red(),
        step.to_string().red(),
        ". 制作PKG =========================\n".red()
    );
    let now = chrono::Local::now();
    let tmp_dir = now.format("%Y_%m_%d_%H_%M");
    let build_dir = PathBuf::from(format!("build/{target}/{product_name}/{mode}/{tmp_dir}"));

    // 创建目录
    fs::create_dir_all(build_dir.join("bundle"))?;

    // 复制 安全令.app 到目标目录
    fs::create_dir_all(build_dir.join(format!("{}.app", product_name)))?;
    utils::copy_recursively(app_path, build_dir.join(format!("{}.app", product_name)))
        .context("Copy app")?;

    // 复制守护进程plist文件
    fs::copy(
        "client/daemon/com.jingantech.oneid.plist",
        build_dir.join("com.jingantech.oneid.plist"),
    )
    .context("Copy plist")?;

    // 守护进程可执行文件路径
    let daemon_path = format!(
        "/Applications/{}.app/Contents/MacOS/oneidservice",
        product_name
    );
    // 替换守护进程可执行文件路径
    let mut command = Command::new("plutil");
    command
        .arg("-replace")
        .arg("ProgramArguments")
        .arg("-json")
        .arg(format!("[\"{}\", \"-v\", \"0\"]", daemon_path))
        .arg(build_dir.join("com.jingantech.oneid.plist"));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // 拷贝安装脚本
    fs::create_dir_all(build_dir.join("scripts"))?;
    utils::copy_recursively(
        "client/attachments/macos/scripts",
        build_dir.join("scripts"),
    )
    .context("Copy scripts")?;

    // 替换占位符
    utils::replace_text_in_file(
        build_dir
            .join("scripts")
            .join("install")
            .join("postinstall"),
        "${PRODUCT_NAME}",
        product_name,
    )
    .context("Replace placeholder")?;

    utils::replace_text_in_file(
        build_dir
            .join("scripts")
            .join("uninstall")
            .join("preinstall"),
        "${PRODUCT_NAME}",
        product_name,
    )
    .context("Replace placeholder")?;

    const IDENTIFIER: &str = "com.jingantech.oneid";
    const INSTALL_LOCATION: &str = "/Applications";
    const INSTALL_PKG_NAME: &str = "oneid.pkg";

    // 创建pkg
    let mut command = Command::new("pkgbuild");
    command
        .arg("--identifier")
        .arg(IDENTIFIER)
        .arg("--version")
        .arg(version::FILE_VERSION)
        .arg("--root")
        .arg(&build_dir)
        .arg("--install-location")
        .arg(INSTALL_LOCATION)
        .arg("--scripts")
        .arg(build_dir.join("scripts").join("install"));

    if opts.verbose {
        command.arg("-verbose");
    }

    command.arg(build_dir.join(INSTALL_PKG_NAME));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    fs::copy(
        "client/attachments/macos/install_distribution.xml",
        build_dir.join("install_distribution.xml"),
    )
    .context("Copy install_distribution")?;
    // 替换占位符
    utils::replace_text_in_file(
        build_dir.join("install_distribution.xml"),
        "${PRODUCT_NAME}",
        product_name,
    )
    .context("Replace placeholder")?;
    utils::replace_text_in_file(
        build_dir.join("install_distribution.xml"),
        "${identifier}",
        IDENTIFIER,
    )
    .context("Replace placeholder")?;
    utils::replace_text_in_file(
        build_dir.join("install_distribution.xml"),
        "${version}",
        version::FILE_VERSION,
    )
    .context("Replace placeholder")?;
    utils::replace_text_in_file(
        build_dir.join("install_distribution.xml"),
        "${install_pkg_name}",
        INSTALL_PKG_NAME,
    )
    .context("Replace placeholder")?;
    utils::replace_text_in_file(
        build_dir.join("install_distribution.xml"),
        "${BACKGROUND_PNG}",
        "oneid_bg.png",
    )
    .context("Replace placeholder")?;

    let mut command = Command::new("productbuild");
    command
        .arg("--package-path")
        .arg(build_dir.display().to_string())
        .arg("--resources")
        .arg("client/attachments/macos/pkg_resources")
        .arg("--distribution")
        .arg(build_dir.join("install_distribution.xml"))
        .arg("--version")
        .arg(version::FILE_VERSION)
        .arg("--identifier")
        .arg(IDENTIFIER)
        .arg("--sign")
        .arg("86A1D5649E9E766FA041EDFE39D53666E1E5D8C3")
        .arg(build_dir.join("bundle").join(format!("{product_name}.pkg")));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // 设置PKG文件的图标
    // SetFile -a C $product_install_pkg_name
    let mut command = Command::new("SetFile");
    command
        .current_dir(build_dir.join("bundle"))
        .arg("-a")
        .arg("C")
        .arg(format!("{}.pkg", product_name));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // sips -i ./src-tauri/icons/icon.icns
    let mut command = Command::new("sips");
    command.args(["-i", "client/src-tauri/icons/oneid_icon.icns"]);

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // DeRez -only icns ./src-tauri/icons/icon.icns > tmpicns.rsrc
    let mut command = Command::new("DeRez");
    command
        .arg("-only")
        .arg("icns")
        .arg("client/src-tauri/icons/oneid_icon.icns");
    let output = command.output().await?;
    let mut file = fs::File::create(build_dir.join("tmpicns.rsrc"))?;
    file.write_all(&output.stdout)?;

    // Rez -append tmpicns.rsrc -o $product_install_pkg_name
    let mut command = Command::new("Rez");
    let bundle = PathBuf::from("bundle");
    command
        .current_dir(&build_dir)
        .arg("-append")
        .arg("tmpicns.rsrc")
        .arg("-o")
        .arg(bundle.join(format!("{}.pkg", product_name)));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // SetFile -a C $product_install_pkg_name
    let mut command = Command::new("SetFile");
    command
        .current_dir(&build_dir)
        .arg("-a")
        .arg("C")
        .arg(bundle.join(format!("{}.pkg", product_name)));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // 清理中间数据
    for entry in fs::read_dir(&build_dir)? {
        let entry = entry?;
        let filetype = entry.file_type()?;
        if filetype.is_dir() {
            match entry.file_name().to_str() {
                Some("bundle") => continue,
                _ => fs::remove_dir_all(entry.path())?,
            }
        } else {
            fs::remove_file(entry.path())?;
        }
    }

    println!(
        "{}{}{}",
        "\n========================= ".red(),
        (step + 1).to_string().red(),
        ". 制作DMG =========================\n".red()
    );

    _ = Command::new("chmod")
        .args(["+x", "client/attachments/macos/dmg/bundle_dmg"])
        .output()
        .await?;

    let mut command = Command::new("client/attachments/macos/dmg/bundle_dmg");
    command.args([
        "--no-internet-enable",
        "--icon-size",
        "100",
        "--window-size",
        "700",
        "533",
        "--background",
        "client/attachments/macos/oneid_bg.png",
        "--volicon",
        "client/src-tauri/icons/oneid_icon.icns",
    ]);

    command
        .args(["--volname", product_name])
        .args(["--icon", &format!("{product_name}.pkg"), "350", "205"])
        .args(["--hide-extension", &format!("{product_name}.pkg")])
        .arg(build_dir.join("bundle").join(format!("{final_name}.dmg")))
        .arg(build_dir.join("bundle"));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    let mut command = Command::new("codesign");
    command.args([
        "-f",
        "-s",
        "7B965FCB612E9CD9AE78E1CBF59B15A5E9A2549A",
        "--options",
        "runtime",
        "--deep",
        &build_dir
            .join("bundle")
            .join(format!("{final_name}.dmg"))
            .display()
            .to_string(),
    ]);

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    if !opts.release {
        return Ok((build_dir.join("bundle"), final_name));
    }

    // 默认不走公证流程
    if !opts.notarize {
        return Ok((build_dir.join("bundle"), final_name));
    }

    println!(
        "{}{}{}",
        "\n========================= ".red(),
        (step + 2).to_string().red(),
        ". 公证 =========================\n".red()
    );

    // 将公证过程中使用到的密码登录信息存储到keychain中
    // xcrun notarytool store-credentials "安全令 MacOS" \
    //                --apple-id  "<EMAIL>" \
    //                --team-id  T334A7JSY7 \
    //                --password efuf-ifav-pywv-cmbd

    // 提交公证申请
    let mut command = Command::new("xcrun");
    command
        .args(["notarytool", "submit"])
        .arg(
            &build_dir
                .join("bundle")
                .join(format!("{final_name}.dmg"))
                .display()
                .to_string(),
        )
        .arg("--keychain-profile")
        .arg("安全令 MacOS");

    if opts.verbose {
        command.arg("--verbose");
        println!("{}", format!("{:?}", command).blue());
    }

    let mut command = command.stdout(Stdio::piped()).spawn()?;

    let mut finished = false;
    let mut request_id_line = None;
    if let Some(stdout) = command.stdout.take() {
        let reader = BufReader::new(stdout);
        let mut lines = reader.lines();

        let mut next_line_is_id = false;
        while let Ok(Some(line)) = lines.next_line().await {
            if !finished && line.starts_with("error: ") {
                finished = true;
            }
            println!("{}", line);
            if next_line_is_id {
                request_id_line = Some(line);
                next_line_is_id = false;
            } else if line.starts_with("Successfully uploaded file") {
                next_line_is_id = true;
            }
        }
    }

    if finished || request_id_line.is_none() {
        bail!("error");
    }

    let request_uuid = request_id_line.unwrap().replace("id:", "");
    let request_uuid = request_uuid.trim();

    let pb = ProgressBar::new_spinner();
    pb.enable_steady_tick(Duration::from_millis(140));

    pb.set_style(
        ProgressStyle::with_template("{spinner:.blue} {msg}")
            .unwrap()
            // For more spinners check out the cli-spinners project:
            // https://github.com/sindresorhus/cli-spinners/blob/master/spinners.json
            .tick_strings(&[
                "▰▱▱▱▱▱▱",
                "▰▰▱▱▱▱▱",
                "▰▰▰▱▱▱▱",
                "▰▰▰▰▱▱▱",
                "▰▰▰▰▰▱▱",
                "▰▰▰▰▰▰▱",
                "▰▰▰▰▰▰▰",
                "▰▱▱▱▱▱▱"
            ]),
    );

    pb.set_message("========================= 公证ing =========================");
    let now = Instant::now();

    loop {
        tokio::time::sleep(Duration::from_secs(5)).await;

        let mut command = Command::new("xcrun");
        let output = command
            .args([
                "notarytool",
                "info",
                &request_uuid,
                "--keychain-profile",
                "安全令 MacOS",
            ])
            .output()
            .await?;

        let output = std::str::from_utf8(&output.stdout)?;
        if output.contains("status: In Progress") {
            continue;
        }

        if output.contains("status: Accepted") {
            break;
        }

        pb.finish_with_message("========================= 公证失败 =========================");
        bail!("{output}");
    }
    pb.finish_with_message(format!(
        "========================= 公证完成 ({:?}) =========================",
        now.elapsed()
    ));

    // 盖章
    let mut command = Command::new("xcrun");
    command
        .args(["stapler", "staple"])
        .arg(build_dir.join("bundle").join(format!("{final_name}.dmg")));

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    Ok((build_dir.join("bundle"), final_name))
}

async fn build_updater(
    tx: Sender<()>,
    opts: &Options,
    final_dir: PathBuf,
    final_name: &str,
    target: &str,
    ext: &str,
    version: &str,
) -> anyhow::Result<()> {
    #[cfg(unix)]
    let subcommand = "tar";
    #[cfg(windows)]
    let subcommand = "zip";

    let file_name = format!("{final_name}.{ext}");

    #[cfg(unix)]
    let updater_file_name = format!("{file_name}.tar.gz");
    #[cfg(windows)]
    let updater_file_name = format!("{final_name}.zip");
    let signature_file_name = format!("{updater_file_name}.sig");

    let mut command = Command::new("cargo");
    command.args([
        "run",
        "-q",
        "--package",
        "build-tool",
        "--",
        &subcommand,
        "--src",
        &final_dir.join(file_name).display().to_string(),
        "--dest",
        &final_dir.join(&updater_file_name).display().to_string(),
    ]);

    let mut rx = tx.subscribe();
    print_command_output!(
        opts.verbose,
        command,
        HashMap::<&str, String>::default(),
        rx
    );

    // 签名私钥和密码
    let private_key = option_env!("TAURI_PRIVATE_KEY");
    let key_password = option_env!("TAURI_KEY_PASSWORD");

    let private_key = private_key.map(ToOwned::to_owned).expect("A public key has been found, but no private key. Make sure to set `TAURI_PRIVATE_KEY` environment variable.");

    let key_password = key_password.map(ToOwned::to_owned).unwrap_or_else(|| {
        let key_password =
            rpassword::prompt_password("Enter the password of private key\n").unwrap();
        key_password.trim().to_owned()
    });

    let mut command = Command::new("cargo");
    let output = command
        .current_dir(&final_dir)
        .args([
            "tauri",
            "signer",
            "sign",
            &updater_file_name,
            "-f",
            &private_key,
            "-p",
            &key_password,
        ])
        .output()
        .await?;

    if !output.status.success() {
        let output = std::str::from_utf8(&output.stderr).unwrap();
        eprintln!("{output}");
        return Ok(());
    }
    let sig = fs::read_to_string(final_dir.join(signature_file_name)).unwrap();
    generate_updater_json(opts, target, ext, final_dir, final_name, version, &sig)?;
    Ok(())
}

/// 生成更新json片段
fn generate_updater_json(
    opts: &Options,
    target: &str,
    ext: &str,
    final_dir: PathBuf,
    final_name: &str,
    version: &str,
    sig: &str,
) -> anyhow::Result<()> {
    let mut value = json!({});
    // 平台 + 架构
    #[cfg(target_os = "linux")]
    let platform = "linux";
    #[cfg(windows)]
    let platform = "windows";
    #[cfg(target_os = "macos")]
    let platform = "darwin";

    let arch = get_arch(target, ext).unwrap();

    let model = model(&opts.modules);
    #[cfg(target_os = "linux")]
    let model = format!("{model}_{ext}");

    // 获取当前 UTC 时间
    let now_utc = Utc::now();

    // 定义东八区的时区偏移
    let offset = FixedOffset::east_opt(8 * 3600).unwrap(); // 8小时的秒数

    // 转换为东八区时间
    let now_east8 = now_utc.with_timezone(&offset);

    // 格式化为指定格式
    let formatted_time = now_east8.format("%Y-%m-%dT%H:%M:%SZ").to_string();

    let mut payload = json!({});
    payload[model] = json!({
        "version": version,
        "force": false,
        "notes": "TODO",
        "pub_date": formatted_time,
        "manifest": {
          "signature": sig,
          "url": "TODO"
        }
    });

    value[format!("{platform}-{arch}")] = payload;

    let value = serde_json::to_string_pretty(&value)?;
    let mut file = fs::File::create(final_dir.join(format!("{final_name}_update.json")))
        .context("Create update.json")?;

    file.write_all(value.as_bytes())
        .context("Write update.json")?;

    Ok(())
}
