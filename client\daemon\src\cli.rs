use clap::{crate_authors, crate_description, crate_name, crate_version, Arg, ArgAction, Command};
use once_cell::sync::Lazy;

#[derive(Debug)]
pub struct Config {
    pub log_level: log::LevelFilter,
    #[cfg(windows)]
    pub run_as_service: bool,
    #[cfg_attr(target_os = "macos", allow(dead_code))]
    pub register_service: bool,
}

pub fn get_config() -> &'static Config {
    static CONFIG: Lazy<Config> = Lazy::new(create_config);
    &CONFIG
}

pub fn create_config() -> Config {
    let command = create_command();
    let matches = command.get_matches();

    let log_level = match matches.get_one::<u32>("v") {
        Some(0) => log::LevelFilter::Info,
        Some(1) => log::LevelFilter::Debug,
        _ => log::LevelFilter::Trace,
    };

    #[cfg(windows)]
    let run_as_service = matches.get_one::<bool>("run_as_service").copied();
    let register_service = matches.get_one::<bool>("register_service").copied();

    Config {
        log_level,
        #[cfg(windows)]
        run_as_service: run_as_service.unwrap_or(false),
        register_service: register_service.unwrap_or(false),
    }
}

fn create_command() -> Command {
    let mut command = Command::new(crate_name!())
        .version(crate_version!())
        .author(crate_authors!(", "))
        .about(crate_description!())
        // .after_help(ENV_DESC.as_str())
        .arg(
            Arg::new("v")
                .short('v')
                .action(ArgAction::Set)
                .value_parser(clap::value_parser!(u32))
                .help("Sets the level of verbosity"),
        );

    if cfg!(windows) {
        command = command.arg(
            Arg::new("run_as_service")
                .long("run-as-service")
                .action(ArgAction::SetTrue)
                .help("Run as a system service. On Windows this option must be used when running a system service"),
        )
    }
    command = command.arg(
        Arg::new("register_service")
            .long("register-service")
            .action(ArgAction::SetTrue)
            .help("Register itself as a system service"),
    );
    command
}
