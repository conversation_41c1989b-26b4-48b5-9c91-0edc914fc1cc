use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
    time::Duration,
};

use tracing::{debug, info};

use crate::{
    arp::ArpHandle, comm::VIRTUAL_IP_MAPPING, config::Segments, types::Servers, EventSender,
};

use super::{client::BackendCommonClient, health_check};

pub async fn setup(
    backend_interval: u64,
    backend_client: Arc<BackendCommonClient>,
    arp_handle: ArpHandle,
    segments: Segments,
    event_sender: EventSender,
) {
    let start = std::time::Instant::now();

    // 等待后台服务启动
    while let None = backend_client.get_backend_state().await {
        // 服务未启动
        info!("waiting for connection to backend service");

        tokio::time::sleep(Duration::from_secs(backend_interval)).await;
    }

    // 定时检查后端服务状态
    health_check::spawn(backend_interval, backend_client.clone()).await;

    info!(elapsed = ?start.elapsed(), "service is ready");

    // 加载资源
    #[allow(unused_variables)]
    let resources = load_resources(backend_client.clone()).await;
    let webapps = load_web_apps(backend_client.clone()).await;

    // IP地址列表
    let addresses = resources
        .iter()
        .map(|(_, servers)| servers.ips.clone())
        .reduce(|mut a, b| {
            a.extend(b.iter());
            a
        })
        .unwrap_or_default();

    for ip in addresses {
        let iface = segments.find_interface(&ip);
        arp_handle.send(iface, ip).await;
    }

    event_sender
        .send(crate::InternalEvent::LoadedResource(resources))
        .await;

    event_sender
        .send(crate::InternalEvent::LoadedWebApps(webapps))
        .await;
}

async fn load_resources(backend_client: Arc<BackendCommonClient>) -> HashMap<String, Servers> {
    info!("load resources.");

    let mut resources = backend_client.fetch_resources().await;
    debug!(?resources);

    let virtual_resources = backend_client.fetch_virtual_resources().await;
    debug!(?virtual_resources);

    let mut virtual_real_resources = HashSet::new();
    virtual_resources.iter().for_each(|(tenant, mapping)| {
        let resources = resources.entry(tenant.to_owned()).or_default();
        resources.ips.extend(mapping.keys().copied());
        virtual_real_resources.extend(mapping.values().copied());
    });

    let mut virtual_ip_mapping = VIRTUAL_IP_MAPPING.write().await;
    virtual_ip_mapping.extend(virtual_resources.into_iter());
    drop(virtual_ip_mapping);

    resources
}

async fn load_web_apps(
    backend_client: Arc<BackendCommonClient>,
) -> HashMap<String, HashSet<tlv_types::WebApp>> {
    let webapps = backend_client.fetch_web_apps().await;

    debug!(?webapps);

    // 添加虚拟IP映射
    for (_, webapps) in &webapps {
        _ = crate::traceablitity::add_apps(webapps.iter()).await;
    }

    webapps
}
