use std::collections::HashSet;

use encoding::{all::GB18030, DecoderTrap, Encoding};
use tokio::process::Command;

/// 获取补丁列表
pub async fn list() -> HashSet<String> {
    let command = "wmic qfe GET hotfixid";
    let mut paths = HashSet::default();
    if let Ok(output) = Command::new("cmd")
        .creation_flags(0x08000000)
        .arg("/c")
        .arg(command)
        .output()
        .await
    {
        if let Ok(output) = GB18030.decode(output.stdout.as_slice(), DecoderTrap::Strict) {
            let lines = output.lines();
            for line in lines.skip(1) {
                paths.insert(line.trim().to_owned());
            }
        }
    }
    paths
}
