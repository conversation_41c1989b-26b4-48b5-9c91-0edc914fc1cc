use ipnet::{IpAddrRange, IpNet, Ipv4AddrRange, Ipv4Net, Ipv6AddrRange};
use serde::Deserialize;
use std::{
    hash::Hash,
    net::{IpAddr, Ipv4Addr, Ipv6Addr},
    ops::RangeInclusive,
};

#[derive(Debug, Deserialize, Eq, PartialEq, Hash, Clone)]
pub struct ServerIp {}

#[derive(Debug, Deserialize, Eq, PartialEq, Hash, Clone)]
#[serde(untagged)]
pub enum Port {
    Single(u16),
    Range(RangeInclusive<u16>),
}

#[derive(Default, Debug, Clone, PartialEq, Eq)]
pub struct Servers {
    pub ips: Vec<IpAddr>,
    pub range: Vec<IpAddrRange>,
    pub cidr: Vec<IpNet>,
}

impl Servers {
    // IP段转为CIDR
    pub fn range_to_cidr(&mut self) {
        for ip_range in self.range.iter_mut() {
            let ip_net = Self::ip_range_to_cidr(ip_range);
            match ip_net {
                Some(ip_net) => {
                    self.cidr.push(ip_net);
                }
                None => {
                    log::warn!("Cannot convert IP segment to CIDR format. {:?}", ip_range);
                }
            }
        }
    }

    fn ip_range_to_cidr(ip_range: &mut IpAddrRange) -> Option<IpNet> {
        let last = (*ip_range).last();

        ip_range
            .next()
            .zip(last)
            .map(|(first, last)| match first {
                IpAddr::V4(ip) => Self::recursion_up_to_cidr(first, 32, last).map(|prefix_len| {
                    let last_mask = 32 - prefix_len;
                    let ip = (u32::from_be_bytes(ip.octets()) >> last_mask) << last_mask;
                    IpNet::new(IpAddr::V4(Ipv4Addr::from(ip)), prefix_len).unwrap()
                }),
                IpAddr::V6(ip) => Self::recursion_up_to_cidr(first, 128, last).map(|prefix_len| {
                    let last_mask = 128 - prefix_len;
                    let ip = (u128::from_be_bytes(ip.octets()) >> last_mask) << last_mask;
                    IpNet::new(IpAddr::V6(Ipv6Addr::from(ip)), prefix_len).unwrap()
                }),
            })
            .unwrap_or_default()
    }

    fn recursion_up_to_cidr(ip: IpAddr, prefix_len: u8, last: IpAddr) -> Option<u8> {
        let Ok(ip_net) = IpNet::new(ip, prefix_len) else {
            return None;
        };
        let contains = ip_net.contains(&last);
        if contains {
            return Some(prefix_len);
        }

        if ip.is_ipv4() && prefix_len > 8 || ip.is_ipv6() && prefix_len > 16 {
            return Self::recursion_up_to_cidr(ip, prefix_len - 1, last);
        }

        None
    }

    // 解析数据
    pub fn new(data: &[u8]) -> Servers {
        let mut i = 0;
        let mut servers = Servers::default();

        loop {
            let ip_type = data[i + 1];
            match data[i] {
                // 单个IP
                0 => match ip_type {
                    4 => {
                        servers.ips.push(IpAddr::V4(Ipv4Addr::new(
                            data[i + 2],
                            data[i + 3],
                            data[i + 4],
                            data[i + 5],
                        )));
                        i += 6;
                    }
                    6 => {
                        servers.ips.push(IpAddr::V6(Ipv6Addr::from(
                            <&[u8] as TryInto<[u8; 16]>>::try_into(&data[i + 2..i + 18]).unwrap(),
                        )));
                        i += 18;
                    }
                    _ => (),
                },
                // IP段
                1 => match ip_type {
                    4 => {
                        let start =
                            Ipv4Addr::new(data[i + 2], data[i + 3], data[i + 4], data[i + 5]);
                        let end = Ipv4Addr::new(data[i + 6], data[i + 7], data[i + 8], data[i + 9]);
                        servers
                            .range
                            .push(IpAddrRange::V4(Ipv4AddrRange::new(start, end)));
                        i += 10;
                    }
                    6 => {
                        let start = Ipv6Addr::from(
                            <&[u8] as TryInto<[u8; 16]>>::try_into(&data[i + 2..i + 18]).unwrap(),
                        );
                        let end = Ipv6Addr::from(
                            <&[u8] as TryInto<[u8; 16]>>::try_into(&data[i + 18..i + 34]).unwrap(),
                        );
                        servers
                            .range
                            .push(IpAddrRange::V6(Ipv6AddrRange::new(start, end)));
                        i += 34;
                    }
                    _ => (),
                },
                // CIDR
                2 => {
                    let prefix = data[i + 2];
                    let ip = Ipv4Addr::new(data[i + 3], data[i + 4], data[i + 5], data[i + 6]);

                    servers
                        .cidr
                        .push(IpNet::V4(Ipv4Net::new(ip, prefix).unwrap_or_default()));
                    i += 7;
                }
                _ => {}
            }
            if i >= data.len() {
                break;
            }
        }

        servers.range_to_cidr();
        servers
            .cidr
            .iter_mut()
            .for_each(|ip_net| match ip_net.addr() {
                IpAddr::V4(ip) => {
                    let last_mask = 32 - ip_net.prefix_len();
                    let ip = (u32::from_be_bytes(ip.octets()) >> last_mask) << last_mask;
                    *ip_net =
                        IpNet::new(IpAddr::V4(Ipv4Addr::from(ip)), ip_net.prefix_len()).unwrap()
                }
                IpAddr::V6(ip) => {
                    let last_mask = 128 - ip_net.prefix_len();
                    let ip = (u128::from_be_bytes(ip.octets()) >> last_mask) << last_mask;
                    *ip_net =
                        IpNet::new(IpAddr::V6(Ipv6Addr::from(ip)), ip_net.prefix_len()).unwrap()
                }
            });

        servers
    }
}
