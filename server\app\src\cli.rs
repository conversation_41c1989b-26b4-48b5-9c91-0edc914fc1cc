use clap::Parser;

use crate::build_info::version_info;

/// SDP Server.
#[derive(Parser, Debug)]
#[clap(name = ABOUT, about = ABOUT, long_about = None, version = version_info())]
pub struct AppArgs {
    /// Server config file.
    #[clap(long, value_parser)]
    pub config: String,

    /// Logs dir.
    #[clap(long, value_parser)]
    pub log_dir: String,

    /// Logs filter.
    #[clap(long, value_parser)]
    pub filter: String,
}

static ABOUT: &str = "SDP server";
