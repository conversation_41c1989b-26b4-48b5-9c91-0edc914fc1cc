<?xml version="1.0" encoding="utf-8"?>
<installer-gui-script minSpecVersion="1">
    <title>Uninstall ${PRODUCT_NAME}</title>
    <background file="${BACKGROUND_PNG}" alignment="bottomleft" scaling="none"/>
    <!-- <welcome file="Welcome.html" mime-type="text/html"/> -->
    <allowed-os-versions>
        <os-version min="10.13"/>
    </allowed-os-versions>
    <!-- <license file="MyAppLicense.txt"/> -->
    <!-- <pkg-ref id="${identifier}" version="${version}">
        <must-close>
            <app id="${identifier}"/>
        </must-close>
    </pkg-ref> -->
    <pkg-ref id="${identifier}" version="${version}">
        ${uninstall_pkg_name}
    </pkg-ref>
    <options customize="never" require-scripts="false" rootVolumeOnly="true" hostArchitectures="arm64,x86_64"/>
    <!-- <conclusion file="Conclusion.html" mime-type="text/html"/> -->
    <choices-outline>
        <line choice="default"/>
    </choices-outline>
    <choice id="default">
        <pkg-ref id="${identifier}"/>
    </choice>
</installer-gui-script>