use rand::Rng;
use serde_json::Value;
use std::{
    collections::HashSet,
    fs,
    io::{self, BufRead, LineWriter, Write},
    path::{Path, PathBuf},
};

pub fn generate_random_string(length: usize) -> String {
    let mut rng = rand::thread_rng();
    let chars: Vec<char> = ('a'..='z').collect(); // 生成小写字母的字符集合

    let random_string: String = (0..length)
        .map(|_| {
            let random_index = rng.gen_range(0..chars.len());
            chars[random_index]
        })
        .collect();

    random_string
}

/// Copy files from source to destination recursively.
pub fn copy_recursively(source: impl AsRef<Path>, destination: impl AsRef<Path>) -> io::Result<()> {
    fs::create_dir_all(&destination)?;
    for entry in fs::read_dir(source)? {
        let entry = entry?;
        let filetype = entry.file_type()?;
        if filetype.is_dir() {
            copy_recursively(entry.path(), destination.as_ref().join(entry.file_name()))?;
        } else {
            fs::copy(entry.path(), destination.as_ref().join(entry.file_name()))?;
        }
    }
    Ok(())
}

pub fn replace_text_in_file(
    file_path: PathBuf,
    pattern: &str,
    replacement: &str,
) -> anyhow::Result<()> {
    let file = fs::File::open(&file_path)?;
    let reader = io::BufReader::new(file);

    let mut output = Vec::new();

    for line in reader.lines() {
        let line = line?;
        let replaced_line = line.replace(pattern, replacement);
        output.push(replaced_line);
    }

    let file = fs::File::create(&file_path)?;
    let mut file = LineWriter::new(file);
    for line in output {
        file.write_all(line.as_bytes())?;
        file.write_all(b"\n")?;
    }

    Ok(())
}

/// 合并tauri配置文件, 对于资源数据, 进行合并操作
pub fn merge_tauri_config(dest: &mut Value, src: &mut Value) {
    let src_resources = src["tauri"]["bundle"]["resources"].as_array();
    let resources = dest["tauri"]["bundle"]["resources"].as_array();
    if let (Some(platform_resources), Some(resources)) = (src_resources, resources) {
        let mut resources = resources
            .iter()
            .map(|r| r.as_str().unwrap().to_string())
            .collect::<HashSet<String>>();
        platform_resources.iter().for_each(|r| {
            _ = resources.insert(r.as_str().unwrap().to_string());
        });

        let resources = resources
            .into_iter()
            .map(Value::String)
            .collect::<Vec<Value>>();
        src["tauri"]["bundle"]["resources"] = Value::Array(resources);
    }

    json_patch::merge(dest, src);
}
