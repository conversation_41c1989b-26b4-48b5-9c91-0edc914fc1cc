import { checkUpdate, installUpdate } from "@tauri-apps/api/updater";
import { relaunch } from "@tauri-apps/api/process";
import { ElMessage, ElMessageBox } from "element-plus";
import { emit } from "@tauri-apps/api/event";
import { timeout } from "./timeout";
import store from "../store";
import { invoke } from "@tauri-apps/api";
// const element = document.querySelector('.el-message-box')
// element.style.background = 'linear-gradient(181deg, #FFDBBC 0%, #FFFFFF 50%)';

export const showUpdateAvailable = async (manifest) => {
  const result = await ElMessageBox.confirm(
    '<div class="NoActionMsg"> <img src="/assets/images/update.png"  alt="" /><h2>发现新版本 v' +
      manifest.version +
      '</h2><div class="titleupdate">' +
      manifest.notes +
      '</div><el-progress :text-inside="true" :stroke-width="26" :percentage="70" /></div>',
    "",
    {
      confirmButtonText: "立即更新",
      cancelButtonText: "稍后再说",
      center: true,
      dangerouslyUseHTMLString: true,
      showClose: false,
      roundButton: true,
      class: "updateBox",
      distinguishCancelAndClose: true,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showCancelButton: !manifest.force,
      beforeClose: (action, instance, done) => {
        if (action === "cancel" || action === "confirm") {
          done();
        }
      },
    }
  )
    .then(() => "confirm")
    .catch(() => "cancel");
  if (result === "confirm") {
    document.querySelector(".container-2").style.display = "flex";
    document.querySelector(".el-message-box__btns").style.display = "none";
    // display dialog
    await installUpdate();
    // install complete, restart the app
    await relaunch();
  }
  return result;
};

export const checkAppUpdate = async (showTip, timeoutMills) => {
  try {
    let response = await timeout(invoke("check_update"), timeoutMills);

    store.commit("setHasNewVersion", { hasNewVersion: response.shouldUpdate });
    if (response.shouldUpdate === true) {
      return response;
    } else if (response.shouldUpdate === false) {
      if (showTip === true) {
        ElMessage({
          message: "当前已是最新版本",
          center: true,
          type: "success",
        });
      }
    } else {
      if (showTip === true) {
        ElMessage({
          message: "当前已是最新版本",
          center: true,
          type: "success",
        });
      }
    }
  } catch (error) {
    emit("log", {
      level: "Error",
      message: "updater: " + JSON.stringify(error),
    });
    if (showTip === true) {
      ElMessage({
        message: "当前已是最新版本",
        center: true,
        type: "success",
      });
    }
  }
};
