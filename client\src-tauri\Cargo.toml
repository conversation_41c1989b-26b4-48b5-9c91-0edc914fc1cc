[package]
authors = ["<EMAIL>"]
build = "build.rs"
default-run = "oneid"
edition = "2021"
license = ""
name = "oneid"
repository = ""
rust-version = "1.65"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
built = { version = "0.7", features = ["git2"] }
cfg-if = "1.0.0"
tauri-build = { version = "1.5.5", features = [] }
time = { version = "0.3", features = ["formatting", "local-offset"] }
version = { path = "../../version" }

[dependencies]
actix-web = { version = "4.3.1", features = ["rustls"] }
anyhow = "1.0.65"
base = { path = "../../base" }
cfg-if = "1.0.0"
chrono = "0.4.22"
clap = { version = "4.3.24", features = ["derive", "string"] }
communicate-interface = { path = "../communicate-interface" }
config = { path = "../config" }
data-url = "0.3.1"
encrypt-files = { path = "../encrypt-files" }
err_ext = { path = "../../err_ext" }
exception_logging = { workspace = true }
flume = { version = "0.10.14", features = ["async"] }
futures = "0.3.30"
http = "1.1.0"
json-patch = "1.2.0"
kuchiki = "0.8.1"
log = "0.4.21"
log-panics = "2.1.0"
log4rs = "1.2.0"
markup5ever = "=0.10.1"
mime_guess = "2.0.5"
notify-rust = "=4.10.0"
once_cell = { workspace = true }
parking_lot = "0.12.1"
paths = { workspace = true }
proxy-request = { path = "../../proxy-request" }
rand = "0.8.5"
reqwest = { version = "0.12.6", default-features = false, features = [
  "blocking",
  "charset",
  "http2",
  "json",
  "macos-system-configuration",
  "rustls-tls",
] }
rust-embed = { version = "8" }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sysinfo = { git = "https://gitee.com/luoffei/sysinfo.git", branch = "0.33" }
tauri = { version = "1.8.1", features = [
  "process-all",
  "system-tray",
  "reqwest-client",
  "window-data-url",
  "shell-open",
  "process-command-api",
  "http-all",
  "window-all",
] }
tauri-plugin-autostart = { git = "https://gitee.com/luoffei/plugins-workspace.git", branch = "v1" }
tauri-plugin-single-instance = { git = "https://gitee.com/luoffei/plugins-workspace.git", branch = "v1" }
tera = { version = "1.17.1" }
thiserror = "2.0.4"
time = { version = "0.3.30", features = ["serde"] }
tokio = { version = "1", features = ["full"] }
tonic = "0.12"
types = { path = "../types" }
version = { version = "0.0.0", path = "../../version" }
window-shadows = "0.2.0"

[target.'cfg(windows)'.dependencies]
biometric = { workspace = true }
tokio-util = "0.7"
windows = { version = "0.58.0", features = [
  "Win32_System_SystemInformation",
  "Win32_UI_WindowsAndMessaging",
  "Win32_System_Power",
] }
windows-sys = { version = "0.52.0", features = [
  "Win32_UI_WindowsAndMessaging",
  "Win32_System_LibraryLoader",
  "Win32_System_Threading",
] }
winreg = "0.10.1"

[target.'cfg(target_os = "linux")'.dependencies]
libc = "0.2.135"

[target.'cfg(target_os = "macos")'.dependencies]
libc = "0.2.135"
objc2 = "0.5.2"
objc2-app-kit = { version = "0.2.2", features = [
  "NSButton",
  "NSControl",
  "NSResponder",
  "NSView",
  "NSWindow",
  "NSWorkspace",
] }
objc2-core-location = { version = "0.2.2", features = ["CLLocationManager"] }
objc2-core-wlan = { version = "0.2.2", features = [
  "CWInterface",
  "CWNetwork",
  "CWWiFiClient",
] }
objc2-foundation = "0.2.2"
regex = "1.6.0"
window-vibrancy = "0.3.2"

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = ["custom-protocol", "cluster", "traceability"]
# this feature is used used for production builds where `devPath` points to the filesystem
# DO NOT remove this
custom-protocol = ["tauri/custom-protocol"]

# 开发环境
dev = []

# SDP 模块
sdp = ["config/sdp"]
# IAM 模块
iam = ["config/iam"]
# TLCP 协议
tlcp = []
# 客户端负载均衡模式
cluster = ["sdp", "config/cluster"]
# 溯源
traceability = []

# [lib]
# crate-type = ["staticlib", "cdylib", "rlib"]
