use proc_macro2::TokenStream;
use quote::quote;
use syn::{spanned::Spanned, <PERSON><PERSON><PERSON>, Fields};

pub(super) fn expand_named_fields(fields: &Fields) -> syn::Result<TokenStream> {
    let mut struct_fields = vec![];
    for field in fields {
        let field_name = field.ident.as_ref().unwrap();

        let token_streams = if let Some(r#type) = crate::get_tlv_value(&field.attrs)? {
            match r#type.as_str() {
                "json" => {
                    quote! {
                        #field_name: {
                            let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                            serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| tlv::BAD_MESSAGE)?
                        },
                    }
                }
                "hex" => {
                    quote! {
                        #field_name: {
                            let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                            hex::to_string!(inner.as_slice_less_safe())
                        },
                    }
                }
                "sequence" => {
                    quote! {
                        #field_name: {
                            let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::Sequence)?;
                            let inner_input = untrusted::Input::from(inner.as_slice_less_safe());

                            inner_input.read_all(tlv::BAD_MESSAGE, |reader| {
                                let mut inner_data = Default::default();
                                while !reader.at_end() {
                                    let value = tlv::Deserialize::deserialize(reader)?;
                                    tlv::Sequence::push(&mut inner_data, value);
                                }
                                Ok(inner_data)
                            })?
                        },
                    }
                }
                "sequence,json" => {
                    quote! {
                        #field_name: {
                            let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::Sequence)?;
                            let inner_input = untrusted::Input::from(inner.as_slice_less_safe());

                            inner_input.read_all(tlv::BAD_MESSAGE, |reader| {
                                let mut inner_data = Default::default();
                                while !reader.at_end() {
                                    let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                    let value = serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| tlv::BAD_MESSAGE)?;
                                    tlv::Sequence::push(&mut inner_data, value);
                                }
                                Ok(inner_data)
                            })?
                        },
                    }
                }
                _ => {
                    // never happen
                    return Err(Error::new(
                        field.span(),
                        format!("unknown value `{}`", r#type),
                    ));
                }
            }
        } else {
            quote! {
                #field_name: tlv::Deserialize::deserialize(reader)?,
            }
        };

        struct_fields.push(token_streams);
    }

    Ok(TokenStream::from_iter(struct_fields))
}
