use objc2_core_location::CLAuthorizationStatus;
use sysinfo::System;

/// 访问SSID需要定位权限
///
/// see https://forums.developer.apple.com/forums/thread/732431?answerId=758611022#758611022
pub fn network_ssid() -> Vec<String> {
    let mut ssids = vec![];

    unsafe {
        let mut is_greate_than_14_5 = false;

        let os_version = System::os_version().unwrap_or_default();
        let mut iter = os_version.split(".");
        if let Some(major) = iter.next() {
            let major = major.parse::<u32>().unwrap_or_default();
            if major > 14 {
                is_greate_than_14_5 = true;
            } else if let Some(minor) = iter.next() {
                let minor = minor.parse::<u32>().unwrap_or_default();
                if major == 14 && minor >= 5 {
                    is_greate_than_14_5 = true;
                }
            }
        }

        // 14.5及以上需要定位权限
        if is_greate_than_14_5 {
            let location_manager = objc2_core_location::CLLocationManager::new();
            let authorization_status = location_manager.authorizationStatus();

            log::debug!(target: "app", "authorization status: {:?}", authorization_status);

            if authorization_status == CLAuthorizationStatus::kCLAuthorizationStatusNotDetermined {
                location_manager.requestWhenInUseAuthorization();
            }
        }

        let client = objc2_core_wlan::CWWiFiClient::sharedWiFiClient();
        if let Some(interface) = client.interface() {
            if let Some(ssid) = interface.ssid() {
                objc2::rc::autoreleasepool(|pool| {
                    ssids.push(ssid.as_str(pool).to_string());
                })
            };
        }
    }
    log::debug!(target: "app", "wifinames: {:?}", ssids);
    ssids
}
