/**
 * Created by <PERSON><PERSON> on 2017/5/8.
 */
function GB2312UTF8() {
    this.Utf8ToGb2312 = function (str1) {
        var substr = "";
        var a = "";
        var b = "";
        var c = "";
        var i = -1;
        i = str1.indexOf("%");
        if (i === -1) {
            return str1;
        }
        while (i !== -1) {
            if (i < 3) {
                substr = substr + str1.substr(0, i - 1);
                str1 = str1.substr(i + 1, str1.length - i);
                a = str1.substr(0, 2);
                str1 = str1.substr(2, str1.length - 2);
                if (parseInt("0x" + a) & 0x80 === 0) {
                    substr = substr + String.fromCharCode(parseInt("0x" + a));
                }
                else if (parseInt("0x" + a) & 0xE0 === 0xC0) { //two byte
                    b = str1.substr(1, 2);
                    str1 = str1.substr(3, str1.length - 3);
                    var widechar = (parseInt("0x" + a) & 0x1F) << 6;
                    widechar = widechar | (parseInt("0x" + b) & 0x3F);
                    substr = substr + String.fromCharCode(widechar);
                }
                else {
                    b = str1.substr(1, 2);
                    str1 = str1.substr(3, str1.length - 3);
                    c = str1.substr(1, 2);
                    str1 = str1.substr(3, str1.length - 3);
                    var widechar = (parseInt("0x" + a) & 0x0F) << 12;
                    widechar = widechar | ((parseInt("0x" + b) & 0x3F) << 6);
                    widechar = widechar | (parseInt("0x" + c) & 0x3F);
                    substr = substr + String.fromCharCode(widechar);
                }
            }
            else {
                substr = substr + str1.substring(0, i);
                str1 = str1.substring(i);
            }
            i = str1.indexOf("%");
        }

        return substr + str1;
    };
}

var getGb2312 = function (str_utf8) {
    if (str_utf8 === null || str_utf8.length === 0) {
        return "";
    }
    var obj = new GB2312UTF8();
    return obj.Utf8ToGb2312(str_utf8);
};

var encodeUTF_8 = function (charset, origin) {
    if (charset === 'gb2312' || charset === 'GB2312') {
        return getGb2312(origin);
    }
    return origin;
};
var setRedirectUrl = function (url) {
    var iframe = $('#ifr')[0];
    if (iframe.attachEvent) {
        iframe.attachEvent("onload", function () {
            window.location.href = url;
        });
    } else {
        iframe.onload = function () {
            window.location.href = url;
        };
    }
};

var processFromLoginData=function (data) {
    if (data.redirectUrl) {
        $("body").append("<iframe name=\"testIframeName\" id=\"ifr\" style=\"display:none;\"></iframe>\n");
        $("body").append("<form id=\"signOnForm\" style=\"display:none;\" method=\"POST\" target=\"testIframeName\"></form>");
    } else {
        $("body").append("<form id=\"signOnForm\" style=\"display:none;\" method=\"POST\" target=\"_self\"></form>");
    }
    var arrays = data.attributeArray;
    var form = $('#signOnForm');
    var url = data.loginUrl;
    var paramStr = "";
    var flag = true;
    $.each(arrays, function (i, attribute) {
        if (attribute.method === 'post') {
            var temp = $("<input>")
                .attr("type", "hidden")
                .attr("name", encodeUTF_8(data.charset, attribute.name)).val(encodeUTF_8(data.charset, attribute.value));
            form.append($(temp));
        }
        else {
            if(flag){
                paramStr += '?' + attribute.name + '=' + attribute.value;
                flag = false;
            }else {
                paramStr += '&' + attribute.name + '=' + attribute.value;
            }

        }
    });
    if (data.redirectUrl) {
         var directURL = data.redirectUrl ? data.redirectUrl : url;
        setRedirectUrl(directURL);
    }
    form.attr('action', url + paramStr);
    form.submit();
}