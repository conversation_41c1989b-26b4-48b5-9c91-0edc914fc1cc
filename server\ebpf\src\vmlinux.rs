// automatically generated by rust-bindgen 0.69.4

#[repr(C)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Ord, PartialEq, PartialOrd)]
pub struct __BindgenBitfieldUnit<Storage> {
    storage: Storage,
}
impl<Storage> __BindgenBitfieldUnit<Storage> {
    #[inline]
    pub const fn new(storage: Storage) -> Self {
        Self { storage }
    }
}
impl<Storage> __BindgenBitfieldUnit<Storage>
where
    Storage: AsRef<[u8]> + AsMut<[u8]>,
{
    #[inline]
    pub fn get_bit(&self, index: usize) -> bool {
        debug_assert!(index / 8 < self.storage.as_ref().len());
        let byte_index = index / 8;
        let byte = self.storage.as_ref()[byte_index];
        let bit_index = if cfg!(target_endian = "big") {
            7 - (index % 8)
        } else {
            index % 8
        };
        let mask = 1 << bit_index;
        byte & mask == mask
    }
    #[inline]
    pub fn set_bit(&mut self, index: usize, val: bool) {
        debug_assert!(index / 8 < self.storage.as_ref().len());
        let byte_index = index / 8;
        let byte = &mut self.storage.as_mut()[byte_index];
        let bit_index = if cfg!(target_endian = "big") {
            7 - (index % 8)
        } else {
            index % 8
        };
        let mask = 1 << bit_index;
        if val {
            *byte |= mask;
        } else {
            *byte &= !mask;
        }
    }
    #[inline]
    pub fn get(&self, bit_offset: usize, bit_width: u8) -> u64 {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
        let mut val = 0;
        for i in 0..(bit_width as usize) {
            if self.get_bit(i + bit_offset) {
                let index = if cfg!(target_endian = "big") {
                    bit_width as usize - 1 - i
                } else {
                    i
                };
                val |= 1 << index;
            }
        }
        val
    }
    #[inline]
    pub fn set(&mut self, bit_offset: usize, bit_width: u8, val: u64) {
        debug_assert!(bit_width <= 64);
        debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
        debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
        for i in 0..(bit_width as usize) {
            let mask = 1 << i;
            let val_bit_is_set = val & mask == mask;
            let index = if cfg!(target_endian = "big") {
                bit_width as usize - 1 - i
            } else {
                i
            };
            self.set_bit(index + bit_offset, val_bit_is_set);
        }
    }
}
pub type __u8 = ::aya_ebpf::cty::c_uchar;
pub type __u16 = ::aya_ebpf::cty::c_ushort;
pub type __u32 = ::aya_ebpf::cty::c_uint;
pub type __be16 = __u16;
pub type __be32 = __u32;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ethhdr {
    pub h_dest: [::aya_ebpf::cty::c_uchar; 6usize],
    pub h_source: [::aya_ebpf::cty::c_uchar; 6usize],
    pub h_proto: __be16,
}
pub type __sum16 = __u16;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcphdr {
    pub source: __be16,
    pub dest: __be16,
    pub seq: __be32,
    pub ack_seq: __be32,
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 2usize]>,
    pub window: __be16,
    pub check: __sum16,
    pub urg_ptr: __be16,
}
impl tcphdr {
    #[inline]
    pub fn res1(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 4u8) as u16) }
    }
    #[inline]
    pub fn set_res1(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(0usize, 4u8, val as u64)
        }
    }
    #[inline]
    pub fn doff(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 4u8) as u16) }
    }
    #[inline]
    pub fn set_doff(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(4usize, 4u8, val as u64)
        }
    }
    #[inline]
    pub fn fin(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(8usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_fin(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(8usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn syn(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(9usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_syn(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(9usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn rst(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(10usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_rst(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(10usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn psh(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(11usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_psh(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(11usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn ack(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(12usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_ack(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(12usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn urg(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(13usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_urg(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(13usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn ece(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(14usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_ece(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(14usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn cwr(&self) -> __u16 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(15usize, 1u8) as u16) }
    }
    #[inline]
    pub fn set_cwr(&mut self, val: __u16) {
        unsafe {
            let val: u16 = ::core::mem::transmute(val);
            self._bitfield_1.set(15usize, 1u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(
        res1: __u16,
        doff: __u16,
        fin: __u16,
        syn: __u16,
        rst: __u16,
        psh: __u16,
        ack: __u16,
        urg: __u16,
        ece: __u16,
        cwr: __u16,
    ) -> __BindgenBitfieldUnit<[u8; 2usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 2usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 4u8, {
            let res1: u16 = unsafe { ::core::mem::transmute(res1) };
            res1 as u64
        });
        __bindgen_bitfield_unit.set(4usize, 4u8, {
            let doff: u16 = unsafe { ::core::mem::transmute(doff) };
            doff as u64
        });
        __bindgen_bitfield_unit.set(8usize, 1u8, {
            let fin: u16 = unsafe { ::core::mem::transmute(fin) };
            fin as u64
        });
        __bindgen_bitfield_unit.set(9usize, 1u8, {
            let syn: u16 = unsafe { ::core::mem::transmute(syn) };
            syn as u64
        });
        __bindgen_bitfield_unit.set(10usize, 1u8, {
            let rst: u16 = unsafe { ::core::mem::transmute(rst) };
            rst as u64
        });
        __bindgen_bitfield_unit.set(11usize, 1u8, {
            let psh: u16 = unsafe { ::core::mem::transmute(psh) };
            psh as u64
        });
        __bindgen_bitfield_unit.set(12usize, 1u8, {
            let ack: u16 = unsafe { ::core::mem::transmute(ack) };
            ack as u64
        });
        __bindgen_bitfield_unit.set(13usize, 1u8, {
            let urg: u16 = unsafe { ::core::mem::transmute(urg) };
            urg as u64
        });
        __bindgen_bitfield_unit.set(14usize, 1u8, {
            let ece: u16 = unsafe { ::core::mem::transmute(ece) };
            ece as u64
        });
        __bindgen_bitfield_unit.set(15usize, 1u8, {
            let cwr: u16 = unsafe { ::core::mem::transmute(cwr) };
            cwr as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct iphdr {
    pub _bitfield_align_1: [u8; 0],
    pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
    pub tos: __u8,
    pub tot_len: __be16,
    pub id: __be16,
    pub frag_off: __be16,
    pub ttl: __u8,
    pub protocol: __u8,
    pub check: __sum16,
    pub __bindgen_anon_1: iphdr__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union iphdr__bindgen_ty_1 {
    pub __bindgen_anon_1: iphdr__bindgen_ty_1__bindgen_ty_1,
    pub addrs: iphdr__bindgen_ty_1__bindgen_ty_2,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iphdr__bindgen_ty_1__bindgen_ty_1 {
    pub saddr: __be32,
    pub daddr: __be32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iphdr__bindgen_ty_1__bindgen_ty_2 {
    pub saddr: __be32,
    pub daddr: __be32,
}
impl iphdr {
    #[inline]
    pub fn ihl(&self) -> __u8 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 4u8) as u8) }
    }
    #[inline]
    pub fn set_ihl(&mut self, val: __u8) {
        unsafe {
            let val: u8 = ::core::mem::transmute(val);
            self._bitfield_1.set(0usize, 4u8, val as u64)
        }
    }
    #[inline]
    pub fn version(&self) -> __u8 {
        unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 4u8) as u8) }
    }
    #[inline]
    pub fn set_version(&mut self, val: __u8) {
        unsafe {
            let val: u8 = ::core::mem::transmute(val);
            self._bitfield_1.set(4usize, 4u8, val as u64)
        }
    }
    #[inline]
    pub fn new_bitfield_1(ihl: __u8, version: __u8) -> __BindgenBitfieldUnit<[u8; 1usize]> {
        let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
        __bindgen_bitfield_unit.set(0usize, 4u8, {
            let ihl: u8 = unsafe { ::core::mem::transmute(ihl) };
            ihl as u64
        });
        __bindgen_bitfield_unit.set(4usize, 4u8, {
            let version: u8 = unsafe { ::core::mem::transmute(version) };
            version as u64
        });
        __bindgen_bitfield_unit
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct udphdr {
    pub source: __be16,
    pub dest: __be16,
    pub len: __be16,
    pub check: __sum16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct arphdr {
    pub ar_hrd: __be16,
    pub ar_pro: __be16,
    pub ar_hln: ::aya_ebpf::cty::c_uchar,
    pub ar_pln: ::aya_ebpf::cty::c_uchar,
    pub ar_op: __be16,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct icmphdr {
    pub type_: __u8,
    pub code: __u8,
    pub checksum: __sum16,
    pub un: icmphdr__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union icmphdr__bindgen_ty_1 {
    pub echo: icmphdr__bindgen_ty_1__bindgen_ty_1,
    pub gateway: __be32,
    pub frag: icmphdr__bindgen_ty_1__bindgen_ty_2,
    pub reserved: [__u8; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct icmphdr__bindgen_ty_1__bindgen_ty_1 {
    pub id: __be16,
    pub sequence: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct icmphdr__bindgen_ty_1__bindgen_ty_2 {
    pub __unused: __be16,
    pub mtu: __be16,
}
