/// 系统类型
#[derive(Debug)]
#[allow(non_camel_case_types)]
pub enum DeviceType {
    MOBILE,
    DESKTOP,
    OTHER,
    UNKNOWN,
}

impl From<&str> for DeviceType {
    fn from(device_id: &str) -> Self {
        match &device_id[0..2] {
            "01" | "02" | "08" => DeviceType::MOBILE,
            "00" | "03" | "04" | "05" => DeviceType::DESKTOP,
            "10" => DeviceType::OTHER,
            _ => DeviceType::UNKNOWN,
        }
    }
}

#[derive(Eq, PartialEq, Debug, Clone, Copy)]
#[repr(u8)]
pub enum CloseReason {
    /// 设备禁用
    DeviceDisabled = 0,
    /// 其他地方登录
    LoginElseWhere,
    /// 不符合安全策略
    PolicyNotMatch,
    /// 用户禁用
    UserDisabled,
    /// 用户删除
    UserDeleted,
    /// 用户未生效
    UserNotActive = 5,
    /// 用户已失效
    UserExpired,
    /// 用户密码变更
    UserPwdChanged,
    /// 用户绑定码变更
    UserBindChanged,
    /// 异常断开
    Exceptions,
    /// 服务端异常
    SERVER = 10,
    /// 租户禁用
    TenantDisabled,
    /// 结束会话
    EndSession,
    /// 正常断开
    Normal = u8::MAX,
}

#[derive(Eq, PartialEq, Debug, Clone, Copy)]
#[repr(u8)]
pub enum Notification {
    /// 服务端异常
    ServerException = 0,
    /// 服务端正常
    ServerNormal = 1,
}

impl From<u8> for Notification {
    fn from(t: u8) -> Self {
        match t {
            0 => Notification::ServerException,
            1 => Notification::ServerNormal,
            _ => {
                panic!("unimplemented!")
            }
        }
    }
}

impl CloseReason {
    pub fn from_mq(state: u8) -> Self {
        match state {
            0 => CloseReason::UserDisabled,
            1 => CloseReason::UserDeleted,
            2 => CloseReason::DeviceDisabled,
            3 => CloseReason::UserNotActive,
            4 => CloseReason::UserExpired,
            5 => CloseReason::UserPwdChanged,
            6 => CloseReason::UserBindChanged,
            7 => CloseReason::TenantDisabled,
            8 => CloseReason::EndSession,
            100 => CloseReason::LoginElseWhere,
            _ => {
                panic!("unimplemented!")
            }
        }
    }
}

impl From<u8> for CloseReason {
    fn from(t: u8) -> Self {
        match t {
            0 => CloseReason::DeviceDisabled,
            1 => CloseReason::LoginElseWhere,
            2 => CloseReason::PolicyNotMatch,
            3 => CloseReason::UserDisabled,
            4 => CloseReason::UserDeleted,
            5 => CloseReason::UserNotActive,
            6 => CloseReason::UserExpired,
            7 => CloseReason::UserPwdChanged,
            8 => CloseReason::UserBindChanged,
            9 => CloseReason::Exceptions,
            10 => CloseReason::SERVER,
            11 => CloseReason::TenantDisabled,
            12 => CloseReason::EndSession,
            u8::MAX => CloseReason::Normal,
            _ => {
                panic!("unimplemented!")
            }
        }
    }
}
