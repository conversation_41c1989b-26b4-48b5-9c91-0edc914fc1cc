use serde_json::{json, Value};
use sysinfo::System;
use winreg::{enums::HKEY_LOCAL_MACHINE, RegKey};

/// 读取系统信息
pub fn info(device_id: &str) -> Value {
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let product_name = hklm
        .open_subkey("HARDWARE\\DESCRIPTION\\System\\BIOS")
        .and_then(|bios| bios.get_value::<String, &str>("SystemProductName"))
        .unwrap_or("Unknown".to_string());

    let manufacturer = hklm
        .open_subkey("HARDWARE\\DESCRIPTION\\System\\BIOS")
        .and_then(|bios| bios.get_value::<String, &str>("SystemManufacturer"))
        .unwrap_or("Unknown".to_string());
    let mac = crate::environment::interface::list()
        .unwrap_or_default()
        .into_iter()
        .map(|mut value| value["mac"].take())
        .filter(|f| f.is_string())
        .map(|mac| mac.as_str().map(String::from).unwrap_or_default())
        .collect::<Vec<String>>()
        .join(",");
    json!({
        "deviceId": device_id,
        "sysType": System::name().unwrap_or_default(),
        "kernelVersion": System::kernel_version().unwrap_or_default(),
        "operatingSystem": System::long_os_version().unwrap_or_default(),
        "systemVersion": System::os_version().unwrap_or_default(),
        "clientVersion": version::FILE_VERSION,
        "mac": mac,
        "model": product_name,
        "name": System::host_name().unwrap_or_default(),
        "manufacturer": manufacturer,
    })
}
