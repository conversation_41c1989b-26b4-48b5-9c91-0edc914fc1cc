use crate::{
    backend_state_machine::BackendCommand, mpsc::Sender, CoreEventSender, InternalCoreEvent,
};
use base::packet::{Message, MessageType};
use futures_channel::{mpsc, oneshot};
use futures_util::{FutureExt, SinkExt, StreamExt};
use moka::{
    future::Cache,
    notification::{ListenerFuture, RemovalCause},
};
use once_cell::sync::Lazy;
use std::{sync::atomic::AtomicU64, time::Duration};
use tlv::Serialize;
use tlv_types::ProxyRequest;
use types::{backend::ConnectionConfig, states::BackendState};

pub const UNIVERSAL_SECRET: [u8; 20] = [
    0b10110110, 0b01011111, 0b10110000, 0b11111011, 0b00100000, 0b00101110, 0b00010001, 0b01111001,
    0b10110001, 0b11111011, 0b01011010, 0b01000110, 0b10001011, 0b11011001, 0b11011100, 0b00101110,
    0b01001011, 0b00001010, 0b10111010, 0b00011010,
];

/// 代理Http返回结果
#[derive(Debug)]
pub enum ProxyHttpResult {
    Ok(Vec<u8>, bool),
    Timeout,
    ConnectionFailed,
    Fail(String),
}

/// 转发代理结果
pub enum ForwardProxyResult {
    Ok(Vec<u8>, bool),
    Timeout,
    Forbidden,
    AuthenticationRequired,
}

pub struct ProxyHttp {
    /// 连接配置
    pub connection_config: Option<ConnectionConfig>,
    /// 请求序号
    pub seq: Sequence,
    /// HttpRequest
    pub request: Vec<u8>,
}

pub type ProxyHttpSender = mpsc::Sender<ProxyHttpResult>;
pub type ProxyHttpChunkSender = mpsc::Sender<Vec<u8>>;

pub static REQUEST_SEQUENCE: AtomicU64 = AtomicU64::new(0);

type Sequence = u64;

pub static REQUESTS: Lazy<Cache<Sequence, (ProxyHttpSender, ProxyHttpChunkSender)>> =
    Lazy::new(|| {
        Cache::builder()
            .time_to_live(Duration::from_secs(15))
            .async_eviction_listener(
                move |_k,
                      mut v: (ProxyHttpSender, ProxyHttpChunkSender),
                      cause|
                      -> ListenerFuture {
                    async move {
                        log::trace!("eviction: {_k}");
                        if cause != RemovalCause::Explicit {
                            // 超时
                            _ = v.0.send(ProxyHttpResult::Timeout).await;
                            v.1.close_channel();
                        }
                    }
                    .boxed()
                },
            )
            .build()
    });

#[derive(Clone)]
pub struct ProxyHandle {
    command_tx: mpsc::UnboundedSender<ProxyHttp>,
}

impl ProxyHandle {
    pub fn send_request(&self, request: ProxyHttp) {
        if self.command_tx.unbounded_send(request).is_err() {
            log::error!("Http proxy thread panicked");
        }
    }
}

/// 代理前端请求接口
pub fn spawn(device_id: String, core_tx: CoreEventSender) -> ProxyHandle {
    let (request_tx, mut request_rx) = mpsc::unbounded::<ProxyHttp>();

    // 定时执行缓存挂起的任务
    tokio::spawn(async move {
        let mut tick = tokio::time::interval(Duration::from_secs(1));
        loop {
            _ = tick.tick().await;
            REQUESTS.run_pending_tasks().await;
        }
    });

    // 客户端请求接口代理
    tokio::spawn({
        let core_tx = core_tx.clone();
        async move {
            // 缓存上一次请求状态
            let mut previous = None;
            let mut tick = tokio::time::interval(Duration::from_secs(2));

            log::info!("Enable http proxy");
            'THREAD: loop {
                tokio::select! {
                    _ = tick.tick() => {
                        previous = None;
                    },
                    result = request_rx.next() => match result {
                        None => {
                            log::error!("Http proxy thread panicked");
                            break;
                        }
                        Some(http) => {
                            loop {
                                let (tx, rx) = oneshot::channel();
                                if core_tx
                                    .send(InternalCoreEvent::GetBackendState(tx))
                                    .is_err()
                                {
                                    log::error!("Core already down or thread panicked");
                                    break 'THREAD;
                                }
                                let backend_state = rx.await;
                                log::trace!("Get backend state: {:?}", backend_state);
                                match backend_state {
                                    Err(_) => {
                                        log::error!("Core already down or thread panicked");
                                        break 'THREAD;
                                    }
                                    // 已连接
                                    Ok(
                                        BackendState::Connected
                                        | BackendState::ProxyConnected,
                                    ) => {
                                        let request = ProxyRequest {
                                            device_id: device_id.clone(),
                                            seq: http.seq,
                                            data: http.request,
                                        };

                                        let message = Message::new(
                                            MessageType::ProxyRequest,
                                            &request.serialize(),
                                        );
                                        if core_tx
                                            .send(InternalCoreEvent::BackendCommand(
                                                BackendCommand::HttpRequest(message),
                                            ))
                                            .is_err()
                                        {
                                            log::error!("Core already down or thread panicked");
                                            break 'THREAD;
                                        }
                                        break;
                                    }
                                    // 半连接, 等待一会儿后再次查看
                                    Ok(BackendState::Connecting | BackendState::ProxyConnecting) => {
                                        log::debug!("Connecting...");
                                        tokio::time::sleep(Duration::from_millis(100)).await;
                                        continue;
                                    }
                                    Ok(_) => {
                                        if http.connection_config.is_none() {
                                            if let Some((mut tx, mut chunk_tx)) = REQUESTS.remove(&http.seq).await {
                                                tx.close_channel();
                                                chunk_tx.close_channel();
                                            }
                                            continue;
                                        }
                                        // 若与之前连接的地址相同, 则不再尝试连接
                                        if http.connection_config == previous {
                                            if let Some((mut tx, _)) = REQUESTS.remove(&http.seq).await
                                            {
                                                log::trace!("cancel request");
                                                _ = tx.send(ProxyHttpResult::ConnectionFailed).await;
                                            }
                                            break;
                                        }

                                        let (tx, rx) = oneshot::channel();
                                        if core_tx
                                            .send(InternalCoreEvent::BackendCommand(
                                                BackendCommand::ProxyConnect {
                                                    tx,
                                                    connection_config: http.connection_config.clone().unwrap(),
                                                },
                                            ))
                                            .is_err()
                                        {
                                            log::error!("Core already down or thread panicked");
                                            break 'THREAD;
                                        }
                                        match rx.await {
                                            Ok(Ok(_)) => continue,
                                            Ok(Err(error)) => {
                                                previous = http.connection_config;
                                                log::error!("{error}");
                                                if let Some((mut tx, _)) =
                                                    REQUESTS.remove(&http.seq).await
                                                {
                                                    if let crate::Error::ConnectError(error) = error {
                                                        if error.kind()
                                                            == std::io::ErrorKind::ConnectionRefused
                                                        {
                                                            _ = tx
                                                                .send(ProxyHttpResult::ConnectionFailed)
                                                                .await;
                                                            break;
                                                        }
                                                    }
                                                    _ = tx.send(ProxyHttpResult::Timeout).await;
                                                }
                                                break;
                                            }
                                            // 超时时, tx直接被drop了
                                            _ => {
                                                previous = http.connection_config;
                                                log::error!("Timeout waiting for proxy connection");
                                                if let Some((mut tx, _)) =
                                                    REQUESTS.remove(&http.seq).await
                                                {
                                                    _ = tx
                                                        .send(ProxyHttpResult::ConnectionFailed)
                                                        .await;
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            log::info!("Http proxy thread exit");
        }
    });

    ProxyHandle {
        command_tx: request_tx,
    }
}
