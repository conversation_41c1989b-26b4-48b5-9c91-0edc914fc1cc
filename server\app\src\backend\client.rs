use std::{
    collections::{HashMap, HashSet},
    net::IpAddr,
    time::Duration,
};

use reqwest::{
    header::{HeaderMap, HeaderValue, AUTHORIZATION},
    Client, ClientBuilder,
};
use serde_json::Value;
use tracing::{error, info, trace, warn};

use base::generate_ua;
use tlv_types::WebApp;
use url::Url;

use crate::{config::BackendConfig, types::Servers};

use super::*;

fn default_headers() -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.append("Content-Type", HeaderValue::from_static("application/json"));
    headers.append("Accept", HeaderValue::from_static("application/json"));
    headers.append(AUTHORIZATION, HeaderValue::from_static(SDP_SERVICE_SECRET));
    headers
}

pub struct BackendCommonClient {
    // 服务端地址
    base_url: String,
    auth_ctx: String,
    resource_ctx: String,
    client: Client,
    config: BackendConfig,
}

impl BackendCommonClient {
    pub fn new(config: BackendConfig) -> Self {
        let headers = default_headers();
        let client = ClientBuilder::new()
            .user_agent("sdp/1.0")
            .default_headers(headers)
            .timeout(Duration::from_secs(config.timeout))
            .danger_accept_invalid_certs(config.ignore_unknown_ca)
            .danger_accept_invalid_hostnames(config.ignore_invalid_cn)
            .use_rustls_tls()
            .build()
            .expect("failed to build client");

        let mut base_url = config.url.as_str().to_owned();
        if base_url.ends_with("/") {
            base_url = base_url.strip_suffix("/").unwrap().to_owned();
        }

        info!(url = base_url, "creating backend client");
        BackendCommonClient {
            base_url,
            auth_ctx: config.auth_ctx.clone(),
            resource_ctx: config.resource_ctx.clone(),
            client,
            config,
        }
    }

    pub fn base_url(&self) -> Url {
        Url::parse(&self.base_url).unwrap()
    }

    pub fn new_client(&self, device_id: &str, tenant: &str, env: &Value) -> BackendClient {
        BackendClient::new(tenant, device_id, env, &self.config)
    }

    /// 登录
    pub async fn auth(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_AUTH);
        match self
            .client
            .post(&url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => {
                        Ok(result.unwrap_or(Value::Null))
                        // if let Ok(token) = hex::decode::<&str>(&token) {
                        //     return Ok(token);
                        // }
                        // return Err(RestResult::failed(SYSTEM_ERROR,
                        // SYSTEM_ERROR_MESSAGE).buffer());
                    }
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("authentication failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    pub async fn auth_sms(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_SMS_AUTH);
        match self
            .client
            .post(&url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => Ok(result.unwrap_or(Value::Null)),
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("authentication mfa failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    pub async fn auth_mfa(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_MFA_AUTH);
        match self
            .client
            .post(url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => Ok(result.unwrap_or(Value::Null)),
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("authentication mfa failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 异常重连
    pub async fn reauth(
        &self,
        tenant: &str,
        buf: Vec<u8>,
        ip: &str,
        env: &Value,
    ) -> Result<Value, Vec<u8>> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_LINKED_AUTH);
        match self
            .client
            .post(url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", ip)
            .header("APP-TYPE", "DESKTOP")
            .header("User-Agent", generate_ua(env))
            .body(buf)
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let bytes = response.bytes().await.unwrap_or_default();

                match parse_result::<Value>(&bytes) {
                    Ok(result) => {
                        Ok(result.unwrap_or(Value::Null))
                        // if let Ok(token) = hex::decode::<&str>(&token) {
                        //     return Ok(token);
                        // }
                        // return Err(RestResult::failed(SYSTEM_ERROR,
                        // SYSTEM_ERROR_MESSAGE).buffer());
                    }
                    Err(err) => Err(RestResult::failed_with_err(err).as_bytes()),
                }
            }
            Err(e) => {
                warn!("authentication mfa failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 当前登录是否需要验证码
    pub async fn require_verify_code(
        &self,
        tenant: &str,
        username: &str,
    ) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}&username={}",
            self.base_url, self.auth_ctx, SDP_REQUIRE_VERIFY_CODE, username
        );
        match self.client.get(url).header("TENANT", tenant).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }

                let bytes = response.bytes().await.unwrap_or_default();
                if status.is_success() {
                    Ok(bytes.to_vec())
                } else {
                    Err(bytes.to_vec())
                }
            }
            // 请求失败
            Err(e) => {
                error!(
                    "Failed to send get verification code message `{}`: {}",
                    username, e
                );
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 通过验证码修改密码
    pub async fn change_pwd_by_verify_code(
        &self,
        tenant: &str,
        buf: &[u8],
        env: &Value,
    ) -> Result<Vec<u8>, (bool, Vec<u8>)> {
        let url = format!(
            "{}{}{}",
            self.base_url, self.auth_ctx, SDP_CHANGE_PWD_BY_VERIFY_CODE
        );

        match self
            .client
            .post(url)
            .header("TENANT", tenant)
            .header("x-forwarded-for", env["ip"].as_str().unwrap())
            .header("User-Agent", generate_ua(env))
            .body(buf.to_vec())
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_server_error() {
                    return Err((
                        true,
                        RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes(),
                    ));
                }
                let bytes = response.bytes().await.unwrap_or_default().to_vec();

                match serde_json::from_slice::<Value>(&bytes) {
                    Ok(result) => {
                        if result["code"].as_str() != Some("SUCCESS") {
                            if let Some(map) = result["data"].as_object() {
                                match map.get("messageKey") {
                                    Some(value) if value.is_string() => {
                                        // 验证码无效
                                        if value.as_str() == Some("PWD.VERIFY.CODE.INVALID") {
                                            return Err((false, bytes));
                                        }
                                    }
                                    _ => {}
                                }
                            }
                            Err((true, bytes))
                        } else {
                            Ok(bytes)
                        }
                    }
                    Err(_) => Err((true, bytes)),
                }
            }
            Err(e) => {
                warn!("authentication failed, {}", e);
                Err((
                    true,
                    RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes(),
                ))
            }
        }
    }

    /// 获取服务器列表
    pub async fn fetch_resources(&self) -> HashMap<String, Servers> {
        let url = format!("{}{}{}", self.base_url, self.resource_ctx, SDP_SERVER_LIST);
        match self.client.get(url).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                if bytes.is_empty() {
                    info!("server list is loaded successfully, but the returned data is empty.");
                    return Default::default();
                }
                match parse_result::<HashMap<String, Servers>>(&bytes) {
                    Ok(server_list) => return server_list.unwrap_or_default(),
                    Err(e) => warn!("err on server list loaded: {}", e),
                }
            }
            Err(e) => warn!("failed to load server list. {}", e),
        };
        Default::default()
    }

    /// 获取服务端状态
    pub async fn get_backend_state(&self) -> Option<Value> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, "/health");
        match self.client.get(url).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                return match serde_json::from_slice::<Value>(&bytes) {
                    Ok(value) => Some(value),
                    Err(_) => None,
                };
            }
            Err(_) => return None,
        };
    }

    /// 获取隐藏服务器IP列表
    pub async fn fetch_virtual_resources(&self) -> HashMap<String, HashMap<IpAddr, IpAddr>> {
        let url = format!(
            "{}{}{}",
            self.base_url, self.resource_ctx, SDP_FAKE_SERVER_LIST
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                if bytes.is_empty() {
                    info!(
                        "fake server list is loaded successfully, but the returned data is empty."
                    );
                    return Default::default();
                }

                match parse_result::<HashMap<String, HashMap<IpAddr, IpAddr>>>(&bytes) {
                    Ok(fake_server_list) => return fake_server_list.unwrap_or_default(),
                    Err(e) => warn!("err on fake server list loaded: {}", e),
                }
            }
            Err(e) => warn!("Failed to load fake server list. {}", e),
        };
        Default::default()
    }

    /// 根据用户名获取secret
    pub async fn exchange_secret(&self, tenant: &str, username: &str) -> Option<Vec<u8>> {
        let url = format!(
            "{}{}{}&username={}",
            self.base_url, self.auth_ctx, SDP_EXCHANGE_SECRET, username
        );
        match self.client.get(url).header("TENANT", tenant).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                if bytes.is_empty() {
                    info!("get the secret successfully, but the returned data is empty.");
                    return None;
                }
                match parse_result::<String>(&bytes) {
                    Ok(secret) => {
                        let secret = secret.unwrap();
                        let secret = hex::decode!(secret.as_bytes());
                        return Some(secret);
                    }
                    Err(e) => warn!(username, "err on getting secret: {}", e),
                }
            }
            Err(e) => warn!("Failed to get secret for `{}`, {}", username, e),
        };

        None
    }

    /// 发送登录成功邮件
    pub async fn send_mail(&self, tenant: &str, username: &str, env: &Value) {
        let url = format!(
            "{}{}/web/auth/{}/sendMail",
            self.base_url, self.auth_ctx, username
        );
        match self
            .client
            .get(url)
            .header("TENANT", tenant)
            .header("APP-TYPE", "DESKTOP")
            .header("x-forwarded-for", env["ip"].as_str().unwrap())
            .header("User-Agent", generate_ua(env))
            .send()
            .await
        {
            Ok(response) => {
                if !response.status().is_success() {
                    error!("Failed to send mail `{}`: {}", username, response.status());
                } else {
                    let bytes = response.bytes().await.unwrap_or_default();
                    match serde_json::from_slice::<Value>(&bytes) {
                        Ok(value) => {
                            trace!("send mail value: {:?}", &value);
                        }
                        Err(err) => {
                            error!("Failed to send mail `{}`: {}", username, err);
                        }
                    };
                }
            }
            // 请求失败
            Err(e) => {
                error!("Failed to send mail message `{}`: {}", username, e);
            }
        };
    }

    /// 拉取WEB应用列表
    pub async fn fetch_web_apps(&self) -> HashMap<String, HashSet<WebApp>> {
        let url = format!("{}{}{}", self.base_url, self.resource_ctx, SDP_WEB_APPS);
        match self.client.get(url).send().await {
            Ok(response) => {
                let bytes = response.bytes().await.unwrap_or_default();
                if bytes.is_empty() {
                    info!("webapps is loaded successfully, but the returned data is empty.");
                    return Default::default();
                }
                match parse_result::<HashMap<String, HashSet<WebApp>>>(&bytes) {
                    Ok(server_list) => return server_list.unwrap_or_default(),
                    Err(e) => warn!("err on webapps loaded: {}", e),
                }
            }
            Err(e) => warn!("failed to load webapps. {}", e),
        }
        Default::default()
    }
}

pub struct BackendClient {
    // 服务端地址
    base_url: String,
    auth_ctx: String,
    mfa_ctx: String,
    client: Client,
}

impl BackendClient {
    pub fn new(tenant: &str, device_id: &str, env: &Value, config: &BackendConfig) -> Self {
        let mut headers = default_headers();
        headers.append("DEVICE-ID", HeaderValue::from_str(device_id).unwrap());
        headers.append("TENANT", HeaderValue::from_str(tenant).unwrap());
        headers.append(
            "x-forwarded-for",
            HeaderValue::from_str(env["ip"].as_str().unwrap()).unwrap(),
        );
        headers.append("APP-TYPE", HeaderValue::from_static("DESKTOP"));
        headers.append(
            "User-Agent",
            HeaderValue::from_str(&generate_ua(env)).unwrap(),
        );

        let client = ClientBuilder::new()
            .user_agent("sdp/1.0")
            .default_headers(headers)
            .timeout(Duration::from_secs(config.timeout))
            .danger_accept_invalid_certs(config.ignore_unknown_ca)
            .danger_accept_invalid_hostnames(config.ignore_invalid_cn)
            .use_rustls_tls()
            .build()
            .expect("failed to build client");

        let mut base_url = config.url.as_str().to_owned();
        if base_url.ends_with("/") {
            base_url = base_url.strip_suffix("/").unwrap().to_owned();
        }

        BackendClient {
            base_url,
            auth_ctx: config.auth_ctx.clone(),
            mfa_ctx: config.mfa_ctx.clone(),
            client,
        }
    }

    pub fn base_url(&self) -> Url {
        Url::parse(&self.base_url).unwrap()
    }

    /// 禁用用户
    pub async fn disable_user(&self, username: &str) {
        let url = format!(
            "{}{}/web/auth/{}/frozen",
            self.base_url, self.auth_ctx, username
        );

        match self.client.get(url).send().await {
            Ok(response) => {
                if !response.status().is_success() {
                    error!(
                        "Failed to disable user `{}`: {}",
                        username,
                        response.status()
                    );
                }
            }
            // 请求失败
            Err(e) => {
                error!("Failed to send disable user message `{}`: {}", username, e);
            }
        };
    }

    /// 退出
    pub async fn logout(&self, username: &str, device_id: &str, socket_addr: &str) {
        let url = format!(
            "{}{}{}&username={}&deviceId={}&socketAddr={}",
            self.base_url, self.auth_ctx, SDP_LOGOUT, username, device_id, socket_addr
        );
        trace!("logout: {}", &url);
        match self.client.get(url).send().await {
            Ok(response) => {
                if !response.status().is_success() {
                    error!("Failed to logout `{}`: {}", username, response.status());
                }
            }
            // 请求失败
            Err(e) => {
                error!("Failed to send logout message `{}`: {}", username, e);
            }
        };
    }

    /// 获取票据
    pub async fn get_linked_ticket(&self) -> Vec<u8> {
        let url = format!("{}{}{}", self.base_url, self.auth_ctx, SDP_LINKED_TICKET);
        trace!("get_linked_ticket: {}", &url);
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes();
                }

                response.bytes().await.unwrap_or_default().to_vec()
            }
            Err(err) => {
                warn!("Failed to generate linked ticket., {}", err);
                RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes()
            }
        }
    }

    /// 生成单点登录票据
    pub async fn generate_ticket(&self, username: &str) -> Vec<u8> {
        let url = format!(
            "{}{}/web/login/{}/ticket",
            self.base_url, self.auth_ctx, username
        );

        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes();
                }

                response.bytes().await.unwrap_or_default().to_vec()
            }
            Err(err) => {
                warn!(
                    "Failed to generate sso ticket. username = {}, {}",
                    username, err
                );
                RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes()
            }
        }
    }

    /// 刷新登录票据
    pub async fn refresh_ticket(&self, ticket: &str) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}&ticket={}",
            self.base_url, self.auth_ctx, SDP_LINKED_GET, ticket
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();

                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                };

                let bytes = response.bytes().await.unwrap_or_default();
                match serde_json::from_slice::<Value>(&bytes) {
                    Ok(value) => {
                        trace!("send mail value: {:?}", &value);
                    }
                    Err(err) => {
                        error!("Failed to send mail : {}", err);
                    }
                };

                let result = bytes.to_vec();
                if status.is_success() {
                    return Ok(result);
                }
                Err(result)
            }
            // 请求失败
            Err(e) => {
                error!("Failed to send mail message `{}`: {}", ticket, e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 修改密码
    pub async fn change_pwd(&self, buf: &[u8]) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}",
            self.base_url, self.auth_ctx, SDP_CHANGE_PWD_BY_OLD
        );

        match self.client.post(&url).body(buf.to_vec()).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }
                let result = response.bytes().await.unwrap_or_default().to_vec();
                if status.is_success() {
                    return Ok(result);
                }
                Err(result)
            }
            Err(e) => {
                warn!("change pwd failed, {}", e);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 询问是否认证成功
    pub async fn ask_auth(&self, auth_result_id: &str) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}{}",
            self.base_url, self.mfa_ctx, MFA_AUTH_RESULT, auth_result_id
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }

                let bytes = response.bytes().await.unwrap_or_default();
                if status.is_success() {
                    Ok(bytes.to_vec())
                } else {
                    Err(bytes.to_vec())
                }
            }
            Err(err) => {
                warn!("Failed to generate request: {}", err);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }

    /// 询问本地执行策略
    pub async fn client_execution_policy(&self, username: &str) -> Result<Vec<u8>, Vec<u8>> {
        let url = format!(
            "{}{}{}&username={}",
            self.base_url, self.auth_ctx, CLIENT_EXECUTION_POLICY, username
        );
        match self.client.get(url).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_server_error() {
                    return Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes());
                }

                let bytes = response.bytes().await.unwrap_or_default();
                if status.is_success() {
                    Ok(bytes.to_vec())
                } else {
                    Err(bytes.to_vec())
                }
            }
            Err(err) => {
                warn!("Failed to generate request: {}", err);
                Err(RestResult::failed(SYSTEM_ERROR, SYSTEM_ERROR_MESSAGE).as_bytes())
            }
        }
    }
}
