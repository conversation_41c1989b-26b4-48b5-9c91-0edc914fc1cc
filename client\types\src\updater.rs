use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct UpdateResponse {
    pub should_update: bool,
    pub payload: Option<UpdateResponseInner>,
}

#[derive(Serialize, Deserialize)]
pub struct UpdateResponseInner {
    pub force: bool,
    pub version: String,
    pub notes: String,
}

#[derive(Debug, Serialize, Clone)]
pub struct UpdateStatus {
    pub status: String,
    pub msg: Option<String>,
}

#[derive(Debug, Serialize, Co<PERSON>, <PERSON>lone)]
#[serde(rename_all = "camelCase")]
pub struct DownloadProgress {
    pub chunk_size: u64,
    pub content_length: u64,
}
