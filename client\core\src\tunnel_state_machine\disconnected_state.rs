use futures::stream::StreamExt;

use err_ext::ErrorExt;
use types::tunnel::TunnelStateTransition;

use crate::tunnel_state_machine::{connecting_state::ConnectingState, TunnelCommand};

use super::{
    EventConsequence, SharedTunnelStateValues, TunnelCommandR<PERSON>eiver, TunnelState,
    TunnelStateWrapper,
};

pub struct DisconnectedState;

impl DisconnectedState {
    async fn reset_dns(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values.dns_monitor.reset().await {
            log::error!("{}", error.display_chain_with_msg("Unable to reset DNS"));
        }
    }
}

#[async_trait::async_trait]
impl TunnelState for DisconnectedState {
    type Bootstrap = ();

    #[cfg_attr(
        any(target_os = "windows", target_os = "linux"),
        allow(unused_variables)
    )]
    async fn enter(
        shared_values: &mut SharedTunnelStateValues,
        _not_need: (),
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        #[cfg(target_os = "macos")]
        if let Err(error) = shared_values.dns_monitor.reset().await {
            log::error!(
                "{}",
                error.display_chain_with_msg("Unable to disable filtering resolver")
            );
        }

        // TODO #[cfg(target_os = "linux")]
        // shared_values.reset_connectivity_check();
        #[cfg(target_os = "android")]
        shared_values.tun_provider.lock().unwrap().close_tun();

        (
            TunnelStateWrapper::from(DisconnectedState),
            TunnelStateTransition::Disconnected,
        )
    }

    async fn handle_event(
        self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match commands.next().await {
            Some(TunnelCommand::Dns(servers)) => {
                // Same situation as allow LAN above.
                shared_values
                    .set_dns_servers(servers)
                    .expect("Failed to reconnect after changing custom DNS servers");

                SameState(self.into())
            }
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::BackendOffline) => {
                _ = shared_values.set_dns_servers(None);
                SameState(self.into())
            }
            Some(TunnelCommand::Connect) => {
                NewState(ConnectingState::enter(shared_values, ()).await)
            }
            None => {
                Self::reset_dns(shared_values).await;
                Finished
            }
            Some(_) => SameState(self.into()),
        }
    }
}
