# SPA 允许的时间误差(秒). 为0时, 不验证时间.
spa_time_deviation = 15
# SPA 端口
spa_port = 65321
# DNS 地址
dns = ["************"]
# SPA 解密私钥
sec_key = """-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgTITLdgYONqYEc6YW
E4H+/JPCVJNeyshHbDryCkFYIPugCgYIKoEcz1UBgi2hRANCAAR7NuNVoiyVhWTh
peEk139qk5xz2G6IdvTGt0MeqGmBzW+04Us2FDiNLULuryDRpM3L+kkZ4oMmXJX3
q0LIeD/k
-----END PRIVATE KEY-----"""
# 授权信息加载间隔时间
authorization_interval = 60
# 轮询后台状态间隔时间. (秒)
backend_interval = 10
# 推送流量统计间隔时间. (毫秒)
push_traffic_interval = 5000

# Keepalive
[keepalive]
# 空闲时间. 单位(秒)
idle = 300
# 间隔时间. 单位(秒)
interval = 15
# 重复次数
retries = 4

# 后端服务配置
[backend]
# 后端服务地址
url = "http://*************:11082"
# 认证服务context path
auth_ctx = "/identity-sso/v8"
# 资源服务context path
resource_ctx = "/access-sdp-sso/v8"
# 多因子 context path
mfa_ctx = "/mfa/v8"
# HTTPS 忽略证书CA校验
ignore_unknown_ca = true

# Redis 配置
[redis]
database = 0
# Redis密码
password = "16F3e8EEFeFf047b"
# Redis协议版本
version = "RESP2"
# 日志
tracing = false
# Redis服务配置
[redis.server]
type = "Centralized"
host = "*************"
port = 6379

# type = "Clustered"
# hosts = [["************", 6379], ["************", 6379]]

# type = "Sentinel"
# hosts = [["************", 6379], ["************", 6379]]
# service_name: xxx,
# username: Option<String>,
# password: Option<String>,

# RabbitMQ 配置
[mq]
# MQ地址
url = "**********************************************//"

[server]
# 服务绑定地址
host = "0.0.0.0"
# TCP隧道端口
port = 3031
# ca证书
ca = "/root/sdp/cert_jingantech/ca.crt"
# cert 证书
cert = "/root/sdp/cert_jingantech/ctrl.crt"
# key 秘钥
key = "/root/sdp/cert_jingantech/ctrl.key"

# 主网卡配置(客户端接入网关流量所在的网卡)
[interface.primary]
# 网卡名称
iface = "ens160"
# 网卡IPv4地址
ipv4 = "*************"
# 开放本机端口. 不经过SPA认证即可访问.
# 支持单个端口和端口范围 [22, [35, 40]], 端口范围为左闭右闭区间
pub_ports = [22]
# 白名单 (控制器地址/后端服务/Redis/MQ)
# 端口为0时, 为IP白名单
whitelist = ["*************:11082", "*************:5672", "*************:6379"]
# 资源白名单. (不用添加到资源列表, 也不用授权) 例如DNS服务
resource_whitelist = ["************:53"]
# 发送队列大小
tx_queue_len = 4096
# 接收队列达标
rx_queue_len = 4096
# 帧大小
frame_size = 2048
# 运行模式
xdp_mode = "Auto"
# 网段(该网段的服务通过该网卡进行访问)
segments = ["0.0.0.0/0"]
# 其他网卡配置
# [[interface.secondary]]
# 网卡名称
# iface = "ens160"
# 网卡IPv4地址
# ipv4 = "*************"
# 开放本机端口. 不经过SPA认证即可访问.
# 支持单个端口和端口范围 [22, [35, 40]], 端口范围为左闭右闭区间
# pub_ports = [22]
# 白名单 (控制器地址/后端服务/Redis/MQ)
# 端口为0时, 为IP白名单
# whitelist = ["*************:82", "*************:5672", "*************:6379"]
# 资源白名单. (不用添加到资源列表, 也不用授权) 例如DNS服务
# resource_whitelist = ["************:53"]
# 发送队列大小
# tx_queue_len = 4096
# 接收队列达标
# rx_queue_len = 4096
# 帧大小
# frame_size = 2048
# 运行模式
# xdp_mode = "Auto"
# 网段(该网段的服务通过该网卡进行访问)
# segments = ["0.0.0.0/0"]
