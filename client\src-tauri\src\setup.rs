use log::error;
use std::{error::Error, path::PathBuf};
use tokio::sync::RwLock;

use tauri::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Wry};

use crate::{core::CoreManager, log_err, notify, resource, state::AppState};

pub(super) fn resolve_setup(
    app: &mut App<Wry>,
    resource_dir: PathBuf,
) -> Result<(), Box<dyn Error>> {
    #[cfg(target_os = "macos")]
    app.set_activation_policy(tauri::ActivationPolicy::Regular);

    let app_handle = app.handle();
    crate::logging::front_log(&app_handle);

    if let Err(error) = resource::init(&app_handle, resource_dir) {
        error!(target: "app", "{error}");
        notify!(app_handle, "程序配置错误");
        std::process::exit(1);
    }

    // 初始化程序状态
    app_handle.manage(RwLock::const_new(AppState::default()));

    // 监听电源事件
    #[cfg(windows)]
    {
        crate::system::power::spawn(app_handle);
    }

    Ok(())
}

pub fn reset(_app_handle: AppHandle<Wry>) {
    log_err!(CoreManager::global().stop_core());
}
