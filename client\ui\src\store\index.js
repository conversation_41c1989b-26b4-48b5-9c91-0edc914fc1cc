import { invoke } from "@tauri-apps/api";
import { createStore } from "vuex";

const store = createStore({
  state() {
    return {
      // 是否有新版本
      hasNewVersion: false,
      // 网络状态
      networkStatus: true,
      // 当前是否登录
      isLogin: false,
      // 当前登录用户名
      username: null,
      // 当前登录用户详细信息
      currentUser: {},
      // #if [includeCluster]
      // 显示服务器设置
      showClusterConfig: false,
      // #elif [includeSDP]
      // 显示节点配置
      showNodeConfig: false,
      // #endif
      // 显示关于我们
      showAboutUs: false,

      changePwdLoading: false,
      lang: "en",
      // 连接状态
      connectState: { state: false },
    };
  },
  getters: {
    hasNewVersion: (state) => state.hasNewVersion,
    networkStatus: (state) => state.networkStatus,
    isLogin: (state) => state.isLogin,
    username: (state) => state.username,
    currentUser: (state) => state.currentUser,
    // i18n: state => state.i18n,
    lang: (state) => state.lang,
    changePwdLoading: (state) => state.changePwdLoading,
    connectState: (state) => state.connectState,
    // #if [includeCluster]
    showClusterConfig: (state) => state.showClusterConfig,
    // #elif [includeSDP]
    showNodeConfig: (state) => state.showNodeConfig,
    // #endif
    showAboutUs: (state) => state.showAboutUs,
    // #if [includeSDP]
    reconnectToken: (state) => state.reconnectToken,
    reconnectTokenExpire: (state) => state.reconnectTokenExpire,
    // #endif
  },
  mutations: {
    setHasNewVersion(state, data) {
      state.hasNewVersion = data.hasNewVersion;
    },
    setNetworkStatus(state, data) {
      state.networkStatus = data;
    },
    updateIsLogin(state, login) {
      state.isLogin = login;
    },
    setUsername(state, username) {
      state.username = username;
    },
    setCurrentUser(state, user) {
      state.currentUser = user;
    },
    updateLanguage(state, data) {
      switch (data) {
        case "en":
          // state.i18n = i18n("en");
          state.lang = "en";
          break;
        default:
          state.lang = "zh";
        // state.i18n = i18n("zh");
      }
    },
    updateChangePwdLoading(state, val) {
      state.changePwdLoading = val;
    },
    setConnectState(state, res) {
      state.connectState = res;
    },
    // #if [includeCluster]
    setClusterConfigVisible(state, flag) {
      state.showClusterConfig = flag;
    },
    // #elif [includeSDP]
    setNodeVisible(start, res) {
      start.showNodeConfig = res;
    },
    // #endif
    setAboutUsVisible(state, flag) {
      state.showAboutUs = flag;
    },
  },
  actions: {
    async connectState({ commit }) {
      let connectState = await invoke("is_offline").catch((e) => {
        return Promise.reject(e.toString());
      });
      commit("setConnectState", connectState);
      return Promise.resolve();
    },
    async updateLanguage({ commit }, lang) {
      commit("updateLanguage", lang);
    },
  },
});
export default store;
