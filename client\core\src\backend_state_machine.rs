use std::{
    collections::HashMap,
    net::{IpAddr, Ipv4Addr},
    sync::Arc,
    time::Duration,
};

use futures::{
    channel::{mpsc, oneshot},
    stream, StreamExt,
};
use serde_json::Value;
use tokio::net::TcpStream;
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use base::packet::{Message, MessageCodec};
use tlv_types::{AuthResult, ModePayload};
use types::backend::{
    ActionAfterDisconnect, BackendStateTransition, CancelAuth, ChangePwdCode, ChangePwdOldPwd,
    CompleteAuth, ConnectionConfig, Login,
};

use crate::{
    backend::server::Backend,
    backend_state_machine::{
        connected_state::ConnectedState, connecting_state::ConnectingState,
        disconnected_state::DisconnectedState, error_state::ErrorState,
        proxy_connected_state::ProxyConnectedState, proxy_connecting_state::ProxyConnectingState,
    },
    mpsc::Sender,
    routing::RouteManagerHandle,
    setting::DeviceIdentity,
    CoreEventSender, EventListener,
};

mod connected_state;
mod connecting_state;
mod disconnected_state;
mod error_state;
mod proxy_connected_state;
mod proxy_connecting_state;

pub type ResponseTx<T, E> = oneshot::Sender<Result<T, E>>;

const BACKEND_STATE_MACHINE_SHUTDOWN_TIMEOUT: Duration = Duration::from_secs(5);

pub enum BackendCommand {
    /// 断开后端连接
    Disconnect(Option<oneshot::Sender<()>>),

    /// Reconnect the tunnel, if one is connecting/connected.
    Reconnect(oneshot::Sender<bool>),

    /// Connect backend in proxy mode.
    ProxyConnect {
        tx: ResponseTx<(), crate::Error>,
        connection_config: ConnectionConfig,
    },

    /// Proxy http request.
    HttpRequest(Message),

    /// Log in backend with a given account.
    Login {
        tx: ResponseTx<AuthResult, crate::Error>,
        system_info: Value,
        payload: Login,
    },

    /// Get login iam ticket.
    GetTicket,

    /// Mfa auth complete.
    CompleteAuth(CompleteAuth, oneshot::Sender<bool>),

    /// Cancel mfa auth
    CancelAuth(CancelAuth, oneshot::Sender<bool>),

    /// Change password by verify code.
    ChangePwdByCode(ResponseTx<String, crate::Error>, Value, ChangePwdCode),

    /// Change password by old password.
    ChangePwdByOldPwd(ChangePwdOldPwd),

    /// Backend channel packets.
    Packet(Message),
}

pub struct BackendParameters<L: EventListener + Send + Clone + 'static> {
    pub interface_name: String,
    pub device_id: String,

    pub event_listener: L,
    pub route_manager_handle: RouteManagerHandle,

    /// 本机IPv4地址
    pub local_ip: Ipv4Addr,
    pub identities: HashMap<String, DeviceIdentity>,
}

pub async fn spawn<L>(
    core_tx: CoreEventSender,
    backend_parameters: BackendParameters<L>,
    state_change_listener: impl Sender<BackendStateTransition> + Send + 'static,
) -> BackendStateMachineHandle
where
    L: EventListener + Send + Clone + 'static,
{
    let (command_tx, command_rx) = mpsc::unbounded();
    let command_tx = Arc::new(command_tx);

    let (shutdown_tx, shutdown_rx) = oneshot::channel();

    let _weak_command_tx = Arc::downgrade(&command_tx);

    let init_args = BackendStateMachineInitArgs {
        interface_name: backend_parameters.interface_name,
        device_id: backend_parameters.device_id,
        core_tx,
        event_listener: backend_parameters.event_listener,
        route_manager_handle: backend_parameters.route_manager_handle,
        local_ip: backend_parameters.local_ip,
        identities: backend_parameters.identities,
        commands_rx: command_rx,
    };
    let state_machine = BackendStateMachine::new(init_args).await;

    tokio::spawn(async move {
        state_machine.run(state_change_listener).await;
        if shutdown_tx.send(()).is_err() {
            log::error!("Can't send shutdown completion to daemon");
        }
    });

    BackendStateMachineHandle {
        command_tx,
        shutdown_rx,
    }
}

type BackendCommandReceiver = stream::Fuse<mpsc::UnboundedReceiver<BackendCommand>>;

enum EventResult {
    Command(Option<BackendCommand>),
    Close(Option<i8>),
}

struct BackendStateMachineInitArgs<L: EventListener + Send + Clone + 'static> {
    interface_name: String,
    device_id: String,

    core_tx: CoreEventSender,
    event_listener: L,
    route_manager_handle: RouteManagerHandle,

    /// 本机IPv4地址
    local_ip: Ipv4Addr,
    identities: HashMap<String, DeviceIdentity>,
    commands_rx: mpsc::UnboundedReceiver<BackendCommand>,
}

pub struct BackendStateMachine<L: EventListener> {
    current_state: Option<BackendStateWrapper>,
    commands: BackendCommandReceiver,
    shared_values: SharedBackendStateValues<L>,
}

impl<L> BackendStateMachine<L>
where
    L: EventListener + Clone + Send + 'static,
{
    async fn new(args: BackendStateMachineInitArgs<L>) -> Self {
        let mut shared_values = SharedBackendStateValues {
            core_tx: args.core_tx,
            interface_name: args.interface_name,
            device_id: args.device_id,
            event_listener: args.event_listener,
            route_manager_handle: args.route_manager_handle,
            local_ip: args.local_ip,
            identities: args.identities,
        };

        tokio::spawn(async move {
            let (initial_state, _) = DisconnectedState::enter(&mut shared_values, None).await;

            BackendStateMachine {
                current_state: Some(initial_state),
                commands: args.commands_rx.fuse(),
                shared_values,
            }
        })
        .await
        .unwrap()
    }

    async fn run(mut self, change_listener: impl Sender<BackendStateTransition> + Send + 'static) {
        use EventConsequence::*;

        while let Some(state_wrapper) = self.current_state.take() {
            match state_wrapper
                .handle_event(&mut self.commands, &mut self.shared_values)
                .await
            {
                NewState((state, transition)) => {
                    self.current_state = Some(state);

                    if change_listener.send(transition).is_err() {
                        log::error!("Failed to send state change event to listener");
                        break;
                    }
                }
                SameState(state) => {
                    self.current_state = Some(state);
                }
                Finished => (),
            }
        }

        log::debug!("Exiting backend state machine loop");
    }
}

struct SharedBackendStateValues<L: EventListener> {
    core_tx: CoreEventSender,

    interface_name: String,
    device_id: String,

    event_listener: L,
    route_manager_handle: RouteManagerHandle,

    /// 本机IPv4地址
    local_ip: Ipv4Addr,
    identities: HashMap<String, DeviceIdentity>,
}

impl<L> SharedBackendStateValues<L>
where
    L: EventListener + Clone + Send + 'static,
{
    fn backend(&self) -> Backend {
        Backend {
            device_id: self.device_id.clone(),
            local_ip: self.local_ip,
            identities: self.identities.clone(),
        }
    }

    pub async fn change_password_by_verify_code(
        &self,
        tx: ResponseTx<String, crate::Error>,
        system_info: Value,
        message: ChangePwdCode,
    ) {
        let backend = self.backend();

        backend
            .change_pwd_by_verify_code(tx, system_info, message)
            .await;
    }

    pub async fn change_password_in_proxy(
        &self,
        framed: Framed<TlsStream<TcpStream>, MessageCodec>,
        tx: ResponseTx<String, crate::Error>,
        system_info: Value,
        message: ChangePwdCode,
    ) {
        let backend = self.backend();

        backend
            .change_pwd_in_proxy(framed, tx, system_info, message)
            .await;
    }

    pub async fn proxy_connect(
        &self,
        tx: ResponseTx<(), crate::Error>,
        connection_config: ConnectionConfig,
    ) -> Option<Framed<TlsStream<TcpStream>, MessageCodec>> {
        let backend = self.backend();

        let result = backend
            .connect(connection_config, base::spa::SPAType::Proxy)
            .await;

        match result {
            Ok(framed) => {
                _ = tx.send(Ok(()));
                Some(framed)
            }
            Err(error) => {
                _ = tx.send(Err(error));
                None
            }
        }
    }

    // 代理连接成功的情况下, 执行登录
    pub async fn login_in_proxy(
        &self,
        framed: Framed<TlsStream<TcpStream>, MessageCodec>,
        tx: ResponseTx<AuthResult, crate::Error>,
        system_info: Value,
        payload: Login,
    ) -> Option<(
        Framed<TlsStream<TcpStream>, MessageCodec>,
        ModePayload,
        Option<Vec<IpAddr>>,
        Option<IpAddr>,
    )> {
        let backend = self.backend();

        backend
            .login_in_proxy(framed, tx, system_info, payload)
            .await
    }

    pub async fn login(
        &self,
        tx: ResponseTx<AuthResult, crate::Error>,
        system_info: Value,
        payload: Login,
    ) -> Option<(
        Framed<TlsStream<TcpStream>, MessageCodec>,
        ModePayload,
        Option<Vec<IpAddr>>,
        Option<IpAddr>,
    )> {
        let backend = self.backend();

        backend.login(tx, system_info, payload).await
    }
}

/// Asynchronous result of an attempt to progress a state.
enum EventConsequence {
    /// Transition to a new state.
    NewState((BackendStateWrapper, BackendStateTransition)),
    /// An event was received, but it was ignored by the state so no transition is performed.
    SameState(BackendStateWrapper),
    /// The state machine has finished its execution.
    Finished,
}

/// Trait that contains the method all states should implement to handle an event and advance the
/// state machine.
#[async_trait::async_trait]
trait BackendState<L: EventListener>: Into<BackendStateWrapper> + Sized {
    /// Type representing extra information required for entering the state.
    type Bootstrap;

    /// Constructor function.
    ///
    /// This is the state entry point. It attempts to enter the state, and may fail by entering an
    /// error or fallback state instead.
    async fn enter(
        shared_values: &mut SharedBackendStateValues<L>,
        bootstrap: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition);

    /// Main state function.
    ///
    /// This is state exit point. It consumes itself and returns the next state to advance to when
    /// it has completed, or itself if it wants to ignore a received event or if no events were
    /// ready to be received. See [`EventConsequence`] for more details.
    ///
    /// An implementation can handle events from many sources, but it should also handle command
    /// events received through the provided `commands` stream.
    ///
    /// [`EventConsequence`]: enum.EventConsequence.html
    async fn handle_event(
        self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence;
}

macro_rules! state_wrapper {
    (enum $wrapper_name:ident { $($state_variant:ident($state_type:ident)),* $(,)* }) => {
        /// Valid states of the backend.
        ///
        /// All implementations must implement `BackendState` so that they can handle events and
        /// commands in order to advance the state machine.
        enum $wrapper_name {
            $($state_variant($state_type),)*
        }

        $(impl From<$state_type> for $wrapper_name {
            fn from(state: $state_type) -> Self {
                $wrapper_name::$state_variant(state)
            }
        })*

        impl $wrapper_name {
            async fn handle_event<L: crate::EventListener>(
                self,
                commands: &mut BackendCommandReceiver,
                shared_values: &mut SharedBackendStateValues<L>,
            ) -> EventConsequence {
                match self {
                    $($wrapper_name::$state_variant(state) => {
                        state.handle_event(commands, shared_values).await
                    })*
                }
            }
        }
    }
}

state_wrapper! {
    enum BackendStateWrapper {
        Disconnected(DisconnectedState),
        Connecting(ConnectingState),
        Connected(ConnectedState),
        Error(ErrorState),
        ProxyConnectingState(ProxyConnectingState),
        ProxyConnectedState(ProxyConnectedState),
    }
}

pub struct BackendStateMachineHandle {
    command_tx: Arc<mpsc::UnboundedSender<BackendCommand>>,
    shutdown_rx: oneshot::Receiver<()>,
}

impl BackendStateMachineHandle {
    /// Waits for the backend state machine to shut down.
    /// This may fail after a timeout of `BACKEND_STATE_MACHINE_SHUTDOWN_TIMEOUT`.
    pub async fn try_join(self) {
        drop(self.command_tx);

        match tokio::time::timeout(BACKEND_STATE_MACHINE_SHUTDOWN_TIMEOUT, self.shutdown_rx).await {
            Ok(_) => log::info!("Backend state machine shut down"),
            Err(_) => log::error!("Backend state machine did not shut down gracefully"),
        }
    }

    pub fn command_tx(&self) -> Arc<mpsc::UnboundedSender<BackendCommand>> {
        self.command_tx.clone()
    }
}

/// Which state should be transitioned to after disconnection is complete.
pub enum AfterDisconnect {
    Nothing,
    Reconnect(u32),
}

impl AfterDisconnect {
    /// Build event representation of the action that will be taken after the disconnection.
    pub fn action(&self) -> ActionAfterDisconnect {
        match self {
            AfterDisconnect::Nothing => ActionAfterDisconnect::Nothing,
            AfterDisconnect::Reconnect(..) => ActionAfterDisconnect::Reconnect,
        }
    }
}
