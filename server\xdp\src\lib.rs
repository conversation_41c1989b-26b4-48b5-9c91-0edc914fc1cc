// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

type Result<T = (), E = std::io::Error> = core::result::Result<T, E>;

/// Low-level bindings to various linux userspace APIs
mod bindings;

/// Default BPF programs to direct QUIC traffic
pub mod bpf;
/// Primitive types for AF-XDP kernel APIs
pub mod if_xdp;
/// Implementations of the IO traits from [`core::io`]
pub mod io;
/// Helpers for creating mmap'd regions
pub mod mmap;
/// Structures for tracking ring cursors and synchronizing with the kernel
pub mod ring;
/// Structure for opening and reference counting an AF-XDP socket
pub mod socket;
/// Helpers for making API calls to AF-XDP sockets
pub mod syscall;
/// A shared region of memory for holding frame (packet) data
pub mod umem;
