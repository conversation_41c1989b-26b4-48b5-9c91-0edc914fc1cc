#[cfg(target_os = "macos")]
use std::process::Command;
#[cfg(feature = "traceability")]
use std::sync::atomic::Ordering;
use std::{
    collections::{HashSet, VecDeque},
    net::{IpAddr, Ipv4Addr},
};

use base::net::IpPacket;
use futures::{
    channel::{mpsc, oneshot},
    future::BoxFuture,
    AsyncReadExt as FuturesAsyncReadExt, AsyncWriteExt, StreamExt,
};
use netconfig::ipnet::IpNet;
#[cfg(windows)]
use netconfig::sys::InterfaceExt;
use tokio::{sync::broadcast, task::Join<PERSON><PERSON>le};
use tunio::{
    traits::{DriverT, InterfaceT},
    DefaultAsyncInterface, DefaultDriver,
};
#[cfg(windows)]
use windows_sys::Win32::NetworkManagement::Ndis::NET_LUID_LH;

use err_ext::ErrorExt;
use types::tunnel::ErrorStateCause;

#[cfg(feature = "traceability")]
use crate::traceability::{parse_if_match_and_change_target_or_source, TRACING_LISTEN_PORT};

use crate::{
    mpsc::Sender,
    tunnel_state_machine::{ResourceEvent, TunnelArgs, TunnelEvent, TunnelMetadata},
    CoreEventSender, Direction, InternalCoreEvent,
};

#[cfg(windows)]
mod windows_ext;

/// Results from operations in the tunnel module.
pub type Result<T> = std::result::Result<T, Error>;
type EventCallback = Box<dyn (Fn(TunnelEvent) -> BoxFuture<'static, ()>) + Send + Sync + 'static>;

/// Errors that can occur in the [`TunioTunnel`].
#[derive(thiserror::Error, Debug)]
pub enum Error {
    /// Tunnel can't have IPv6 enabled because the system has disabled IPv6 support.
    #[error("Can't enable IPv6 on tunnel interface because IPv6 is disabled")]
    EnableIpv6Error,

    /// Failure in Windows syscall.
    #[cfg(windows)]
    #[error("Failure in Windows syscall")]
    WinnetError(#[from] crate::routing::Error),

    /// Running on an operating system which is not supported yet.
    #[error("Tunnel type not supported on this operating system")]
    UnsupportedPlatform,

    /// Failure to build tunnel.
    #[error("Failure to build tunnel. {0}")]
    TunnelError(String),

    /// Could not detect and assign the correct mtu
    #[error("Could not detect and assign a correct MTU for the tunnel")]
    AssignMtuError,
}

/// Tunnel parameters required to start a `TunioTunnel`.
/// See [`crate::net::TunnelParameters`].
#[derive(Clone)]
pub struct TunnelParameters {
    pub core_tx: CoreEventSender,

    /// Interface name.
    pub interface_name: String,

    #[cfg(target_os = "linux")]
    pub fwmark: Option<u32>,

    /// Enable configuration of IPv6 on the tunnel interface, allowing IPv6 communication to be
    /// forwarded through the tunnel.
    pub enable_ipv6: bool,
}

pub struct TunioTunnel {
    interface: DefaultAsyncInterface,
    event_callback: EventCallback,
    tunnel_close_rx: oneshot::Receiver<()>,
    /// 发送数据包
    core_rx: CoreEventSender,
    /// 向网卡发送数据
    #[cfg(feature = "traceability")]
    packet_tx: mpsc::UnboundedSender<Vec<u8>>,
    /// 接收向网卡发送的数据包
    packet_rx: mpsc::UnboundedReceiver<Vec<u8>>,

    auth_resources_rx: mpsc::UnboundedReceiver<ResourceEvent>,
    change_network_rx: mpsc::Receiver<(IpAddr, oneshot::Sender<()>)>,
    #[cfg(feature = "traceability")]
    traceability_rx: mpsc::Receiver<bool>,
}

impl TunioTunnel {
    /// Creates a new `TunnelMonitor` that connects to the given remote and notifies `on_event`
    /// on tunnel state changes.
    #[cfg_attr(any(target_os = "android", windows), allow(unused_variables))]
    pub async fn start<L>(
        tunnel_parameters: TunnelParameters,
        args: TunnelArgs<L>,
        #[cfg(feature = "traceability")] packet_tx: mpsc::UnboundedSender<Vec<u8>>,
        packet_rx: mpsc::UnboundedReceiver<Vec<u8>>,
        auth_resources_rx: mpsc::UnboundedReceiver<ResourceEvent>,
        change_network_rx: mpsc::Receiver<(IpAddr, oneshot::Sender<()>)>,
        #[cfg(feature = "traceability")] traceability_rx: mpsc::Receiver<bool>,
    ) -> Result<Self>
    where
        L: (Fn(TunnelEvent) -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>)
            + Send
            + Clone
            + Sync
            + 'static,
    {
        // Self::ensure_ipv6_can_be_used_if_enabled(tunnel_parameters)?;
        let interface = Self::open_tunnel(&tunnel_parameters)?;

        let metadata = Self::tunnel_metadata(&tunnel_parameters);
        ((args.on_event)(TunnelEvent::Up(metadata.clone()))).await;

        let event_callback = Box::new(args.on_event.clone());
        Ok(TunioTunnel {
            interface,
            event_callback,
            tunnel_close_rx: args.tunnel_close_rx,
            core_rx: tunnel_parameters.core_tx,
            #[cfg(feature = "traceability")]
            packet_tx,
            packet_rx,
            auth_resources_rx,
            change_network_rx,
            #[cfg(feature = "traceability")]
            traceability_rx,
        })
    }

    fn open_tunnel(tunnel_parameters: &TunnelParameters) -> Result<DefaultAsyncInterface> {
        let mut driver =
            DefaultDriver::new().map_err(|error| Error::TunnelError(error.display_chain()))?;
        let mut interface_config = DefaultAsyncInterface::config_builder();
        interface_config.name(tunnel_parameters.interface_name.clone());
        #[cfg(target_os = "windows")]
        interface_config
            .platform(|mut b| b.description("SDP Virtual Network Adapter".into()).build())
            .unwrap();
        let interface_config = interface_config.build().unwrap();
        let interface = DefaultAsyncInterface::new_up(&mut driver, interface_config)
            .map_err(|error| Error::TunnelError(error.display_chain()))?;

        let handle = interface.handle();
        #[cfg(windows)]
        windows_ext::initialize_interfaces(
            NET_LUID_LH {
                Value: handle.luid().map_err(|error| {
                    Error::TunnelError(error.display_chain_with_msg("Failed to get interface luid"))
                })?,
            },
            Some(1400),
        )
        .map_err(|error| {
            Error::TunnelError(error.display_chain_with_msg("Unable to initialize interface"))
        })?;

        #[cfg(target_os = "linux")]
        if let Err(error) = handle.set_mtu(1400) {
            log::warn!("Failed to set interface mtu. {}", error);
        }

        #[cfg(target_os = "macos")]
        if let Err(error) = Command::new("ifconfig")
            .args([&tunnel_parameters.interface_name, "mtu", "1400"])
            .output()
        {
            log::warn!("Failed to set interface mtu. {}", error);
        }

        // 默认IP
        #[cfg(any(windows, target_os = "linux"))]
        let ipnet = IpNet::new(IpAddr::V4(Ipv4Addr::new(10, 6, 7, 7)), 24).unwrap();
        #[cfg(target_os = "macos")]
        let ipnet = IpNet::from(IpAddr::V4(Ipv4Addr::new(10, 6, 7, 7)));
        if let Err(error) = handle.add_address(ipnet) {
            log::error!(
                "{}",
                error.display_chain_with_msg("Unable to set ip to tunnel")
            );
        }

        Ok(interface)
    }

    pub async fn listen(mut self) -> Option<ErrorStateCause> {
        log::debug!("Tunnel started");

        let handle = self.interface.handle();
        let (tx, mut rx) = broadcast::channel(1);

        let (mut reader, mut writer) = self.interface.split();
        // 转发数据
        let mut read_rx = tx.subscribe();
        let read_handle: JoinHandle<std::result::Result<(), Option<ErrorStateCause>>> =
            tokio::spawn(async move {
                let mut buf = vec![0u8; 1500];
                // Block traffic transmission during MFA authentication.
                let mut block_resources: VecDeque<Option<IpNet>> = Default::default();
                let mut resources: HashSet<IpNet> = Default::default();
                let mut virtual_ip = IpAddr::V4(Ipv4Addr::new(10, 6, 7, 7));
                #[cfg(feature = "traceability")]
                {
                    let mut traceability_available = true;
                    loop {
                        tokio::select! {
                            _ = read_rx.recv() => {
                                break Ok(());
                            }
                            Some(event) = self.auth_resources_rx.next() => {
                                match event {
                                    ResourceEvent::AddBlock(resource) => {
                                        if resource.is_none() {
                                            block_resources.push_front(None)
                                        } else {
                                            block_resources.push_back(resource);
                                        }
                                        log::trace!("Block resources: {:?}", block_resources);
                                    }
                                    ResourceEvent::DeleteBlock(resource, tx) => {
                                        block_resources.retain(|x|{
                                            x != &resource
                                        });

                                        log::trace!("Block resources: {:?}", block_resources);
                                        _ = tx.send(!block_resources.is_empty());
                                    }
                                    ResourceEvent::GetFirstBlock(tx) => {
                                        _ = tx.send(block_resources.front().map(ToOwned::to_owned));
                                        log::debug!("Remaining resources that require certification: {:?}", block_resources);
                                    }
                                    ResourceEvent::AddResources(new_resources) => {
                                        resources.extend(new_resources.iter());
                                        log::debug!("Resources: {:?}", resources);
                                    }
                                    ResourceEvent::DeleteResource(del_resources) => {
                                        del_resources.iter().for_each(|resource| {
                                            resources.remove(resource);
                                        });
                                        log::debug!("Resources: {:?}", resources);
                                    }
                                    ResourceEvent::Clear => {
                                        block_resources.clear();
                                        resources.clear();
                                    }
                                }
                            }
                            Some((ip, tx)) = self.change_network_rx.next() => {
                                virtual_ip = ip;

                                #[cfg(windows)]
                                if let Ok(addresses) = handle.addresses() {
                                    addresses.into_iter().for_each(|ipnet| {
                                        _ = handle.remove_address(ipnet);
                                    });
                                }

                                #[cfg(any(windows, target_os = "linux"))]
                                let ipnet = IpNet::new(ip, 24).unwrap();
                                #[cfg(target_os = "macos")]
                                let ipnet = IpNet::from(ip);
                                if let Err(error) = handle.add_address(ipnet) {
                                    log::error!(
                                        "{}",
                                        error.display_chain_with_msg("Unable to set ip to tunnel")
                                    );
                                    break Err(Some(ErrorStateCause::StartTunnelError));
                                }
                                _ = tx.send(());
                            }
                            Some(available) = self.traceability_rx.next() => {
                                traceability_available = available;
                            }
                            Ok(size) = reader.read(buf.as_mut_slice()) => {
                                #[cfg(target_os = "macos")]
                                let begin = 4;
                                #[cfg(not(target_os = "macos"))]
                                let begin = 0;

                                // 不在资源列表或者需要认证后访问的资源, 拒绝转发
                                // 代理回来的流量, 放过去
                                let Some(mut packet) = base::utils::net::parse_ip_packet(buf[begin..size].to_vec()) else {
                                    continue;
                                };

                                let blocked = Self::is_block(virtual_ip, &resources, &block_resources, &packet);
                                if blocked {
                                    continue;
                                }

                                if traceability_available && parse_if_match_and_change_target_or_source(virtual_ip, &mut packet).await {
                                    _ = self.packet_tx.send(packet.data);
                                } else {
                                    if let Err(error) = self
                                        .core_rx
                                        .send(InternalCoreEvent::TunnelPacket(Direction::Out, packet.data))
                                    {
                                        log::error!("Failed to send packet to gateway. {}", error);
                                        break Err(Some(ErrorStateCause::StartTunnelError));
                                    }
                                }
                            }
                        }
                    }
                }
                #[cfg(not(feature = "traceability"))]
                {
                    loop {
                        tokio::select! {
                            _ = read_rx.recv() => {
                                break Ok(());
                            }
                            Some(event) = self.auth_resources_rx.next() => {
                                match event {
                                    ResourceEvent::AddBlock(resource) => {
                                        if resource.is_none() {
                                            block_resources.push_front(None)
                                        } else {
                                            block_resources.push_back(resource);
                                        }
                                        log::trace!("Block resources: {:?}", block_resources);
                                    }
                                    ResourceEvent::DeleteBlock(resource, tx) => {
                                        block_resources.retain(|x|{
                                            x != &resource
                                        });

                                        log::trace!("Block resources: {:?}", block_resources);
                                        _ = tx.send(!block_resources.is_empty());
                                    }
                                    ResourceEvent::GetFirstBlock(tx) => {
                                        _ = tx.send(block_resources.front().map(ToOwned::to_owned));
                                        log::debug!("Remaining resources that require certification: {:?}", block_resources);
                                    }
                                    ResourceEvent::AddResources(new_resources) => {
                                        resources.extend(new_resources.iter());
                                        log::debug!("Resources: {:?}", resources);
                                    }
                                    ResourceEvent::DeleteResource(del_resources) => {
                                        del_resources.iter().for_each(|resource| {
                                            resources.remove(resource);
                                        });
                                        log::debug!("Resources: {:?}", resources);
                                    }
                                    ResourceEvent::Clear => {
                                        block_resources.clear();
                                        resources.clear();
                                    }
                                }
                            }
                            Some((ip, tx)) = self.change_network_rx.next() => {
                                virtual_ip = ip;

                                #[cfg(windows)]
                                if let Ok(addresses) = handle.addresses() {
                                    addresses.into_iter().for_each(|ipnet| {
                                        _ = handle.remove_address(ipnet);
                                    });
                                }

                                #[cfg(any(windows, target_os = "linux"))]
                                let ipnet = IpNet::new(ip, 24).unwrap();
                                #[cfg(target_os = "macos")]
                                let ipnet = IpNet::from(ip);
                                if let Err(error) = handle.add_address(ipnet) {
                                    log::error!(
                                        "{}",
                                        error.display_chain_with_msg("Unable to set ip to tunnel")
                                    );
                                    break Err(Some(ErrorStateCause::StartTunnelError));
                                }
                                _ = tx.send(());
                            }
                            Ok(size) = reader.read(buf.as_mut_slice()) => {
                                #[cfg(target_os = "macos")]
                                let begin = 4;
                                #[cfg(not(target_os = "macos"))]
                                let begin = 0;

                                // 不在资源列表或者需要认证后访问的资源, 拒绝转发
                                let Some(packet) = base::utils::net::parse_ip_packet(buf[begin..size].to_vec()) else {
                                    continue;
                                };

                                let blocked = Self::is_block(virtual_ip, &resources, &block_resources, &packet);
                                if blocked {
                                    continue;
                                }

                                if let Err(error) = self
                                    .core_rx
                                    .send(InternalCoreEvent::TunnelPacket(Direction::Out, packet.data))
                                {
                                    log::error!("Failed to send packet to gateway. {}", error);
                                    break Err(Some(ErrorStateCause::StartTunnelError));
                                }
                            }
                        }
                    }
                }
            });

        // 写入数据
        #[cfg_attr(not(target_os = "macos"), allow(unused_mut))]
        let write_handle: JoinHandle<std::result::Result<(), Option<ErrorStateCause>>> =
            tokio::spawn(async move {
                loop {
                    tokio::select! {
                        _ = rx.recv() => {
                            break Ok(());
                        }
                        Some(mut buf) = self.packet_rx.next() => {
                            #[cfg(target_os = "macos")]
                            {
                                buf.insert(0, 0);
                                buf.insert(1, 0);
                                buf.insert(2, 0);
                                buf.insert(3, 2);
                            }
                            if let Err(error) = writer.write_all(&buf).await {
                                log::error!("Failed to write data to interface. {}", error);
                                break Err(Some(ErrorStateCause::StartTunnelError));
                            }
                        }
                    }
                }
            });

        let cause = tokio::select! {
            _ = &mut self.tunnel_close_rx => {
                None
            }
            reason = read_handle => {
                reason.ok().map(|result|result.err().unwrap_or_default()).unwrap_or_default()
            }
            reason = write_handle => {
                reason.ok().map(|result|result.err().unwrap_or_default()).unwrap_or_default()
            }
        };
        if tx.send(()).is_err() {
            log::debug!("Tunnel already closed");
        }

        ((self.event_callback)(TunnelEvent::Down)).await;
        log::debug!("Tunnel closed");
        cause
    }

    fn is_block(
        virtual_ip: IpAddr,
        resources: &HashSet<IpNet>,
        block_resources: &VecDeque<Option<IpNet>>,
        packet: &IpPacket,
    ) -> bool {
        #[cfg(feature = "traceability")]
        if packet.src == virtual_ip
            && packet
                .ports
                .map(|(src_port, _)| src_port == TRACING_LISTEN_PORT.load(Ordering::Relaxed))
                .unwrap_or_default()
        {
            return false;
        }

        #[cfg(not(feature = "traceability"))]
        if packet.src == virtual_ip {
            return false;
        }

        if block_resources.contains(&None) {
            return true;
        }

        let contains = resources
            .iter()
            .any(|resource| resource.contains(&packet.dest));
        let block_contains = block_resources.iter().any(|resource| {
            resource
                .map(|resource| resource.contains(&packet.dest))
                .unwrap_or_default()
        });

        !contains || block_contains
    }

    fn tunnel_metadata(tunnel_parameters: &TunnelParameters) -> TunnelMetadata {
        TunnelMetadata {
            interface: tunnel_parameters.interface_name.to_owned(),
        }
    }

    // fn ensure_ipv6_can_be_used_if_enabled(tunnel_parameters: &TunnelParameters) -> Result<()> {
    //     let options = tunnel_parameters.get_generic_options();
    //     if options.enable_ipv6 {
    //         if is_ipv6_enabled_in_os() {
    //             Ok(())
    //         } else {
    //             Err(Error::EnableIpv6Error)
    //         }
    //     } else {
    //         Ok(())
    //     }
    // }
}

#[cfg(target_os = "windows")]
#[allow(dead_code)]
fn is_ipv6_enabled_in_os() -> bool {
    use winreg::{enums::*, RegKey};

    const IPV6_DISABLED_ON_TUNNELS_MASK: u32 = 0x01;

    // Check registry if IPv6 is disabled on tunnel interfaces, as documented in
    // https://support.microsoft.com/en-us/help/929852/guidance-for-configuring-ipv6-in-windows-for-advanced-users
    let globally_enabled = RegKey::predef(HKEY_LOCAL_MACHINE)
        .open_subkey(r#"SYSTEM\CurrentControlSet\Services\Tcpip6\Parameters"#)
        .and_then(|ipv6_config| ipv6_config.get_value("DisabledComponents"))
        .map(|ipv6_disabled_bits: u32| (ipv6_disabled_bits & IPV6_DISABLED_ON_TUNNELS_MASK) == 0)
        .unwrap_or(true);

    if globally_enabled {
        true
    } else {
        log::debug!("IPv6 disabled in all tunnel interfaces");
        false
    }
}

#[cfg(not(target_os = "windows"))]
#[allow(dead_code)]
fn is_ipv6_enabled_in_os() -> bool {
    #[cfg(target_os = "linux")]
    {
        std::fs::read_to_string("/proc/sys/net/ipv6/conf/all/disable_ipv6")
            .map(|disable_ipv6| disable_ipv6.trim() == "0")
            .unwrap_or(false)
    }
    #[cfg(any(target_os = "macos", target_os = "android"))]
    {
        true
    }
}
