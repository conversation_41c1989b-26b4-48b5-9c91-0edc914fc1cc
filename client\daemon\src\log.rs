use std::{
    env, fmt, fs, io,
    path::{Path, PathBuf},
};

use fern::{
    colors::{Color, ColoredLevelConfig},
    Output,
};

pub fn log_dir() -> crate::Result<PathBuf> {
    #[cfg(unix)]
    let permissions = Some(std::os::unix::prelude::PermissionsExt::from_mode(0o755));
    #[cfg(not(unix))]
    let permissions = None;
    create_and_return(get_log_dir, permissions)
}

fn create_and_return(
    dir_fn: fn() -> crate::Result<PathBuf>,
    permissions: Option<fs::Permissions>,
) -> crate::Result<PathBuf> {
    let dir = dir_fn()?;
    fs::create_dir_all(&dir)
        .map_err(|e| crate::Error::CreateDirFailed(dir.display().to_string(), e))?;
    if let Some(permissions) = permissions {
        fs::set_permissions(&dir, permissions)
            .map_err(|e| crate::Error::SetDirPermissionFailed(dir.display().to_string(), e))?;
    }
    Ok(dir)
}

/// Get the logging directory, but don't try to create it.
fn get_log_dir() -> crate::Result<PathBuf> {
    match env::var_os("UNIT_LOG_DIR") {
        Some(path) => Ok(PathBuf::from(path)),
        None => get_default_log_dir(),
    }
}

fn get_default_log_dir() -> crate::Result<PathBuf> {
    #[cfg(unix)]
    {
        Ok(PathBuf::from("/var/log").join(crate::PRODUCT_NAME))
    }
    #[cfg(windows)]
    {
        get_allusersprofile_dir()
            .map(|dir| dir.join(".config").join(crate::PRODUCT_NAME).join("logs"))
    }
}

#[cfg(windows)]
fn get_allusersprofile_dir() -> crate::Result<PathBuf> {
    match std::env::var_os("ALLUSERSPROFILE") {
        Some(dir) => Ok(PathBuf::from(&dir)),
        None => Err(crate::Error::NoProgramDataDir),
    }
}

const COLORS: ColoredLevelConfig = ColoredLevelConfig {
    error: Color::Red,
    warn: Color::Yellow,
    info: Color::Green,
    debug: Color::Blue,
    trace: Color::Black,
};

#[derive(thiserror::Error, Debug)]
pub enum Error {
    /// Unable to open log file for writing
    #[error("Unable to open log file for writing: {path}")]
    WriteFile {
        path: String,
        #[source]
        source: io::Error,
    },

    #[error("Unable to rotate daemon log file")]
    RotateLog(#[from] RotateLogError),

    #[error("Unable to set logger")]
    SetLoggerError(#[from] log::SetLoggerError),
}

#[cfg(not(windows))]
const LINE_SEPARATOR: &str = "\n";

#[cfg(windows)]
const LINE_SEPARATOR: &str = "\r\n";

const DATE_TIME_FORMAT_STR: &str = "[%Y-%m-%d %H:%M:%S%.3f]";

pub fn init_logger(
    log_level: log::LevelFilter,
    log_file: &PathBuf,
    output_timestamp: bool,
) -> std::result::Result<(), Error> {
    let mut top_dispatcher = fern::Dispatch::new().level(log_level);

    let stdout_formatter = Formatter {
        output_timestamp,
        output_color: true,
    };
    let stdout_dispatcher = fern::Dispatch::new()
        .format(move |out, message, record| stdout_formatter.output_msg(out, message, record))
        .chain(io::stdout());
    top_dispatcher = top_dispatcher.chain(stdout_dispatcher);

    rotate_log(log_file).map_err(Error::RotateLog)?;
    let file_formatter = Formatter {
        output_timestamp: true,
        output_color: false,
    };
    let f = fern::log_file(log_file).map_err(|source| Error::WriteFile {
        path: log_file.display().to_string(),
        source,
    })?;
    let file_dispatcher = fern::Dispatch::new()
        .format(move |out, message, record| file_formatter.output_msg(out, message, record))
        .chain(Output::file(f, LINE_SEPARATOR));
    top_dispatcher = top_dispatcher.chain(file_dispatcher);
    top_dispatcher.apply().map_err(Error::SetLoggerError)?;
    Ok(())
}

#[derive(Default, Debug)]
struct Formatter {
    pub output_timestamp: bool,
    pub output_color: bool,
}

impl Formatter {
    fn get_timetsamp_fmt(&self) -> &str {
        if self.output_timestamp {
            DATE_TIME_FORMAT_STR
        } else {
            ""
        }
    }

    fn get_record_level(&self, level: log::Level) -> Box<dyn fmt::Display> {
        if self.output_color && cfg!(not(windows)) {
            Box::new(COLORS.color(level))
        } else {
            Box::new(level)
        }
    }

    pub fn output_msg(
        &self,
        out: fern::FormatCallback<'_>,
        message: &fmt::Arguments<'_>,
        record: &log::Record<'_>,
    ) {
        let message = escape_newlines(format!("{}", message));

        #[cfg(debug_assertions)]
        out.finish(format_args!(
            "{}[{}]#{:?} [{}] {}",
            chrono::Local::now().format(self.get_timetsamp_fmt()),
            record.target(),
            record.line(),
            self.get_record_level(record.level()),
            message,
        ));

        #[cfg(not(debug_assertions))]
        out.finish(format_args!(
            "{}[{}][{}] {}",
            chrono::Local::now().format(self.get_timetsamp_fmt()),
            record.target(),
            self.get_record_level(record.level()),
            message,
        ));
    }
}

#[cfg(not(windows))]
fn escape_newlines(text: String) -> String {
    text
}

#[cfg(windows)]
fn escape_newlines(text: String) -> String {
    text.replace("\n", LINE_SEPARATOR)
}

/// Unable to create new log file
#[derive(thiserror::Error, Debug)]
#[error("Unable to create new log file")]
pub struct RotateLogError(#[source] io::Error);

/// Create a new log file while backing up a previous version of it.
///
/// A new log file is created with the given file name, but if a file with that name already exists
/// it is backed up with the extension changed to `.old.log`.
pub fn rotate_log(file: &Path) -> std::result::Result<(), RotateLogError> {
    let backup = file.with_extension("old.log");
    if let Err(error) = fs::rename(file, &backup) {
        if error.kind() != io::ErrorKind::NotFound {
            log::warn!(
                "Failed to rotate log file to {}: {}",
                backup.display(),
                error
            );
        }
    }

    fs::File::create(file).map(|_| ()).map_err(RotateLogError)
}
