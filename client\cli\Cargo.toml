[package]
edition = "2021"
name = "oneid-cli"
version = "0.0.0"

[dependencies]
anyhow = { version = "1.0.94", features = ["backtrace"] }
clap = { version = "4.5.22", features = ["derive", "string"] }
faster-hex = "0.10.0"
futures-channel = { version = "0.3.31", default-features = false }
futures-util = { version = "0.3.31", default-features = false }
gm-sm2 = { workspace = true }
hex = { workspace = true }
libcore = { package = "oneidcore", path = "../core", features = [
    "sdp",
], default-features = false, version = "0.0.0" }
log-panics = { version = "2.1.0", features = ["with-backtrace"] }
proxy-request = { version = "0.0.0", path = "../../proxy-request" }
serde = { version = "1.0.215", features = ["derive"] }
serde_json = "1.0.133"
time = { version = "0.3", features = ["macros"] }
tokio = { version = "1.42.0", features = ["full"] }
toml = "0.8.19"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-rolling = "0.3.0"
tracing-subscriber = { version = "0.3.19", features = [
    "time",
    "local-time",
    "env-filter",
] }
types = { path = "../types" }
uniqueid = { workspace = true }
url = { version = "2.5.4", features = ["serde"] }
version = { path = "../../version" }

[build-dependencies]
built = { version = "0.7", features = ["git2"] }
time = { version = "0.3", features = ["formatting", "local-offset"] }

[features]
default = ["tlcp"]
tlcp = ["libcore/tlcp"]
