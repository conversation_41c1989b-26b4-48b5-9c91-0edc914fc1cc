use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

use amq_protocol::types::FieldTable;
use deadpool_lapin::Pool;
use futures::{future::BoxFuture, FutureExt, StreamExt};
use lapin::{
    options::{
        BasicAckOptions, BasicConsumeOptions, BasicQosOptions, QueueBindOptions,
        QueueDeclareOptions,
    },
    Channel,
};
use rand::Rng;
use tracing::{debug, error, info, warn, Instrument};

use crate::{message::MQ<PERSON>ommand, Error, EventSender, InternalEvent};

use super::{SDP_EXCHANGE, USER_STATE_QUEUE};

fn do_listen(
    pool: Pool,
    channel: Channel,
    event_sender: EventSender,
) -> BoxFuture<'static, Result<(), lapin::Error>> {
    async move {
        // 声明队列
        let mut queue_declare_options = QueueDeclareOptions::default();
        // 连接断开时, 自动删除队列
        queue_declare_options.auto_delete = true;
        let num: u32 = rand::rng().random_range(0..100);
        let user_state_queue = format!("{}_{}", USER_STATE_QUEUE, num);
        channel
            .queue_declare(
                &user_state_queue,
                queue_declare_options,
                FieldTable::default(),
            )
            .await?;
        let bind_options = QueueBindOptions::default();
        channel
            .queue_bind(
                &user_state_queue,
                SDP_EXCHANGE,
                USER_STATE_QUEUE,
                bind_options,
                FieldTable::default(),
            )
            .await?;
        let qos_options = BasicQosOptions::default();
        channel.basic_qos(1, qos_options).await?;
        let mut user_state_consumer = channel
            .basic_consume(
                &user_state_queue,
                "user_state_consumer",
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await?;
        tokio::spawn(
            async move {
                while let Some(Ok(delivery)) = user_state_consumer.next().await {
                    let buf = delivery.data.clone();
                    debug!("received mq event{:?}", &buf);
                    match buf[0] {
                        // 用户被禁用 | 用户被删除 | 用户未生效 | 用户已失效 | 用户绑定码变更
                        r#type @ (0 | 1 | 3 | 4 | 6) => {
                            let mut splits = buf[1..].splitn(2, |b| *b == b'|');
                            if let (Some(tenant), Some(username)) = (splits.next(), splits.next()) {
                                let tenant = match std::str::from_utf8(tenant) {
                                    Ok(tenant) => tenant,
                                    Err(_) => continue,
                                };

                                let username = match std::str::from_utf8(username) {
                                    Ok(username) => username,
                                    Err(_) => continue,
                                };

                                debug!(
                                    tenant,
                                    username, "received user state change event. type: {}", r#type
                                );
                                let command = MQCommand::UserState {
                                    reason: r#type,
                                    tenant: tenant.to_owned(),
                                    username: username.to_owned(),
                                };

                                event_sender.send(InternalEvent::MQMessage(command)).await;
                            }
                        }
                        // 用户密码变更
                        5 => {
                            let device_id = hex::to_string!(&buf[1..17]);
                            let mut splits = buf[17..].splitn(2, |b| *b == b'|');
                            if let (Some(tenant), Some(username)) = (splits.next(), splits.next()) {
                                let tenant = match std::str::from_utf8(tenant) {
                                    Ok(tenant) => tenant,
                                    Err(_) => continue,
                                };

                                let username = match std::str::from_utf8(username) {
                                    Ok(username) => username,
                                    Err(_) => continue,
                                };
                                let command = MQCommand::UserPasswordChange {
                                    device_id,
                                    tenant: tenant.to_owned(),
                                    username: username.to_owned(),
                                };

                                event_sender.send(InternalEvent::MQMessage(command)).await;
                            }
                        }
                        // 设备被禁用
                        2 => {
                            let device_id = match std::str::from_utf8(&buf[1..]) {
                                Ok(device_id) => device_id,
                                Err(_) => continue,
                            };
                            debug!(device.id = device_id, "received device disabled event");

                            let command = MQCommand::DisableDevice(device_id.to_owned());
                            event_sender.send(InternalEvent::MQMessage(command)).await;
                        }
                        // 设备被禁用
                        7 => {
                            let tenant = match std::str::from_utf8(&buf[1..]) {
                                Ok(tenant) => tenant,
                                Err(_) => continue,
                            };
                            debug!(tenant, "received tenant disabled event");
                            let command = MQCommand::DisableTenant(tenant.to_owned());
                            event_sender.send(InternalEvent::MQMessage(command)).await;
                        }
                        // 结束会话
                        8 => {
                            let device_id = hex::to_string!(&buf[1..17]);
                            debug!(device.id = device_id, "received end session event");
                            let command = MQCommand::EndSession(device_id.to_owned());
                            event_sender.send(InternalEvent::MQMessage(command)).await;
                        }
                        // 异地登录
                        100 => {
                            let ip = match buf[1] {
                                4 => IpAddr::V4(Ipv4Addr::from(
                                    <&[u8] as TryInto<[u8; 4]>>::try_into(&buf[2..6]).unwrap(),
                                )),
                                6 => IpAddr::V6(Ipv6Addr::from(
                                    <&[u8] as TryInto<[u8; 16]>>::try_into(&buf[2..18]).unwrap(),
                                )),
                                _ => continue,
                            };
                            let device_id = match std::str::from_utf8(&buf[6..]) {
                                Ok(device_id) => device_id,
                                Err(_) => continue,
                            };

                            let command = MQCommand::LoginElseWhere {
                                ip,
                                device_id: device_id.to_owned(),
                            };
                            event_sender.send(InternalEvent::MQMessage(command)).await;
                        }
                        _ => {}
                    }
                    delivery
                        .ack(BasicAckOptions::default())
                        .await
                        .expect("basic_ack");
                }

                error!("reconnecting...");

                tokio::spawn(async move {
                    let mut timeout = 1;

                    loop {
                        let channel = get_channel!(pool, timeout);

                        if let Err(err) =
                            do_listen(pool.clone(), channel, event_sender.clone()).await
                        {
                            error!("listen error: {err}");

                            tokio::time::sleep(std::time::Duration::from_millis(
                                2 << std::cmp::max(12, timeout),
                            ))
                            .await;
                            timeout += 1;
                            continue;
                        }

                        info!("reconnected");
                        break;
                    }
                });
            }
            .instrument(tracing::error_span!(parent: None, "UserStateConsumer")),
        );

        Ok(())
    }
    .boxed()
}

/// 接收用户状态消息
pub(super) async fn listen(pool: Pool, event_sender: EventSender) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;

    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("user_state channel status is abnormal: {error}");
    });

    do_listen(pool, channel, event_sender)
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;

    Ok(())
}
