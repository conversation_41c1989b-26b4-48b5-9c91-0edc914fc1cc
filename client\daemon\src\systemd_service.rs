use std::{env, ffi::OsString};

use service_manager::{ServiceInstallCtx, ServiceLabel, ServiceManager, ServiceStartCtx};

static SERVICE_LABEL: &'static str = "com.jingantech.oneid";

#[derive(thiserror::Error, Debug)]
pub enum InstallError {
    #[error("Unable to connect to service manager")]
    ConnectServiceManager(#[source] std::io::Error),

    #[error("Unable to create a service")]
    CreateService(#[source] std::io::Error),
}

pub fn install_service() -> Result<(), InstallError> {
    // Create a label for our service
    let label: ServiceLabel = SERVICE_LABEL.parse().unwrap();

    // Get generic service by detecting what is available on the platform
    let manager = <dyn ServiceManager>::native().map_err(InstallError::ConnectServiceManager)?;

    if let Ok(false) = systemctl::exists(SERVICE_LABEL) {
        // Install our service using the underlying service management platform
        manager
            .install(ServiceInstallCtx {
                label: label.clone(),
                program: env::current_exe().unwrap(),
                args: vec![OsString::from("-v"), OsString::from("0")],
            })
            .map_err(InstallError::CreateService)?;
    }

    _ = manager.start(ServiceStartCtx {
        label: label.clone(),
    });

    Ok(())
}
