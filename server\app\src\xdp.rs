use std::{
    collections::HashMap as StdHashMap,
    ffi::CString,
    net::{IpAddr, Ipv4Addr, SocketAddr},
    os::fd::AsRawFd,
    sync::Arc,
};

use crate::{
    config::{BaseInterface, Interface, Segments},
    packet::server::ServerPacketSender,
    Error,
};

use aya::{
    maps::{Array, HashMap, MapData, XskMap},
    programs, EbpfLoader,
};

use serde::Deserialize;
use share::{Addr, BPF_ANY, BPF_NOEXIST};
use tlv_types::Port;
use tokio::{io::unix::AsyncFd, sync::oneshot};
use tracing::{debug, error, info, trace, Instrument};
use xdp::{
    bpf,
    if_xdp::{self, XdpFlags},
    io as xdp_io, ring, socket, syscall, umem,
};

use sdpcore::io::{
    rx::{Queue as RxQueue, Rx},
    tx::{Queue as TxQueue, Tx},
};

#[derive(Debug)]
pub enum XdpCommand {
    /// 删除收到SPA包的标记
    DelSPARecord(IpAddr, u16),
    // 打开端口
    OpenPort(IpAddr),
    // 关闭端口
    ClosePort(IpAddr),
    // 建立连接
    Connected(IpAddr, u16),
    // 关闭连接
    CloseConnect(IpAddr, u16),
    // 建立NAT映射
    Nat((IpAddr, u16), Option<oneshot::Sender<bool>>),
    // 删除NAT映射
    DelNat((IpAddr, u16), Option<oneshot::Sender<bool>>),
}

#[derive(Clone)]
pub struct ClientPacketSenders {
    senders: StdHashMap<String, ClientPacketSender>,
}

impl ClientPacketSenders {
    pub async fn send(&self, iface: &str, buf: Vec<u8>) {
        if let Some(sender) = self.senders.get(iface) {
            sender.send(buf).await;
        }
    }
}

#[derive(Clone)]
pub struct XdpCommandSenders {
    primary_xdp_sender: XdpCommandSender,
    senders: StdHashMap<String, XdpCommandSender>,
    segments: Segments,
}

impl XdpCommandSenders {
    pub fn primary_xdp_sender(&self) -> XdpCommandSender {
        self.primary_xdp_sender.clone()
    }

    pub async fn send(&self, command: XdpCommand) {
        match command {
            XdpCommand::Nat((ip, port), callback) => {
                let command = XdpCommand::Nat((ip, port), callback);

                let iface = self.segments.find_interface(&ip);
                if let Some(sender) = self.senders.get(iface) {
                    sender.send(command).await;
                } else {
                    self.primary_xdp_sender.send(command).await;
                }
            }
            XdpCommand::DelNat((ip, port), callback) => {
                let command = XdpCommand::DelNat((ip, port), callback);

                let iface = self.segments.find_interface(&ip);
                if let Some(sender) = self.senders.get(iface) {
                    sender.send(command).await;
                } else {
                    self.primary_xdp_sender.send(command).await;
                }
            }
            _ => (),
        }
    }
}

/// 启动eBPF程序
pub fn start_ebpf(
    interface: Interface,
    segments: Segments,
    server_packet_sender: ServerPacketSender,
    spa_port: u16,
) -> (ClientPacketSenders, XdpCommandSenders) {
    // 客户端流量发送器
    let mut client_packet_senders = StdHashMap::new();
    // xdp命令发送器
    let mut xdp_command_senders = StdHashMap::new();

    let iface_name = interface.primary.base.iface.clone();

    let primary_xdp = Xdp::new(interface.primary.base, Some(spa_port));

    let (xdp_command_sender, receiver) = flume::unbounded();
    let client_packet_sender = primary_xdp
        .start(receiver, server_packet_sender.clone())
        .unwrap();

    let primary_xdp_sender = XdpCommandSender {
        command_tx: xdp_command_sender,
    };
    xdp_command_senders.insert(iface_name.clone(), primary_xdp_sender.clone());
    client_packet_senders.insert(iface_name, client_packet_sender);

    if let Some(secondary) = interface.secondary {
        secondary.into_iter().for_each(|config| {
            let iface_name = config.base.iface.clone();

            let secondary_xdp = Xdp::new(config.base, None);

            let (xdp_command_sender, receiver) = flume::unbounded();
            let sender = secondary_xdp
                .start(receiver, server_packet_sender.clone())
                .unwrap();

            xdp_command_senders.insert(
                iface_name.clone(),
                XdpCommandSender {
                    command_tx: xdp_command_sender,
                },
            );
            client_packet_senders.insert(iface_name, sender);
        });
    }

    (
        ClientPacketSenders {
            senders: client_packet_senders,
        },
        XdpCommandSenders {
            primary_xdp_sender,
            senders: xdp_command_senders,
            segments,
        },
    )
}

struct Xdp {
    config: BaseInterface,
    spa_port: Option<u16>,
    secondary: bool,
}

#[derive(Clone, Copy, Debug, Deserialize)]
pub enum XdpMode {
    /// Automatically selects an XDP mode based on the capabilities of the NIC
    Auto,
    /// Uses the software SKB (socket buffer) mode - usually requires no NIC support
    Skb,
    /// Uses the driver mode, which integrates with XDP directly in the kernel driver
    Drv,
    /// Uses the hardware mode, which integrates with XDP directly in the actual NIC hardware
    Hw,
}

impl core::str::FromStr for XdpMode {
    type Err = crate::config::Error;

    fn from_str(v: &str) -> Result<Self, Self::Err> {
        Ok(match v {
            "auto" => Self::Auto,
            "skb" => Self::Skb,
            "drv" | "driver" => Self::Drv,
            "hw" | "hardware" => Self::Hw,
            _ => return Err(crate::config::Error::XdpModeError(v.to_owned())),
        })
    }
}

impl From<XdpMode> for programs::xdp::XdpFlags {
    fn from(mode: XdpMode) -> Self {
        match mode {
            XdpMode::Auto => Self::default(),
            XdpMode::Skb => Self::SKB_MODE,
            XdpMode::Drv => Self::DRV_MODE,
            XdpMode::Hw => Self::HW_MODE,
        }
    }
}

type SetupResult = Result<
    (
        umem::Umem,
        Vec<xdp_io::rx::Channel<Arc<AsyncFd<socket::Fd>>>>,
        Vec<(u32, socket::Fd)>,
        Vec<xdp_io::tx::Channel<xdp_io::tx::BusyPoll>>,
    ),
    Error,
>;

impl Xdp {
    pub fn new(config: BaseInterface, spa_port: Option<u16>) -> Self {
        Self {
            config,
            spa_port,
            secondary: spa_port.is_none(),
        }
    }

    fn setup(&self) -> SetupResult {
        let frame_size = self.config.frame_size;
        let rx_queue_len = self.config.rx_queue_len;
        let tx_queue_len = self.config.tx_queue_len;
        let fill_ring_len = rx_queue_len * 2;
        let completion_ring_len = tx_queue_len;

        let queues = syscall::max_queues(&self.config.iface);
        debug!("open queues: {}", queues);

        let umem_size = (rx_queue_len + tx_queue_len) * queues;

        // create a UMEM
        let umem = umem::Builder {
            frame_count: umem_size,
            frame_size,
            ..Default::default()
        }
        .build()
        .map_err(|error| Error::IoError(error))?;

        // setup the address we're going to bind to
        let mut address = if_xdp::Address {
            flags: XdpFlags::USE_NEED_WAKEUP,
            ..Default::default()
        };
        address
            .set_if_name(&CString::new(self.config.iface.clone()).unwrap())
            .map_err(|error| Error::IoError(error))?;

        let mut shared_umem_fd = None;
        let mut tx_channels = vec![];
        let mut rx_channels = vec![];
        let mut rx_fds = vec![];
        let mut desc = umem.frames();

        // iterate over all of the queues and create sockets for each one
        for queue_id in 0..queues {
            let socket = socket::Fd::open()?;

            // if we've already attached a socket to the UMEM, then reuse the first FD
            if let Some(fd) = shared_umem_fd {
                address.set_shared_umem(&fd);
            } else {
                socket.attach_umem(&umem)?;
                shared_umem_fd = Some(socket.as_raw_fd());
            }

            // set the queue id to the current value
            address.queue_id = queue_id;

            // file descriptors can only be added once so wrap it in an Arc
            let async_fd = Arc::new(AsyncFd::new(socket.clone()).unwrap());

            // get the offsets for each of the rings
            let offsets = syscall::offsets(&socket).map_err(|error| Error::IoError(error))?;

            {
                // create a pair of rings for receiving packets
                let mut fill = ring::Fill::new(socket.clone(), &offsets, fill_ring_len)
                    .map_err(|error| Error::IoError(error))?;
                let rx = ring::Rx::new(socket.clone(), &offsets, rx_queue_len)
                    .map_err(|error| Error::IoError(error))?;

                // remember the FD so we can add it to the XSK map later
                rx_fds.push((queue_id, socket.clone()));

                // put descriptors in the Fill queue
                fill.init((&mut desc).take(rx_queue_len as _));

                rx_channels.push(xdp_io::rx::Channel {
                    rx,
                    fill,
                    driver: async_fd.clone(),
                });
            };

            {
                // create a pair of rings for transmitting packets
                let mut completion =
                    ring::Completion::new(socket.clone(), &offsets, completion_ring_len)
                        .map_err(|error| Error::IoError(error))?;
                let tx = ring::Tx::new(socket.clone(), &offsets, tx_queue_len)
                    .map_err(|error| Error::IoError(error))?;

                // put descriptors in the completion queue
                completion.init((&mut desc).take(tx_queue_len as _));

                tx_channels.push(xdp_io::tx::Channel {
                    tx,
                    completion,
                    driver: xdp_io::tx::BusyPoll,
                });
            };

            // finally bind the socket to the configured address
            syscall::bind(&socket, &mut address).map_err(|error| Error::IoError(error))?;
        }

        // make sure we've allocated all descriptors from the UMEM to a queue
        assert_eq!(desc.count(), 0, "descriptors have been leaked");

        Ok((umem, rx_channels, rx_fds, tx_channels))
    }

    /// 加载eBPF程序, 填充配置信息
    fn bpf_task(
        &self,
        xdp_command_receiver: flume::Receiver<XdpCommand>,
        rx_fds: Vec<(u32, socket::Fd)>,
    ) -> Result<(), Error> {
        let xdp_config = self.config.clone();
        let local_addr = xdp_config.addr;
        let spa_port = self.spa_port.clone();

        let mut ebpf_loader = EbpfLoader::new();
        let physics = <Ipv4Addr as Into<u32>>::into(local_addr.physics);
        let vip = local_addr
            .vip
            .map(<Ipv4Addr as Into<u32>>::into)
            .unwrap_or_default();
        let spa_port = spa_port.unwrap_or(65321);
        ebpf_loader
            .set_global("LOCAL_IP", &physics, true)
            .set_global("VIP", &vip, true)
            .set_global("SPA_PORT", &spa_port, false);

        let mut bpf = if self.secondary {
            ebpf_loader
                .load(bpf::SECONDARY_BPF_PROGRAM)
                .map_err(|error| Error::BpfError(error))?
        } else {
            ebpf_loader
                .load(bpf::PRIMARY_BPF_PROGRAM)
                .map_err(|error| Error::BpfError(error))?
        };

        let interface = self.config.iface.clone();
        let xdp_mode = self.config.xdp_mode.into();

        let program: &mut programs::Xdp = bpf
            .program_mut(if self.secondary {
                bpf::SECONDARY_PROGRAM_NAME
            } else {
                bpf::PRIMARY_PROGRAM_NAME
            })
            .expect("missing default program")
            .try_into()
            .map_err(|error| Error::EbpfProgramError(error))?;
        program
            .load()
            .map_err(|error| Error::EbpfProgramError(error))?;

        // attach the BPF program to the interface
        let link_id = program
            .attach(&interface, xdp_mode)
            .map_err(|error| Error::EbpfProgramError(error))?;

        let xdp_config = self.config.clone();
        let is_secondary = self.secondary;
        let bpf_task = async move {
            // 端口白名单
            if let Some(ref ports) = xdp_config.pub_ports {
                let mut pub_ports: Array<_, _> = bpf
                    .map_mut(bpf::PUB_PORTS_MAP_NAME)
                    .expect("missing pub ports map")
                    .try_into()
                    .map_err(|error| Error::EbpfMapError(error))?;

                for (index, port) in ports.iter().enumerate() {
                    let port = match port {
                        Port::Single(port) => *port as u32,
                        Port::Range(range) => {
                            (*(range.start()) as u32) << 16 | *(range.end()) as u32
                        }
                    };
                    pub_ports
                        .set(index as u32, port, BPF_ANY)
                        .map_err(|error| Error::EbpfMapError(error))?;
                }
            }

            // IP白名单
            let mut whitelist_map: HashMap<_, _, _> = bpf
                .map_mut(bpf::WHITELIST_MAP_NAME)
                .expect("missing whitelist map")
                .try_into()
                .map_err(|error| Error::EbpfMapError(error))?;

            if let Some(whitelist) = xdp_config.whitelist {
                for addr in whitelist {
                    match addr {
                        SocketAddr::V4(addr) => {
                            let ip = u32::from_be_bytes(addr.ip().octets());
                            whitelist_map
                                .insert(Addr::new(ip, addr.port()), 1u8, BPF_NOEXIST)
                                .map_err(|error| Error::EbpfMapError(error))?;
                        }
                        SocketAddr::V6(_) => {}
                    }
                }
            }

            // register all of the RX sockets on each of the queues
            let mut xskmap: XskMap<&mut MapData> = bpf
                .map_mut(bpf::XSK_MAP_NAME)
                .expect("missing socket map")
                .try_into()
                .map_err(|error| Error::EbpfMapError(error))?;

            for (queue_id, socket) in &rx_fds {
                xskmap
                    .set(*queue_id, socket.as_raw_fd(), BPF_ANY)
                    .map_err(|error| Error::EbpfMapError(error))?;
            }

            // print xdp stats every second, if configured
            loop {
                match xdp_command_receiver.recv_async().await {
                    Ok(command) => {
                        trace!("received command: {:?}", command);
                        match command {
                            XdpCommand::DelSPARecord(ip, port) => {
                                if is_secondary {
                                    continue;
                                }

                                let mut received_spa_clients: HashMap<_, _, u8> = bpf
                                    .map_mut(bpf::RECEIVED_MAP_NAME)
                                    .expect("missing received map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        let socket_addr = (ip as u64) | (port as u64) << 32;
                                        _ = received_spa_clients.remove(&socket_addr);
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                            XdpCommand::OpenPort(ip) => {
                                if is_secondary {
                                    continue;
                                }

                                let mut preauthed: HashMap<_, _, _> = bpf
                                    .map_mut(bpf::PREAUTHED_MAP_NAME)
                                    .expect("missing preauthed map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        if let Err(err) = preauthed.insert(ip, 1u8, BPF_ANY) {
                                            error!(
                                                "failed insert allow connect {}: {}",
                                                ip_addr, err
                                            );
                                        }
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                            XdpCommand::ClosePort(ip) => {
                                if is_secondary {
                                    continue;
                                }

                                let mut preauthed: HashMap<_, _, u8> = bpf
                                    .map_mut(bpf::PREAUTHED_MAP_NAME)
                                    .expect("missing preauthed map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        if let Err(err) = preauthed.remove(&ip) {
                                            error!(
                                                "failed remove allow connect {}: {}",
                                                ip_addr, err
                                            );
                                        }
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                            XdpCommand::Connected(ip, port) => {
                                if is_secondary {
                                    continue;
                                }

                                let mut connected: HashMap<_, _, _> = bpf
                                    .map_mut(bpf::CONNECTED_MAP_NAME)
                                    .expect("missing connected map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        if let Err(err) =
                                            connected.insert(Addr::new(ip, port), 1u8, BPF_ANY)
                                        {
                                            error!("failed insert connected {}: {}", ip_addr, err);
                                        }
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                            XdpCommand::CloseConnect(ip, port) => {
                                if is_secondary {
                                    continue;
                                }

                                let mut connected: HashMap<_, _, u8> = bpf
                                    .map_mut(bpf::CONNECTED_MAP_NAME)
                                    .expect("missing connected map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        if let Err(err) = connected.remove(&Addr::new(ip, port)) {
                                            error!("failed remove connected {}: {}", ip_addr, err);
                                        }
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                            XdpCommand::Nat((ip, port), tx) => {
                                let mut nat_table: HashMap<_, _, _> = bpf
                                    .map_mut(bpf::NAT_MAP_NAME)
                                    .expect("missing nat map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        trace!(%ip, port, "add nat");
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        let status = match nat_table.insert(
                                            Addr::new(ip, port),
                                            1u8,
                                            BPF_ANY,
                                        ) {
                                            Ok(_) => true,
                                            Err(err) => {
                                                error!("failed insert nat {}: {}", ip_addr, err);
                                                false
                                            }
                                        };
                                        if let Some(tx) = tx {
                                            _ = tx.send(status);
                                        }
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                            XdpCommand::DelNat((ip, port), tx) => {
                                let mut nat_table: HashMap<_, _, u8> = bpf
                                    .map_mut(bpf::NAT_MAP_NAME)
                                    .expect("missing nat map")
                                    .try_into()
                                    .map_err(|error| Error::EbpfMapError(error))?;

                                match ip {
                                    IpAddr::V4(ip_addr) => {
                                        trace!(%ip_addr, port, "delete nat");
                                        let ip = u32::from_be_bytes(ip_addr.octets());
                                        let status = match nat_table.remove(&Addr::new(ip, port)) {
                                            Ok(_) => true,
                                            Err(err) => {
                                                error!("failed remove nat {}: {}", ip_addr, err);
                                                false
                                            }
                                        };
                                        if let Some(tx) = tx {
                                            _ = tx.send(status);
                                        }
                                    }
                                    IpAddr::V6(_) => {}
                                }
                            }
                        }
                    }
                    Err(_) => break,
                }
            }

            // we want this future to go until the end of the program so we can keep the BPF
            // program active on the the NIC.
            core::future::pending::<()>().await;

            // retain the bpf program for the duration of execution
            let _ = bpf;
            let _ = link_id;
            let _ = rx_fds;

            Result::<(), crate::Error>::Ok(())
        };

        tokio::spawn(
            async move {
                if let Err(error) = bpf_task.await {
                    panic!("BPF ERROR: {error}");
                }
            }
            .instrument(tracing::error_span!(parent: None, "Kernel")),
        );

        Ok(())
    }

    pub fn start(
        &self,
        xdp_command_receiver: flume::Receiver<XdpCommand>,
        server_packet_sender: ServerPacketSender,
    ) -> Result<ClientPacketSender, Error> {
        info!(iface = &self.config.iface, "enable traffic takeover");

        let (umem, rx, rx_fds, tx) = self.setup()?;

        self.bpf_task(xdp_command_receiver, rx_fds)?;

        let mut io_rx = xdp_io::rx::Rx::new(rx, umem.clone());
        let mut io_tx = xdp_io::tx::Tx::new(tx, umem);

        tokio::spawn(
            async move {
                loop {
                    let rx_ready = io_rx.ready();

                    match rx_ready.await {
                        Ok(()) => {
                            // we received some packets. give them to the packet handler.
                            io_rx.queue(|queue| {
                                queue.for_each(|packet| {
                                    let mut data = Vec::with_capacity(packet.len() - 14);
                                    data.extend_from_slice(&packet[14..]);

                                    if let Some(packet) = base::utils::net::parse_ip_packet(data) {
                                        server_packet_sender.send(packet);
                                    }
                                });
                            });
                        }
                        Err(()) => {
                            error!("afxdp thread panicked.");
                            break;
                        }
                    }
                }
            }
            .instrument(tracing::error_span!("PacketReceiver")),
        );

        let (tx, rx) = flume::unbounded();

        tokio::spawn(
            async move {
                loop {
                    match rx.recv_async().await {
                        Ok(buf) => io_tx.queue(|queue| {
                            if queue.push(buf).is_err() {
                                error!("tx error");
                            }
                        }),
                        Err(_) => {
                            error!("afxdp thread panicked.");
                            break;
                        }
                    }
                }
            }
            .instrument(tracing::error_span!("PacketSender")),
        );

        Ok(ClientPacketSender { command_tx: tx })
    }
}

#[derive(Clone)]
pub struct XdpCommandSender {
    command_tx: flume::Sender<XdpCommand>,
}

impl XdpCommandSender {
    pub async fn send(&self, command: XdpCommand) {
        if self.command_tx.send_async(command).await.is_err() {
            error!("xdp thread panicked.");
        }
    }
}

#[derive(Clone)]
struct ClientPacketSender {
    command_tx: flume::Sender<Vec<u8>>,
}

impl ClientPacketSender {
    pub async fn send(&self, buf: Vec<u8>) {
        if self.command_tx.send_async(buf).await.is_err() {
            error!("afxdp thread panicked.");
        }
    }
}
