use hickory_proto::rr::{
    rdata::{A, AAAA},
    LowerName, Name, RData, Record, RecordType,
};
use hickory_server::{
    authority::{AuthorityObject, Catalog, ZoneType},
    resolver::config::{NameServerConfigGroup, ResolverOpts},
    server::{Request, Request<PERSON><PERSON><PERSON>, ResponseHandler, ResponseInfo},
    store::{
        forwarder::{ForwardAuthority, ForwardConfig},
        in_memory::InMemoryAuthority,
    },
    ServerFuture,
};
use log::info;
use std::{collections::HashSet, io, net::IpAddr, str::FromStr, sync::Arc};
use tlv::Sequence;
use tokio::{net::UdpSocket, sync::RwLock};

type Result<T> = std::result::Result<T, Error>;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Unable to listen on port 53")]
    IoError(#[from] io::Error),

    #[error("DNS error")]
    ProtoError(#[from] hickory_proto::error::ProtoError),
}

pub struct DnsServer {
    server: ServerFuture<DnsHandler>,
}

impl DnsServer {
    pub async fn spawn(dns_handler: DnsHandler) -> Result<Self> {
        let mut server = ServerFuture::new(dns_handler);

        let udp_socket = UdpSocket::bind("127.0.0.1:53").await?;
        server.register_socket(udp_socket);

        info!("DNS server listening on 127.0.0.1:53");

        Ok(Self { server })
    }

    /// Shutdown the server an wait for all tasks to complete.
    pub async fn shutdown(mut self) -> Result<()> {
        self.server.shutdown_gracefully().await?;
        info!("DNS server shutdown successfully");
        Ok(())
    }
}

#[derive(Clone)]
pub struct DnsHandler {
    default_dns_servers: Option<Vec<IpAddr>>,
    domains: Arc<RwLock<HashSet<String>>>,
    catalog: Arc<RwLock<Catalog>>,
}

impl Default for DnsHandler {
    fn default() -> Self {
        Self::new()
    }
}

impl DnsHandler {
    pub fn new() -> Self {
        let default_dns_servers = netdev::get_default_interface()
            .map(|interface| interface.dns_servers)
            .ok();

        Self {
            default_dns_servers,
            domains: Arc::new(RwLock::const_new(HashSet::new())),
            catalog: Arc::new(RwLock::const_new(Catalog::new())),
        }
    }

    pub async fn set_upstream(&self, upstreams: Option<Vec<IpAddr>>) -> Result<()> {
        let mut dns_servers = vec![];
        if let Some(upstreams) = upstreams {
            dns_servers.extend(upstreams.clone());
        }
        if let Some(ref servers) = self.default_dns_servers {
            dns_servers.extend(servers.clone());
        }

        let mut catalog = self.catalog.write().await;
        if dns_servers.is_empty() {
            catalog.remove(&LowerName::from_str(".").unwrap());
        } else {
            let forward_config = ForwardConfig {
                name_servers: NameServerConfigGroup::from_ips_clear(&dns_servers, 53, true),
                options: Some(ResolverOpts::default()),
            };

            let authority = ForwardAuthority::try_from_config(
                Name::from_str(".").unwrap(),
                ZoneType::Forward,
                &forward_config,
            )
            .unwrap();

            catalog.upsert(
                LowerName::from_str(".").unwrap(),
                Box::new(Arc::new(authority)) as Box<dyn AuthorityObject>,
            );
        }
        Ok(())
    }

    pub async fn upsert(&self, domain: &str, ip: IpAddr) -> Result<()> {
        log::info!("Add DNS record {} -> {}", domain, ip);
        let mut domains = self.domains.write().await;
        domains.push(domain.to_string());
        drop(domains);

        let mut catalog = self.catalog.write().await;
        let authority =
            InMemoryAuthority::empty(Name::from_str(domain).unwrap(), ZoneType::Primary, false);
        let mut record = Record::new();
        record
            .set_name(Name::from_str(domain).unwrap())
            .set_ttl(3600)
            .set_rr_type(RecordType::A)
            .set_data(Some(match ip {
                IpAddr::V4(ipv4_addr) => RData::A(A(ipv4_addr)),
                IpAddr::V6(ipv6_addr) => RData::AAAA(AAAA(ipv6_addr)),
            }));
        authority.upsert(record, 0).await;
        catalog.upsert(
            LowerName::from_str(domain).unwrap(),
            Box::new(Arc::new(authority)) as Box<dyn AuthorityObject>,
        );
        Ok(())
    }

    pub async fn remove(&self, domain: &str) -> Result<()> {
        log::info!("Remove DNS record {}", domain);

        let mut domains = self.domains.write().await;
        domains.remove(domain);
        drop(domains);

        let mut catalog = self.catalog.write().await;
        catalog.remove(&LowerName::from_str(domain).unwrap());
        Ok(())
    }

    pub async fn reset(&self) -> Result<()> {
        _ = self.set_upstream(None).await;
        let domains_guard = self.domains.read().await;
        let domains = domains_guard.clone();
        drop(domains_guard);
        for domain in domains.iter() {
            _ = self.remove(domain.as_str()).await;
        }
        Ok(())
    }
}

#[async_trait::async_trait]
impl RequestHandler for DnsHandler {
    async fn handle_request<R: ResponseHandler>(
        &self,
        request: &Request,
        response_handle: R,
    ) -> ResponseInfo {
        self.catalog
            .read()
            .await
            .handle_request(request, response_handle)
            .await
    }
}
