#!/bin/bash

# stop service
echo "Stop service"
systemctl stop jingantech-oneid.service
systemctl disable jingantech-oneid.service

# remove service
echo "Remove service"
rm -f /etc/systemd/system/jingantech-oneid.service

# remove logs
rm -rf /var/log/oneid

# remove polkit policy
rm -f /usr/share/polkit-1/actions/com.jingantech.oneid.policy

# remove trust ca
rm -f /usr/local/share/ca-certificates/oneid.crt
update-ca-certificates

remove_desktop_entry_file() {
  if [ -f "$1/jingantech-oneid.desktop" ]; then
    xdg-desktop-menu uninstall "$1/jingantech-oneid.desktop"
    rm -f "$1/jingantech-oneid.desktop"
  fi
}

uninstall_desktop_entry_file() {
  if [ "$1" = "remove" ]; then
    if [ -d "/root/桌面" ]; then
      remove_desktop_entry_file "/root/桌面"
    elif [ -d "/root/Desktop" ]; then
      remove_desktop_entry_file "/root/Desktop"
    fi

    for FILENAME in /home/<USER>
      if [ -f "${FILENAME}/.config/user-dirs.dirs" ]; then
        HOME=${FILENAME}
        . "${FILENAME}/.config/user-dirs.dirs"
        remove_desktop_entry_file ${XDG_DESKTOP_DIR}
      else
        if [ -d "${FILENAME}/桌面" ]; then
          remove_desktop_entry_file "${FILENAME}/桌面"
        elif [ -d "${FILENAME}/Desktop" ]; then
          remove_desktop_entry_file "${FILENAME}/Desktop"
        fi
      fi
    done
  fi
}

if [ -f "/etc/os-release" ]; then
  OS_PRETTY_NAME="$(cat /etc/os-release | grep PRETTY_NAME)"
  if [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i Kylin)" ] || [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i UnionTech)" ] || [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i UOS)" ]; then
    uninstall_desktop_entry_file $1 || true
  fi
else
  uninstall_desktop_entry_file $1 || true
fi

# Update cache of .desktop file MIME types. Non-fatal since it's just a cache.
update-desktop-database > /dev/null 2>&1 || true