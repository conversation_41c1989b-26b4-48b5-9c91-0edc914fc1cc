use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

use communicate_interface::ControlServiceClient;
use tauri::State;
use tokio::sync::Mutex;
use tonic::Code;
use types::backend::{CancelAuth, CompleteAuth};

use crate::plugins::ErrorResponse;

use crate::plugins::Result;

#[tauri::command]
pub(super) async fn pop_mfa_event(
    rpc: State<'_, Mutex<ControlServiceClient>>,
) -> Result<Option<IpAddr>> {
    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);
    match rpc.next_auth_event(tonic::Request::new(())).await {
        Ok(response) => {
            let event = response.into_inner();

            match event.ip_addr {
                None => Ok(None),
                Some(bytes) => {
                    let ip = if bytes.len() == 16 {
                        IpAddr::V6(Ipv6Addr::from(
                            <Vec<u8> as TryInto<[u8; 16]>>::try_into(bytes).unwrap(),
                        ))
                    } else {
                        IpAddr::V4(Ipv4Addr::from(
                            <Vec<u8> as TryInto<[u8; 4]>>::try_into(bytes).unwrap(),
                        ))
                    };
                    Ok(Some(ip))
                }
            }
        }
        Err(status) => {
            if status.code() == Code::NotFound {
                return Err(ErrorResponse::new(204, None));
            }
            log::error!("Failed to pop auth event. {status}");
            Err(ErrorResponse::new(620, None))
        }
    }
}

#[tauri::command]
pub(super) async fn auth_complete(
    auth_result_id: String,
    ip_addr: Option<IpAddr>,
    rpc: State<'_, Mutex<ControlServiceClient>>,
) -> Result<bool> {
    log::trace!(target: "app", "Received complete auth event. {:?}", ip_addr);
    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    let request = CompleteAuth {
        auth_result_id,
        ip_addr,
    };

    match rpc
        .complete_auth_event(tonic::Request::new(request.into()))
        .await
    {
        Err(status) => {
            log::error!(target: "app", "Failed to call complete auth. {status}");
            Err(ErrorResponse::new(621, None))
        }
        Ok(response) => Ok(response.into_inner()),
    }
}

#[tauri::command]
pub(super) async fn cancel_auth(
    ip_addr: Option<IpAddr>,
    rpc: State<'_, Mutex<ControlServiceClient>>,
) -> Result<bool> {
    log::trace!(target: "app", "Received cancel auth event. {:?}", ip_addr);
    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    let request = CancelAuth { ip_addr };

    match rpc
        .cancel_auth_event(tonic::Request::new(request.into()))
        .await
    {
        Err(status) => {
            log::error!(target: "app", "Failed to call cancel auth. {status}");
            Err(ErrorResponse::new(621, None))
        }
        Ok(response) => Ok(response.into_inner()),
    }
}
