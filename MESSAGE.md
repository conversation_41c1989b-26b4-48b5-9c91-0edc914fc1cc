# 通信消息定义

**数据使用 TLV 三元组表示, 长度字段不再用固定的双字节表示, 类型字段为第一个字节**

**其中13和15两种类型的消息只有HEADER是TLV格式, 负载部分不遵循这个规则.**

数据长度域也有两种形式，短形式和长形式。

短形式的数据长度域只有一个字节，第 8 位为 0，其它低 7 位给出数据长度。

长形式的数据长度域有 2 到 8 个字节。第一个字节的第 8 位为 1，其它低 7 位给出后面该域使用的字节的数量，从该域第二个字节开始给出数据的长度，基于 256，高位优先。长度域最大为8。

## TCP 负载中数据类型定义(Tag)

| 类型 | 说明            | 示例 |
| :--- | :-------------- | :--- |
| 0x00 | 字节序列        |      |
| 0x01 | 布尔值          |      |
| 0x02 | u8              |      |
| 0x03 | u16             |      |
| 0x04 | u32             |      |
| 0x05 | u64             |      |
| 0x06 | UTF8字符串      |      |
| 0x07 | 多个字段的队列  |      |
| 0x08 | IPv4 地址       |      |
| 0x09 | IPv6 地址       |      |
| 0x0a | IPv4 范围       |      |
| 0x0b | IPv6 范围       |      |
| 0x0c | IPv4 CIDR       |      |
| 0x0d | IPv6 CIDR       |      |
| 0x0e | IPv4 SocketAddr |      |
| 0x0f | IPv6 SocketAddr |      |

```rust
pub enum Tag {
    /// 字节序列
    RawBytes = 0x00,
    /// 布尔值
    Boolean = 0x01,
    /// u8
    Uint8 = 0x02,
    /// u16
    Uint16 = 0x03,
    /// u32
    Uint32 = 0x04,
    /// u64
    Uint64 = 0x05,
    /// UTF8 字符串
    UTF8String = 0x06,
    /// 一个或多个字段队列
    Sequence = 0x07,
    /// Ipv4地址
    Ipv4 = 0x08,
    /// Ipv6地址
    Ipv6 = 0x09,
    /// Ipv4范围
    Ipv4Range = 0x0a,
    /// Ipv6范围
    Ipv6Range = 0x0b,
    /// Ipv4 Cidr
    Ipv4Net = 0x0c,
    /// Ipv6 Cidr
    Ipv6Net = 0x0d,
    /// SocketV4Addr
    SocketV4Addr = 0x0e,
    /// SocketV6Addr
    SocketV6Addr = 0x0f,
    /// 未定义
    Unknown = u8::MAX,
}
```

## TCP 消息类型(Type)

<table>
    <tr>
        <th>类型</th>
        <th>说明</th>
        <th>代码</th>
        <th>方向</th>
    </tr>
    <tr>
        <td>1</td>
        <td>认证消息</td>
        <td><pre>|Tag::RawBytes|16|设备ID|
|Tag::RawBytes|原始JSON数据长度|原始JSON数据|</pre></td>
        <td>客户端->控制器</td>
    </tr>
    <tr>
        <td>2</td>
        <td>控制器下发客户端认证数据</td>
        <td><pre>|Tag::RawBytes|16|设备ID|
|Tag::RawBytes|16|Token|
以下二选一
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|6|Ipv6Addr|</pre></td>
        <td>控制器->网关</td>
    </tr>
    <tr>
        <td>4</td>
        <td>资源列表</td>
        <td><pre>|Tag::Sequence|资源列表总长度|</pre>
包含以下六种类型数据, 可以有多个<pre>
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|
|Tag::Ipv4Range|8|Ipv4开始Ipv4结束|
|Tag::Ipv6Range|32|Ipv6开始Ipv6结束|
|Tag::Ipv4Net|5|Netmask Ipv4Addr|
|Tag::Ipv6Net|17|Netmask Ipv6Addr|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>5</td>
        <td>资源增删</td>
        <td><pre>|Tag::Boolean|1|0或1|
|Tag::Sequence|资源列表总长度|</pre>
后面的数据包含以下六种类型数据, 可以有多个
<pre>
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|
|Tag::Ipv4Range|8|Ipv4开始Ipv4结束|
|Tag::Ipv6Range|32|Ipv6开始Ipv6结束|
|Tag::Ipv4Net|5|Netmask Ipv4Addr|
|Tag::Ipv6Net|17|Netmask Ipv6Addr|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>6</td>
        <td>客户端修改密码</td>
        <td><pre>|Tag::RawBytes|原始JSON数据长度|原始JSON数据|</pre></td>
        <td>客户端->控制器</td>
    </tr>
    <tr>
        <td>8</td>
        <td>客户端认证结果</td>
        <td><pre>|Tag::RawBytes|16|Token|
|Tag::Uint32|4|密码过期提醒时间|
|Tag::Uint32|4|密码过期时间|
|Tag::Sequence|DNS列表长度|DNS列表|
    DNS列表包含以下两种类型数据, 可以有多个
    |Tag::Ipv4|4|Ipv4Addr|
    |Tag::Ipv6|16|Ipv6Addr|
|Tag::RawBytes|1|服务端运行模式(混合模式/标准模式)|
混合模式
|Tag::UTF8String|16|重连票据|
|Tag::Uint64|8|重连票据过期时间|
标准模式
|Tag::RawBytes|网关信息JSON数据长度|网关信息JSON数据|
最后附加一个客户端地址(未来可能是分配给客户端的虚拟IP地址)
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|
</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>9</td>
        <td>客户端认证失败结果</td>
        <td><pre>|Tag::UTF8String|16|结果|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>10</td>
        <td>网关认证消息</td>
        <td><pre>|Tag::RawBytes|16|Token|
|Tag::UTF8String|租户编码长度|租户编码|
|Tag::RawBytes|环境数据长度|环境数据|</pre></td>
        <td>客户端->网关</td>
    </tr>
    <tr>
        <td>11</td>
        <td>断开连接</td>
        <td><pre>|Tag::RawBytes|16|设备ID|</pre></td>
        <td>控制器->网关<br/>网关->控制器</td>
    </tr>
    <tr>
        <td>12</td>
        <td>网关认证失败</td>
        <td></td>
        <td>网关->客户端</td>
    </tr>
    <tr>
        <td>13</td>
        <td>服务端返回的数据包</td>
        <td>IP包</td>
        <td>网关->客户端</td>
    </tr>
    <tr>
        <td>14</td>
        <td>网关认证成功</td>
        <td><pre>|Tag::UTF8String|16|重连票据|
|Tag::Uint64|8|重连票据过期时间|</pre></td>
        <td>网关->客户端</td>
    </tr>
    <tr>
        <td>15</td>
        <td>客户端的数据包</td>
        <td>IP包</td>
        <td>客户端->网关</td>
    </tr>
    <tr>
        <td>16</td>
        <td>网关已准备好</td>
        <td><pre>|Tag::RawBytes|16|设备ID|</pre></td>
        <td>网关->控制器</td>
    </tr>
    <tr>
        <td>17</td>
        <td>通知客户端断开原因</td>
        <td><pre>|Tag::Uint8|1|断开原因|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>18</td>
        <td>访问需要认证</td>
        <td>任一以下数据类型<pre>
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|
|Tag::SocketV4Addr|6|Ipv4Addr Port|
|Tag::SocketV6Addr|18|Ipv6Addr Port|</pre></td>
        <td>网关->客户端</td>
    </tr>
    <tr>
        <td>19</td>
        <td>认证完成</td>
        <td><pre>|Tag::UTF8String|48|认证结果ID|</pre><br/>
包含任一以下数据类型<pre>
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|
|Tag::SocketV4Addr|6|Ipv4Addr Port|
|Tag::SocketV6Addr|18|Ipv6Addr Port|</pre></td>
        <td>客户端->网关</td>
    </tr>
    <tr>
        <td>20</td>
        <td>取消认证</td>
        <td>任一以下数据类型<pre>
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|
|Tag::SocketV4Addr|6|Ipv4Addr Port|
|Tag::SocketV6Addr|18|Ipv6Addr Port|</pre></td>
        <td>客户端->网关</td>
    </tr>
    <tr>
        <td>30</td>
        <td>登录需要修改密码</td>
        <td><pre>|Tag::UTF8String|临时修改密码票据长度|临时修改密码票据|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>31</td>
        <td>通过临时密码票据修改密码</td>
        <td><pre>|Tag::RawBytes|16|设备ID|
|Tag::RawBytes|修改密码数据长度|修改密码数据|</pre></td>
        <td>客户端->控制器</td>
    </tr>
    <tr>
        <td>32</td>
        <td>通过临时密码票据修改密码结果</td>
        <td><pre>|Tag::RawBytes|修改密码结果长度|修改密码结果|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>37</td>
        <td>下发客户端执行策略</td>
        <td><pre>|Tag::RawBytes|策略数据长度|策略数据|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>39</td>
        <td>刷新重连票据</td>
        <td><pre>|Tag::UTF8String|票据长度|票据|</pre></td>
        <td>客户端->控制器</td>
    </tr>
    <tr>
        <td>40</td>
        <td>刷新重连票据结果</td>
        <td><pre>|Tag::RawBytes|票据结果长度|票据结果|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>41</td>
        <td>资源白名单</td>
        <td><pre>|Tag::Sequence|白名单总长度|</pre>
包含以下两种种类型数据, 可以有多个<pre>
|Tag::Ipv4|4|Ipv4Addr|
|Tag::Ipv6|16|Ipv6Addr|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>42</td>
        <td>WEB应用列表</td>
        <td><pre>|Tag::Boolean|1|0或1|
|Tag::RawBytes|应用列表长度|应用列表JSON数据|
应用数据格式如下
[
    {
        "url":"https://sso.jingantech.com",
        "ips":[
            {
                "ip":"*************"
            }
        ],
        "cidrs":[
            {
                "prefix":31,
                "ip":"***********"
            }
        ],
        "ranges":[
            {
                "start": "***********",
                "end": "***********0",
            }
        ]
    }
]
</pre></td>
        <td>网关->客户端</td>
    </tr>
    <tr>
        <td>43</td>
        <td>通知消息</td>
        <td><pre>|Tag::Uint8|1|通知类型|</pre></td>
        <td>控制器->客户端<br/>网关->客户端</td>
    </tr>
    <tr>
        <td>44</td>
        <td>WEB应用虚拟IP映射</td>
        <td><pre>|Tag::Boolean|1|0或1|
|Tag::Sequence|Ip映射总长度|
后续跟着多个IP映射
|Tag::Ipv4|4|真实IP地址|
|Tag::Ipv4|4|虚拟IP地址|
或
|Tag::Ipv6|16|真实IP地址|
|Tag::Ipv6|16|虚拟IP地址|
</pre>
</td>
        <td>网关->客户端</td>
    </tr>
    <tr>
        <td>100</td>
        <td>生成IAM单点登录票据</td>
        <td></td>
        <td>客户端->控制器</td>
    </tr>
    <tr>
        <td>101</td>
        <td>生成IAM单点登录票据结果</td>
        <td><pre>|Tag::RawBytes|票据结果长度|票据结果|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>102</td>
        <td>代理接口请求</td>
        <td><pre>|Tag::RawBytes|16|设备ID|
|Tag::Uint64|8|请求序号|
|Tag::RawBytes|HTTP请求数据长度|HTTP请求数据|</pre>
其中HTTP请求数据为JSON格式, 结构如下
<pre>
{
    "body":{
        "payload":{},
        "type":"Json"
    },
    "headers":{},
    "method":"POST",
    "responseType": 1,
    "url":"http://*************:8080/common-sso/v1/sdk/client/tenants"
}
responseType为数字. 1表示JSON, 2表示文本, 3表示字节流
body中type有四种类型. Json, Form, Text, Bytes
</pre>
</td>
        <td>客户端->控制器</td>
    </tr>
    <tr>
        <td>103</td>
        <td>代理请求响应</td>
        <td><pre>|Tag::Uint64|8|请求序号|
|Tag::RawBytes|请求响应数据长度|请求响应数据|
|Tag::Boolean|1|是否分块|</pre>
其中响应数据结构如下
<pre>
{
    "url": "http://*************:8080/common-sso/v1/sdk/client/tenants",
    "status": 200,
    "headers": {},
    "raw_headers": {},
    "data": {}
}
</pre>
</td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>104</td>
        <td>代理请求响应-chunk包</td>
        <td>数据长度为0时, 表示chunk数据已发送完<pre>|Tag::Uint64|8|请求序号|
|Tag::RawBytes|chunk长度|chunk数据|</pre></td>
        <td>控制器->客户端</td>
    </tr>
    <tr>
        <td>105</td>
        <td>代理请求失败</td>
        <td><pre>|Tag::RawBytes|Uint64|请求序号|
|Tag::UTF8String|失败消息长度|失败消息|</pre></td>
        <td>控制器->客户端</td>
    </tr>
</table>

## 代理客户端接口访问消息定义

由于客户端与控制器交互第一步都需要经过SPA认证, 同时为了使用更方便(牺牲一定安全性), 绑定码现采用通用绑定码模式, 内置于客户端中.

代理访问接口时, 同样需要先建立mTLS隧道. 隧道打开时间为15秒, 若接口请求在这段时间未接收到返回数据, 需要重新执行SPA认证和建立mTLS隧道.

**同时为了提高登录效率, 可复用代理连接.**

> 客户端点击登录, 先发送认证类型的SPA包, 然后在代理连接隧道内直接发送认证数据.

消息格式参照102和103, 其中请求序号在同一个隧道内的请求期间, 应保持其唯一性, 防止消息响应错乱, 客户端根据响应的序号区分是哪个接口请求的响应.

登录前和登录后都采用这种方式进行请求.
