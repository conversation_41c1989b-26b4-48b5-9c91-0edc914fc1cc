use anyhow::{bail, Context};
use clap::Parser;
use serde_json::Value as JsonValue;
use std::{fs, path::PathBuf};

#[derive(Debug, Parser)]
pub struct Options {
    /// JSON字符串
    #[clap(long)]
    pub content: Option<String>,
    /// json文件路径
    #[clap(long)]
    pub path: Option<PathBuf>,
}

pub fn run(opts: Options) -> anyhow::Result<()> {
    let value = if let Some(content) = opts.content {
        let merge: JsonValue =
            serde_json::from_str(&content).with_context(|| "failed to parse content")?;
        merge
    } else if let Some(path) = opts.path {
        let content = fs::read_to_string(path).with_context(|| "failed to read content")?;
        let merge: JsonValue =
            serde_json::from_str(&content).with_context(|| "failed to parse content")?;
        merge
    } else {
        bail!("Choose one of `source` and `source_path`");
    };

    let output = serde_json::to_string(&value)?;
    println!("{}", output);
    Ok(())
}
