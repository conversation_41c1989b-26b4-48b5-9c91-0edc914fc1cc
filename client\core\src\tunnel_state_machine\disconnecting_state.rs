use futures::{channel::oneshot, future::FusedFuture, StreamExt};
use types::tunnel::{ActionAfterDisconnect, ErrorStateCause, TunnelStateTransition};

use crate::tunnel_state_machine::{
    connecting_state::ConnectingState, disconnected_state::DisconnectedState,
    error_state::<PERSON><PERSON><PERSON><PERSON>tate, EventResult, TunnelCommand,
};

use super::{
    connecting_state::TunnelCloseEvent, EventConsequence, SharedTunnelStateValues,
    TunnelCommandReceiver, TunnelState, TunnelStateWrapper,
};

pub struct DisconnectingState {
    tunnel_close_event: TunnelCloseEvent,
    after_disconnect: AfterDisconnect,
}

impl DisconnectingState {
    async fn handle_commands(
        mut self,
        command: Option<TunnelCommand>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        let after_disconnect = self.after_disconnect;

        self.after_disconnect = match after_disconnect {
            AfterDisconnect::Nothing => match command {
                Some(TunnelCommand::Dns(_servers)) => {
                    // let _ = shared_values.set_dns_servers(servers);
                    AfterDisconnect::Nothing
                }
                Some(TunnelCommand::ResetDns(_)) => AfterDisconnect::Nothing,
                Some(TunnelCommand::Routes(_)) => AfterDisconnect::Nothing,
                Some(TunnelCommand::AskIsOffline(tx)) => {
                    let connectivity = shared_values.offline_monitor.connectivity().await;
                    _ = tx.send(connectivity.is_offline());
                    AfterDisconnect::Nothing
                }
                Some(TunnelCommand::Connect) => AfterDisconnect::Reconnect(0),
                Some(TunnelCommand::Disconnect(_)) | None => AfterDisconnect::Nothing,
                Some(TunnelCommand::Resource(_)) => AfterDisconnect::Nothing,
                _ => AfterDisconnect::Nothing,
            },
            AfterDisconnect::Block(reason) => match command {
                Some(TunnelCommand::Dns(_servers)) => {
                    // let _ = shared_values.set_dns_servers(servers);
                    AfterDisconnect::Block(reason)
                }
                Some(TunnelCommand::Connect) => AfterDisconnect::Reconnect(0),
                Some(TunnelCommand::Disconnect(_)) => AfterDisconnect::Nothing,
                Some(TunnelCommand::Resource(_)) => AfterDisconnect::Nothing,
                None => AfterDisconnect::Block(reason),
                _ => AfterDisconnect::Nothing,
            },
            AfterDisconnect::Reconnect(retry_attempt) => match command {
                Some(TunnelCommand::Dns(_servers)) => {
                    // let _ = shared_values.set_dns_servers(servers);
                    AfterDisconnect::Reconnect(retry_attempt)
                }
                Some(TunnelCommand::Connect) => AfterDisconnect::Reconnect(retry_attempt),
                Some(TunnelCommand::Disconnect(_)) | None => AfterDisconnect::Nothing,
                Some(TunnelCommand::Resource(_)) => AfterDisconnect::Nothing,
                _ => AfterDisconnect::Nothing,
            },
        };

        EventConsequence::SameState(self.into())
    }

    async fn after_disconnect(
        self,
        block_reason: Option<ErrorStateCause>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        if let Some(reason) = block_reason {
            return ErrorState::enter(shared_values, reason).await;
        }

        match self.after_disconnect {
            AfterDisconnect::Nothing => DisconnectedState::enter(shared_values, ()).await,
            AfterDisconnect::Block(cause) => ErrorState::enter(shared_values, cause).await,
            AfterDisconnect::Reconnect(_retry_attempt) => {
                ConnectingState::enter(shared_values, ()).await
            }
        }
    }
}

#[async_trait::async_trait]
impl TunnelState for DisconnectingState {
    type Bootstrap = (oneshot::Sender<()>, TunnelCloseEvent, AfterDisconnect);

    async fn enter(
        _: &mut SharedTunnelStateValues,
        (tunnel_close_tx, tunnel_close_event, after_disconnect): Self::Bootstrap,
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        let _ = tunnel_close_tx.send(());
        let action_after_disconnect = after_disconnect.action();

        (
            TunnelStateWrapper::from(DisconnectingState {
                tunnel_close_event,
                after_disconnect,
            }),
            TunnelStateTransition::Disconnecting(action_after_disconnect),
        )
    }

    async fn handle_event(
        mut self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        let result = if self.tunnel_close_event.is_terminated() {
            if commands.is_done() {
                EventResult::Close(Ok(None))
            } else if let Ok(command) = commands.get_mut().try_next() {
                EventResult::Command(command)
            } else {
                EventResult::Close(Ok(None))
            }
        } else {
            tokio::select! {
                command = commands.next() => EventResult::Command(command),
                result = &mut self.tunnel_close_event => EventResult::Close(result),
            }
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Close(result) => {
                let block_reason = result.unwrap_or(None);
                NewState(self.after_disconnect(block_reason, shared_values).await)
            }
            _ => unreachable!("unexpected event result"),
        }
    }
}

/// Which state should be transitioned to after disconnection is complete.
pub enum AfterDisconnect {
    Nothing,
    Block(ErrorStateCause),
    Reconnect(u32),
}

impl AfterDisconnect {
    /// Build event representation of the action that will be taken after the disconnection.
    pub fn action(&self) -> ActionAfterDisconnect {
        match self {
            AfterDisconnect::Nothing => ActionAfterDisconnect::Nothing,
            AfterDisconnect::Block(..) => ActionAfterDisconnect::Block,
            AfterDisconnect::Reconnect(..) => ActionAfterDisconnect::Reconnect,
        }
    }
}
