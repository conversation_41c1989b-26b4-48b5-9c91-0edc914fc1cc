use std::env::current_exe;
use windows::{
    core::BSTR,
    Win32::{
        Foundation::VARIANT_BOOL,
        NetworkManagement::WindowsFirewall::{INetFwPolicy2, INetFwRule, NetFwPolicy2, NetFwRule},
        System::Com::{CoCreateInstance, CoInitialize, CLSCTX_INPROC_SERVER},
    },
};

/// 允许核心程序通过Windows Defender进行通信
pub fn allow_self_in_windows_defender() -> windows::core::Result<()> {
    let self_path = current_exe().unwrap();
    let filename = self_path.file_name().unwrap();
    let self_name = filename.to_string_lossy().to_string();

    let self_name = BSTR::from(self_name);

    let self_path = self_path.to_string_lossy().to_string();
    let self_path = BSTR::from(self_path);

    unsafe { CoInitialize(None) }.ok()?;

    let net_fw_policy: INetFwPolicy2 =
        unsafe { CoCreateInstance(&NetFwPolicy2, None, CLSCTX_INPROC_SERVER) }?;

    let rules = unsafe { net_fw_policy.Rules() }?;
    match unsafe { rules.Item(&self_name) } {
        Ok(rule) => unsafe {
            rule.SetApplicationName(&self_path)?;
            rule.SetEnabled(VARIANT_BOOL::from(true))?;

            log::debug!("A rule already exists to allow the current app to communicate through Windows Defender Firewall");
        },
        Err(error) => {
            // 应用不存在
            if error.code().0 == -2147024894 {
                let fw_rule: INetFwRule =
                    unsafe { CoCreateInstance(&NetFwRule, None, CLSCTX_INPROC_SERVER) }?;

                unsafe {
                    fw_rule.SetName(&self_name)?;
                    fw_rule.SetEnabled(VARIANT_BOOL::from(true))?;
                    fw_rule.SetApplicationName(&self_path)?;
                    rules.Add(&fw_rule)?;
                }

                log::debug!(
                    "Allow the current app to communicate through Windows Defender Firewall"
                );
            } else {
                log::error!("Failed to add a rule to allow the current app to communicate through Windows Defender Firewall. {}", error);
                return Err(error);
            }
        }
    };

    Ok(())
}

#[test]
fn test_fw() {
    allow_self_in_windows_defender().unwrap();
}
