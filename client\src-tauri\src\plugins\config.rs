use crate::plugins::{ErrorResponse, Result};
use config::AppConfig;
use serde_json::Value;
use tauri::State;
use tokio::sync::Mutex;

#[tauri::command(async)]
pub async fn patch(config: State<'_, Mutex<AppConfig>>, value: Value) -> Result<AppConfig> {
    log::trace!(target: "app", "patch update: {:#?}", {
        let mut value = value.clone();
        desensitization(&mut value);
        value
    });

    let mut config = config.lock().await;
    let tmp_config = config.clone();
    let mut tmp_config = serde_json::to_value(tmp_config).unwrap();

    json_patch::merge(&mut tmp_config, &value);

    match serde_json::from_value::<AppConfig>(tmp_config) {
        Ok(app_config) => {
            if let Err(err) = app_config.save_file() {
                log::error!(target: "app", "{err}");
                return Err(ErrorResponse::new(622, None));
            } else {
                *config = app_config.clone();
                Ok(app_config)
            }
        }
        Err(err) => {
            log::error!(target: "app", "{err}");
            return Err(ErrorResponse::new(622, None));
        }
    }
}

fn desensitization(value: &mut Value) {
    match value {
        Value::Object(inner) => {
            for (key, value) in inner {
                if key == "password" && value.is_string() {
                    *value = Value::String("******".to_owned());
                } else if key == "secret" && value.is_string() {
                    let secret = value.as_str().unwrap();
                    *value = Value::String(mask_string(&secret));
                } else if value.is_object() {
                    desensitization(value);
                }
            }
        }
        _ => (),
    }
}

fn mask_string(s: &str) -> String {
    let len = s.len();
    if len <= 4 {
        return s.to_string(); // 如果字符串长度小于等于4，则不做处理
    }

    if len == 32 {
        let mut s = s.to_string();
        s.replace_range(8..24, "******");
        return s;
    }

    let prefix = &s[..2]; // 前两位
    let suffix = &s[len - 2..]; // 后两位
    format!("{}******{}", prefix, suffix) // 拼接
}
