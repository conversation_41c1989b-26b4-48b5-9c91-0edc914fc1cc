use std::time::Duration;

use crate::{
    backend_state_machine::BackendCommand, mpsc::Sender, CoreEventSender, InternalCoreEvent,
};
use base::packet::{Message, MessageType};
use tlv::Serialize;

pub(crate) const TASK_NAME: &str = "refresh_reconnect_token";

/// 刷新重连票据
pub(crate) async fn delay_refresh_reconnect_ticket(
    core_tx: CoreEventSender,
    ticket: String,
    expire: u64,
) {
    tokio::time::sleep(Duration::from_secs(expire / 2 * 60)).await;

    log::debug!("Refresh reconnect token");

    let message = Message::new(MessageType::RefreshToken, &ticket.serialize());
    let command = BackendCommand::Packet(message);
    if core_tx
        .send(InternalCoreEvent::BackendCommand(command))
        .is_err()
    {
        log::error!("Core already down or thread panicked");
    }
}
