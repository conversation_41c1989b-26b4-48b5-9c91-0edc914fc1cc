use actix_web::{
    web::{<PERSON><PERSON>, Query},
    HttpResponse,
};
use log::{error, info};
use serde::{Deserialize, Serialize};
use std::ffi::CString;
use whoami::distro;
use winapi::{
    shared::minwindef::HINSTANCE__,
    um::libloaderapi::{FreeLibrary, LoadLibraryA},
};

extern crate libc;

use skf_lib::{
    skf_api::skfapi::ECCSIGNATUREBLOB,
    skf_service::{
        skf_change_pin, skf_close_application, skf_close_container, skf_connect_dev, skf_digest,
        skf_digest_init, skf_disconnect_dev, skf_ecc_sign_data, skf_enum_container, skf_enum_dev,
        skf_export_certificate, skf_export_public_key, skf_open_application, skf_open_container,
        skf_verify_pin,
    },
};

#[derive(Serialize)]
pub struct RestResult<T> {
    code: i32,
    message: String,
    data: T,
}

#[derive(Deserialize)]
struct Params {
    ukey_type: String,
}

#[actix_web::get("/api/list_cert")]
pub async fn list(Query(params): Query<Params>) -> HttpResponse {
    let certs;
    if params.ukey_type == "bjca" {
        certs = fetch_cert_list(1);
    } else {
        certs = fetch_cert_list(2);
    }
    HttpResponse::Ok().json(RestResult {
        code: 0,
        message: "success".to_string(),
        data: certs,
    })
}

#[derive(Serialize, Deserialize)]
pub struct VerifyBody {
    id: String,
    password: String,
    random_str: String,
    der_format: Option<String>,
    ukey_type: Option<String>,
}

#[actix_web::post("/api/verify")]
pub async fn verify(Json(data): Json<VerifyBody>) -> HttpResponse {
    let mut der_format = true;
    if let Some(format) = data.der_format.as_ref() {
        if format == "false" {
            der_format = false;
        }
    }
    let mut ukey_type = 2;
    if let Some(ukey_type_str) = &data.ukey_type {
        if ukey_type_str == "bjca" {
            ukey_type = 1;
        }
    }
    let result = validate_and_fetch_certificate(
        ukey_type,
        &data.id,
        &data.password,
        &data.random_str,
        der_format,
    );
    HttpResponse::Ok().json(RestResult {
        code: 0,
        message: "success".to_string(),
        data: result,
    })
}

#[derive(Deserialize)]
pub struct ChangePasswordParam {
    /// 设备序列号
    sn: String,
    /// 旧密码
    old_password: String,
    /// 新密码
    new_password: String,
}

#[allow(dead_code)]
// #[actix_web::post("")]
pub async fn change_password(Json(param): Json<ChangePasswordParam>) -> HttpResponse {
    let result = do_change_password(
        &param.sn,
        &param.old_password,
        &param.new_password,
        "BJCA-Application",
    );
    HttpResponse::Ok().json(RestResult {
        code: 0,
        message: "success".to_string(),
        data: result,
    })
}

#[derive(Serialize)]
pub struct ContainerCert {
    id: String,
    name: String,
}

fn fetch_cert_list(ukey_type: u32) -> Vec<ContainerCert> {
    let mut result: Vec<ContainerCert> = Vec::new();
    if ukey_type == 1 {
        let lib = load_library();
        let dev_list = skf_enum_dev(lib);
        if dev_list.len() > 0 {
            result = do_fetch_cert_list(lib, dev_list, "BJCA-Application");
        }
        free_library(lib);
    } else {
        if let Some(dll) = load_watch_library() {
            let dev_list = skf_enum_dev(dll);
            if dev_list.len() > 0 {
                result = do_fetch_cert_list(dll, dev_list, "JAYX-Application");
            }
            free_library(dll);
        }
    }
    result
}

fn do_fetch_cert_list(
    lib: *mut HINSTANCE__,
    dev_list: Vec<String>,
    app_name: &str,
) -> Vec<ContainerCert> {
    let mut result: Vec<ContainerCert> = Vec::new();
    if dev_list.len() > 0 {
        if let Some(dev_handle) = skf_connect_dev(lib, dev_list[0].clone()) {
            // 打开应用
            if let Some(app_handle) = skf_open_application(lib, dev_handle, app_name) {
                let containers = skf_enum_container(lib, app_handle);
                // 遍历容器
                for container in containers {
                    info!("container: {}", container);
                    if let Some(container_handle) =
                        skf_open_container(lib, app_handle, container.as_str())
                    {
                        let certificate = skf_export_certificate(lib, container_handle, 1);
                        let subject = fetch_subject_from_cert(&certificate);
                        info!("subject is: {}", subject);
                        let cert = ContainerCert {
                            id: container,
                            name: subject,
                        };
                        result.push(cert);
                        // 关闭容器
                        skf_close_container(lib, container_handle);
                    }
                }
                // 关闭应用
                skf_close_application(lib, app_handle);
            }

            // 断开连接
            skf_disconnect_dev(lib, dev_handle);
        }
    }
    free_library(lib);
    result
}

#[derive(Serialize)]
pub struct VerifyResult {
    result: bool,
    retry_count: usize,
    certificate: String,
    signature: String,
}

fn validate_and_fetch_certificate(
    ukey_type: u32,
    container_name: &str,
    password: &str,
    random_str: &str,
    format_result: bool,
) -> VerifyResult {
    let mut verify_result_obj = VerifyResult {
        result: false,
        retry_count: 0,
        certificate: String::new(),
        signature: String::new(),
    };
    if ukey_type == 1 {
        let lib = load_library();
        let dev_list = skf_enum_dev(lib);
        if dev_list.len() > 0 {
            verify_result_obj = do_validate_and_fetch_certificate(
                lib,
                dev_list,
                container_name,
                "BJCA-Application",
                password,
                random_str,
                format_result,
            );
        }
        free_library(lib);
    } else {
        if let Some(dll) = load_watch_library() {
            let dev_list = skf_enum_dev(dll);
            if dev_list.len() > 0 {
                verify_result_obj = do_validate_and_fetch_certificate(
                    dll,
                    dev_list,
                    container_name,
                    "JAYX-Application",
                    password,
                    random_str,
                    format_result,
                );
            }
            free_library(dll);
        }
    }
    verify_result_obj
}

fn do_validate_and_fetch_certificate(
    lib: *mut HINSTANCE__,
    dev_list: Vec<String>,
    container_name: &str,
    app_name: &str,
    password: &str,
    random_str: &str,
    format_result: bool,
) -> VerifyResult {
    let mut verify_result_obj = VerifyResult {
        result: false,
        retry_count: 0,
        certificate: String::new(),
        signature: String::new(),
    };

    if let Some(dev_handle) = skf_connect_dev(lib, dev_list[0].clone()) {
        // 打开应用
        if let Some(app_handle) = skf_open_application(lib, dev_handle, app_name) {
            if let Some(container_handle) = skf_open_container(lib, app_handle, &container_name) {
                let certificate = skf_export_certificate(lib, container_handle, 1);

                use base64::{engine::general_purpose, Engine as _};
                let mut buf = Vec::new();
                // make sure we'll have a slice big enough for base64 + padding
                buf.resize(certificate.len() * 4 / 3 + 4, 0);
                // encode the slice
                let bytes_written = general_purpose::STANDARD
                    .encode_slice(certificate.clone(), &mut buf)
                    .unwrap();
                if let Some(cert_data) = skf_export_public_key(lib, container_handle) {
                    info!("cert_data: {:?}", cert_data);
                    let (verify_result, retry_count) = skf_verify_pin(lib, app_handle, password);
                    if verify_result {
                        // init digest
                        if let Some(digest_handle) =
                            skf_digest_init(lib, dev_handle, cert_data.clone())
                        {
                            // digest
                            if let Some(digest) =
                                skf_digest(lib, digest_handle, random_str.as_bytes())
                            {
                                // 转为base64打印
                                let mut buf1 = Vec::new();
                                // make sure we'll have a slice big enough for base64 + padding
                                buf1.resize(digest.len() * 4 / 3 + 4, 0);

                                let bytes_written1 = general_purpose::STANDARD
                                    .encode_slice(digest.clone(), &mut buf1)
                                    .unwrap();

                                info!(
                                    "digest: {}",
                                    String::from_utf8(buf1[..bytes_written1].to_vec()).unwrap()
                                );
                                // sign
                                if let Some(sig) =
                                    skf_ecc_sign_data(lib, container_handle, digest.clone())
                                {
                                    verify_result_obj.result = verify_result;
                                    verify_result_obj.retry_count = retry_count;
                                    verify_result_obj.certificate =
                                        String::from_utf8(buf[..bytes_written].to_vec()).unwrap();
                                    // 封装签名结果
                                    if !format_result {
                                        let r = &sig.r[32..64];
                                        let s = &sig.s[32..64];
                                        // 将sig的32..64和96..128两段拼接
                                        let mut sign = Vec::new();
                                        for i in 0..32 {
                                            sign.push(r[i]);
                                        }
                                        for i in 0..32 {
                                            sign.push(s[i]);
                                        }
                                        info!("unencoded sign is: {:?}", sign);
                                        let sign = general_purpose::STANDARD.encode(&sign);
                                        verify_result_obj.signature = sign;
                                    } else {
                                        let sig = der_encode(&sig);
                                        info!("unencoded sign is: {:?}", sig);
                                        let sign = general_purpose::STANDARD.encode(&sig);
                                        info!("encoded sign is: {}", sign);
                                        verify_result_obj.signature = sign;
                                    }
                                } else {
                                    error!("sign failed")
                                }
                            }
                        }
                    } else {
                        verify_result_obj.result = verify_result;
                        verify_result_obj.retry_count = retry_count;
                    }
                }
                // 关闭容器
                skf_close_container(lib, container_handle);
            }
            // 关闭应用
            skf_close_application(lib, app_handle);
        }

        // 断开连接
        skf_disconnect_dev(lib, dev_handle);
    }
    verify_result_obj
}

#[derive(Serialize)]
pub struct ChangePasswordResult {
    result: bool,
    retry_count: usize,
}

#[allow(dead_code)]
fn do_change_password(
    _sn: &str,
    old_password: &str,
    new_password: &str,
    app_name: &str,
) -> ChangePasswordResult {
    let mut verify_result_obj = ChangePasswordResult {
        result: false,
        retry_count: 0,
    };
    let lib = load_library();
    let dev_list = skf_enum_dev(lib);
    if dev_list.len() > 0 {
        if let Some(dev_handle) = skf_connect_dev(lib, dev_list[0].clone()) {
            // 打开应用
            if let Some(app_handle) = skf_open_application(lib, dev_handle, app_name) {
                let (verify_result, retry_count) = skf_verify_pin(lib, app_handle, old_password);
                if verify_result {
                    let change_result = skf_change_pin(lib, app_handle, old_password, new_password);
                    verify_result_obj.result = change_result.result;
                    verify_result_obj.retry_count = change_result.retry_count;
                } else {
                    verify_result_obj.result = verify_result;
                    verify_result_obj.retry_count = retry_count;
                }
                // 关闭应用
                skf_close_application(lib, app_handle);
            }

            // 断开连接
            skf_disconnect_dev(lib, dev_handle);
        }
    }
    free_library(lib);
    verify_result_obj
}

fn der_encode(signature: &ECCSIGNATUREBLOB) -> Vec<u8> {
    let r = &signature.r[32..64];
    let s = &signature.s[32..64];
    let mut der_r = vec![0x02];
    if r[0] & 0x80 != 0 {
        der_r.push(33);
        der_r.push(0);
    } else {
        der_r.push(32);
    }
    der_r.extend_from_slice(r);

    let mut der_s = vec![0x02];
    if s[0] & 0x80 != 0 {
        der_s.push(33);
        der_s.push(0);
    } else {
        der_s.push(32);
    }
    der_s.extend_from_slice(s);

    let mut sequence = vec![0x30];
    let len = der_r.len() + der_s.len();
    if len > 0x7f {
        sequence.push(0x81);
        sequence.push(len as u8);
    } else {
        sequence.push(len as u8);
    }
    sequence.append(&mut der_r);
    sequence.append(&mut der_s);
    sequence
}

fn fetch_subject_from_cert(cert_bytes: &[u8]) -> String {
    // 调用CertCreateCertificateContext
    let cert_context = unsafe {
        winapi::um::wincrypt::CertCreateCertificateContext(
            winapi::um::wincrypt::X509_ASN_ENCODING | winapi::um::wincrypt::PKCS_7_ASN_ENCODING,
            cert_bytes.as_ptr() as *const u8,
            cert_bytes.len() as u32,
        )
    };
    if cert_context.is_null() {
        info!("CertCreateCertificateContext failed");
        return String::new();
    }
    // 调用CertGetNameString
    let mut subject = [0u8; 256];
    let subject_len = unsafe {
        winapi::um::wincrypt::CertGetNameStringA(
            cert_context,
            winapi::um::wincrypt::CERT_NAME_SIMPLE_DISPLAY_TYPE,
            0,
            std::ptr::null_mut(),
            subject.as_mut_ptr() as *mut i8,
            subject.len() as u32,
        )
    };
    if subject_len == 0 {
        info!("CertGetNameString failed");
        return String::new();
    }
    // 通过找到第一个\0来截取字符串
    let subject = subject_to_string(subject);
    subject
}

fn subject_to_string(arr: [u8; 256]) -> String {
    let mut str = String::new();
    for i in 0..arr.len() {
        if arr[i] == 0 {
            break;
        }
        str.push(arr[i] as u8 as char);
    }
    str
}

// fn to_string(arr: [i8; 32]) -> String {
//     let mut str = String::new();
//     for i in 0..arr.len() {
//         if arr[i] == 0 {
//             break;
//         }
//         str.push(arr[i] as u8 as char);
//     }
//     str
// }

fn free_library(lib: *mut HINSTANCE__) {
    unsafe { FreeLibrary(lib) };
}

fn load_library() -> *mut HINSTANCE__ {
    // 从配置文件中获取dll的位置
    let dll_path = get_dll_path();
    let dll = unsafe { LoadLibraryA(dll_path.as_ptr()) };
    if dll.is_null() {
        info!("Failed to load dll");
    } else {
        info!("Load dll success");
    }
    dll
}

fn load_watch_library() -> Option<*mut HINSTANCE__> {
    // 从配置文件中获取dll的位置
    if let Some(dll_path) = get_watch_dll() {
        let dll = unsafe { LoadLibraryA(dll_path.as_ptr()) };
        if dll.is_null() {
            info!("Failed to load dll");
        } else {
            info!("Load dll success");
        }
        return Some(dll);
    }
    None
}

fn get_dll_path() -> CString {
    let os_type = distro();
    let mut dll_path = String::from("resources/pki/lite/axtx5018_gm_x64.dll");
    info!("os_type: {}", os_type);
    if os_type.starts_with("Windows 10.0.22") {
        info!("load resources/pki/big/axtx5018_gm_x64.dll");
        dll_path = String::from("resources/pki/big/axtx5018_gm_x64.dll");
    } else {
        info!("load resources/pki/lite/axtx5018_gm_x64.dll");
    }
    let dll_path = CString::new(dll_path).unwrap();
    dll_path
}

// 加载握奇dll
fn get_watch_dll() -> Option<CString> {
    let win_dir = std::env::var("windir");
    match win_dir {
        Ok(value) => Some(
            CString::new(String::from(
                value.as_str().to_owned()
                    + "\\System32\\WatchDataV5\\Watchdata CSP v5.1\\WDSKF.dll",
            ))
            .unwrap(),
        ),
        Err(_e) => None,
    }
}
