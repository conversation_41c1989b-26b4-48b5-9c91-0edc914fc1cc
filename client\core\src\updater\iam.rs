use crate::{EventListener, PRODUCT_NAME};
use futures::StreamExt;
use http::{
    header::{ACCEPT, RANGE},
    HeaderValue,
};
use semver::Version;

use crate::updater::{
    get_updater_arch, get_updater_target, run, save_files, verify_signature, RemoteRelease, Result,
    FLAG,
};
use std::{fs, io::Cursor, path::PathBuf, sync::atomic::Ordering};

#[cfg(target_os = "linux")]
use crate::linux::detect_package_ext;

pub struct Update {
    /// 当前版本
    _current_version: Version,
    /// 是否需要更新
    should_update: bool,
    /// 下载的临时文件存放目录
    tmp_dir: Option<PathBuf>,
    /// 更新内容
    remote_release: Option<RemoteRelease>,
}

#[derive(Clone)]
pub struct UpdateBuilder {
    pub version: Version,
    pub platform: String,
    pub arch: String,
    pub ext: &'static str,
    pub model: Option<String>,
    pub endpoint: Option<String>,
}

impl Default for UpdateBuilder {
    fn default() -> Self {
        #[cfg(windows)]
        let ext = "exe";
        #[cfg(target_os = "macos")]
        let ext = "dmg";
        #[cfg(target_os = "linux")]
        let ext = detect_package_ext();

        let (Some(platform), Some(arch)) = (get_updater_target(), get_updater_arch(ext)) else {
            panic!("Unsupported platform.");
        };

        let version: Version = version::PRODUCT_VERSION.parse().unwrap();
        Self {
            version,
            platform: platform.to_owned(),
            arch: arch.to_owned(),
            ext,
            model: None,
            endpoint: None,
        }
    }
}

impl UpdateBuilder {
    pub fn model(&mut self, model: String) -> &mut Self {
        self.model = Some(model);
        self
    }

    pub fn endpoint(&mut self, endpoint: String) -> &mut Self {
        self.endpoint = Some(endpoint);
        self
    }

    pub async fn build(&self) -> Option<Update> {
        let current_version: Version = version::PRODUCT_VERSION.parse().unwrap();

        let mut headers = http::header::HeaderMap::new();
        headers.insert(ACCEPT, "application/json".parse().unwrap());
        headers.insert("ext", self.ext.parse().unwrap());

        let endpoint = self.endpoint.clone().unwrap();
        let model = self.model.clone().unwrap();
        let url = if endpoint.ends_with("/") {
            format!(
                "{}check_update/{}/{}/{}/{}/{}",
                endpoint, PRODUCT_NAME, self.platform, self.arch, self.version, model
            )
        } else {
            format!(
                "{}/check_update/{}/{}/{}/{}/{}",
                endpoint, PRODUCT_NAME, self.platform, self.arch, self.version, model
            )
        };

        let client = crate::http::client().await;

        match client.get(url).send().await {
            Ok(response) => match response.status() {
                http::status::StatusCode::NO_CONTENT => {
                    log::debug!("No updates available.");
                    None
                }
                http::status::StatusCode::OK => {
                    if let Some(0) = response.content_length() {
                        log::debug!("No updates available.");
                        return None;
                    }

                    let Ok(remote_release) = response.json::<RemoteRelease>().await else {
                        log::error!("Failed to deserialize update json");
                        return None;
                    };

                    return Some(Update {
                        _current_version: current_version.clone(),
                        should_update: remote_release.version > current_version,
                        tmp_dir: None,
                        remote_release: Some(remote_release),
                    });
                }
                _ => {
                    log::error!("Update interface request failed: {:?}", response.status());
                    None
                }
            },
            Err(error) => {
                log::error!("Update interface request failed: {:?}", error.status());
                None
            }
        }
    }
}

impl Update {
    pub fn should_update(&self) -> bool {
        self.should_update
    }
    pub fn remote_release(&self) -> Option<&RemoteRelease> {
        self.remote_release.as_ref()
    }

    pub async fn download<L: EventListener>(&mut self, event_listener: L) {
        let Some(remote_release) = self.remote_release.as_ref() else {
            // 没有更新可用
            event_listener.notify_update_status("ERROR", Some(String::from("Update unavailable")));
            return;
        };

        FLAG.store(true, Ordering::Release);
        // 开始下载
        event_listener.notify_update_status("PENDING", None);

        let mut headers = http::header::HeaderMap::new();
        headers.insert(ACCEPT, "application/octet-stream".parse().unwrap());
        headers.insert(
            "User-Agent",
            HeaderValue::from_str("sdp/selfupdater").unwrap(),
        );
        let mut size_headers = headers.clone();
        size_headers.insert(RANGE, "bytes=0-1".parse().unwrap());

        let client = crate::http::client().await;

        // 获取包大小
        let content_length = match client
            .get(remote_release.manifest.url.clone())
            .headers(size_headers)
            .send()
            .await
        {
            Ok(response) => {
                if !response.status().is_success() {
                    let msg = format!("Download request failed with status: {}", response.status());
                    log::error!("{msg}");
                    event_listener.notify_update_status("ERROR", Some(msg));
                    return;
                }

                let range = response.headers().get("content-range").unwrap();
                range
                    .to_str()
                    .map(|range| {
                        range
                            .split_once('/')
                            .map(|(_range, size)| size)
                            .map(|content_length| content_length.parse::<u64>().unwrap_or_default())
                    })
                    .ok()
                    .flatten()
                    .unwrap_or_default()
            }
            Err(error) => {
                log::error!("Updater: {error}");
                event_listener.notify_update_status("ERROR", Some(error.to_string()));
                return;
            }
        };

        // 下载
        match client
            .get(remote_release.manifest.url.clone())
            .headers(headers)
            .send()
            .await
        {
            Ok(response) => {
                if !response.status().is_success() {
                    let msg = format!("Download request failed with status: {}", response.status());
                    log::error!("{msg}");
                    event_listener.notify_update_status("ERROR", Some(msg));
                    return;
                }

                let mut buffer = Vec::new();
                {
                    let mut stream = response.bytes_stream();
                    while let Some(Ok(buf)) = stream.next().await {
                        // 检查是否已取消更新
                        if !FLAG.load(Ordering::Acquire) {
                            log::info!("Updater: User canceled");
                            event_listener.notify_update_status("CANCEL", None);
                            return;
                        }
                        buffer.extend(buf);
                        event_listener.notify_download_progress(buffer.len(), content_length);
                    }
                }

                log::trace!("Updater buffer size: {}", buffer.len());

                // create memory buffer from our archive (Seek + Read)
                let mut archive_buffer = Cursor::new(buffer);

                // We need an announced signature by the server
                // if there is no signature, bail out.
                if let Err(error) =
                    verify_signature(&mut archive_buffer, &remote_release.manifest.signature)
                {
                    log::error!("Updater: {error}");
                    event_listener.notify_update_status("ERROR", Some(error.to_string()));
                    return;
                }

                // 下载完成
                event_listener.notify_update_status("DOWNLOADED", None);

                #[cfg(target_os = "windows")]
                let save_result = save_files(archive_buffer);

                #[cfg(not(target_os = "windows"))]
                let save_result = save_files(archive_buffer);

                match save_result {
                    Ok(path) => {
                        self.tmp_dir = Some(path);
                    }
                    Err(error) => {
                        let msg = format!("Save error: {error}",);
                        log::error!("Updater: {msg}");
                        event_listener.notify_update_status("ERROR", Some(msg));
                    }
                }
            }
            Err(error) => {
                log::error!("Updater: {error}");
                event_listener.notify_update_status("ERROR", Some(error.to_string()));
            }
        }
    }

    pub async fn install(&mut self) -> Result<()> {
        let Some(tmp_dir) = &self.tmp_dir else {
            log::warn!("No upgrade file");
            return Ok(());
        };
        log::trace!("install updater: {:?}", self.tmp_dir);
        run(tmp_dir)
    }

    pub fn cancel() {
        FLAG.store(false, Ordering::Release);
    }

    /// 取消更新后, 删除临时文件
    pub fn clean(&self) {
        if let Some(tmp_dir) = &self.tmp_dir {
            log::debug!("Clean tmpdir: {:?}", tmp_dir);
            _ = fs::remove_dir_all(tmp_dir);
        }
    }
}
