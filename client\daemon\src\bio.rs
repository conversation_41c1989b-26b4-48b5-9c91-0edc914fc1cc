use actix_web::{web::Query, HttpRequest, HttpResponse};
use serde::Deserialize;

#[actix_web::get("/enabled")]
pub async fn is_support_facial_recognition() -> HttpResponse {
    HttpResponse::Ok().json(serde_json::json!({
        "code": "SUCCESS",
        "data": biometric::is_support_facial_recognition(),
    }))
}

#[derive(Deserialize)]
#[serde(rename_all = "camelCase")]
struct RequestId {
    request_id: String,
    tenant: String,
}

#[actix_web::get("/start")]
pub async fn facial_recognition(
    req: HttpRequest,
    Query(RequestId { request_id, tenant }): Query<RequestId>,
) -> HttpResponse {
    let Some(origin) = req.headers().get("origin") else {
        return HttpResponse::BadRequest().body("origin not set");
    };
    let origin = origin.to_str().unwrap_or_default().to_string();
    // let url = format!(
    //     "{}/mfa/v8/web/mfa/client/request/user?requestId={}",
    //     origin, request_id
    // );
    let client = crate::http::client().await;
    // let username = match client.get(url).send().await {
    //     Ok(response) => {
    //         if response.status().is_success() {
    //             let result = response.json::<Value>().await.unwrap();
    //             result["data"]["username"]
    //                 .as_str()
    //                 .map(ToOwned::to_owned)
    //                 .unwrap()
    //         } else {
    //             return Response::builder().status(response.status()).finish();
    //         }
    //     }
    //     Err(error) => {
    //         log::error!("facial_recognition: {error}");
    //         return Json(serde_json::json!({
    //             "code": "ERROR",
    //             "data": "网络错误",
    //         }))
    //         .with_status(StatusCode::SERVICE_UNAVAILABLE)
    //         .into_response();
    //     }
    // };

    let url = format!("{}/mfa/v8/web/mfa/client/{}/audit", origin, request_id);
    match biometric::facial_recognition_verify() {
        Ok(status) => {
            if status.is_some() {
                let response = client
                    .post(url.clone())
                    .body(
                        serde_json::to_vec(&serde_json::json!({
                            "errorMessage": "",
                        }))
                        .unwrap(),
                    )
                    .header("TENANT", tenant.clone())
                    .send()
                    .await;
                match response {
                    Ok(response) => {
                        if response.status().is_success() {
                            return HttpResponse::Ok().json(serde_json::json!({
                                "code": "SUCCESS",
                            }));
                        } else {
                            log::error!("{:?}", response.text().await);
                        }
                    }
                    Err(error) => {
                        log::error!("{error}");
                    }
                }
                _ = client
                    .post(url)
                    .body(
                        serde_json::to_vec(&serde_json::json!({
                            "errorMessage": "AUTH FAILED",
                        }))
                        .unwrap(),
                    )
                    .header("TENANT", tenant)
                    .send()
                    .await;
                return HttpResponse::Ok().json(serde_json::json!({
                    "code": "ERROR",
                    "data": "AUTH FAILED",
                }));
            }
            _ = client
                .post(url)
                .body(
                    serde_json::to_vec(&serde_json::json!({
                        "errorMessage": "Does not support facial recognition",
                    }))
                    .unwrap(),
                )
                .header("TENANT", tenant.clone())
                .send()
                .await;
            HttpResponse::Ok().json(serde_json::json!({
                "code": "ERROR",
                "data": "Does not support facial recognition",
            }))
        }
        Err(msg) => {
            _ = client
                .post(url)
                .body(
                    serde_json::to_vec(&serde_json::json!({
                        "errorMessage": msg,
                    }))
                    .unwrap(),
                )
                .header("TENANT", tenant.clone())
                .send()
                .await;
            HttpResponse::Ok().json(serde_json::json!({
                "code": "ERROR",
                "data": msg,
            }))
        }
    }
}

#[actix_web::get("/stop")]
pub async fn cancel_facial_recognition() -> HttpResponse {
    biometric::cancel_facial_recognition();
    HttpResponse::Ok().json(serde_json::json!({
        "code": "SUCCESS",
    }))
}
