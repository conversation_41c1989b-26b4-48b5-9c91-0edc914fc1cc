<template>
  <div class="app">
    <!-- 4.找出口 -->
    <router-view v-if="isRouterAlive"></router-view>
  </div>
</template>

<script>
import EventBus from "./tools/event-bus";

export default {
  data() {
    return {
      isRouterAlive: true,
    };
  },
  provide() {
    return {
      reload: this.reload,
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false; //先关闭，
      this.$nextTick(function () {
        this.isRouterAlive = true; //再打开
      });
    },
  },
  mounted() {
    new EventBus().on("reload", this.reload);
  },
  beforeDestroy() {
    new EventBus().off("reload", this.reload);
  },
};
</script>

<style lang="less" scoped>
.app {
  height: 652px;
}
</style>