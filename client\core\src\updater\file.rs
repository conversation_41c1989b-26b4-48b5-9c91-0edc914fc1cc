// Copyright 2019-2023 Tauri Programme within The Commons Conservancy
// SPDX-License-Identifier: Apache-2.0
// SPDX-License-Identifier: MIT

//! Types and functions related to file operations.

mod extract;
use std::{
    fs,
    path::{Display, Path},
};

pub use extract::*;

use serde::{de::Error as DeError, Deserialize, Deserializer};

#[derive(Clone, Debug)]
pub(crate) struct SafePathBuf(std::path::PathBuf);

impl SafePathBuf {
    pub fn new(path: std::path::PathBuf) -> Result<Self, &'static str> {
        if path
            .components()
            .any(|x| matches!(x, std::path::Component::ParentDir))
        {
            Err("cannot traverse directory, rewrite the path without the use of `../`")
        } else {
            Ok(Self(path))
        }
    }

    #[allow(dead_code)]
    pub unsafe fn new_unchecked(path: std::path::PathBuf) -> Self {
        Self(path)
    }

    #[allow(dead_code)]
    pub fn display(&self) -> Display<'_> {
        self.0.display()
    }
}

impl AsRef<Path> for SafePathBuf {
    fn as_ref(&self) -> &Path {
        self.0.as_ref()
    }
}

impl<'de> Deserialize<'de> for SafePathBuf {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let path = std::path::PathBuf::deserialize(deserializer)?;
        SafePathBuf::new(path).map_err(DeError::custom)
    }
}

/// Reads the entire contents of a file into a string.
pub fn read_string<P: AsRef<Path>>(file: P) -> super::Result<String> {
    fs::read_to_string(file).map_err(Into::into)
}

/// Reads the entire contents of a file into a bytes vector.
pub fn read_binary<P: AsRef<Path>>(file: P) -> super::Result<Vec<u8>> {
    fs::read(file).map_err(Into::into)
}
