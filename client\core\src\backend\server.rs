use std::{
    collections::HashMap,
    io,
    net::{IpAddr, Ipv4Addr, SocketAddr},
};

use futures::{SinkExt, StreamExt};

use rustls::pki_types::{PrivateKeyDer, ServerName};
use serde_json::Value;
use tokio::{net::TcpStream, sync::oneshot};

use tokio_rustls::{client::TlsStream, TlsConnector};
use tokio_util::codec::Framed;

use base::{
    packet::{Message, MessageCodec, MessageType},
    spa::SPAType,
};
use tlv::{Serialize, Tag};
use tlv_types::{AuthResult, ModePayload};
use tonic::transport::CertificateDer;
use types::backend::{ChangePwdCode, ConnectionConfig, Login};

use crate::{
    backend::{
        common::{handle_proxy_fail, handle_proxy_response, handle_proxy_response_chunk},
        spa::{self, SpaArgs},
    },
    backend_state_machine::ResponseTx,
    setting::DeviceIdentity,
};

#[derive(Clone)]
pub struct Backend {
    pub device_id: String,

    // pub(super) event_listener: L,
    // pub(super) route_manager_handle: RouteManagerHandle,
    /// 本机IPv4地址
    pub local_ip: Ipv4Addr,
    pub identities: HashMap<String, DeviceIdentity>,
}

impl Backend {
    async fn send_spa(
        &self,
        spa_type: SPAType,
        connection_config: ConnectionConfig,
    ) -> Result<(oneshot::Sender<()>, IpAddr), crate::Error> {
        let addr = if let Some(ip) = connection_config.ip {
            format!("{}:{}", ip, connection_config.spa_port)
        } else {
            format!("{}:{}", connection_config.host, connection_config.spa_port)
        };

        let host_hash = gm_sm3::sm3_hash(connection_config.host.as_bytes());
        let host_hash = hex::to_string!(&host_hash[..]).to_uppercase();
        let identity = self
            .identities
            .get(&host_hash)
            .ok_or_else(|| crate::Error::MissingDomain(connection_config.host.clone()))?;

        let (secret, tenant, username) = match connection_config.authorization {
            Some(authorization) => (
                Some(authorization.secret.clone()),
                Some(authorization.tenant.clone()),
                Some(authorization.username.clone()),
            ),
            None => (None, None, None),
        };

        let args = SpaArgs {
            device_id: &self.device_id,
            ipv4: self.local_ip,
            pubkey: identity.encrypt_pubkey,
            addr: &addr,
            secret: secret.as_deref(),
            tenant: tenant.as_deref(),
            username: username.as_deref(),
        };

        spa::send_spa(args, spa_type)
            .await
            .map_err(crate::Error::BackendError)
    }

    pub async fn connect(
        &self,
        connection_config: ConnectionConfig,
        spa_type: SPAType,
    ) -> Result<Framed<TlsStream<TcpStream>, MessageCodec>, crate::Error> {
        let Ok(domain) =
            ServerName::try_from(connection_config.host.as_str()).map(|n| n.to_owned())
        else {
            return Err(crate::Error::DomainError(connection_config.host));
        };

        let host_hash = gm_sm3::sm3_hash(connection_config.host.as_bytes());
        let host_hash = hex::to_string!(&host_hash[..]).to_uppercase();

        let identity = self
            .identities
            .get(&host_hash)
            .ok_or_else(|| crate::Error::MissingDomain(connection_config.host.clone()))?;

        let (prevent_tx, remote_ip) = self.send_spa(spa_type, connection_config.clone()).await?;

        let addr = SocketAddr::new(remote_ip, connection_config.port);
        let framed = match Self::do_connect(
            addr,
            domain,
            identity.roots.clone(),
            identity.certs.clone(),
            identity.key.clone_key(),
            #[cfg(feature = "tlcp")]
            identity.enc_cert.clone(),
            #[cfg(feature = "tlcp")]
            identity.enc_key.clone_key(),
        )
        .await
        {
            Ok(framed) => framed,
            Err(error) => return Err(crate::Error::ConnectError(error)),
        };

        log::trace!("Controller connect successfully");
        _ = prevent_tx.send(());

        Ok(framed)
    }

    /// 通过验证码修改密码
    pub async fn change_pwd_by_verify_code(
        &self,
        tx: ResponseTx<String, crate::Error>,
        env: Value,
        message: ChangePwdCode,
    ) {
        let mut framed = match self
            .connect(message.connection_config, SPAType::ChangePwd)
            .await
        {
            Ok(framed) => framed,
            Err(error) => {
                _ = tx.send(Err(error));
                return;
            }
        };

        Self::change_pwd_by_code(tx, &mut framed, message.payload, env).await;
        _ = framed.close().await;
    }

    /// 代理连接时, 重置密码
    pub async fn change_pwd_in_proxy(
        &self,
        mut framed: Framed<TlsStream<TcpStream>, MessageCodec>,
        tx: ResponseTx<String, crate::Error>,
        env: Value,
        message: ChangePwdCode,
    ) {
        if let Err(error) = self
            .send_spa(SPAType::ChangePwd, message.connection_config)
            .await
        {
            _ = tx.send(Err(error));
            return;
        }

        Self::change_pwd_by_code(tx, &mut framed, message.payload, env).await;
        _ = framed.close().await;
    }

    /// 登录
    pub async fn login(
        self,
        tx: ResponseTx<AuthResult, crate::Error>,
        system_info: Value,
        payload: Login,
    ) -> Option<(
        Framed<TlsStream<TcpStream>, MessageCodec>,
        ModePayload,
        Option<Vec<IpAddr>>,
        Option<IpAddr>,
    )> {
        let Some(ref spa_authorization) = payload.connection_config.authorization else {
            log::error!("missing spa authorization");
            _ = tx.send(Err(crate::Error::MissingAuthorization("spa_authorization")));
            return None;
        };
        let username = spa_authorization.username.clone();

        let mut framed = match self.connect(payload.connection_config, SPAType::Auth).await {
            Ok(framed) => framed,
            Err(error) => {
                _ = tx.send(Err(error));
                return None;
            }
        };

        match Self::authentication(
            tx,
            &mut framed,
            username,
            payload.payload,
            system_info,
            false,
        )
        .await
        {
            Some(result) => Some((framed, result.data, result.dns, result.client_ip)),
            None => {
                _ = framed.close().await;
                None
            }
        }
    }

    /// 代理连接成功时的认证
    pub async fn login_in_proxy(
        self,
        mut framed: Framed<TlsStream<TcpStream>, MessageCodec>,
        tx: ResponseTx<AuthResult, crate::Error>,
        system_info: Value,
        payload: Login,
    ) -> Option<(
        Framed<TlsStream<TcpStream>, MessageCodec>,
        ModePayload,
        Option<Vec<IpAddr>>,
        Option<IpAddr>,
    )> {
        let Some(ref spa_authorization) = payload.connection_config.authorization else {
            log::error!("missing spa authorization");
            _ = tx.send(Err(crate::Error::MissingAuthorization("spa_authorization")));
            return None;
        };
        let username = spa_authorization.username.clone();

        if let Err(error) = self
            .send_spa(SPAType::Auth, payload.connection_config)
            .await
        {
            _ = tx.send(Err(error));
            return None;
        }

        match Self::authentication(
            tx,
            &mut framed,
            username,
            payload.payload,
            system_info,
            true,
        )
        .await
        {
            Some(result) => Some((framed, result.data, result.dns, result.client_ip)),
            None => {
                _ = framed.close().await;
                None
            }
        }
    }

    async fn do_connect(
        addr: SocketAddr,
        domain: ServerName<'static>,
        roots: Option<Vec<Vec<u8>>>,
        certs: Vec<CertificateDer<'static>>,
        key: PrivateKeyDer<'static>,
        #[cfg(feature = "tlcp")] enc_cert: CertificateDer<'static>,
        #[cfg(feature = "tlcp")] enc_key: PrivateKeyDer<'static>,
    ) -> Result<Framed<TlsStream<TcpStream>, MessageCodec>, io::Error> {
        log::debug!("Connect to: {}", addr);

        let stream = TcpStream::connect(addr).await?;

        let config = base::make_client_config_with_certs(
            roots,
            certs,
            key,
            #[cfg(feature = "tlcp")]
            enc_cert,
            #[cfg(feature = "tlcp")]
            enc_key,
        );
        let connector = TlsConnector::from(config);

        let stream = connector.connect(domain, stream).await?;

        let codec = MessageCodec::new();
        Ok(Framed::new(stream, codec))
    }

    async fn change_pwd_by_code(
        tx: ResponseTx<String, crate::Error>,
        framed: &mut Framed<TlsStream<TcpStream>, MessageCodec>,
        mut payload: Value,
        env: Value,
    ) {
        let message = env["deviceId"].as_str().unwrap();
        let mut message = hex::decode!(message.as_bytes());
        tlv::utils::insert_tag(&mut message, Tag::RawBytes);

        // 发送修改密码的包
        payload["ENVIRONMENT"] = env;

        let mut payload = serde_json::to_vec(&payload).unwrap();
        tlv::utils::insert_tag(&mut payload, Tag::RawBytes);
        message.extend(payload);

        let chpwd = Message::new(MessageType::ChangePwdByVerifyCode, &message);

        if let Err(error) = framed.send(chpwd).await {
            log::error!("Failed to send changepwd packet to controller. {}", error);
            _ = tx.send(Err(crate::Error::ConnectError(error)));
            return;
        }

        // 处理密码修改结果
        match framed.next().await {
            None => {
                log::error!("Lose connection with controller");
                _ = tx.send(Err(crate::Error::ConnectionClosed));
            }
            Some(Err(error)) => {
                log::error!("Lost connection with controller. {}", error);
                _ = tx.send(Err(crate::Error::ConnectError(error)));
            }
            Some(Ok(message)) => match message.r#type() {
                MessageType::ChangePwdResult => {
                    let input = message.payload();
                    let result: Result<Vec<u8>, &'static str> = tlv::read_to_struct!(input);
                    let Ok(result) = result else {
                        log::error!("bad message. type = {:?}", MessageType::ChangePwdResult);
                        return;
                    };
                    let Ok(result) = String::from_utf8(result) else {
                        log::error!(
                            "invalid utf8 sequence. type = {:?}",
                            MessageType::ChangePwdResult
                        );
                        return;
                    };
                    _ = tx.send(Ok(result));
                }
                _ => {
                    log::error!(
                        "Change password failed. Received unexpected message. {:?}",
                        message.r#type()
                    );
                    // 认证失败
                    _ = tx.send(Err(crate::Error::AuthFailed(
                        "Controller auth failed".to_owned(),
                    )));
                }
            },
        }
    }

    async fn authentication(
        tx: ResponseTx<AuthResult, crate::Error>,
        framed: &mut Framed<TlsStream<TcpStream>, MessageCodec>,
        username: String,
        mut payload: Value,
        env: Value,
        in_proxy_connection: bool,
    ) -> Option<AuthResult> {
        let device_id = env["deviceId"].as_str().unwrap();
        let device_id = device_id.to_string();
        payload["username"] = Value::String(username);
        payload["ENVIRONMENT"] = env;
        let auth_packet = tlv_types::AuthPacket { device_id, payload };

        let auth_pkt = Message::new(MessageType::Authentication, &auth_packet.serialize());

        if let Err(error) = framed.send(auth_pkt).await {
            log::error!(
                "Failed to send authentication packet to controller. {}",
                error
            );
            _ = tx.send(Err(crate::Error::ConnectError(error)));
            return None;
        }

        log::trace!("Authentication packet sent");

        loop {
            // 处理认证结果
            match framed.next().await {
                None => {
                    log::error!("Lose connection with backend");
                    if in_proxy_connection {
                        // 在代理认证时, 如果是正常断开, 则表示代理连接超时,
                        // 重新走正常认证流程(发送SPA->建立连接->认证)
                        _ = tx.send(Err(crate::Error::ProxyConnectionClosed));
                    } else {
                        _ = tx.send(Err(crate::Error::ConnectionClosed));
                    }
                    break;
                }
                Some(Err(error)) => {
                    log::error!("Lost connection with backend. {}", error);
                    _ = tx.send(Err(crate::Error::ConnectError(error)));
                    break;
                }
                Some(Ok(message)) => match message.r#type() {
                    MessageType::AuthSuccess => {
                        let input = message.payload();
                        let result: Result<AuthResult, &'static str> = tlv::read_to_struct!(input);

                        let Ok(result) = result else {
                            log::error!("bad message. type = {:?}", MessageType::AuthSuccess);
                            return None;
                        };
                        log::info!("Backend authentication successfully");
                        _ = tx.send(Ok(result.clone()));
                        return Some(result);
                    }
                    MessageType::AuthFailed => {
                        log::error!("Backend authentication failed");
                        let input = message.payload();
                        let result: Result<String, &'static str> = tlv::read_to_struct!(input);
                        let Ok(failed_reason) = result else {
                            log::error!("bad message. type = {:?}", MessageType::AuthFailed);
                            return None;
                        };
                        // 如果是密码过期, 也直接执行断开操作, 填入信息后再次连接修改密码
                        // 控制器认证失败
                        _ = tx.send(Err(crate::Error::AuthFailed(failed_reason)));
                        break;
                    }
                    // 需要修改密码
                    MessageType::NeedChangePwd => {
                        log::info!("Backend authentication succeed, but need change password");
                        let input = message.payload();
                        let result: Result<String, &'static str> = tlv::read_to_struct!(input);
                        let Ok(payload) = result else {
                            log::error!("bad message. type = {:?}", MessageType::NeedChangePwd);
                            return None;
                        };
                        _ = tx.send(Err(crate::Error::NeedChangePwd(payload)));
                        break;
                    }
                    MessageType::ProxyResponse => {
                        let input = message.payload();
                        if handle_proxy_response(input).await.is_err() {
                            break;
                        }
                    }
                    MessageType::ProxyResponseChunk => {
                        let input = message.payload();
                        if handle_proxy_response_chunk(input).await.is_err() {
                            break;
                        }
                    }
                    MessageType::ProxyFail => {
                        let input = message.payload();
                        if handle_proxy_fail(input).await.is_err() {
                            break;
                        }
                    }
                    _ => {
                        log::error!(
                            "Backend authentication failed. Received unexpected message. {:?}",
                            message.r#type()
                        );
                        // 控制器认证失败
                        _ = tx.send(Err(crate::Error::AuthFailed(
                            "Backend authentication failed".to_owned(),
                        )));
                        break;
                    }
                },
            }
        }

        None
    }
}
