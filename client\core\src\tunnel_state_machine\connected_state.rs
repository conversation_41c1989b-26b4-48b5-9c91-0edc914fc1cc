use std::net::IpAddr;

use futures::{
    channel::{mpsc, oneshot},
    stream::Fuse,
    SinkExt, StreamExt,
};

use err_ext::ErrorExt;
use types::{
    tunnel::{ErrorStateCause, TunnelStateTransition},
    BoxedError,
};

use crate::tunnel_state_machine::{
    connecting_state::ConnectingState, error_state::ErrorState, EventResult,
};

use super::{
    connecting_state::TunnelCloseEvent,
    disconnecting_state::{AfterDisconnect, DisconnectingState},
    EventConsequence, ResourceEvent, SharedTunnelStateValues, TunnelCommand, TunnelCommandReceiver,
    TunnelEvent, TunnelMetadata, TunnelState, TunnelStateWrapper,
};

pub(crate) type TunnelEventsReceiver =
    Fuse<mpsc::UnboundedReceiver<(TunnelEvent, oneshot::Sender<()>)>>;

pub struct ConnectedStateBootstrap {
    pub metadata: TunnelMetadata,
    pub tunnel_events: TunnelEventsReceiver,
    pub tunnel_close_event: TunnelCloseEvent,
    pub tunnel_close_tx: oneshot::Sender<()>,
    pub tunnel_packet_tx: mpsc::UnboundedSender<Vec<u8>>,
    pub auth_resource_tx: mpsc::UnboundedSender<ResourceEvent>,
    pub change_network_tx: mpsc::Sender<(IpAddr, oneshot::Sender<()>)>,
    #[cfg(feature = "traceability")]
    pub traceability_tx: mpsc::Sender<bool>,
}

pub struct ConnectedState {
    metadata: TunnelMetadata,
    tunnel_events: TunnelEventsReceiver,
    tunnel_close_event: TunnelCloseEvent,
    tunnel_close_tx: oneshot::Sender<()>,
    tunnel_packet_tx: mpsc::UnboundedSender<Vec<u8>>,
    auth_resource_tx: mpsc::UnboundedSender<ResourceEvent>,
    change_network_tx: mpsc::Sender<(IpAddr, oneshot::Sender<()>)>,
    #[cfg(feature = "traceability")]
    traceability_tx: mpsc::Sender<bool>,
}

impl ConnectedState {
    fn from(bootstrap: ConnectedStateBootstrap) -> Self {
        ConnectedState {
            metadata: bootstrap.metadata,
            tunnel_events: bootstrap.tunnel_events,
            tunnel_close_event: bootstrap.tunnel_close_event,
            tunnel_close_tx: bootstrap.tunnel_close_tx,
            tunnel_packet_tx: bootstrap.tunnel_packet_tx,
            auth_resource_tx: bootstrap.auth_resource_tx,
            change_network_tx: bootstrap.change_network_tx,
            #[cfg(feature = "traceability")]
            traceability_tx: bootstrap.traceability_tx,
        }
    }

    #[allow(unused_variables)]
    fn get_dns_servers(&self, shared_values: &SharedTunnelStateValues) -> Vec<IpAddr> {
        if let Some(ref servers) = shared_values.dns_servers {
            servers.clone()
        } else {
            vec![]
        }
    }

    async fn set_dns(&self, shared_values: &mut SharedTunnelStateValues) -> Result<(), BoxedError> {
        let dns_ips = self.get_dns_servers(shared_values);

        shared_values
            .dns_monitor
            .set(&self.metadata.interface, &dns_ips)
            .await
            .map_err(BoxedError::new)?;

        Ok(())
    }

    async fn reset_dns(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values
            .dns_monitor
            .reset_before_interface_removal()
            .await
        {
            log::error!("{}", error.display_chain_with_msg("Unable to reset DNS"));
        }
    }

    async fn reset_routes(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values.route_manager.clear_routes() {
            log::error!("{}", error.display_chain_with_msg("Failed to clear routes"));
        }
        #[cfg(target_os = "linux")]
        if let Err(error) = shared_values.route_manager.clear_routing_rules().await {
            log::error!(
                "{}",
                error.display_chain_with_msg("Failed to clear routing rules")
            );
        }
    }

    async fn disconnect(
        self,
        shared_values: &mut SharedTunnelStateValues,
        after_disconnect: AfterDisconnect,
        callback: Option<oneshot::Sender<()>>,
    ) -> EventConsequence {
        Self::reset_dns(shared_values).await;
        Self::reset_routes(shared_values).await;
        _ = shared_values.set_dns_servers(None);
        if let Some(tx) = callback {
            _ = tx.send(());
        }
        EventConsequence::NewState(
            DisconnectingState::enter(
                shared_values,
                (
                    self.tunnel_close_tx,
                    self.tunnel_close_event,
                    after_disconnect,
                ),
            )
            .await,
        )
    }

    async fn handle_commands(
        mut self,
        command: Option<TunnelCommand>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match command {
            Some(TunnelCommand::Dns(servers)) => match shared_values.set_dns_servers(servers) {
                Ok(_) => match self.set_dns(shared_values).await {
                    Ok(_) => SameState(self.into()),
                    Err(error) => {
                        log::error!("{}", error.display_chain_with_msg("Failed to set DNS"));
                        SameState(self.into())
                    }
                },
                // Ok(false) => SameState(self.into()),
                Err(error_cause) => {
                    self.disconnect(shared_values, AfterDisconnect::Block(error_cause), None)
                        .await
                }
            },
            Some(TunnelCommand::ResetDns(tx)) => {
                Self::reset_dns(shared_values).await;
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                SameState(self.into())
            }
            Some(TunnelCommand::Routes((routes, tx))) => {
                match match routes {
                    Some(routes) => shared_values.route_manager.add_routes(routes).await,
                    None => shared_values.route_manager.clear_routes(),
                } {
                    Ok(_) => {
                        if let Some(tx) = tx {
                            _ = tx.send(());
                        }
                        SameState(self.into())
                    }
                    Err(error) => {
                        log::error!("{}", error.display_chain_with_msg("Failed to set routes"));
                        self.disconnect(
                            shared_values,
                            AfterDisconnect::Block(ErrorStateCause::SetRouteError),
                            None,
                        )
                        .await
                    }
                }
            }
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::BackendOffline) => {
                _ = shared_values.set_dns_servers(None);

                _ = self.auth_resource_tx.unbounded_send(ResourceEvent::Clear);
                SameState(self.into())
            }
            Some(TunnelCommand::Connect) => {
                self.disconnect(shared_values, AfterDisconnect::Reconnect(0), None)
                    .await
            }
            Some(TunnelCommand::Disconnect(tx)) => {
                self.disconnect(shared_values, AfterDisconnect::Nothing, tx)
                    .await
            }
            Some(TunnelCommand::Resource(event)) => {
                _ = self.auth_resource_tx.unbounded_send(event);
                SameState(self.into())
            }
            Some(TunnelCommand::Network { ip, callback_tx }) => {
                _ = self.change_network_tx.send((ip, callback_tx)).await;
                SameState(self.into())
            }
            Some(TunnelCommand::Packet(packet)) => {
                if self.tunnel_packet_tx.unbounded_send(packet).is_err() {
                    log::error!("Tunnel has stopped");
                    self.disconnect(
                        shared_values,
                        AfterDisconnect::Block(ErrorStateCause::StartTunnelError),
                        None,
                    )
                    .await
                } else {
                    SameState(self.into())
                }
            }
            #[cfg(feature = "traceability")]
            Some(TunnelCommand::TraceabilityFunction(available)) => {
                _ = self.traceability_tx.send(available).await;
                SameState(self.into())
            }
            None => {
                self.disconnect(shared_values, AfterDisconnect::Nothing, None)
                    .await
            }
        }
    }

    async fn handle_tunnel_events(
        self,
        event: Option<(TunnelEvent, oneshot::Sender<()>)>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match event {
            Some((TunnelEvent::Down, _)) | None => {
                self.disconnect(shared_values, AfterDisconnect::Reconnect(0), None)
                    .await
            }
            Some(_) => SameState(self.into()),
        }
    }

    async fn handle_tunnel_close_event(
        self,
        block_reason: Option<ErrorStateCause>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        if let Some(block_reason) = block_reason {
            Self::reset_dns(shared_values).await;
            Self::reset_routes(shared_values).await;
            return NewState(ErrorState::enter(shared_values, block_reason).await);
        }

        log::info!("Tunnel closed. Reconnecting");
        Self::reset_dns(shared_values).await;
        Self::reset_routes(shared_values).await;
        NewState(ConnectingState::enter(shared_values, ()).await)
    }
}

#[async_trait::async_trait]
impl TunnelState for ConnectedState {
    type Bootstrap = ConnectedStateBootstrap;

    #[cfg_attr(target_os = "android", allow(unused_variables))]
    async fn enter(
        _shared_values: &mut SharedTunnelStateValues,
        bootstrap: Self::Bootstrap,
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        let connected_state = ConnectedState::from(bootstrap);

        (
            TunnelStateWrapper::from(connected_state),
            TunnelStateTransition::Connected,
        )
    }

    async fn handle_event(
        mut self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        let result = futures::select! {
            command = commands.next() => EventResult::Command(command),
            event = self.tunnel_events.next() => EventResult::Event(event),
            result = &mut self.tunnel_close_event => EventResult::Close(result),
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Event(event) => self.handle_tunnel_events(event, shared_values).await,
            EventResult::Close(result) => {
                if result.is_err() {
                    log::warn!("Tunnel monitor thread has stopped unexpectedly");
                }
                let block_reason = result.unwrap_or(None);
                self.handle_tunnel_close_event(block_reason, shared_values)
                    .await
            }
        }
    }
}
