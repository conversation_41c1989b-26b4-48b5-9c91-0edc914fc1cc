use http::Version;
use hyper::header;
pub use log::trace;
use std::{
    net::{IpAddr, SocketAddr},
    time::Duration,
};
use url::Url;

#[derive(Debug, <PERSON>lone)]
pub struct MessageLog {
    // pub tls_server_name: String,
    pub client_addr: String,
    pub method: String,
    pub host: String,
    pub origin: String,
    pub p_and_q: String,
    pub version: hyper::Version,
    pub url: Url,
    pub ua: String,
    pub xff: String,
    pub status: String,
    pub time: Duration,
    pub final_url: Option<Url>,
}

impl<T> From<(&hyper::Request<T>, Url)> for MessageLog {
    fn from((req, url): (&hyper::Request<T>, Url)) -> Self {
        let header_mapper = |v: header::HeaderName| {
            req.headers()
                .get(v)
                .map_or_else(|| "", |s| s.to_str().unwrap_or(""))
                .to_string()
        };
        Self {
            // tls_server_name: "".to_string(),
            client_addr: "".to_string(),
            method: req.method().to_string(),
            host: header_mapper(header::HOST),
            origin: "".to_string(),
            p_and_q: req
                .uri()
                .path_and_query()
                .map_or_else(|| "", |v| v.as_str())
                .to_string(),
            version: req.version(),
            url,
            ua: header_mapper(header::USER_AGENT),
            xff: header_mapper(header::HeaderName::from_static("x-forwarded-for")),
            status: "".to_string(),
            time: Duration::from_millis(0),
            final_url: None,
        }
    }
}

impl MessageLog {
    pub fn client_addr(&mut self, client_addr: &SocketAddr) -> &mut Self {
        self.client_addr = client_addr.to_string();
        self
    }

    pub fn origin(&mut self, origin: IpAddr) -> &mut Self {
        self.origin = origin.to_string();
        self
    }

    pub fn final_url(&mut self, url: Url) -> &mut Self {
        self.final_url = Some(url);
        self
    }

    // pub fn tls_server_name(&mut self, tls_server_name: &str) -> &mut Self {
    //   self.tls_server_name = tls_server_name.to_string();
    //   self
    // }
    pub fn status_code(&mut self, status_code: &hyper::StatusCode) -> &mut Self {
        self.status = status_code.to_string();
        self
    }

    pub fn version(&mut self, version: Version) -> &mut Self {
        self.version = version;
        self
    }

    pub fn xff(&mut self, xff: &Option<&header::HeaderValue>) -> &mut Self {
        self.xff = xff
            .map_or_else(|| "", |v| v.to_str().unwrap_or(""))
            .to_string();
        self
    }

    pub fn time(&mut self, time: Duration) -> &mut Self {
        self.time = time;
        self
    }

    pub fn output(&self) {
        trace!(
            "{}/{} <- {} -- {} {} {:?} -- {} -- {} \"{}\", \"{}\" -- {:?}",
            if !self.host.is_empty() {
                self.host.as_str()
            } else {
                self.url.host_str().unwrap_or("")
            },
            self.origin,
            self.client_addr,
            self.method,
            self.final_url
                .as_ref()
                .map(|uri| uri.as_str())
                .unwrap_or(self.p_and_q.as_str()),
            self.version,
            self.status,
            self.url.as_str(),
            self.ua,
            self.xff,
            self.time,
            // self.tls_server_name
        );
    }
}
