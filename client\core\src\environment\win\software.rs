use winreg::{
    enums::{HKEY_LOCAL_MACHINE, HKEY_USERS, KEY_READ, KEY_WOW64_64KEY},
    <PERSON><PERSON><PERSON>,
};

use regex::Regex;

use crate::environment;

/// 软件列表
pub fn list() -> Vec<environment::Software> {
    let mut processes = environment::running::processes(false);
    processes.sort();
    processes.dedup();

    const PRODUCTS: &str = "Software\\Classes\\Installer\\Products";
    const UNINSTALL: &str = "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall";
    const UNINSTALL_6432NODE: &str =
        "Software\\Wow6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall";

    let hlm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let mut hlm_softwares = list_software(&hlm, UNINSTALL, &processes);
    let hlm_products = list_software(&hlm, PRODUCTS, &processes);
    let hlm_uninstall_6432node = list_software(&hlm, UNINSTALL_6432NODE, &processes);
    hlm_softwares.extend(hlm_products);
    hlm_softwares.extend(hlm_uninstall_6432node);

    let hu = RegKey::predef(HKEY_USERS);
    let hu_softwares = hu
        .enum_keys()
        .filter_map(|key| {
            let Ok(key) = key else {
                return None;
            };

            let Ok(subkey) = hu.open_subkey_with_flags(key, KEY_READ | KEY_WOW64_64KEY) else {
                return None;
            };

            let mut uninstalles = list_software(&subkey, UNINSTALL, &processes);
            let products = list_software(&subkey, PRODUCTS, &processes);
            let uninstalles_6432node = list_software(&subkey, UNINSTALL_6432NODE, &processes);
            uninstalles.extend(products);
            uninstalles.extend(uninstalles_6432node);

            Some(uninstalles)
        })
        .reduce(|first, mut curr| {
            curr.extend(first);
            curr
        })
        .unwrap_or_default();

    hlm_softwares.extend(hu_softwares);
    hlm_softwares.sort_by(|a, b| a.software_name.cmp(&b.software_name));
    hlm_softwares.dedup();

    hlm_softwares
        .into_iter()
        .filter(|x| x.system_version.is_some())
        .collect()
}

fn list_software(
    regkey: &RegKey,
    subkey: &str,
    processes: &[String],
) -> Vec<environment::Software> {
    let Ok(uninstall) = regkey.open_subkey_with_flags(subkey, KEY_READ | KEY_WOW64_64KEY) else {
        return Vec::default();
    };

    uninstall
        .enum_keys()
        .filter_map(|key| {
            let Ok(key) = key else {
                return None;
            };

            let Ok(soft_key) = uninstall.open_subkey_with_flags(&key, KEY_READ | KEY_WOW64_64KEY)
            else {
                log::warn!("Failed to open subkey.");
                return None;
            };

            let display_name =
                if let Ok(product_name) = soft_key.get_value::<String, &str>("ProductName") {
                    product_name
                } else if let Ok(display_name) = soft_key.get_value::<String, &str>("DisplayName") {
                    display_name
                } else {
                    let Ok(locale_name) = soft_key.get_value::<String, &str>("LocaleName") else {
                        return None;
                    };
                    locale_name
                };
            let name_exe = match &key.to_lowercase().contains('{') {
                true => display_name.clone(),
                false => key.to_lowercase(),
            };
            let (display_icon, executable_file_location) =
                match soft_key.get_value::<String, &str>("DisplayIcon") {
                    Ok(display_icon) => {
                        let seperator = Regex::new(r"^(.*[\\]+)([^\\]+)\.exe").unwrap();
                        let exe = match seperator.captures_iter(&display_icon).next() {
                            Some(des) => (Some(des[2].to_string()), Some(des[1].to_string())),
                            None => (Some(name_exe), None),
                        };
                        exe
                    }
                    Err(_) => (Some(name_exe), None),
                };
            let display_version = match soft_key.get_value::<String, &str>("DisplayVersion") {
                Ok(display_version) => Some(display_version),
                Err(_) => None,
            };
            let run = &processes
                .iter()
                .any(|e| &e.to_lowercase() == &display_icon.clone().unwrap().to_lowercase());
            let software = environment::Software {
                run: run.to_owned(),
                software_name: display_name,
                exe: display_icon,
                executable_file_location,
                system_version: display_version,
            };
            Some(software)
        })
        .collect::<Vec<environment::Software>>()
}

/// https://mcpforlife.com/2020/04/14/how-to-resolve-this-state-value-of-av-providers/
#[allow(dead_code)]
pub fn antivirus() -> Option<(String, Option<String>)> {
    const PRODUCT_STATE_FLAGS: u32 = 0x0000F000u32;
    const PRODUCT_STATE_ON: u32 = 0x1000u32;
    const DEFENDER_ID: &str = "{D68DDC3A-831F-4fae-9E44-DA132C1ACF46}";

    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let Ok(av) = hklm.open_subkey("SOFTWARE\\Microsoft\\Security Center\\Provider\\Av") else {
        return None;
    };

    av.enum_keys().find_map(|key| {
        if key.is_err() {
            return None;
        }
        let subkey = key.unwrap();
        let Ok(product) = av.open_subkey(&subkey) else {
            return None;
        };
        let Ok(state) = product.get_value::<u32, &str>("STATE") else {
            return None;
        };
        if state & PRODUCT_STATE_FLAGS & PRODUCT_STATE_ON == PRODUCT_STATE_ON {
            let Ok(product_name) = product.get_value::<String, &str>("DISPLAYNAME") else {
                return None;
            };
            if DEFENDER_ID.eq_ignore_ascii_case(&subkey) {
                if let Ok(defender) =
                    hklm.open_subkey("SOFTWARE\\Microsoft\\Windows Defender\\Signature Updates")
                {
                    if let Ok(version) = defender.get_value::<String, &str>("AVSignatureVersion") {
                        return Some((product_name, Some(version)));
                    }
                }
            }

            return Some((product_name, None));
        }
        None
    })
}
