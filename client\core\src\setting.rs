use gm_sm2::key::Sm2PublicKey;
use pkcs8::DecodePublicKey;
use rustls::pki_types::PrivateKeyDer;
use std::{
    collections::HashMap,
    io::{<PERSON><PERSON><PERSON>eader, Cursor},
    path::PathBuf,
};
use tonic::transport::CertificateDer;

use serde::{Deserialize, Serialize};

use encrypt_files::help;
use err_ext::ErrorExt;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Read config error: {0}")]
    ReadErr(String),

    #[error("Save config error: {0}")]
    WriteErr(String),

    #[error("Wrong certificate: {0}")]
    CertificateError(String),

    #[error("Wrong key: {0}")]
    KeyError(String),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Setting {
    /// CA证书. pem 格式
    pub ca: String,
    /// 证书. pem 格式
    pub cert: String,
    /// 私钥. pem 格式
    pub key: String,
    /// TLCP 加密证书
    #[cfg(feature = "tlcp")]
    pub enc_cert: String,
    /// TLCP 加密证书私钥
    #[cfg(feature = "tlcp")]
    pub enc_key: String,
}

impl Setting {
    pub fn load(path: &PathBuf) -> Result<HashMap<String, Self>, Error> {
        help::read_yaml::<HashMap<String, Self>>(path)
            .map_err(|error| Error::ReadErr(error.to_string()))
    }

    /// Save App Config
    #[allow(dead_code)]
    pub fn save_file(&self, path: &PathBuf) -> Result<(), Error> {
        help::save_yaml(path, &self, Some("# SDP Core Config"))
            .map_err(|error| Error::WriteErr(error.to_string()))
    }
}

pub struct DeviceIdentity {
    pub roots: Option<Vec<Vec<u8>>>,
    pub certs: Vec<CertificateDer<'static>>,
    pub key: PrivateKeyDer<'static>,
    #[cfg(feature = "tlcp")]
    pub enc_cert: CertificateDer<'static>,
    #[cfg(feature = "tlcp")]
    pub enc_key: PrivateKeyDer<'static>,
    pub encrypt_pubkey: Sm2PublicKey,
}

impl Clone for DeviceIdentity {
    fn clone(&self) -> Self {
        Self {
            roots: self.roots.clone(),
            certs: self.certs.clone(),
            key: self.key.clone_key(),
            #[cfg(feature = "tlcp")]
            enc_cert: self.enc_cert.clone(),
            #[cfg(feature = "tlcp")]
            enc_key: self.enc_key.clone_key(),
            encrypt_pubkey: self.encrypt_pubkey.clone(),
        }
    }
}

impl TryFrom<Setting> for DeviceIdentity {
    type Error = Error;

    fn try_from(setting: Setting) -> Result<Self, Self::Error> {
        const PUBKEY: &str = include_str!("../resources/pubkey.pem");

        let pubkey = Sm2PublicKey::from_public_key_pem(PUBKEY)
            .map_err(|error| Error::KeyError(error.to_string()))?;

        let root_certs = {
            let mut reader = BufReader::new(Cursor::new(setting.ca.as_bytes()));
            rustls_pemfile::certs(&mut reader)
                .map_err(|error| Error::CertificateError(error.display_chain()))?
        };

        let client_certs = base::load_pem_certs(&setting.cert);
        let client_key = base::load_pem_private_key(&setting.key);

        #[cfg(feature = "tlcp")]
        let mut enc_cert = base::load_pem_certs(&setting.enc_cert);
        #[cfg(feature = "tlcp")]
        let enc_key = base::load_pem_private_key(&setting.enc_key);

        Ok(Self {
            roots: Some(root_certs),
            certs: client_certs,
            key: client_key,
            encrypt_pubkey: pubkey,
            #[cfg(feature = "tlcp")]
            enc_cert: enc_cert.pop().unwrap(),
            #[cfg(feature = "tlcp")]
            enc_key,
        })
    }
}
