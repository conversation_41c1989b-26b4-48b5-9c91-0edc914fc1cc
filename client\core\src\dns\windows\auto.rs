use super::{iphlpapi, netsh, tcpip};
use crate::dns::DnsMonitorT;
use windows_sys::Win32::System::Rpc::RPC_S_SERVER_UNAVAILABLE;

pub struct DnsMonitor {
    current_monitor: InnerMonitor,
}
enum InnerMonitor {
    Iph<PERSON><PERSON><PERSON>(iphlpapi::DnsMonitor),
    Netsh(netsh::DnsMonitor),
    Tcpip(tcpip::DnsMonitor),
}

impl InnerMonitor {
    async fn set(
        &mut self,
        interface: &str,
        servers: &[std::net::IpAddr],
    ) -> Result<(), super::Error> {
        match self {
            InnerMonitor::<PERSON><PERSON><PERSON><PERSON><PERSON>(monitor) => monitor.set(interface, servers).await?,
            InnerMonitor::Netsh(monitor) => monitor.set(interface, servers).await?,
            InnerMonitor::Tcpip(monitor) => monitor.set(interface, servers).await?,
        }
        Ok(())
    }

    async fn reset(&mut self) -> Result<(), super::Error> {
        match self {
            InnerMonitor::<PERSON><PERSON><PERSON><PERSON><PERSON>(monitor) => monitor.reset().await?,
            InnerMonitor::Netsh(monitor) => monitor.reset().await?,
            InnerMonitor::Tcpip(monitor) => monitor.reset().await?,
        }
        Ok(())
    }

    async fn reset_before_interface_removal(&mut self) -> Result<(), super::Error> {
        match self {
            InnerMonitor::Iphlpapi(monitor) => monitor.reset_before_interface_removal().await?,
            InnerMonitor::Netsh(monitor) => monitor.reset_before_interface_removal().await?,
            InnerMonitor::Tcpip(monitor) => monitor.reset_before_interface_removal().await?,
        }
        Ok(())
    }
}

#[async_trait::async_trait]
impl DnsMonitorT for DnsMonitor {
    type Error = super::Error;

    async fn new() -> Result<Self, Self::Error> {
        let current_monitor = if iphlpapi::DnsMonitor::is_supported() {
            InnerMonitor::Iphlpapi(iphlpapi::DnsMonitor::new().await?)
        } else {
            InnerMonitor::Netsh(netsh::DnsMonitor::new().await?)
        };

        Ok(Self { current_monitor })
    }

    async fn set(
        &mut self,
        interface: &str,
        servers: &[std::net::IpAddr],
    ) -> Result<(), Self::Error> {
        let result = self.current_monitor.set(interface, servers).await;
        if self.fallback_due_to_dnscache(&result).await {
            return self.set(interface, servers).await;
        }
        result
    }

    async fn reset(&mut self) -> Result<(), Self::Error> {
        let result = self.current_monitor.reset().await;
        if self.fallback_due_to_dnscache(&result).await {
            return self.reset().await;
        }
        result
    }

    async fn reset_before_interface_removal(&mut self) -> Result<(), Self::Error> {
        let result = self.current_monitor.reset_before_interface_removal().await;
        if self.fallback_due_to_dnscache(&result).await {
            return self.reset_before_interface_removal().await;
        }
        result
    }
}

impl DnsMonitor {
    async fn fallback_due_to_dnscache(&mut self, result: &Result<(), super::Error>) -> bool {
        let is_dnscache_error = match result {
            Err(super::Error::Iphlpapi(iphlpapi::Error::SetInterfaceDnsSettings(error))) => {
                *error == RPC_S_SERVER_UNAVAILABLE
            }
            Err(super::Error::Netsh(netsh::Error::NetshError(Some(1)))) => true,
            _ => false,
        };
        if is_dnscache_error {
            log::warn!("dnscache is not running? Falling back on tcpip method");

            match tcpip::DnsMonitor::new().await {
                Ok(mut tcpip) => {
                    // We need to disable flushing here since it may fail.
                    // Because dnscache is disabled, there's nothing to flush anyhow.
                    tcpip.disable_flushing();
                    self.current_monitor = InnerMonitor::Tcpip(tcpip);
                    true
                }
                Err(error) => {
                    log::error!("Failed to init tcpip DNS module: {error}");
                    false
                }
            }
        } else {
            false
        }
    }
}
