//! Code for migrating between different versions of the settings.
//! Migration only supports migrating forward, to newer formats.
//!
//! A settings migration module is responsible for converting
//! from its own version to the next version. So `v3::migrate`
//! migrates from settings version `V3` to `V4` etc.
//!
//! Migration modules may NOT import and use structs that may
//! change. Because then a later change to the current code can break
//! old migrations. The only items a settings migration module may import
//! are anything from `std`, `serde` and `version`.
//!
//! Any other type must be vendored into the migration module so the format
//! it has is locked over time.
//!
//! There should never be multiple migrations between two official releases. At most one.
//! Between releases, dev builds can break the settings without having a proper migration path.
//!
//! # Creating a migration
//!
//! 1. Copy `vX.rs.template` to `vX.rs` where `X` is the latest settings version right now.
//! 1. Write a comment in the new module about how the format changed, what it needs to migrate.
//! 1. Implement the migration and add adequate tests.
//! 1. Add to the changelog: "Settings format updated to `vY`"

use encrypt_files::help;
use serde_json::Value;
use std::{io, path::Path};

use crate::MAIN_CONFIG;

mod v1;
mod v2;

#[allow(dead_code)]
#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Failed to read the settings: {0}")]
    Read(String),

    #[error("Unexpected settings format")]
    InvalidSettingsContent,

    #[error("Unable to open settings for writing")]
    Open(#[source] io::Error),

    #[error("Unable to write new settings: {0}")]
    Write(String),
}

pub type Result<T> = std::result::Result<T, Error>;

pub async fn migrate_all(settings_dir: &Path) -> Result<()> {
    let path = settings_dir.join(MAIN_CONFIG);
    if !path.is_file() {
        return Ok(());
    }

    let mut config =
        help::read_yaml::<Value>(&path).map_err(|error| Error::Read(error.to_string()))?;

    if !config.is_object() {
        return Err(Error::InvalidSettingsContent);
    }

    let old_config = config.clone();

    v1::migrate(&mut config)?;
    v2::migrate(&mut config)?;

    if config == old_config {
        // Nothing changed
        return Ok(());
    }

    help::save_yaml(&path, &config, Some("# SDP Config"))
        .map_err(|error| Error::Write(error.to_string()))?;

    log::debug!(target: "app", "Migrated settings. Wrote settings to {}", path.display());

    Ok(())
}

fn version_matches(settings: &serde_json::Value, to_version: semver::Version) -> bool {
    settings
        .get("version")
        .and_then(|version| version.as_str())
        .and_then(|version| semver::Version::parse(version).ok())
        .map(|version| to_version > version)
        .unwrap_or(true)
}
