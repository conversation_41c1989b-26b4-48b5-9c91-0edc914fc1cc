use proc_macro2::{Ident, TokenStream};
use quote::quote;
use syn::{spanned::Spanned, <PERSON><PERSON><PERSON>, Fields, GenericArgument, PathArguments, Type};

pub(super) fn expand_struct(fields: &Fields, struct_name: &Ident) -> syn::Result<TokenStream> {
    let mut struct_fields = vec![];
    for field in fields {
        let field_name = field.ident.as_ref().unwrap();

        let is_option = if let Type::Path(type_path) = &field.ty {
            if let Some(first) = type_path.path.segments.first() {
                first.ident == "Option"
            } else {
                false
            }
        } else {
            false
        };

        let token_streams = if let Some(r#type) = crate::get_tlv_value(&field.attrs)? {
            match r#type.as_str() {
                "json" => {
                    if is_option {
                        quote! {
                            #field_name: {
                                if reader.peek(tlv::Tag::RawBytes as u8) {
                                    let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                    Some(serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| tlv::BAD_MESSAGE)?)
                                } else {
                                    None
                                }
                            },
                        }
                    } else {
                        quote! {
                            #field_name: {
                                let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| tlv::BAD_MESSAGE)?
                            },
                        }
                    }
                }
                "hex" => {
                    if is_option {
                        quote! {
                            #field_name: {
                                if reader.peek(tlv::Tag::RawBytes as u8) {
                                    let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                    Some(hex::to_string!(inner.as_slice_less_safe()))
                                } else {
                                    None
                                }
                            },
                        }
                    } else {
                        quote! {
                            #field_name: {
                                let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                hex::to_string!(inner.as_slice_less_safe())
                            },
                        }
                    }
                }
                "sequence" => {
                    if is_option {
                        quote! {
                            #field_name: {
                                if reader.peek(tlv::Tag::Sequence as u8) {
                                    let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::Sequence)?;
                                    let inner_input = untrusted::Input::from(inner.as_slice_less_safe());

                                    Some(inner_input.read_all(tlv::BAD_MESSAGE, |reader| {
                                        let mut inner_data = Default::default();
                                        while !reader.at_end() {
                                            let value = tlv::Deserialize::deserialize(reader)?;
                                            tlv::Sequence::push(&mut inner_data, value);
                                        }
                                        Ok(inner_data)
                                    })?)
                                } else {
                                    None
                                }
                            },
                        }
                    } else {
                        quote! {
                            #field_name: {
                                let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::Sequence)?;
                                let inner_input = untrusted::Input::from(inner.as_slice_less_safe());

                                inner_input.read_all(tlv::BAD_MESSAGE, |reader| {
                                    let mut inner_data = Default::default();
                                    while !reader.at_end() {
                                        let value = tlv::Deserialize::deserialize(reader)?;
                                        tlv::Sequence::push(&mut inner_data, value);
                                    }
                                    Ok(inner_data)
                                })?
                            },
                        }
                    }
                }
                "sequence,json" => {
                    if is_option {
                        quote! {
                            #field_name: {
                                if reader.peek(tlv::Tag::Sequence as u8) {
                                    let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::Sequence)?;
                                    let inner_input = untrusted::Input::from(inner.as_slice_less_safe());

                                    Some(inner_input.read_all(tlv::BAD_MESSAGE, |reader| {
                                        let mut inner_data = Default::default();
                                        while !reader.at_end() {
                                            let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                            let value = serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| tlv::BAD_MESSAGE)?;
                                            tlv::Sequence::push(&mut inner_data, value);
                                        }
                                        Ok(inner_data)
                                    })?)
                                } else {
                                    None
                                }
                            },
                        }
                    } else {
                        quote! {
                            #field_name: {
                                let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::Sequence)?;
                                let inner_input = untrusted::Input::from(inner.as_slice_less_safe());

                                inner_input.read_all(tlv::BAD_MESSAGE, |reader| {
                                    let mut inner_data = Default::default();
                                    while !reader.at_end() {
                                        let inner = tlv::utils::except_tag_and_get_value(reader, tlv::Tag::RawBytes)?;
                                        let value = serde_json::from_slice(inner.as_slice_less_safe()).map_err(|_| tlv::BAD_MESSAGE)?;
                                        tlv::Sequence::push(&mut inner_data, value);
                                    }
                                    Ok(inner_data)
                                })?
                            },
                        }
                    }
                }
                _ => {
                    return Err(Error::new(
                        field.span(),
                        format!("unknown type `{}`", r#type),
                    ));
                }
            }
        } else if is_option {
            let Type::Path(type_path) = &field.ty else {
                return Err(Error::new(field.span(), "unsupported type"));
            };
            let inner_type = type_path.path.segments.first().unwrap();
            let PathArguments::AngleBracketed(generic) = &inner_type.arguments else {
                return Err(Error::new(inner_type.arguments.span(), "unsupported type"));
            };
            let Some(GenericArgument::Type(inner_type)) = generic.args.first() else {
                return Err(Error::new(generic.args.span(), "unsupported type"));
            };
            quote! {
                #field_name: {
                    if #inner_type::FIXED_TAG == tlv::Tag::Unknown {
                        let mut iter = #inner_type::MAYBE_TAGS.iter();
                        loop {
                            match iter.next() {
                                Some(tag) => {
                                    if reader.peek(*tag as u8) {
                                        break Some(tlv::Deserialize::deserialize(reader)?);
                                    }
                                }
                                None => break None,
                            }
                        }
                    } else if reader.peek(#inner_type::FIXED_TAG as u8) {
                        Some(tlv::Deserialize::deserialize(reader)?)
                    } else {
                        None
                    }
                },
            }
        } else {
            quote! {
                #field_name: tlv::Deserialize::deserialize(reader)?,
            }
        };

        struct_fields.push(token_streams);
    }

    let struct_fields = TokenStream::from_iter(struct_fields);

    Ok(quote! {
        impl tlv::Deserialize for #struct_name {
            const FIXED_TAG: tlv::Tag = tlv::Tag::Unknown;

            fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
                Ok(Self {
                    #struct_fields
                })
            }
        }
    })
}
