use std::{net::SocketAddr, sync::Arc};

use base::packet::{Message, MessageType};
use serde_json::Value;
use tracing::{error, warn};

use crate::{
    backend::{client::BackendCommonClient, RestResult},
    cache::CacheManager,
    constants::ONLINE_DEVICES,
};

use super::Error;

pub(super) struct AuthResult {
    pub tenant: String,
    pub device_id: String,
    pub username: String,
    pub token: Vec<u8>,
    pub expire_tip_day: i32,
    pub expire_day: i32,
    pub env: Value,
}

pub(super) async fn auth<'a>(
    peer_addr: SocketAddr,
    mut auth_pkt: Value,
    cache_manager: &CacheManager,
    backend_client: Arc<BackendCommonClient>,
    tx: flume::Sender<super::server::ChannelMessage>,
) -> Result<AuthResult, Error> {
    let ip = peer_addr.ip().to_string();
    let port = peer_addr.port().to_string();

    let mut env = auth_pkt["ENVIRONMENT"].take();
    if !env.is_object() {
        return Err(Error::EnvMissing);
    }

    env["ip"] = Value::String(ip.clone());
    auth_pkt["ip"] = Value::String(ip.clone());
    auth_pkt["mac"] = env["mac"].take();
    auth_pkt["port"] = Value::String(port.clone());
    auth_pkt["deviceName"] = Value::String(
        env["name"]
            .as_str()
            .map(ToOwned::to_owned)
            .ok_or(Error::EnvMissingField("deviceName"))?,
    );

    // 从环境获取设备ID
    let device_id = env["deviceId"]
        .as_str()
        .map(|id| id.to_lowercase())
        .ok_or(Error::EnvMissingField("deviceId"))?;

    auth_pkt["deviceId"] = Value::String(device_id.to_owned());

    let tenant = auth_pkt["tenant"]
        .as_str()
        .ok_or(Error::EnvMissingField("tenant"))?;

    let username = auth_pkt["username"]
        .as_str()
        .ok_or(Error::EnvMissingField("username"))?;

    // 设备是否在线
    if let Some(true) = cache_manager
        .exists(&format!("{tenant}|{}{}", ONLINE_DEVICES, &device_id))
        .await
    {
        warn!(device.id = %device_id, "device is already online.");
    }
    // 认证方式
    let result = match auth_pkt["type"].as_str() {
        Some("mfa") => {
            let data = serde_json::to_vec(&auth_pkt).unwrap();
            backend_client.auth_mfa(tenant, data, &ip, &env).await
        }
        Some("link") => {
            let data = serde_json::to_vec(&auth_pkt).unwrap();
            backend_client.reauth(tenant, data, &ip, &env).await
        }
        Some("sms") => {
            let data = serde_json::to_vec(&auth_pkt).unwrap();
            backend_client.auth_sms(tenant, data, &ip, &env).await
        }
        _ => {
            let data = serde_json::to_vec(&auth_pkt).unwrap();
            backend_client.auth(tenant, data, &ip, &env).await
        }
    };

    match result {
        Ok(result) => {
            let expire_tip_day = if let Some(v) = result["expireTipDay"].as_i64() {
                v as i32
            } else {
                i32::MAX
            };
            let expire_day = if let Some(v) = result["expireDay"].as_i64() {
                v as i32
            } else {
                i32::MAX
            };

            let token = match result["type"].as_str().unwrap() {
                // 需要强制修改密码
                "NEED_FORCE_PASSWORD"
                | "PASSWORD_EXPIRED"
                | "NEED_FORCE_PASSWORD_EXPIRED"
                | "NEED_FORCE_PASSWORD_POLICY" => {
                    let mut result = RestResult::ok(result).as_bytes().to_vec();
                    tlv::utils::insert_tag(&mut result, tlv::Tag::UTF8String);
                    let message = Message::new(MessageType::NeedChangePwd, &result);

                    let (callback_tx, rx) = tokio::sync::oneshot::channel();
                    _ = tx
                        .send_async(super::server::ChannelMessage::Message {
                            message,
                            callback: Some(callback_tx),
                        })
                        .await;
                    _ = rx.await;
                    return Err(Error::NeedChangePwd);
                }
                ty => {
                    if let Some(token) = result["token"].as_str() {
                        token.to_string()
                    } else {
                        error!("No token received, authentication failed. {}", ty);
                        return Err(Error::AuthenticationFailed);
                    }
                }
            };

            Ok(AuthResult {
                tenant: tenant.to_owned(),
                device_id,
                username: username.to_owned(),
                token: hex::decode!(token.as_bytes()),
                expire_tip_day,
                expire_day,
                env,
            })
        }
        Err(mut buf) => {
            error!("backend auth failed.");
            tlv::utils::insert_tag(&mut buf, tlv::Tag::UTF8String);
            let message = Message::new(MessageType::AuthFailed, &buf);
            let (callback_tx, rx) = tokio::sync::oneshot::channel();
            _ = tx
                .send_async(super::server::ChannelMessage::Message {
                    message,
                    callback: Some(callback_tx),
                })
                .await;
            _ = rx.await;
            Err(Error::AuthenticationFailed)
        }
    }
}
