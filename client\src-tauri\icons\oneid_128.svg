<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="128" height="128" viewBox="0 0 128 128">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="0.387" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#f1f1f1"/>
    </linearGradient>
    <clipPath id="clip-path">
      <path id="路径_2463" data-name="路径 2463" d="M15.326,0H91.954a15.326,15.326,0,0,1,15.326,15.326V91.954a15.326,15.326,0,0,1-15.326,15.326H15.326A15.326,15.326,0,0,1,0,91.954V15.326A15.326,15.326,0,0,1,15.326,0Z" fill="#f87c10"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="-0.027" x2="0.569" y2="0.699" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" y1="-0.027" x2="0.569" y2="0.699" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.49"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <clipPath id="clip-path-2">
      <path id="路径_2482" data-name="路径 2482" d="M14.848,76H89.091a14.912,14.912,0,0,1,14.848,14.975V103.61a14.912,14.912,0,0,1-14.848,14.975H14.848A14.912,14.912,0,0,1,0,103.61V90.975A14.912,14.912,0,0,1,14.848,76Z" transform="translate(0 -76)" fill="url(#linear-gradient)"/>
    </clipPath>
  </defs>
  <g id="_128" data-name="128" transform="translate(6058 -9465)">
    <rect id="矩形_2032" data-name="矩形 2032" width="128" height="128" transform="translate(-6058 9465)" fill="none"/>
    <g id="组_33011" data-name="组 33011" transform="translate(614.36 878.36)">
      <g id="组_32266" data-name="组 32266" transform="translate(-6662 8597)">
        <path id="路径_2458" data-name="路径 2458" d="M15.326,0H91.954a15.326,15.326,0,0,1,15.326,15.326V91.954a15.326,15.326,0,0,1-15.326,15.326H15.326A15.326,15.326,0,0,1,0,91.954V15.326A15.326,15.326,0,0,1,15.326,0Z" fill="#f87c10"/>
        <g id="组_32" data-name="组 32" clip-path="url(#clip-path)">
          <path id="路径_2459" data-name="路径 2459" d="M40.429,0C62.757,0,80.858,15.507,80.858,34.636s-18.1,34.636-40.429,34.636S0,53.765,0,34.636,18.1,0,40.429,0Z" transform="matrix(0.719, -0.695, 0.695, 0.719, -42.501, 92.622)" opacity="0.19" fill="url(#linear-gradient-2)"/>
          <path id="路径_2460" data-name="路径 2460" d="M77.839,0c42.989,0,77.839,29.856,77.839,66.686s-34.85,66.686-77.839,66.686S0,103.515,0,66.686,34.85,0,77.839,0Z" transform="translate(183.097 179.54) rotate(-155)" opacity="0.51" fill="url(#linear-gradient-2)"/>
          <path id="路径_2462" data-name="路径 2462" d="M77.839,0c42.989,0,77.839,29.856,77.839,66.686s-34.85,66.686-77.839,66.686S0,103.515,0,66.686,34.85,0,77.839,0Z" transform="translate(107.279 -99.178) rotate(101)" opacity="0.51" fill="url(#linear-gradient-4)"/>
        </g>
        <g id="图层_1" data-name="图层 1" transform="translate(32.606 13.384)">
          <path id="路径_2381" data-name="路径 2381" d="M93.58,476.215a19.206,19.206,0,0,1-6.857,1.894,17.435,17.435,0,0,1,1.041,4.859,28.226,28.226,0,0,1,.166,2.847l0,0a2.863,2.863,0,0,1-3,2.875l-5.248-.03V477.529a22.431,22.431,0,0,1-5.14-2.131v32.929s0,.823,0,.823v5.763a34.414,34.414,0,0,0,5.138-2.694V512.2l0,0v-6.784h0V492.922l4.345.024a3.514,3.514,0,0,0,.563.042c2.4-.017,2.3,1.579,2.178,2.17-.185.728-.391,1.473-.635,2.239a16,16,0,0,1-2.274,4.5v7.115A28.938,28.938,0,0,0,91.484,498.4,40.91,40.91,0,0,0,93.58,476.215Z" transform="translate(-53.175 -462.667)" fill="#fff"/>
          <path id="路径_2382" data-name="路径 2382" d="M8.375,554.492h0v12.493a10.107,10.107,0,0,1-2.641-1.653c-.243-.256-.48-.526-.714-.8a13.9,13.9,0,0,1-.989-1.158q-2.25.9-4.679,1.737-.56-.818-1.078-1.7a30.6,30.6,0,0,0,7.46,8.476v-.021c.952.763,1.936,1.442,2.642,1.908v.019a34.52,34.52,0,0,0,4.934,2.694l0-6.061-.009,0V559.068q-2.331,1.252-4.924,2.444Z" transform="translate(6.195 -524.237)" fill="#fff"/>
          <path id="路径_2383" data-name="路径 2383" d="M43.763,489.293V478.165a20.153,20.153,0,0,0,4.736-1.95,20.155,20.155,0,0,1-4.736,1.95v11.129h0Z" transform="translate(-29.193 -463.302)" fill="#fff"/>
          <path id="路径_2384" data-name="路径 2384" d="M52.086,548.514Z" transform="translate(-35.679 -519.583)" fill="#fff"/>
          <path id="路径_2385" data-name="路径 2385" d="M66.092,519.054h0V503.1s.009,0,.011-.006a.122.122,0,0,0-.011.006Z" transform="translate(-46.598 -484.223)" fill="#fff"/>
          <path id="路径_2386" data-name="路径 2386" d="M136.381,479.084a19.213,19.213,0,0,1-6.858,1.893,17.383,17.383,0,0,1,1.041,4.859,28.1,28.1,0,0,1,.166,2.847l0,0a3.394,3.394,0,0,1-.189,1,55.082,55.082,0,0,0,6.343-7.051A30.137,30.137,0,0,0,136.381,479.084Z" transform="translate(-95.975 -465.536)" fill="#fff"/>
          <path id="路径_2387" data-name="路径 2387" d="M79.688,488.657V477.529a22.419,22.419,0,0,1-5.139-2.131v20.581c1.758-1.011,3.613-1.687,5.14-2.729v-.328l.474,0a67.239,67.239,0,0,0,5.594-4.324,3.871,3.871,0,0,1-.819.087Z" transform="translate(-53.18 -462.667)" fill="#fff"/>
          <path id="路径_2388" data-name="路径 2388" d="M-11.67,504.671C-17.811,496.655-15,482.884-15,482.884l.043.028a11.9,11.9,0,0,1,.594-1.932,19.215,19.215,0,0,1-6.858-1.893,40.913,40.913,0,0,0,2.1,22.188c.016.04.033.078.05.117q.153.369.315.729c.05.111.1.22.151.329.081.175.165.348.249.519.056.115.112.23.169.344q.175.344.359.679c.088.162.178.32.269.478l.137.238q.517.883,1.078,1.7Q-13.918,505.571-11.67,504.671Z" transform="translate(21.896 -465.538)" fill="#fff"/>
          <path id="路径_2389" data-name="路径 2389" d="M43.763,477.755v11.129h-.029v4.264h.029v7.02q2.595-1.189,4.924-2.444V481.766s.009,0,.011-.006v-6.071l-.2.117A20.152,20.152,0,0,1,43.763,477.755Z" transform="translate(-29.193 -462.893)" fill="#fff"/>
          <path id="路径_2390" data-name="路径 2390" d="M43.763,477.755v11.129h-.029v4.264h.029v7.02q2.595-1.189,4.924-2.444V481.766s.009,0,.011-.006v-6.071l-.2.117A20.152,20.152,0,0,1,43.763,477.755Z" transform="translate(-29.193 -462.893)" fill="#fff"/>
          <path id="路径_2391" data-name="路径 2391" d="M48.683,562.655q-.063-.094-.124-.191Q48.62,562.562,48.683,562.655Z" transform="translate(-32.949 -530.443)" fill="#fff"/>
          <path id="路径_2392" data-name="路径 2392" d="M47.615,560.861q-.087-.144-.169-.3Q47.528,560.716,47.615,560.861Z" transform="translate(-32.083 -528.965)" fill="#fff"/>
          <path id="路径_2393" data-name="路径 2393" d="M49.326,563.876a6.539,6.539,0,0,0,.457.541c-.146-.176-.288-.36-.423-.554Z" transform="translate(-33.546 -531.532)" fill="#fff"/>
          <path id="路径_2394" data-name="路径 2394" d="M46.715,559.114c-.046-.09-.091-.18-.135-.273Q46.645,558.98,46.715,559.114Z" transform="translate(-31.409 -527.623)" fill="#fff"/>
          <path id="路径_2395" data-name="路径 2395" d="M45.105,555.125c-.044-.118-.087-.238-.126-.361C45.018,554.887,45.061,555.007,45.105,555.125Z" transform="translate(-30.162 -524.448)" fill="#fff"/>
          <path id="路径_2396" data-name="路径 2396" d="M45.878,557.221c-.053-.116-.1-.234-.149-.355C45.776,556.987,45.826,557.105,45.878,557.221Z" transform="translate(-30.746 -526.085)" fill="#fff"/>
          <path id="路径_2397" data-name="路径 2397" d="M49.118,563.327c.026.04.052.08.079.119h0C49.171,563.407,49.144,563.367,49.118,563.327Z" transform="translate(-33.384 -531.115)" fill="#fff"/>
          <path id="路径_2398" data-name="路径 2398" d="M48.286,562.023c-.026-.042-.052-.083-.078-.125C48.233,561.941,48.26,561.982,48.286,562.023Z" transform="translate(-32.676 -530.002)" fill="#fff"/>
          <path id="路径_2399" data-name="路径 2399" d="M47.245,560.182c-.019-.036-.038-.073-.057-.109C47.207,560.108,47.226,560.146,47.245,560.182Z" transform="translate(-31.882 -528.581)" fill="#fff"/>
          <path id="路径_2400" data-name="路径 2400" d="M45.589,556.5c-.014-.035-.027-.069-.04-.1C45.562,556.43,45.575,556.465,45.589,556.5Z" transform="translate(-30.606 -525.718)" fill="#fff"/>
          <path id="路径_2401" data-name="路径 2401" d="M46.441,558.549c-.013-.028-.027-.054-.039-.083C46.415,558.495,46.428,558.522,46.441,558.549Z" transform="translate(-31.27 -527.331)" fill="#fff"/>
          <path id="路径_2402" data-name="路径 2402" d="M83.392,478.131a20.809,20.809,0,0,1-8.844-2.735v14a32.552,32.552,0,0,0,5.08-1.727V483.1c.277.024.559.04.851.04a51.859,51.859,0,0,0,7.582-.789c1.662-1.626,1.495-3.083,2.765-5.047A21.076,21.076,0,0,1,83.392,478.131Z" transform="translate(-53.18 -462.665)" fill="#fff"/>
          <path id="路径_2403" data-name="路径 2403" d="M34.649,417.994a10.519,10.519,0,0,1,9.275,11.573c-1.106-5.024-8.661-8.643-12.043-4.849,1.875-.653,2.45.693,2.381,1.374a4.856,4.856,0,0,1-2.887,3.1c-4.02,1.7-9.206-.495-7.411-4.89a10.438,10.438,0,0,1,10.684-6.31Z" transform="translate(-13.519 -417.934)" fill="#fff" fill-rule="evenodd"/>
        </g>
        <g id="蒙版组_35" data-name="蒙版组 35" transform="translate(1.67 63.025)" clip-path="url(#clip-path-2)">
          <path id="路径_2457" data-name="路径 2457" d="M75.415,0c41.651,0,75.415,20.113,75.415,44.924S117.066,89.848,75.415,89.848,0,69.735,0,44.924,33.765,0,75.415,0Z" transform="translate(-23.446 8.891)" fill="#fff"/>
        </g>
      </g>
      <g id="组_32240" data-name="组 32240" transform="translate(-6638.245 8680.291)">
        <g id="组_2446" data-name="组 2446" transform="translate(41.435 0)">
          <path id="路径_2237" data-name="路径 2237" d="M123.088,235.271V221.664c3.116.238,6.211.29,9.225.811a4.387,4.387,0,0,1,2.552,2.423,9.819,9.819,0,0,1,.053,7.589,3.817,3.817,0,0,1-3.382,2.746C128.784,235.37,126.019,235.271,123.088,235.271Zm2.709-11.1c0,2.854-.013,5.428.017,8a1.259,1.259,0,0,0,.372.9c.927.668,4.83-.018,5.453-1.021a6.456,6.456,0,0,0,.393-6.085c-.752-1.872-2.421-1.783-4.023-1.8C127.368,224.165,126.728,224.17,125.8,224.17Z" transform="translate(-117.32 -221.664)" fill="#f87c10"/>
          <path id="路径_2240" data-name="路径 2240" d="M115.655,235.457h-2.647V222.125l2.647-.18Z" transform="translate(-113.008 -221.783)" fill="#f87c10"/>
        </g>
        <g id="组_2445" data-name="组 2445" transform="translate(0 0)">
          <path id="路径_2238" data-name="路径 2238" d="M51.245,228.958c.034,4.546-2.147,6.908-6.413,6.947-4.731.043-7.15-2.306-7.226-7.018-.074-4.58,2.134-6.918,6.588-6.977C49.01,221.848,51.209,224.044,51.245,228.958Zm-3.069-.171c-.035-2.779-.926-4.256-2.805-4.651a3.5,3.5,0,0,0-4.436,2.614c-.841,3.027.133,6.121,2.133,6.766a3.832,3.832,0,0,0,.529.111C46.6,234.136,48.222,232.425,48.176,228.787Z" transform="translate(-37.604 -221.909)" fill="#f87c10"/>
          <path id="路径_2270" data-name="路径 2270" d="M374.225-127.164c.149,1.033.558,1.607,1.432,1.75a3.46,3.46,0,0,0,2.886-.688,1.836,1.836,0,0,1,1.37-.419,2.339,2.339,0,0,1,1.341,1.023c.156.289-.118.984-.408,1.311a5.413,5.413,0,0,1-4.1,1.532,5.126,5.126,0,0,1-5.349-3.207,5.72,5.72,0,0,1,1.933-6.605,5.652,5.652,0,0,1,8.332,2.594c.667,1.791.042,2.708-1.849,2.708Zm3.993-2.521c-1.068-1.258-2.957-1.206-3.46,0Z" transform="translate(-343.73 135.02)" fill="#f87c10"/>
          <path id="路径_2271" data-name="路径 2271" d="M315.942-126.22c0,.9.013,1.795,0,2.691-.017,1.04-.439,1.458-1.452,1.481-1.2.027-1.6-.266-1.647-1.347-.062-1.471-.042-2.946-.07-4.418-.027-1.428-.633-2.064-1.937-2.056-1.485.009-2.209.7-2.222,2.138s.009,2.884-.008,4.325c-.012,1.012-.46,1.434-1.485,1.463-1.09.03-1.561-.306-1.582-1.334-.054-2.626-.074-5.253-.043-7.879.014-1.2.9-1.739,2.077-1.209a3.324,3.324,0,0,0,2,.061,11.434,11.434,0,0,1,3.666,0,3.271,3.271,0,0,1,2.694,3.489C315.96-127.951,315.941-127.085,315.942-126.22Z" transform="translate(-290.153 134.19)" fill="#f87c10"/>
        </g>
      </g>
    </g>
  </g>
</svg>
