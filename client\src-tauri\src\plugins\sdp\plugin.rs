use tauri::{
    plugin::{<PERSON>uild<PERSON>, TauriPlugin},
    Runtime,
};

pub fn init<R: Runtime>() -> TauriPlugin<R> {
    Builder::new("sdp")
        .invoke_handler(tauri::generate_handler![
            super::node::set_nodes,
            super::auth::login,
            super::auth::proxy_ready,
            super::auth::logout,
            super::password::change_password_by_code,
            super::password::change_password,
            super::mfa::pop_mfa_event,
            super::mfa::auth_complete,
            super::mfa::cancel_auth,
            super::login::ticket,
            super::proxy::http_request,
        ])
        .build()
}
