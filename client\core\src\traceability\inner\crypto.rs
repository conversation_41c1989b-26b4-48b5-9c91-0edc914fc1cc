use once_cell::sync::Lazy;
use std::{
    collections::{HashMap, HashSet},
    net::IpAddr,
    sync::Arc,
};

use rustls::ServerConfig;
use tlv_types::{BackendIp, WebApp};
use tokio::sync::RwLock;

use crate::traceability::utils::cert;

/// https证书缓存
pub static SNI_SERVER_CRYPTO_MAP: Lazy<RwLock<HashMap<String, Arc<ServerConfig>>>> =
    Lazy::new(|| RwLock::new(Default::default()));

/// WEB应用列表
pub static WEB_APPS: Lazy<RwLock<HashSet<WebApp>>> = Lazy::new(|| RwLock::new(Default::default()));

/// 添加一个Web应用
pub async fn add_app(app: WebApp) {
    log::debug!("Add web app: {:?}", &app);
    let webapps = WEB_APPS.read().await;
    if webapps.contains(&app) {
        log::warn!("Duplicate application access address");
        return;
    }
    drop(webapps);

    let mut webapps = WEB_APPS.write().await;
    webapps.insert(app.clone());
    drop(webapps);
    if app.url.scheme() != "https" {
        return;
    }

    let Some(host) = app.url.host_str() else {
        log::warn!("no host: {}", app.url);
        return;
    };

    let sni_map = SNI_SERVER_CRYPTO_MAP.read().await;
    if sni_map.contains_key(host) {
        log::warn!("A certificate with host `{host}` already exists");
        return;
    }
    drop(sni_map);

    let Some(server_config) = cert::generate(host) else {
        return;
    };

    log::debug!("Host `{host}` certificate generated successfully");
    let mut sni_map = SNI_SERVER_CRYPTO_MAP.write().await;
    sni_map.insert(host.to_owned(), server_config);
}

/// 删除应用
pub async fn remove_app(app: WebApp) {
    log::debug!("Del web app: {:?}", &app);
    let mut webapps = WEB_APPS.write().await;
    webapps.remove(&app);
}

/// 获取访问地址的scheme
pub async fn get_http_scheme(ip: IpAddr, port: u16) -> Option<&'static str> {
    let webapps = WEB_APPS.read().await;
    let Some(Some(scheme)) = webapps
        .iter()
        .find(|app| {
            let Some(p) = app.url.port_or_known_default() else {
                return false;
            };

            if p != port {
                return false;
            }

            if let Some(ips) = app.ips.as_ref() {
                if ips.contains(&BackendIp { ip }) {
                    return true;
                }
            }

            if let Some(ranges) = app.ranges.as_ref() {
                if ranges.iter().any(|range| range.contains(&ip)) {
                    return true;
                }
            }

            if let Some(cidrs) = app.cidrs.as_ref() {
                if cidrs.iter().any(|cidr| cidr.contains(&ip)) {
                    return true;
                }
            }

            false
        })
        .map(|app| {
            let scheme = app.url.scheme();
            let scheme = match scheme {
                "http" => "http",
                "https" => "https",
                _ => return None,
            };
            Some(scheme)
        })
    else {
        return None;
    };
    Some(scheme)
}

pub(super) async fn find_tls_config(
    server_name: Option<&str>,
    ip: IpAddr,
    port: u16,
) -> Option<Arc<ServerConfig>> {
    let sni_map = SNI_SERVER_CRYPTO_MAP.read().await;
    if let Some(server_name) = server_name {
        return sni_map.get(server_name).cloned();
    }

    let webapps = WEB_APPS.read().await;
    let Some(Some(host)) = webapps
        .iter()
        .find(|app| {
            let Some(p) = app.url.port_or_known_default() else {
                return false;
            };

            if p != port {
                return false;
            }

            if let Some(ips) = app.ips.as_ref() {
                if ips.contains(&BackendIp { ip }) {
                    return true;
                }
            }

            if let Some(ranges) = app.ranges.as_ref() {
                if ranges.iter().any(|range| range.contains(&ip)) {
                    return true;
                }
            }

            if let Some(cidrs) = app.cidrs.as_ref() {
                if cidrs.iter().any(|cidr| cidr.contains(&ip)) {
                    return true;
                }
            }

            false
        })
        .map(|app| {
            let Some(host) = app.url.host_str() else {
                return None;
            };
            Some(host.to_owned())
        })
    else {
        return None;
    };

    sni_map.get(&host).cloned()
}
