#[cfg(target_os = "windows")]
mod win;

#[cfg(target_os = "windows")]
pub use win::*;

#[cfg(target_os = "linux")]
mod linux;

#[cfg(target_os = "linux")]
pub use linux::*;

#[cfg(target_os = "macos")]
mod macos;

#[cfg(target_os = "macos")]
pub use macos::*;

pub mod interface;
pub mod ip;
pub mod running;

#[allow(dead_code)]
const UNKNOWN_UUID: &str = "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF";

use serde::{Deserialize, Serialize};

/// 安装软件信息
#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Software {
    pub run: bool,
    pub software_name: String,
    pub system_version: Option<String>,
    pub executable_file_location: Option<String>,
    pub exe: Option<String>,
}

#[cfg(test)]
mod tests {
    use sysinfo::{Components, Disks, Networks, System};

    use crate::environment::{firewall, interface, running, software, system};

    #[cfg(not(target_os = "macos"))]
    use super::wifi;
    // sudo cargo test test_env -- --show-output

    #[test]
    fn test_env() {
        let mut sys = System::new_all();
        // First we update all information of our `System` struct.
        sys.refresh_all();

        // We display all disks' information:
        println!("=> disks:");
        let disks = Disks::new_with_refreshed_list();
        for disk in &disks {
            println!("{disk:?}");
        }

        // Network interfaces name, total data received and total data transmitted:
        let networks = Networks::new_with_refreshed_list();
        println!("=> networks:");
        for (interface_name, data) in &networks {
            println!(
                "{interface_name}: {} B (down) / {} B (up)",
                data.total_received(),
                data.total_transmitted(),
            );
            // If you want the amount of data received/transmitted since last call
            // to `Networks::refresh`, use `received`/`transmitted`.
        }

        // Components temperature:
        let components = Components::new_with_refreshed_list();
        println!("=> components:");
        for component in &components {
            println!("{component:?}");
        }

        println!("=> system:");
        // RAM and swap information:
        println!("total memory: {} bytes", sys.total_memory());
        println!("used memory : {} bytes", sys.used_memory());
        println!("total swap  : {} bytes", sys.total_swap());
        println!("used swap   : {} bytes", sys.used_swap());

        // Display system information:
        println!("System name:             {:?}", System::name());
        println!("System kernel version:   {:?}", System::kernel_version());
        println!("System OS version:       {:?}", System::os_version());
        println!("System host name:        {:?}", System::host_name());

        // Number of CPUs:
        println!("NB CPUs: {}", sys.cpus().len());

        // Display processes ID, name na disk usage:
        for (pid, process) in sys.processes() {
            println!("[{pid}] {:?} {:?}", process.name(), process.disk_usage());
        }
    }

    #[test]
    fn test_read_env() {
        let device_id = uniqueid::device_id().unwrap();
        let env = system::info(&device_id);
        println!("{env:#?}");

        println!("{}", base::generate_ua(&env));
    }

    #[cfg(not(target_os = "macos"))]
    #[test]
    fn test_connected_wifi() {
        let names = wifi::network_ssid();
        println!("WiFi names: {names:?}");
    }

    #[test]
    fn test_interfaces() {
        let interfaces = interface::list();
        println!("Interfaces: {interfaces:#?}");
    }

    #[test]
    fn test_running() {
        let running = running::processes(true);
        println!("{running:#?}");
    }

    #[test]
    fn test_software_list() {
        let software_list = software::list();
        println!("{software_list:#?}");
    }

    #[test]
    fn test_firewall_state() {
        println!("firewall state: {:?}", firewall::is_enable());
    }
}
