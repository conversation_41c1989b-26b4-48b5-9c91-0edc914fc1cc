use core::fmt;
use serde::{Deserialize, Serialize};

/// Event emitted from the states in `oneidcore::tunnel_state_machine` when the tunnel state
/// machine enters a new state.
#[derive(<PERSON>lone, Debug)]
pub enum TunnelStateTransition {
    /// No connection is established and network is unsecured.
    Disconnected,
    /// Network is secured but tunnel is still connecting.
    Connecting,
    /// Tunnel is connected.
    Connected,
    /// Disconnecting tunnel.
    Disconnecting(ActionAfterDisconnect),
    /// Tunnel is disconnected but usually secured by blocking all connections.
    Error(ErrorState),
}

/// Action that will be taken after disconnection is complete.
#[derive(<PERSON>lone, Copy, Debug, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "net.sdp.tunnel"))]
pub enum ActionAfterDisconnect {
    Nothing,
    Block,
    Reconnect,
}

/// Represents the tunnel state machine entering an error state during a [`TunnelStateTransition`].
#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "net.sdp.tunnel"))]
pub struct ErrorState {
    /// Reason why the tunnel state machine ended up in the error state
    cause: ErrorStateCause,
}

impl ErrorState {
    pub fn new(cause: ErrorStateCause) -> Self {
        Self { cause }
    }

    pub fn cause(&self) -> &ErrorStateCause {
        &self.cause
    }
}

/// Reason for the tunnel state machine entering an [`ErrorState`].
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
#[serde(tag = "reason", content = "details")]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "net.sdp.tunnel"))]
pub enum ErrorStateCause {
    /// Failed to configure IPv6 because it's disabled in the platform.
    Ipv6Unavailable,
    /// Failed to set system DNS server.
    SetDnsError,
    SetRouteError,
    // /// Android has rejected one or more DNS server addresses.
    // #[cfg(target_os = "android")]
    // InvalidDnsServers(Vec<IpAddr>),
    /// Failed to start connection to remote server.
    StartTunnelError,
    /// Tunnel parameter missing
    TunnelParameterMissing,
    /// This device is offline, no tunnels can be established.
    IsOffline,
}

impl ErrorStateCause {
    #[cfg(target_os = "macos")]
    pub fn prevents_filtering_resolver(&self) -> bool {
        matches!(self, Self::SetDnsError)
    }
}

impl fmt::Display for ErrorStateCause {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        use self::ErrorStateCause::*;
        let description = match *self {
            Ipv6Unavailable => "Failed to configure IPv6 because it's disabled in the platform",
            SetDnsError => "Failed to set system DNS server",
            SetRouteError => "Failed to set routes",
            #[cfg(target_os = "android")]
            InvalidDnsServers(ref addresses) => {
                return write!(
                    f,
                    "Invalid DNS server addresses used in tunnel configuration: {}",
                    addresses
                        .iter()
                        .map(IpAddr::to_string)
                        .collect::<Vec<_>>()
                        .join(", ")
                );
            }
            StartTunnelError => "Failed to start connection to remote server",
            TunnelParameterMissing => "Tunnel parameter missing",
            IsOffline => "This device is offline, no tunnels can be established",
        };

        write!(f, "{description}")
    }
}
