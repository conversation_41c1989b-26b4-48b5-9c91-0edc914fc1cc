use std::fs;

use time::{macros::format_description, UtcOffset};
use tracing_appender::rolling;
use tracing_subscriber::{
    fmt::{self, time::OffsetTime},
    prelude::__tracing_subscriber_SubscriberExt,
    util::Subscriber<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Env<PERSON><PERSON><PERSON>, <PERSON><PERSON>,
};

use err_ext::ErrorExt;

use crate::cli::Mode;

/// initialize this instance's log file
pub fn init_logger(filter: &str, mode: Mode) -> Result<(), String> {
    let log_filter = EnvFilter::new(filter);

    let log_dir =
        paths::log_dir().map_err(|e| e.display_chain_with_msg("Unable to initialize logger"))?;
    if !log_dir.exists() {
        _ = fs::create_dir_all(&log_dir);
    }
    let timer = OffsetTime::new(
        UtcOffset::from_hms(8, 0, 0).unwrap(),
        format_description!("[year]-[month]-[day] [hour]:[minute]:[second].[subsecond digits:3]"),
    );

    match mode {
        Mode::Standalone => {
            #[cfg(feature = "trace")]
            let console_layer = console_subscriber::spawn();

            let log_appender = rolling::daily(log_dir.as_path(), "core");
            let log_layer = fmt::layer()
                .compact()
                .with_timer(timer)
                .with_line_number(false)
                .with_file(false)
                .with_thread_ids(false)
                .with_thread_names(false)
                .with_target(false)
                .with_ansi(false)
                .with_writer(log_appender);

            #[cfg(not(debug_assertions))]
            let log_layer = log_layer
                .with_line_number(false)
                .with_file(false)
                .with_target(false)
                .with_ansi(false);

            let log_layer = log_layer.with_filter(log_filter);

            #[cfg(feature = "trace")]
            tracing_subscriber::registry()
                .with(log_layer)
                .with(console_layer)
                .init();

            #[cfg(not(feature = "trace"))]
            tracing_subscriber::registry().with(log_layer).init();
        }
        Mode::Embed => {
            #[cfg(feature = "trace")]
            let console_layer = console_subscriber::spawn();
            let log_layer = fmt::layer()
                .compact()
                .with_file(true)
                .with_line_number(true)
                .with_target(true)
                .with_ansi(false)
                .without_time();
            #[cfg(not(debug_assertions))]
            let log_layer = log_layer
                .with_file(false)
                .with_line_number(false)
                .with_target(false)
                .with_ansi(false)
                .without_time();

            let log_layer = log_layer.with_filter(log_filter);

            #[cfg(feature = "trace")]
            tracing_subscriber::registry()
                .with(log_layer)
                .with(console_layer)
                .init();

            #[cfg(not(feature = "trace"))]
            tracing_subscriber::registry().with(log_layer).init();
        }
    }
    log_panics::init();
    exception_logging::enable();
    log_version();

    Ok(())
}

/// Contains the date of the git commit this was built from
const COMMIT_DATE: &str = include_str!(concat!(env!("OUT_DIR"), "/git-commit-date.txt"));

fn log_version() {
    #[cfg(not(feature = "tlcp"))]
    let tlcp = "";
    #[cfg(feature = "tlcp")]
    let tlcp = " With TLCP";
    log::info!(
        "Starting {} - {} {}{}",
        env!("CARGO_PKG_NAME"),
        version::FILE_VERSION,
        COMMIT_DATE,
        tlcp,
    )
}
