#![no_std]

#[cfg(feature = "user")]
use aya::Pod;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>)]
#[repr(C)]
pub struct Addr(u32, u32);

#[cfg(feature = "user")]
unsafe impl Pod for Addr {}

impl Addr {
    pub fn new(ip: u32, port: u16) -> Self {
        Self(ip, port as u32)
    }
}

/// 创建一个新元素或者更新一个已有的
pub const BPF_ANY: u64 = 0;
/// 只在元素不存在的情况下创建一个新的元素
pub const BPF_NOEXIST: u64 = 1;
/// 更新一个已经存在的元素
pub const BPF_EXIST: u64 = 2;
