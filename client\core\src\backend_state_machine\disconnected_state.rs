use futures::stream::StreamExt;

use types::backend::BackendStateTransition;

use crate::{
    backend_state_machine::{
        connecting_state::ConnectingState, proxy_connecting_state::ProxyConnectingState,
        BackendCommand, BackendCommandReceiver, BackendState, BackendStateWrapper,
        SharedBackendStateValues,
    },
    EventListener,
};

use super::EventConsequence;

pub struct DisconnectedState;

#[async_trait::async_trait]
impl<L> BackendState<L> for DisconnectedState
where
    L: EventListener + Send + Clone + 'static,
{
    type Bootstrap = Option<i8>;

    async fn enter(
        _shared_values: &mut SharedBackendStateValues<L>,
        args: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition) {
        (
            BackendStateWrapper::from(DisconnectedState),
            BackendStateTransition::Disconnected(args),
        )
    }

    async fn handle_event(
        self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match commands.next().await {
            Some(BackendCommand::Disconnect(tx)) => {
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                SameState(self.into())
            }
            Some(BackendCommand::ProxyConnect {
                tx,
                connection_config,
            }) => {
                NewState(ProxyConnectingState::enter(shared_values, (tx, connection_config)).await)
            }
            Some(BackendCommand::ChangePwdByCode(tx, system_info, message)) => {
                shared_values
                    .change_password_by_verify_code(tx, system_info, message)
                    .await;
                SameState(self.into())
            }
            Some(BackendCommand::Login {
                tx,
                system_info,
                payload,
            }) => NewState(ConnectingState::enter(shared_values, (tx, system_info, payload)).await),
            None => Finished,
            Some(_) => SameState(self.into()),
        }
    }
}
