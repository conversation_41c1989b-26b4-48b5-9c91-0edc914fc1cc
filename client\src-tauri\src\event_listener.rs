use std::time::Duration;

use chrono::Local;
use communicate_interface::{
    types::communicate_service_client::CommunicateServiceClient, Channel, ControlServiceClient,
};
use serde_json::json;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Wry};
use tokio::sync::{Mutex, RwLock};
use types::{
    backend::{ConnectionConfig, Login},
    net::Connectivity,
};

use crate::state::{AppState, PowerState, User};

/// 网络状态变更
pub async fn on_connectivity_event(connectivity: Connectivity, app_handle: AppHandle<Wry>) {
    let app_state = app_handle.state::<RwLock<AppState>>();
    let mut app_state = app_state.write().await;
    app_state.connectivity = connectivity;

    let power_state = app_state.power_state.take();
    let user = app_state.user.clone();
    let reconnect_token = app_state.reconnect_token.clone();
    drop(app_state);

    // 通知前端网络状态变更, 如果是系统进入睡眠/休眠状态, 则不通知
    if Some(PowerState::Suspend) != power_state {
        _ = app_handle.emit_all(
            "network_changed",
            if connectivity.is_offline() { "0" } else { "1" },
        );
    }

    if connectivity.is_offline() {
        return;
    }

    valid_token_and_reconnect(user, reconnect_token, power_state, app_handle).await;
}

/// 后台连接断开
///
/// - `status_code`: 断开状态码
pub async fn on_backend_disconnect(status_code: Option<i8>, app_handle: AppHandle<Wry>) {
    let rpc = app_handle.state::<Mutex<ControlServiceClient>>();
    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    match status_code {
        // 异常断开
        Some(-1) => {
            log::info!(target: "app", "Abnormal disconnection");
            _ = app_handle.emit_all("logout", "-1");
        }
        Some(status_code) => {
            log::info!(target: "app", "Logout code: {status_code}");
            let app_state = app_handle.state::<RwLock<AppState>>();
            let mut app_state = app_state.write().await;

            app_state.reset();
            drop(app_state);

            _ = app_handle.emit_all("logout", status_code.to_string());
            _ = rpc.disconnect(tonic::Request::new(())).await;
            return;
        }
        _ => {
            log::debug!(target: "app", "Logout");
            return;
        }
    }

    // 获取网络状态
    let app_state = app_handle.state::<RwLock<AppState>>();
    let mut app_state = app_state.write().await;
    let is_offline = app_state.connectivity.is_offline();

    let power_state = app_state.power_state.take();
    let user = app_state.user.clone();
    let reconnect_token = app_state.reconnect_token.clone();
    drop(app_state);

    if is_offline {
        log::info!(target: "app", "Waiting for connect to internet");
        return;
    }

    // 异常断开, 尝试重连
    valid_token_and_reconnect(user, reconnect_token, power_state, app_handle).await;
}

/// 验证令牌有效性, 并重连
///
/// - `user`: 登录用户
/// - `reconnect_token`: 重连令牌
/// - `power_state`: 是否从睡眠/休眠中恢复
async fn valid_token_and_reconnect(
    user: Option<User>,
    reconnect_token: Option<(String, i64)>,
    power_state: Option<PowerState>,
    app_handle: AppHandle<Wry>,
) {
    // 若当前是登录状态, 网络恢复后, 重新连接
    if user.is_some() {
        log::info!(target: "app", "Trying to reconnect");

        // 令牌过期或令牌不存在, 通知前端退出, 同时重新获取节点
        let (token, valid) = match reconnect_token {
            Some((token, expire)) => {
                let timestamp = Local::now().timestamp_millis();
                if timestamp < expire {
                    (Some(token), true)
                } else {
                    (None, false)
                }
            }
            None => (None, false),
        };

        if !valid {
            log::warn!(target: "app", "Try to reconnect, reconnect token does not exist or has expired");

            // 令牌不存在或已过期, 检查是否从睡眠/休眠模式恢复
            if let Some(PowerState::SuspendResume) = power_state {
                _ = app_handle.emit_all(
                    "recconect_status",
                    json!({
                        "status": false,
                        "show_long_time_inacitivity_tip": true,
                    }),
                );
            }

            #[cfg(feature = "cluster")]
            crate::cluster::reload(app_handle).await;
            return;
        }

        // 令牌有效, 尝试重连
        reconnect(token.unwrap(), user.unwrap(), app_handle).await;
    }
}

/// 重新连接
///
/// - `token`: 重连令牌
/// - `user`: 登录用户信息
async fn reconnect(token: String, user: User, app_handle: AppHandle<Wry>) {
    let rpc = app_handle.state::<Mutex<ControlServiceClient>>();
    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    let config_state = app_handle.state::<Mutex<config::AppConfig>>();
    let config_guard = config_state.lock().await;
    let config = config_guard.clone();
    drop(config_guard);

    let Some(node) = config.node else {
        log::warn!(target: "app", "Try to reconnect, node does not exist");
        return;
    };

    let payload = json!({
        "type": "link",
        "ticket": token,
        "tenant": user.unit,
    });

    let request = Login {
        connection_config: ConnectionConfig {
            ip: node.ip,
            host: node.host,
            port: node.port,
            spa_port: node.spa_port,
            authorization: Some(types::backend::SPAAuthorization {
                tenant: user.unit,
                secret: user.secret,
                username: user.username,
            }),
        },
        payload,
    };

    // 尝试重连8次
    for i in 0..8 {
        // 执行重连
        match do_reconnect(&mut rpc, request.clone()).await {
            Some(action) => match action {
                AfterReconnectFail::Continue => (),
                AfterReconnectFail::Abort => {
                    break;
                }
            },
            None => {
                // 重连成功, 通知前端
                _ = app_handle.emit_all(
                    "recconect_status",
                    json!({
                        "status": true,
                    }),
                );
                return;
            }
        }

        log::debug!(target: "app", "Attempt {} failed. Retrying...", i + 1);

        // 等待
        tokio::time::sleep(Duration::from_millis(500 * 2u64.pow(i))).await;

        // 检查网络状态. Windows上, 断网后, 连接也是异常断开, 且比断网的事件优先到达
        let app_state = app_handle.state::<RwLock<AppState>>();
        let app_state = app_state.read().await;
        let is_offline = app_state.connectivity.is_offline();
        drop(app_state);

        if is_offline {
            log::debug!(target: "app", "Network is offline and will not reconnect");
            return;
        }
    }

    // 重连失败
    _ = app_handle.emit_all(
        "recconect_status",
        json!({
            "status": false,
        }),
    );

    // 恢复托盘连接地址设置
    let tray_handle = app_handle.tray_handle();
    #[cfg(not(feature = "cluster"))]
    let menu_handle = tray_handle.get_item("set");
    #[cfg(feature = "cluster")]
    let menu_handle = tray_handle.get_item("cluster_config");
    _ = menu_handle.set_enabled(true);
}

async fn do_reconnect(
    rpc: &mut CommunicateServiceClient<Channel>,
    request: Login,
) -> Option<AfterReconnectFail> {
    match rpc.login(tonic::Request::new(request.into())).await {
        Ok(_response) => {
            log::info!(target: "app", "Reconnection successful");
            None
        }
        Err(status) => {
            // 后台报告错误, 登录失败
            if status.code() == tonic::Code::Unauthenticated {
                let result = String::from_utf8(status.details().to_vec()).ok();
                log::error!(target: "app", "Reconnection failed, reason: {:?}", result);

                return Some(AfterReconnectFail::Abort);
            }

            log::error!(target: "app", "Reconnection failed. {}", status.message());
            Some(AfterReconnectFail::Continue)
        }
    }
}

enum AfterReconnectFail {
    Continue,
    Abort,
}
