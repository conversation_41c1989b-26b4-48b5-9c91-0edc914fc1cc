// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

use core::task::{Context, Poll};

/// Handle to a receive IO provider
pub trait Rx: Sized {
    type Error;
    type Queue<'a>: Queue
    where
        Self: 'a;

    /// Returns a future that yields after a packet is ready to be received
    #[inline]
    fn ready(&mut self) -> RxReady<Self> {
        RxReady(self)
    }

    /// Polls the IO provider for a packet that is ready to be received
    fn poll_ready(&mut self, cx: &mut Context) -> Poll<Result<(), Self::Error>>;

    /// Calls the provided callback with the IO provider queue
    fn queue<F: FnOnce(&mut Self::Queue<'_>)>(&mut self, f: F);
}

impl_ready_future!(Rx, RxReady, Result<(), T::Error>);

/// A structure capable of queueing and receiving messages
pub trait Queue {
    /// Iterates over all of the packets in the receive queue and processes them
    fn for_each<F: FnMut(&mut [u8])>(&mut self, on_packet: F);

    /// Returns if there are items in the queue or not
    fn is_empty(&self) -> bool;
}
