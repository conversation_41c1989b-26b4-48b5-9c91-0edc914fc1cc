use serde_json::{json, Value};
use sysinfo::System;

pub fn info(device_id: &str) -> Value {
    // 启动时, 可能会没联网
    let mac = crate::environment::interface::list()
        .unwrap_or_default()
        .into_iter()
        .map(|mut value| value["mac"].take())
        .filter(|f| f.is_string())
        .map(|mac| mac.as_str().map(String::from).unwrap_or_default())
        .collect::<Vec<String>>()
        .join(",");
    json!({
        "deviceId": device_id,
        "sysType": "MacOS",
        "operatingSystem": "Mac OS",
        "systemVersion": System::os_version().unwrap_or_default(),
        "clientVersion": version::FILE_VERSION,
        "mac": mac,
        "manufacturer": "Apple",
        "name": System::host_name().unwrap_or_default(),
    })
}
