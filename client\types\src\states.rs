use crate::tunnel::{ActionAfterDisconnect, ErrorState};

/// Represents the state the client tunnel is in.
#[derive(<PERSON>bug, <PERSON>lone, PartialEq, Eq)]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "com.jingantech.oneid.model"))]
pub enum TunnelState {
    Disconnected,
    Connecting,
    Connected,
    Disconnecting(ActionAfterDisconnect),
    Error(ErrorState),
}

impl TunnelState {
    /// Returns true if the tunnel state is in the error state.
    pub fn is_in_error_state(&self) -> bool {
        matches!(self, TunnelState::Error(_))
    }

    /// Returns true if the tunnel state is in the connected state.
    pub fn is_connected(&self) -> bool {
        matches!(self, TunnelState::Connected { .. })
    }

    /// Returns true if the tunnel state is in the disconnected state.
    pub fn is_disconnected(&self) -> bool {
        matches!(self, TunnelState::Disconnected)
    }
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum BackendState {
    Disconnected(Option<i8>),
    Connecting,
    ProxyConnecting,
    ProxyConnected,
    Connected,
    Disconnecting,
    Error(crate::backend::ErrorState),
}
