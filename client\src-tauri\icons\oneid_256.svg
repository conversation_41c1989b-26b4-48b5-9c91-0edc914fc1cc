<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="256" height="256" viewBox="0 0 256 256">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="0.387" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#f1f1f1"/>
    </linearGradient>
    <clipPath id="clip-path">
      <path id="路径_2463" data-name="路径 2463" d="M30.667,0H184a30.667,30.667,0,0,1,30.667,30.667V184A30.667,30.667,0,0,1,184,214.669H30.667A30.667,30.667,0,0,1,0,184V30.667A30.667,30.667,0,0,1,30.667,0Z" fill="#f87c10"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.5" y1="-0.027" x2="0.569" y2="0.699" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.5" y1="-0.027" x2="0.569" y2="0.699" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.49"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
    <clipPath id="clip-path-2">
      <path id="路径_2482" data-name="路径 2482" d="M29.712,76H178.273a29.839,29.839,0,0,1,29.712,29.965v25.283a29.839,29.839,0,0,1-29.712,29.965H29.712A29.839,29.839,0,0,1,0,131.248V105.965A29.839,29.839,0,0,1,29.712,76Z" transform="translate(0 -76)" fill="url(#linear-gradient)"/>
    </clipPath>
  </defs>
  <g id="_256" data-name="256" transform="translate(6062 -9002)">
    <rect id="矩形_2032" data-name="矩形 2032" width="256" height="256" transform="translate(-6062 9002)" fill="none"/>
    <g id="组_33010" data-name="组 33010" transform="translate(621 426)">
      <g id="组_32266" data-name="组 32266" transform="translate(-6662 8597)">
        <path id="路径_2458" data-name="路径 2458" d="M30.667,0H184a30.667,30.667,0,0,1,30.667,30.667V184A30.667,30.667,0,0,1,184,214.669H30.667A30.667,30.667,0,0,1,0,184V30.667A30.667,30.667,0,0,1,30.667,0Z" fill="#f87c10"/>
        <g id="组_32" data-name="组 32" clip-path="url(#clip-path)">
          <path id="路径_2459" data-name="路径 2459" d="M80.9,0c44.679,0,80.9,31.03,80.9,69.308s-36.22,69.308-80.9,69.308S0,107.586,0,69.308,36.22,0,80.9,0Z" transform="matrix(0.719, -0.695, 0.695, 0.719, -85.046, 185.34)" opacity="0.19" fill="url(#linear-gradient-2)"/>
          <path id="路径_2460" data-name="路径 2460" d="M155.758,0C241.78,0,311.515,59.743,311.515,133.44s-69.735,133.44-155.758,133.44S0,207.137,0,133.44,69.735,0,155.758,0Z" transform="translate(366.383 359.265) rotate(-155)" opacity="0.51" fill="url(#linear-gradient-2)"/>
          <path id="路径_2462" data-name="路径 2462" d="M155.758,0C241.78,0,311.515,59.743,311.515,133.44s-69.735,133.44-155.758,133.44S0,207.137,0,133.44,69.735,0,155.758,0Z" transform="translate(214.669 -198.458) rotate(101)" opacity="0.51" fill="url(#linear-gradient-4)"/>
        </g>
        <g id="图层_1" data-name="图层 1" transform="translate(65.245 26.782)">
          <path id="路径_2381" data-name="路径 2381" d="M112.639,477.033a38.431,38.431,0,0,1-13.722,3.789A34.889,34.889,0,0,1,101,490.544a56.49,56.49,0,0,1,.333,5.7l0,0s0,5.724-6,5.753l-10.5-.059V479.662A44.886,44.886,0,0,1,74.544,475.4V541.29c-.007,0,0,1.647,0,1.647v11.531a68.865,68.865,0,0,0,10.282-5.39v-.039l.005,0V535.46h0v-25l8.695.047a7.035,7.035,0,0,0,1.127.084c4.811-.034,4.6,3.159,4.359,4.342-.371,1.456-.783,2.948-1.271,4.48a32.015,32.015,0,0,1-4.551,9.014v14.238a57.907,57.907,0,0,0,15.256-21.237C117.613,499,112.639,477.033,112.639,477.033Z" transform="translate(-31.787 -449.922)" fill="#fff"/>
          <path id="路径_2382" data-name="路径 2382" d="M18.487,554.492h0v25a20.223,20.223,0,0,1-5.284-3.308c-.486-.512-.961-1.053-1.428-1.607a27.831,27.831,0,0,1-1.98-2.317q-4.5,1.8-9.364,3.476-1.121-1.636-2.157-3.4A61.231,61.231,0,0,0,13.2,589.292v-.041c1.906,1.526,3.873,2.885,5.288,3.818v.039a69.068,69.068,0,0,0,9.872,5.39l0-12.127a.1.1,0,0,1-.018-.008V563.649q-4.664,2.506-9.853,4.891Z" transform="translate(10.668 -493.951)" fill="#fff"/>
          <path id="路径_2383" data-name="路径 2383" d="M43.793,502.385V480.116a40.331,40.331,0,0,0,9.477-3.9,40.336,40.336,0,0,1-9.478,3.9v22.269h0Z" transform="translate(-14.638 -450.377)" fill="#fff"/>
          <path id="路径_2384" data-name="路径 2384" d="M52.106,548.514Z" transform="translate(-19.276 -490.623)" fill="#fff"/>
          <path id="路径_2385" data-name="路径 2385" d="M66.092,535.035h0V503.1c.006-.006.017-.009.021-.012a.244.244,0,0,0-.022.012Z" transform="translate(-27.084 -465.337)" fill="#fff"/>
          <path id="路径_2386" data-name="路径 2386" d="M143.246,479.084a38.446,38.446,0,0,1-13.723,3.789,34.782,34.782,0,0,1,2.083,9.723,56.219,56.219,0,0,1,.333,5.7l0,0a6.791,6.791,0,0,1-.378,2,110.226,110.226,0,0,0,12.692-14.109A60.306,60.306,0,0,0,143.246,479.084Z" transform="translate(-62.393 -451.974)" fill="#fff"/>
          <path id="路径_2387" data-name="路径 2387" d="M84.833,501.93V479.662A44.864,44.864,0,0,1,74.549,475.4V516.58c3.518-2.024,7.23-3.376,10.285-5.46v-.657l.948,0a134.543,134.543,0,0,0,11.193-8.652,7.746,7.746,0,0,1-1.639.174Z" transform="translate(-31.791 -449.922)" fill="#fff"/>
          <path id="路径_2388" data-name="路径 2388" d="M-1.433,530.282c-12.288-16.042-6.668-43.6-6.668-43.6l.086.055a23.806,23.806,0,0,1,1.188-3.866,38.448,38.448,0,0,1-13.723-3.788c0,.011-4.97,21.969,4.2,44.4.032.079.067.156.1.235q.306.739.63,1.458c.1.221.2.44.3.658.163.351.329.7.5,1.039.113.23.223.461.339.688q.351.689.718,1.359c.176.323.357.64.538.957.091.159.182.318.274.475q1.035,1.767,2.157,3.4Q-5.93,532.083-1.433,530.282Z" transform="translate(21.896 -451.975)" fill="#fff"/>
          <path id="路径_2389" data-name="路径 2389" d="M43.793,479.823v22.269h-.059v8.533h.059v14.047q5.192-2.38,9.853-4.89V487.849c.007-.006.018-.008.022-.012V475.689l-.4.233A40.326,40.326,0,0,1,43.793,479.823Z" transform="translate(-14.638 -450.084)" fill="#fff"/>
          <path id="路径_2390" data-name="路径 2390" d="M43.793,479.823v22.269h-.059v8.533h.059v14.047q5.192-2.38,9.853-4.89V487.849c.007-.006.018-.008.022-.012V475.689l-.4.233A40.326,40.326,0,0,1,43.793,479.823Z" transform="translate(-14.638 -450.084)" fill="#fff"/>
          <path id="路径_2391" data-name="路径 2391" d="M48.807,562.846q-.126-.187-.248-.381Q48.681,562.659,48.807,562.846Z" transform="translate(-17.324 -498.389)" fill="#fff"/>
          <path id="路径_2392" data-name="路径 2392" d="M47.784,561.156q-.174-.289-.338-.591Q47.61,560.866,47.784,561.156Z" transform="translate(-16.704 -497.332)" fill="#fff"/>
          <path id="路径_2393" data-name="路径 2393" d="M49.326,563.89a13.047,13.047,0,0,0,.914,1.082c-.293-.352-.577-.72-.847-1.109C49.371,563.871,49.348,563.88,49.326,563.89Z" transform="translate(-17.75 -499.167)" fill="#fff"/>
          <path id="路径_2394" data-name="路径 2394" d="M46.85,559.388c-.093-.179-.183-.36-.27-.547Q46.71,559.119,46.85,559.388Z" transform="translate(-16.222 -496.372)" fill="#fff"/>
          <path id="路径_2395" data-name="路径 2395" d="M45.232,555.487c-.089-.236-.174-.477-.253-.723C45.057,555.01,45.143,555.25,45.232,555.487Z" transform="translate(-15.331 -494.102)" fill="#fff"/>
          <path id="路径_2396" data-name="路径 2396" d="M46.027,557.576c-.105-.232-.2-.469-.3-.71C45.823,557.107,45.922,557.345,46.027,557.576Z" transform="translate(-15.748 -495.272)" fill="#fff"/>
          <path id="路径_2397" data-name="路径 2397" d="M49.118,563.327c.053.079.1.16.159.238h0C49.223,563.487,49.171,563.406,49.118,563.327Z" transform="translate(-17.635 -498.869)" fill="#fff"/>
          <path id="路径_2398" data-name="路径 2398" d="M48.364,562.149c-.053-.083-.1-.166-.156-.251C48.259,561.983,48.311,562.066,48.364,562.149Z" transform="translate(-17.128 -498.073)" fill="#fff"/>
          <path id="路径_2399" data-name="路径 2399" d="M47.3,560.291c-.039-.072-.076-.146-.114-.219C47.227,560.145,47.263,560.219,47.3,560.291Z" transform="translate(-16.56 -497.057)" fill="#fff"/>
          <path id="路径_2400" data-name="路径 2400" d="M45.629,556.6c-.027-.069-.054-.139-.08-.209C45.575,556.465,45.6,556.535,45.629,556.6Z" transform="translate(-15.648 -495.01)" fill="#fff"/>
          <path id="路径_2401" data-name="路径 2401" d="M46.481,558.632c-.026-.055-.054-.109-.079-.165C46.427,558.523,46.455,558.577,46.481,558.632Z" transform="translate(-16.123 -496.163)" fill="#fff"/>
          <path id="路径_2402" data-name="路径 2402" d="M92.246,480.868a41.64,41.64,0,0,1-17.7-5.472l0,12.235V503.4c3.307-1.151,7.071-1.753,10.165-3.456v-9.131c.554.047,1.118.081,1.7.081a103.773,103.773,0,0,0,15.171-1.579c3.325-3.253,2.991-6.17,5.532-10.1A42.175,42.175,0,0,1,92.246,480.868Z" transform="translate(-31.791 -449.921)" fill="#fff"/>
          <path id="路径_2403" data-name="路径 2403" d="M45.71,418.054a21.048,21.048,0,0,1,18.56,23.158c-2.212-10.053-17.331-17.294-24.1-9.7,3.752-1.306,4.9,1.386,4.765,2.749-.172,1.7-2.42,4.792-5.776,6.208-8.043,3.395-18.422-.99-14.83-9.786A20.886,20.886,0,0,1,45.71,418.054Z" transform="translate(-3.429 -417.934)" fill="#fff" fill-rule="evenodd"/>
        </g>
        <g id="蒙版组_35" data-name="蒙版组 35" transform="translate(3.342 126.115)" clip-path="url(#clip-path-2)">
          <path id="路径_2457" data-name="路径 2457" d="M150.908,0c83.344,0,150.908,40.247,150.908,89.895s-67.564,89.894-150.908,89.894S0,139.542,0,89.895,67.564,0,150.908,0Z" transform="translate(-46.915 17.792)" fill="#fff"/>
        </g>
      </g>
      <g id="组_32240" data-name="组 32240" transform="translate(-6614.465 8763.667)">
        <g id="组_2446" data-name="组 2446" transform="translate(82.913 0)">
          <path id="路径_2237" data-name="路径 2237" d="M123.088,248.891V221.664c6.236.476,12.429.581,18.46,1.623,1.965.339,4.206,2.814,5.107,4.849a19.648,19.648,0,0,1,.106,15.186c-1.167,3.1-3.433,5.33-6.768,5.495C134.485,249.09,128.953,248.891,123.088,248.891Zm5.421-22.212c0,5.71-.025,10.861.033,16.011a2.52,2.52,0,0,0,.744,1.81c1.855,1.337,9.664-.037,10.911-2.043a12.918,12.918,0,0,0,.787-12.177c-1.5-3.745-4.845-3.568-8.05-3.6C131.653,226.668,130.372,226.679,128.509,226.679Z" transform="translate(-111.547 -221.664)" fill="#f87c10"/>
          <path id="路径_2240" data-name="路径 2240" d="M118.305,248.983h-5.3V222.306l5.3-.361Z" transform="translate(-113.008 -221.621)" fill="#f87c10"/>
        </g>
        <g id="组_2445" data-name="组 2445" transform="translate(0 0)">
          <path id="路径_2238" data-name="路径 2238" d="M64.9,236.014c.068,9.1-4.3,13.823-12.833,13.9C42.6,250,37.759,245.3,37.607,235.872c-.148-9.164,4.27-13.844,13.182-13.96C60.429,221.786,64.827,226.181,64.9,236.014Zm-6.14-.342c-.07-5.561-1.853-8.516-5.613-9.306-4.366-.916-7.716,1.057-8.876,5.23-1.683,6.056.265,12.248,4.268,13.54a7.671,7.671,0,0,0,1.059.222C55.614,246.376,58.852,242.952,58.759,235.672Z" transform="translate(-37.604 -221.909)" fill="#f87c10"/>
          <path id="路径_2270" data-name="路径 2270" d="M377.4-120.923c.3,2.066,1.117,3.215,2.866,3.5a6.925,6.925,0,0,0,5.774-1.376,3.673,3.673,0,0,1,2.742-.838,4.681,4.681,0,0,1,2.683,2.047c.313.579-.236,1.969-.817,2.624-2.152,2.431-5.112,2.915-8.2,3.067-5.029.247-9.227-2.227-10.7-6.418-1.746-4.958-.138-10.454,3.868-13.217a11.31,11.31,0,0,1,16.674,5.19c1.336,3.583.083,5.418-3.7,5.419C384.951-120.922,381.307-120.923,377.4-120.923Zm7.991-5.045c-2.137-2.516-5.917-2.413-6.924,0Z" transform="translate(-316.381 136.643)" fill="#f87c10"/>
          <path id="路径_2271" data-name="路径 2271" d="M326.415-119.865c0,1.8.026,3.591,0,5.386-.035,2.081-.878,2.918-2.905,2.964-2.392.053-3.2-.532-3.295-2.7-.124-2.944-.085-5.895-.14-8.842-.053-2.858-1.266-4.129-3.877-4.114-2.972.018-4.421,1.391-4.447,4.279s.018,5.77-.016,8.655c-.024,2.025-.92,2.87-2.971,2.927-2.181.061-3.124-.613-3.166-2.67-.108-5.254-.149-10.511-.086-15.766.029-2.406,1.8-3.479,4.157-2.419,1.446.649,2.611.3,4.012.122a22.88,22.88,0,0,1,7.336,0c3.571.728,5.291,3.292,5.392,6.981C326.452-123.328,326.413-121.6,326.415-119.865Z" transform="translate(-274.81 135.813)" fill="#f87c10"/>
        </g>
      </g>
    </g>
  </g>
</svg>
