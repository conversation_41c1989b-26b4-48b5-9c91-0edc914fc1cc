use crate::dns::DnsMonitorT;
use err_ext::ErrorExt;
use std::{
    io::{self},
    net::IpAddr,
    time::Duration,
};
use types::net::IpVersion;
use windows_net::{index_from_luid, luid_from_alias};

const NETSH_TIMEOUT: Duration = Duration::from_secs(10);

/// Errors that can happen when configuring DNS on Windows.
#[derive(thiserror::Error, Debug)]
pub enum Error {
    /// Failure to obtain an interface LUID given an alias.
    #[error("Failed to obtain LUID for the interface alias")]
    InterfaceLuidError(#[source] io::Error),

    /// Failure to obtain an interface index.
    #[error("Failed to obtain index of the interface")]
    InterfaceIndexError(#[source] io::Error),

    /// Failure to spawn netsh subprocess.
    #[error("Failed to spawn 'netsh'")]
    SpawnNetsh(#[source] io::Error),

    /// Failure to spawn netsh subprocess.
    #[error("Failed to obtain system directory")]
    GetSystemDir(#[source] io::Error),

    /// Failure to write to stdin.
    #[error("Failed to write to stdin for 'netsh'")]
    NetshInput(#[source] io::Error),

    /// Failure to wait for netsh result.
    #[error("Failed to wait for 'netsh'")]
    WaitNetsh(#[source] io::Error),

    /// netsh returned a non-zero status.
    #[error("'netsh' returned an error: {0:?}")]
    NetshError(Option<i32>),

    /// netsh did not return in a timely manner.
    #[error("'netsh' took too long to complete")]
    NetshTimeout,
}

pub struct DnsMonitor {
    current_index: Option<u32>,
}

#[async_trait::async_trait]
impl DnsMonitorT for DnsMonitor {
    type Error = Error;

    async fn new() -> Result<Self, Error> {
        Ok(DnsMonitor {
            current_index: None,
        })
    }

    async fn set(&mut self, interface: &str, servers: &[IpAddr]) -> Result<(), Error> {
        let interface_luid = luid_from_alias(interface).map_err(Error::InterfaceLuidError)?;
        let interface_index =
            index_from_luid(&interface_luid).map_err(Error::InterfaceIndexError)?;

        self.current_index = Some(interface_index);

        let mut added_ipv4_server = false;
        let mut added_ipv6_server = false;

        let mut netsh_input = String::new();

        for server in servers {
            let is_additional_server;

            if server.is_ipv4() {
                is_additional_server = added_ipv4_server;
                added_ipv4_server = true;
            } else {
                is_additional_server = added_ipv6_server;
                added_ipv6_server = true;
            };

            if is_additional_server {
                netsh_input.push_str(&create_netsh_add_command(interface_index, server));
            } else {
                netsh_input.push_str(&create_netsh_set_command(interface_index, server));
            }
        }

        if !added_ipv4_server {
            netsh_input.push_str(&create_netsh_flush_command(interface_index, IpVersion::V4));
        }
        if !added_ipv6_server {
            netsh_input.push_str(&create_netsh_flush_command(interface_index, IpVersion::V6));
        }

        netsh::run_netsh_with_timeout(netsh_input, NETSH_TIMEOUT).map_err(|error| match error {
            netsh::Error::SpawnNetsh(error) => Error::SpawnNetsh(error),
            netsh::Error::GetSystemDir(error) => Error::GetSystemDir(error),
            netsh::Error::NetshInput(error) => Error::NetshInput(error),
            netsh::Error::WaitNetsh(error) => Error::WaitNetsh(error),
            netsh::Error::NetshError(error) => Error::NetshError(error),
            netsh::Error::NetshTimeout => Error::NetshTimeout,
        })?;

        Ok(())
    }

    async fn reset(&mut self) -> Result<(), Error> {
        if let Some(index) = self.current_index.take() {
            let mut netsh_input = String::new();
            netsh_input.push_str(&create_netsh_flush_command(index, IpVersion::V4));
            netsh_input.push_str(&create_netsh_flush_command(index, IpVersion::V6));

            if let Err(error) = netsh::run_netsh_with_timeout(netsh_input, NETSH_TIMEOUT) {
                log::error!("{}", error.display_chain_with_msg("Failed to reset DNS"));
            }
        }
        Ok(())
    }

    async fn reset_before_interface_removal(&mut self) -> Result<(), Self::Error> {
        // do nothing since the tunnel interface goes away
        let _ = self.current_index.take();
        Ok(())
    }
}

fn create_netsh_set_command(interface_index: u32, server: &IpAddr) -> String {
    // Set primary DNS server:
    // netsh interface ipv4 set dnsservers name="Mullvad" source=static address=*********
    // validate=no

    let interface_type = if server.is_ipv4() { "ipv4" } else { "ipv6" };
    format!("interface {interface_type} set dnsservers name={interface_index} source=static address={server} validate=no\r\n")
}

fn create_netsh_add_command(interface_index: u32, server: &IpAddr) -> String {
    // Add DNS server:
    // netsh interface ipv4 add dnsservers name="Mullvad" address=********* validate=no

    let interface_type = if server.is_ipv4() { "ipv4" } else { "ipv6" };
    format!("interface {interface_type} add dnsservers name={interface_index} address={server} validate=no\r\n")
}

fn create_netsh_flush_command(interface_index: u32, ip_version: IpVersion) -> String {
    // Flush DNS settings:
    // netsh interface ipv4 set dnsservers name="Mullvad" source=static address=none validate=no

    let interface_type = match ip_version {
        IpVersion::V4 => "ipv4",
        IpVersion::V6 => "ipv6",
    };

    format!("interface {interface_type} set dnsservers name={interface_index} source=static address=none validate=no\r\n")
}
