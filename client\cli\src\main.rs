use anyhow::Context;
use clap::Parser;
use futures_channel::mpsc;
use libcore::*;
use std::{collections::HashMap, fs, net::IpAddr, path::Path};
use tracing::{debug, error, info};
use url::Url;

mod cli;
mod config;
mod environment;
mod event_listener;
mod log;
mod login;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let args = cli::AppArgs::parse();
    let config = config::load(args.config)?;
    let token = log::init(&config.log.dir, &config.log.level, &config.log.name);

    // 获取网关地址
    info!("Start getting gateway configuration");
    let nodes = match get_gateway_configuration(config.url).await {
        Ok(nodes) => nodes,
        Err(error) => {
            error!("Failed to obtain gateway configuration: {}", error);
            std::process::exit(1);
        }
    };

    debug!("Got gateway configuration: {:#?}", nodes);

    // 获取设备ID
    info!("Start getting device ID");
    let device_id = match uniqueid::device_id() {
        Ok(id) => id,
        Err(error) => {
            error!("Failed to generate device ID: {}", error.1);
            std::process::exit(1);
        }
    };

    // 获取环境信息
    info!("Start getting environment information");
    let system_info = libcore::environment::system::info(&device_id);
    debug!("Got environment information: {}", system_info);

    // 加载证书
    info!("Reading certificates");
    let certs = match read_certs(config.cert_dir) {
        Ok(certs) => certs,
        Err(error) => {
            error!("{}", error);
            std::process::exit(1);
        }
    };
    let mut identities = HashMap::new();
    for (domain, setting) in certs {
        let identity: setting::DeviceIdentity = setting.try_into()?;
        identities.insert(domain, identity);
    }

    info!("Creating the core");
    let command_channel = CoreCommandChannel::new();
    let core_command_sender = command_channel.sender();
    let (core_event_sender, core_event_receiver) = command_channel.destructure();
    let (reconnect_tx, reconnect_rx) = mpsc::unbounded();

    // 代理请求服务
    let proxy_handle = proxy_request::spawn(device_id.clone(), core_event_sender.clone());

    let core = match Core::start(
        device_id.clone(),
        identities,
        event_listener::Listener {
            proxy_handle: proxy_handle.clone(),
            reconnect_tx,
        },
        core_event_sender.clone(),
        core_event_receiver,
    )
    .await
    {
        Ok(core) => core,
        Err(error) => {
            error!("Failed to start core: {}", error);
            std::process::exit(1);
        }
    };

    let shutdown_handle = core.shutdown_handle();
    #[cfg(any(target_os = "linux", target_os = "macos"))]
    libcore::shutdown::set_shutdown_signal_handler(move || shutdown_handle.shutdown())?;
    #[cfg(any(windows, target_os = "android"))]
    libcore::shutdown::set_shutdown_signal_handler(move || shutdown_handle.shutdown())?;

    // 执行登录
    login::spawn(
        core_command_sender.clone(),
        nodes,
        proxy_handle,
        system_info,
        config.user,
        reconnect_rx,
    );

    let exit_code = match core.run().await {
        Ok(_) => 0,
        Err(error) => {
            error!("{}", error);
            1
        }
    };

    drop(core_command_sender);
    drop(token);
    debug!("Process exiting with code {}", exit_code);
    std::process::exit(exit_code);
}

#[derive(Debug, Clone, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
struct RemoteNode {
    host: String,
    ip: Option<IpAddr>,
    port: u16,
    spa_port: u16,
}

async fn get_gateway_configuration(url: Url) -> anyhow::Result<Vec<RemoteNode>> {
    let http_client = libcore::http::client().await;
    let response = http_client.get(url).send().await?;
    let nodes = response.json::<Vec<RemoteNode>>().await?;

    Ok(nodes)
}

fn read_certs(dir: impl AsRef<Path>) -> anyhow::Result<HashMap<String, setting::Setting>> {
    let mut core = serde_json::json!({});
    for entry in dir.as_ref().read_dir().with_context(|| {
        format!(
            "Failed to read certificates from {}",
            dir.as_ref().display()
        )
    })? {
        let entry = entry?;
        let domain = entry.file_name().to_string_lossy().to_string();

        let path = entry.path();
        if !path.is_dir() {
            continue;
        }

        let cert_dir = path
            .read_dir()
            .with_context(|| format!("Failed to read certificates from {}", path.display()))?;

        let mut domain_config = serde_json::json!({});
        for entry in cert_dir {
            let entry = entry?;

            let file_name = entry.file_name().into_string().unwrap();
            match file_name.as_str() {
                "ca.crt" => {
                    domain_config["ca"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                "client.crt" => {
                    domain_config["cert"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                "client.key" => {
                    domain_config["key"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                #[cfg(feature = "tlcp")]
                "client_enc.crt" => {
                    domain_config["enc_cert"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                #[cfg(feature = "tlcp")]
                "client_enc.key" => {
                    domain_config["enc_key"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                _ => (),
            }
        }

        core[domain.clone()] = domain_config;
    }

    let config = serde_json::from_value(core)?;
    Ok(config)
}
