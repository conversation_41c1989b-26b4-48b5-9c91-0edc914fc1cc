use gm_sm2::key::Sm2P<PERSON><PERSON><PERSON><PERSON>;
use std::{
    net::{IpAddr, Ipv4Addr},
    time::Duration,
};

use rand::Rng;
use tokio::{net::UdpSocket, sync::oneshot};

use base::{
    hotp::Hotp,
    spa::{SPAType, SPA},
};

use crate::proxy_request::UNIVERSAL_SECRET;

pub struct SpaArgs<'a> {
    pub device_id: &'a str,
    pub ipv4: Ipv4Addr,
    pub pubkey: Sm2PublicKey,
    pub addr: &'a str,
    pub secret: Option<&'a str>,
    pub tenant: Option<&'a str>,
    pub username: Option<&'a str>,
}

/// 发送SPA包
///
/// # Arguments
///
/// - `addr`: 目标地址
/// - `secret`: 绑定码
/// - `username`: 用户名
/// - `spa_type`: SPA消息类型
///
/// # Return
/// 后续连接已成功, 停止发送SPA
pub(super) async fn send_spa(
    args: SpaArgs<'_>,
    spa_type: SPAType,
) -> Result<(oneshot::Sender<()>, IpAddr), super::Error> {
    let counter = rand::thread_rng().gen::<[u8; 8]>();
    let counter = u64::from_be_bytes(counter);
    // 1. 构造SPA包
    let mut spa = SPA::new(spa_type);
    spa.set_client_ip(&args.ipv4);
    let device_id = hex::decode!(args.device_id.as_bytes());
    spa.set_device(&device_id.try_into().unwrap());
    spa.set_counter(counter);

    match spa_type {
        SPAType::Auth | SPAType::ChangePwd => {
            let secret = args.secret.ok_or(super::Error::MissingSecret)?;
            let tenant = args.tenant.ok_or(super::Error::MissingUnit)?;
            let username = args.username.ok_or(super::Error::MissingUsername)?;

            let secret = hex::decode!(secret.as_bytes());

            let hotp = Hotp::new(&secret);
            let password = hotp.generate(counter);
            spa.set_password(password);

            let mut payload = tenant.as_bytes().to_vec();
            payload.extend_from_slice(&[b'|']);
            payload.extend_from_slice(username.as_bytes());

            spa.set_payload(&payload);
        }
        SPAType::Proxy => {
            let hotp = Hotp::new(&UNIVERSAL_SECRET);
            let password = hotp.generate(counter);
            spa.set_password(password);
        }
    }

    let spa_pkt = base::spa::new_packet(args.pubkey, spa);

    let sock = UdpSocket::bind((Ipv4Addr::UNSPECIFIED, 0)).await?;
    sock.connect(args.addr).await.map_err(|err| {
        log::error!("Failed to connect to the server: {err}");
        super::Error::IoError(err)
    })?;

    let peer_addr = sock.peer_addr().unwrap();
    match sock.send(&spa_pkt).await {
        Ok(_size) => {
            log::debug!("Spa sent. type = {:?}, {}", spa_type, peer_addr);
        }
        Err(error) => {
            log::error!("Failed to send spa to {}. {}", peer_addr, error);
            return Err(super::Error::IoError(error));
        }
    }

    let (prevent_tx, mut prevent_rx) = oneshot::channel();
    tokio::spawn(async move {
        let mut tick = tokio::time::interval(Duration::from_millis(500));
        for _ in 0..10 {
            tokio::select! {
                _ = tick.tick() => {
                    match sock.send(&spa_pkt).await {
                        Ok(_size) => {
                            log::trace!("Spa sent. {}", peer_addr);
                        }
                        Err(error) => log::error!("Failed to send spa to {}. {}", peer_addr, error),
                    }
                }
                _ = &mut prevent_rx => {
                    break;
                }
            }
        }
    });

    Ok((prevent_tx, peer_addr.ip()))
}
