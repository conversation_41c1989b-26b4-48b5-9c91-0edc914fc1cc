use serde::{Deserialize, Serialize};

/// 系统类型
#[derive(Serialize, Deserialize, <PERSON>lone, Eq, PartialEq, Copy, Debug)]
#[allow(non_camel_case_types)]
pub enum SystemType {
    ANDROID,
    WINDOWS,
    LINUX,
    MACOS,
    IOS,
    UNIX,
    UNKNOWN,
    HARMONY_OS,
    <PERSON>TH<PERSON>,
}

impl From<&str> for SystemType {
    fn from(sys_type: &str) -> Self {
        match sys_type {
            "WINDOWS" | "00" => SystemType::WINDOWS,
            "ANDROID" | "01" => SystemType::ANDROID,
            "IOS" | "02" => SystemType::IOS,
            "MACOS" | "03" => SystemType::MACOS,
            "LINUX" | "04" => SystemType::LINUX,
            "UNIX" | "05" => SystemType::UNIX,
            "HARMONY_OS" | "08" => SystemType::HARMONY_OS,
            "OTHER" | "10" => SystemType::OTH<PERSON>,
            _ => SystemType::<PERSON>K<PERSON>OW<PERSON>,
        }
    }
}

#[derive(Serial<PERSON>, Deserialize, <PERSON><PERSON>, <PERSON>q, <PERSON>ialEq, Copy, Debug)]
#[allow(non_camel_case_types)]
pub enum AttrType {
    STRING,
    DATE_TIME,
    IP_RANGE,
    SOFTWARE,
    NUMBER,
    DEVICE_CREDIT,
    VERSION,
}

#[derive(Serialize, Deserialize, Eq, PartialEq, Copy, Debug, Clone)]
pub enum ValueType {
    SINGLE,
    ARRAY,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
#[allow(non_camel_case_types)]
pub enum Operator {
    // 等于
    EQ,
    // 不等于
    NE,
    // 在列表内
    IN,
    // 不在列表
    NOT_IN,
    // 大于
    GT,
    // 大于等于
    GTE,
    // 小于
    LT,
    // 小于等于
    LTE,
    // 在..之间
    BETWEEN,
}
