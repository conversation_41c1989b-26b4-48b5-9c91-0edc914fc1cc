[package]
edition = "2021"
name = "proxy-request"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
http = "1.1.0"
reqwest = { version = "0.12.6", default-features = false }
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0.105"
serde_repr = "0.1.16"
thiserror = "2.0.4"
url = { version = "2.4.1", features = ["serde"] }
