use std::time::Duration;

use reqwest::{
    header::{HeaderMap, HeaderValue},
    Client, ClientBuilder,
};

use tokio::sync::OnceCell;

static INSTANCE: OnceCell<Client> = OnceCell::const_new();

fn default_headers() -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.append("Content-Type", HeaderValue::from_static("application/json"));
    headers.append("Accept", HeaderValue::from_static("application/json"));
    headers.append("APP-TYPE", HeaderValue::from_static("DESKTOP"));
    headers
}

fn make_headers(tenant: &str, deviceid: &str) -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.append("Content-Type", HeaderValue::from_static("application/json"));
    headers.append("Accept", HeaderValue::from_static("application/json"));
    headers.append("APP-TYPE", HeaderValue::from_static("DESKTOP"));
    headers.append("DEVICE-ID", HeaderValue::from_str(deviceid).unwrap());
    headers.append("TENANT", HeaderValue::from_str(tenant).unwrap());
    headers
}

pub async fn client<'a>() -> &'a Client {
    INSTANCE
        .get_or_init(|| async {
            ClientBuilder::new()
                .user_agent("sdp/8.0")
                .default_headers(default_headers())
                .timeout(Duration::from_secs(15))
                .danger_accept_invalid_certs(true)
                .danger_accept_invalid_hostnames(true)
                .no_proxy()
                .build()
                .expect("failed to build client")
        })
        .await
}

pub async fn client_tenant_deviceid(tenant: &str, deviceid: &str) -> Client {
    let client = ClientBuilder::new()
        .user_agent("sdp/8.0")
        .default_headers(make_headers(tenant, deviceid))
        .timeout(Duration::from_secs(15))
        .danger_accept_invalid_certs(true)
        .danger_accept_invalid_hostnames(true)
        .no_proxy()
        .build()
        .expect("failed to build client");
    client
}
