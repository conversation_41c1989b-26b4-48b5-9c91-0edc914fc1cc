# 修改客户端 LOGO

## Windows 安装包 LOGO

目录 `client/src-tauri/icons`

需要提供一张 1240x1240 大小边距为 0 的 PNG 图标(oneid_installer.png), 通过下面的命令生成 ico 格式的图标文件, 然后删除其他不需要的文件

```shell
cargo tauri icon oneid_installer.png
```

## 提供的原始图片

- PNG 格式的大小为 1240x1240 边距为 100 的 LOGO 图标(oneid_logo_1240_100.png)
- PNG 格式的大小为 1240x1240 边距为 0 的 LOGO 图标(oneid_logo_1240.png)
- PNG 格式的白色背景, 中间镂空, 大小为 32x32 的 LOGO 图标(oneid_icon_template.png)
- BMP 格式的大小为 164x314 的 LOGO 图标(oneid_icon.bmp)
- BMP 格式的大小为 150x57 的 LOGO 图标(oneid_header.bmp)

## Windows 图标

**通过通过 1240 大小边距为 0 的 PNG 生成 ico 的图标**

```shell
# 安全令
cargo tauri icon icons/oneid_logo_1240.png
```

保留 icon.png 和 icon.ico, 并添加前缀 `oneid_`

守护进程程序需要 oneid 的 ico 图标, 复制一份到 daemon 目录.

## Linux 图标

通过 1240 大小边距为 100 的 PNG 生成

```shell
cargo tauri icon icons/oneid_logo_1240_100.png -p 16,24,32,48,64,96,128,256
```

在生成的图标名称加上前缀 `oneid_`

## MacOS 图标

**通过 1240 大小边距为 100 的 PNG 生成其他尺寸的图标**

```shell
# 安全令
cargo tauri icon icons/oneid_logo_1240_100.png
```

保留 icon.icns, 并添加前缀 `oneid_`

目录 `client/attachments`中的图片, 按照现有的提供新的图

## 客户端 LOGO

目录 `client/ui/public/assets`

大小为 128x128, 边距为 0 (oneid_logo.png)
