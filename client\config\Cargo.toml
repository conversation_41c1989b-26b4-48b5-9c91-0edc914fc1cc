[package]
description = "配置, 包含一个可执行程序, 用于处理安装包与本地配置不匹配时, 更新本地配置文件"
edition = "2021"
name = "config"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1.0.83"
encrypt-files = { path = "../encrypt-files" }
konst = "0.3.9"
log = "0.4.21"
paths = { workspace = true }
rand = "0.9.0"
semver = { version = "1.0.23", features = ["serde"] }
serde = { version = "1.0.201", features = ["derive"] }
serde_json = "1.0.117"
thiserror = "2.0.4"
url = { version = "2.5.0", features = ["serde"] }
version = { path = "../../version" }

[build-dependencies]
cfg-if = "1.0.0"

[features]
default = ["sdp"]

# SDP 模块
sdp = []
# IAM 模块
iam = []
# 客户端负载均衡模式
cluster = ["sdp", "url/serde"]
