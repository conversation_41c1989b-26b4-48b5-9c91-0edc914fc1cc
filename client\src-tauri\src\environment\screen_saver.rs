/// 屏幕保护程序
/// defaults -currentHost read com.apple.screensaver idleTime
#[cfg(target_os = "macos")]
pub fn screen_saver_active() -> bool {
    use std::process::Command;

    match Command::new("defaults")
        .arg("-currentHost")
        .arg("read")
        .arg("com.apple.screensaver")
        .arg("idleTime")
        .output()
    {
        Ok(output) => {
            if output.status.success() {
                if let Ok(idle_time) = String::from_utf8(output.stdout) {
                    let idle_time: u32 = idle_time.trim().parse().unwrap_or_default();
                    return idle_time > 0;
                }
            }
        }
        Err(err) => log::error!(target: "app", "Failed to get screen server state. {:?}", err),
    }
    false
}

/// 屏幕保护程序
#[cfg(windows)]
pub fn screen_saver_active() -> bool {
    use winreg::{enums::HKEY_CURRENT_USER, RegKey};

    let reg = RegKey::predef(HKEY_CURRENT_USER);
    let Ok(desktop) = reg.open_subkey("Control Panel\\Desktop") else {
        return false;
    };

    desktop.get_value::<String, &str>("SCRNSAVE.EXE").is_ok()
}

/// 屏幕保护程序
#[cfg(target_os = "linux")]
pub fn screen_saver_active() -> bool {
    false
}
