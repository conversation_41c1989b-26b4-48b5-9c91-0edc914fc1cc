use crate::linux::{get_distribution_type, DistributionType};

/// 防火墙
///
/// - 统信/深度:
/// ```shell
/// dbus-send --system --print-reply --dest=com.deepin.defender.firewall /com/deepin/defender/firewall com.deepin.defender.firewall.GetSwitch
/// ```
///
/// - 银河麒麟:
/// ```shell
/// # 设置ksc-defender调用白名单
/// # 将当前程序添加到/etc/dbus-1/conf/com.ksc.defender.limit
///
/// #获取网络状态
/// dbus-send --system --print-reply --dest=com.ksc.defender /firewall com.ksc.defender.firewall.get_networkModeConfig
/// #获取防火墙状态
/// dbus-send --system --print-reply --dest=com.ksc.defender /firewall com.ksc.defender.firewall.get_firewall_mode
/// ```
pub fn is_enable() -> bool {
    let distribution_type = get_distribution_type();
    match distribution_type {
        DistributionType::UOS | DistributionType::Deepin => {
            if match sdp_dbus::deepin_firewall::is_open() {
                Ok(is_open) => is_open,
                Err(err) => {
                    log::error!(
                        "{}",
                        err_ext::ErrorExt::display_chain_with_msg(
                            &err,
                            "Failed to determine if firewall is being turned off, assuming it is turned off"
                        )
                    );
                    false
                }
            } {
                return true;
            }
        }
        DistributionType::Kylin => {
            if match sdp_dbus::kylin_firewall::is_open() {
                Ok(is_open) => is_open,
                Err(err) => {
                    log::error!(
                        "{}",
                        err_ext::ErrorExt::display_chain_with_msg(
                            &err,
                            "Failed to determine if firewall is being turned off, assuming it is turned off"
                        )
                    );
                    false
                }
            } {
                return true;
            }
        }
        _ => (),
    }

    if let Ok(output) = std::process::Command::new("systemctl")
        .args(["is-active", "firewalld"])
        .output()
    {
        if let Ok(status) = String::from_utf8(output.stdout) {
            if status.trim() == "active" {
                return true;
            }
        }
    }

    // 检查 ufw 状态
    if let Ok(output) = std::process::Command::new("ufw").arg("status").output() {
        if let Ok(status) = String::from_utf8(output.stdout) {
            if status.contains("Status: active") {
                return true;
            }
        }
    }

    false
}
