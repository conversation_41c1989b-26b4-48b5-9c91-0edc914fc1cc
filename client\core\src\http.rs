use reqwest::{
    header::{HeaderMap, HeaderValue},
    C<PERSON>, ClientBuilder,
};
use std::time::Duration;
use tokio::sync::OnceCell;

static INSTANCE: OnceCell<Client> = OnceCell::const_new();

fn default_headers() -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.append("Content-Type", HeaderValue::from_static("application/json"));
    headers.append("Accept", HeaderValue::from_static("application/json"));
    headers.append("APP-TYPE", HeaderValue::from_static("DESKTOP"));
    headers
}

pub async fn client() -> &'static Client {
    INSTANCE
        .get_or_init(|| async {
            ClientBuilder::new()
                .default_headers(default_headers())
                .timeout(Duration::from_secs(15))
                .no_proxy()
                .danger_accept_invalid_certs(true)
                .danger_accept_invalid_hostnames(true)
                .build()
                .expect("failed to build client")
        })
        .await
}
