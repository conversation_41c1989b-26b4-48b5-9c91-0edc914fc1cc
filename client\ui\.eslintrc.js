module.exports = {
    root: true,
    env: {
        browser: true,
        node: true,
        es6: true
    },
    // https://github.com/feross/standard/blob/master/RULES.md#javascript-standard-style
    // extends: 'standard',
    extends: ['plugin:vue/recommended', 'eslint:recommended'],
    // required to lint *.vue files
    plugins: [
        'vue', // eslint-plugin-vue
        'prettier' // eslint-plugin-prettier
    ],
    parserOptions: {},
    rules: {
        "vue/html-indent": ["error", 4],
        "no-console": 2, //禁止使用console
        "no-alert": 2, //禁止使用alert
        "no-debugger": 2, //禁止使用debugger
        "indent": [2, 4], //缩进风格设置
        "no-constant-condition": 2, //禁止在条件中使用常量表达式 if(true) if(1)
        "no-dupe-args": 2, //函数参数不能重复
        "no-duplicate-case": 2, //switch中的case标签不能重复
        "no-empty": 2, //块语句中的内容不能为空
        "no-eq-null": 2, //禁止对null使用==或!=运算符
        "no-var": 0, //禁用var，用let和const代替
        "no-unused-vars": 2, //禁止出现未使用过的变量
        "no-use-before-define": 2, //禁止在变量定义之前使用它们
        "no-mixed-spaces-and-tabs": 2, //禁止空格和 tab 的混合缩进
        "no-undef": 2, //不能有未定义的变量
        "vue/multi-word-component-names": 0 //关闭组件命名规则
    },
    // overrides: [{
    //     files: [
    //         '**/__tests__/*.{j,t}s?(x)',
    //         '**/tests/unit/**/*.spec.{j,t}s?(x)'
    //     ],
    //     env: {
    //         jest: true
    //     }
    // }]
}