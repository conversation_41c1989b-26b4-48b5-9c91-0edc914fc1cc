use proc_macro2::{I<PERSON>, TokenStream};
use quote::quote;
use syn::{spanned::Spanned, <PERSON><PERSON><PERSON>, <PERSON>, Type};

pub(super) fn expand_struct(fields: &Fields, struct_name: &Ident) -> syn::Result<TokenStream> {
    if !matches!(fields, Fields::Named(_)) {
        return Err(Error::new(
            proc_macro2::Span::call_site(),
            "can only work on named fields",
        ));
    }

    let mut struct_fields = vec![];
    for field in fields {
        let field_name = field.ident.as_ref().unwrap();

        let is_option = if let Type::Path(type_path) = &field.ty {
            if let Some(first) = type_path.path.segments.first() {
                first.ident == "Option"
            } else {
                false
            }
        } else {
            false
        };

        let token_streams = if let Some(r#type) = crate::get_tlv_value(&field.attrs)? {
            match r#type.as_str() {
                "json" => {
                    if is_option {
                        quote! {
                            if let Some(data) = &self.#field_name {
                                let mut field_bytes = serde_json::to_vec(data).unwrap();
                                tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);
                                bytes.extend(field_bytes);
                            }
                        }
                    } else {
                        quote! {
                            let mut field_bytes = serde_json::to_vec(&self.#field_name).unwrap();
                            tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);

                            bytes.extend(field_bytes);
                        }
                    }
                }
                "hex" => {
                    if is_option {
                        quote! {
                            if let Some(data) = &self.#field_name {
                                let mut field_bytes = hex::decode!(data.as_bytes());
                                tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);

                                bytes.extend(field_bytes);
                            }
                        }
                    } else {
                        quote! {
                            let mut field_bytes = hex::decode!(self.#field_name.as_bytes());
                            tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);

                            bytes.extend(field_bytes);
                        }
                    }
                }
                "sequence" => {
                    if is_option {
                        quote! {
                            if let Some(data) = &self.#field_name {
                                let mut seq_bytes = vec![];
                                for ele in data.iter() {
                                    seq_bytes.extend(tlv::Serialize::serialize(ele));
                                }

                                let len = seq_bytes.len();
                                let mut len_bytes = tlv::utils::len_to_bytes(len);
                                len_bytes.insert(0, tlv::Tag::Sequence as u8);

                                bytes.extend(len_bytes);
                                bytes.extend(seq_bytes);
                            }
                        }
                    } else {
                        quote! {
                            let mut seq_bytes = vec![];
                            for ele in self.#field_name.iter() {
                                seq_bytes.extend(tlv::Serialize::serialize(ele));
                            }

                            let len = seq_bytes.len();
                            let mut len_bytes = tlv::utils::len_to_bytes(len);
                            len_bytes.insert(0, tlv::Tag::Sequence as u8);

                            bytes.extend(len_bytes);
                            bytes.extend(seq_bytes);
                        }
                    }
                }
                "sequence,json" => {
                    if is_option {
                        quote! {
                            if let Some(data) = &self.#field_name {
                                let mut seq_bytes = vec![];
                                for ele in data.iter() {
                                    let mut field_bytes = serde_json::to_vec(ele).unwrap();
                                    tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);
                                    seq_bytes.extend(field_bytes);
                                }

                                let len = seq_bytes.len();
                                let mut len_bytes = tlv::utils::len_to_bytes(len);
                                len_bytes.insert(0, tlv::Tag::Sequence as u8);

                                bytes.extend(len_bytes);
                                bytes.extend(seq_bytes);
                            }
                        }
                    } else {
                        quote! {
                            let mut seq_bytes = vec![];
                            for ele in self.#field_name.iter() {
                                let mut field_bytes = serde_json::to_vec(ele).unwrap();
                                tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);
                                seq_bytes.extend(field_bytes);
                            }

                            let len = seq_bytes.len();
                            let mut len_bytes = tlv::utils::len_to_bytes(len);
                            len_bytes.insert(0, tlv::Tag::Sequence as u8);

                            bytes.extend(len_bytes);
                            bytes.extend(seq_bytes);
                        }
                    }
                }
                _ => {
                    return Err(Error::new(
                        field.span(),
                        format!("unknown type `{}`", r#type),
                    ));
                }
            }
        } else if is_option {
            quote! {
                if let Some(data) = &self.#field_name {
                    let field_bytes = tlv::Serialize::serialize(data);
                    bytes.extend(field_bytes);
                }
            }
        } else {
            quote! {
                let field_bytes = tlv::Serialize::serialize(&self.#field_name);
                bytes.extend(field_bytes);
            }
        };

        struct_fields.push(token_streams);
    }

    let struct_fields = TokenStream::from_iter(struct_fields);

    Ok(quote! {
        impl tlv::Serialize for #struct_name {
            fn serialize(&self) -> Vec<u8> {
                let mut bytes = vec![];

                #struct_fields

                bytes
            }
        }
    })
}
