use std::{
    ffi::{self, CString},
    fs, io,
    process::Command,
};

pub fn iface_index(name: &str) -> Result<libc::c_uint, IfaceIndexLookupError> {
    let c_name = CString::new(name)
        .map_err(|e| IfaceIndexLookupError::InvalidInterfacename(name.to_owned(), e))?;

    let index = unsafe { libc::if_nametoindex(c_name.as_ptr()) };
    if index == 0 {
        Err(IfaceIndexLookupError::InterfaceLookupError(
            name.to_owned(),
            io::Error::last_os_error(),
        ))
    } else {
        Ok(index)
    }
}

#[derive(Debug, thiserror::Error)]
pub enum IfaceIndexLookupError {
    #[error("Invalid network interface name: {0}")]
    InvalidInterfacename(String, #[source] ffi::NulError),

    #[error("Failed to get index for interface {0}")]
    InterfaceLookupError(String, #[source] io::Error),
}

pub fn detect_package_ext() -> &'static str {
    // Prioritize checking for specific package managers in a logical order
    if check_package_manager("apt") {
        "deb"
    } else if check_package_manager("rpm") {
        "rpm"
    } else if check_package_manager("dnf") {
        "rpm"
    } else if check_package_manager("yum") {
        "rpm"
    } else if check_package_manager("pacman") {
        "pkg"
    } else if check_package_manager("zypper") {
        "rpm"
    } else {
        unreachable!()
    }
}

fn check_package_manager(manager_name: &str) -> bool {
    match Command::new(manager_name).arg("--version").output() {
        Ok(output) => {
            // Check for successful execution (exit code 0)
            if output.status.success() {
                true
            } else {
                false
            }
        }
        Err(_) => false, // Command not found
    }
}

/// Linux 发行版类型
pub enum DistributionType {
    /// 统信
    UOS,
    /// 麒麟
    Kylin,
    /// 深度
    Deepin,
    /// 其他
    Other,
}

pub fn get_distribution_type() -> DistributionType {
    // 读取 /etc/os-release 文件
    if let Ok(content) = fs::read_to_string("/etc/os-release") {
        // 检查是否为统信 UOS
        if content.contains("UnionTech") || content.contains("uos") {
            return DistributionType::UOS;
        }

        // 检查是否为银河麒麟
        if content.contains("Kylin") {
            return DistributionType::Kylin;
        }

        // 检查是否为深度 Deepin
        if content.contains("Deepin") {
            return DistributionType::Deepin;
        }
    }

    // 如果都不匹配或无法读取文件，返回空字符串
    DistributionType::Other
}
