// #[cfg(target_os = "macos")]
// #[path = "power/macos.rs"]
// mod imp;

#[cfg(target_os = "windows")]
#[path = "power/windows.rs"]
mod imp;

use std::time::Duration;

pub use imp::*;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Wry};
use tokio::sync::{Mute<PERSON>, RwLock};
use tokio_util::sync::CancellationToken;
#[cfg(windows)]
use windows::Win32::UI::WindowsAndMessaging::{
    PBT_APMRESUMEAUTOMATIC, PBT_APMRESUMESUSPEND, PBT_APMSUSPEND,
};

use crate::state::{AppState, PowerState};

/// Power management events
#[allow(dead_code)]
#[non_exhaustive]
#[derive(Debug, Clone, Copy)]
pub enum PowerManagementEvent {
    /// The system is resuming from sleep or hibernation
    /// irrespective of user activity.
    ResumeAutomatic,
    /// The system is resuming from sleep or hibernation
    /// due to user activity.
    ResumeSuspend,
    /// The computer is about to enter a suspended state.
    Suspend,
}

impl PowerManagementEvent {
    fn try_from_winevent(wparam: usize) -> Option<Self> {
        use PowerManagementEvent::*;
        match wparam as u32 {
            PBT_APMRESUMEAUTOMATIC => Some(ResumeAutomatic),
            PBT_APMRESUMESUSPEND => Some(ResumeSuspend),
            PBT_APMSUSPEND => Some(Suspend),
            _ => None,
        }
    }
}

pub fn spawn(app_handle: AppHandle<Wry>) {
    let mut power_mgmt_rx = PowerManagementListener::new();
    tokio::spawn({
        async move {
            app_handle.manage(Mutex::new(Option::<TokenTask>::None));
            while let Some(event) = power_mgmt_rx.next().await {
                match event {
                    PowerManagementEvent::Suspend => {
                        log::debug!(target: "app", "Machine is preparing to enter sleep mode");
                        // 在进去睡眠/休眠模式时, 若处于已登录状态, 则插入一个标记
                        let app_state = app_handle.state::<RwLock<AppState>>();
                        let mut app_state = app_state.write().await;
                        if app_state.user.is_none() {
                            continue;
                        }
                        app_state.power_state = Some(PowerState::Suspend);
                    }
                    PowerManagementEvent::ResumeAutomatic => {
                        log::debug!(target: "app", "Machine is resuming from sleep mode");
                        // 从睡眠/休眠状态还原, 若不具备重连(重连令牌过期)条件,
                        // 通知前端, 给用户一个友好的提示
                        let app_state = app_handle.state::<RwLock<AppState>>();
                        let mut app_state = app_state.write().await;
                        if app_state.user.is_none() {
                            continue;
                        }

                        if !app_state.reonnect_token_is_valid() {
                            app_state.reset();
                            _ = app_handle.emit_all(
                                "recconect_status",
                                serde_json::json!({
                                    "status": false,
                                    "show_long_time_inacitivity_tip": true,
                                }),
                            );
                        } else {
                            if let Some(PowerState::Suspend) = app_state.power_state {
                                app_state.power_state = Some(PowerState::SuspendResume);
                            }

                            // 从睡眠/休眠模式恢复后, 存在长时间没恢复网络的情况.
                            // 启动一个定时任务, 检测重连令牌是否过期, 在未恢复网络之前,
                            // 检测到令牌过期后, 给出提示
                            let new_task = TokenTask::new();
                            new_task.run(app_handle.app_handle()).await;
                        }
                    }
                    _ => (),
                }
            }
        }
    });
}

struct TokenTask {
    shutdown_token: CancellationToken,
}

impl TokenTask {
    fn new() -> Self {
        Self {
            shutdown_token: CancellationToken::new(),
        }
    }

    async fn run(&self, app_handle: AppHandle<Wry>) {
        let task = app_handle.state::<Mutex<Option<TokenTask>>>();
        let mut task = task.lock().await;
        if let Some(task) = task.take() {
            task.shutdown_gracefully();
        }
        drop(task);

        let shutdown = self.shutdown_token.clone();
        tokio::spawn(async move {
            let mut tick = tokio::time::interval(Duration::from_secs(1));

            loop {
                tokio::select! {
                    _ = tick.tick() => {}
                    _ = shutdown.cancelled() => break,
                }

                let app_state = app_handle.state::<RwLock<AppState>>();
                let app_state_guard = app_state.read().await;
                if app_state_guard.connectivity.is_online() {
                    log::debug!(target: "app", "Network is connected, exit the task of checking the reconnect token");
                    break;
                } else if !app_state_guard.reonnect_token_is_valid() {
                    drop(app_state_guard);

                    let mut app_state_guard = app_state.write().await;
                    app_state_guard.reset();
                    drop(app_state_guard);

                    _ = app_handle.emit_all(
                        "recconect_status",
                        serde_json::json!({
                            "status": false,
                            "show_long_time_inacitivity_tip": true,
                        }),
                    );

                    break;
                }
            }
        });
    }

    fn shutdown_gracefully(&self) {
        self.shutdown_token.cancel();
    }
}
