# SDP安装镜像

> 基于ubuntu-22.04.2-live-server-amd64.iso制作.

## 源

```shell
# 备份
cp /etc/apt/sources.list /etc/apt/sources.list.bak

bash -c "cat << EOF > /etc/apt/sources.list && apt update 
deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-proposed main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ jammy-proposed main restricted universe multiverse
deb http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse
deb-src http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse
EOF"
```

## prepare

hosts

```shell
echo "127.0.0.1 cubic" > /etc/hosts
```

## 安装软件列表

### base software

```shell
apt install vim -y
apt install netstat -y
apt install tcpdump -y
```

### jdk

拷贝文件 `jdk-8u321-linux.tar.gz`, 将其解压至 `/opt`目录下

```shell
tar -zxf jdk-8u321-linux.tar.gz -C /opt/
or
scp -r root@*************:/opt/oraclejdk_8u321 /opt
mv /opt/oraclejdk_8u321 /opt/jdk1.8.0_321
```

添加环境变量 `/etc/profile.d/jdk.sh`

```shell
tee /etc/profile.d/jdk.sh << EOF
export JAVA_HOME=/opt/jdk1.8.0_321
export PATH=$JAVA_HOME/bin:$PATH
EOF
```

### rabbitmq

1. 启用apt https

```shell
apt-get install apt-transport-https -y
```

2. 添加存储库签名秘钥

```shell
## Team RabbitMQ's main signing key
curl -1sLf "https://keys.openpgp.org/vks/v1/by-fingerprint/0A9AF2115F4687BD29803A206B73A36E6026DFCA" | sudo gpg --dearmor | sudo tee /usr/share/keyrings/com.rabbitmq.team.gpg > /dev/null
## Community mirror of Cloudsmith: modern Erlang repository
curl -1sLf https://ppa1.novemberain.com/gpg.E495BB49CC4BBE5B.key | sudo gpg --dearmor | sudo tee /usr/share/keyrings/rabbitmq.E495BB49CC4BBE5B.gpg > /dev/null
## Community mirror of Cloudsmith: RabbitMQ repository
curl -1sLf https://ppa1.novemberain.com/gpg.9F4587F226208342.key | sudo gpg --dearmor | sudo tee /usr/share/keyrings/rabbitmq.9F4587F226208342.gpg > /dev/null
```

3. 添加源

```shell
## Add apt repositories maintained by Team RabbitMQ
sudo tee /etc/apt/sources.list.d/rabbitmq.list <<EOF
## Provides modern Erlang/OTP releases
##
deb [signed-by=/usr/share/keyrings/rabbitmq.E495BB49CC4BBE5B.gpg] https://ppa1.novemberain.com/rabbitmq/rabbitmq-erlang/deb/ubuntu jammy main
deb-src [signed-by=/usr/share/keyrings/rabbitmq.E495BB49CC4BBE5B.gpg] https://ppa1.novemberain.com/rabbitmq/rabbitmq-erlang/deb/ubuntu jammy main

## Provides RabbitMQ
##
deb [signed-by=/usr/share/keyrings/rabbitmq.9F4587F226208342.gpg] https://ppa1.novemberain.com/rabbitmq/rabbitmq-server/deb/ubuntu jammy main
deb-src [signed-by=/usr/share/keyrings/rabbitmq.9F4587F226208342.gpg] https://ppa1.novemberain.com/rabbitmq/rabbitmq-server/deb/ubuntu jammy main
EOF
```

4. 更新包索引

```shell
apt update -y
```

5. 安装erlang

```shell
## Install Erlang packages
apt install -y erlang-base \
                        erlang-asn1 erlang-crypto erlang-eldap erlang-ftp erlang-inets \
                        erlang-mnesia erlang-os-mon erlang-parsetools erlang-public-key \
                        erlang-runtime-tools erlang-snmp erlang-ssl \
                        erlang-syntax-tools erlang-tftp erlang-tools erlang-xmerl
```

6. 安装rabbitmq-server

```shell
## Install rabbitmq-server and its dependencies
apt install rabbitmq-server -y --fix-missing
```

7. 添加配置文件

   a. /etc/rabbitmq/enabled_plugins

   ```ini
   tee /etc/rabbitmq/enabled_plugins << EOF
   [rabbitmq_management].
   EOF
   ```

   b. /etc/rabbitmq/rabbit.conf

   ```conf
   tee /etc/rabbitmq/rabbit.conf << EOF
   ## This example configuration file demonstrates various settings
   ## available via rabbitmq.conf. It primarily focuses core broker settings
   ## but some tier 1 plugin settings are also covered.
   ##
   ## This file is AN EXAMPLE. It is NOT MEANT TO BE USED IN PRODUCTION. Instead of
   ## copying the entire (large!) file, create or generate a new rabbitmq.conf for the target system
   ## and populate it with the necessary settings.
   ##
   ## See https://rabbitmq.com/configure.html to learn about how to configure RabbitMQ,
   ## the ini-style format used by rabbitmq.conf, how it is different from `advanced.config`,
   ## how to verify effective configuration, and so on.
   ##
   ## See https://rabbitmq.com/documentation.html for the rest of RabbitMQ documentation.
   ##
   ## In case you have questions, please use RabbitMQ community Slack and the rabbitmq-users Google group
   ## instead of GitHub issues.

   # ======================================
   # Core broker section
   # ======================================


   ## Networking
   ## ====================
   ##
   ## Related doc guide: https://rabbitmq.com/networking.html.
   ##
   ## By default, RabbitMQ will listen on all interfaces, using
   ## the standard (reserved) AMQP 0-9-1 and 1.0 port.
   ##
   listeners.tcp.default = 5672


   ## To listen on a specific interface, provide an IP address with port.
   ## For example, to listen only on localhost for both IPv4 and IPv6:
   ##
   # IPv4
   # listeners.tcp.local    = 127.0.0.1:5672
   # IPv6
   # listeners.tcp.local_v6 = ::1:5672

   ## You can define multiple listeners using listener names
   # listeners.tcp.other_port = 5673
   # listeners.tcp.other_ip   = ***********:5672


   ## TLS listeners are configured in the same fashion as TCP listeners,
   ## including the option to control the choice of interface.
   ##
   # listeners.ssl.default = 5671

   ## It is possible to disable regular TCP (non-TLS) listeners. Clients
   ## not configured to use TLS and the correct TLS-enabled port won't be able
   ## to connect to this node.
   # listeners.tcp = none

   ## Number of Erlang processes that will accept connections for the TCP
   ## and TLS listeners.
   ##
   # num_acceptors.tcp = 10
   # num_acceptors.ssl = 10

   ## Socket writer will force GC every so many bytes transferred.
   ## Default is 1 GiB (`1000000000`). Set to 'off' to disable.
   ##
   # socket_writer.gc_threshold = 1000000000
   #
   ## To disable:
   # socket_writer.gc_threshold = off

   ## Maximum amount of time allowed for the AMQP 0-9-1 and AMQP 1.0 handshake
   ## (performed after socket connection and TLS handshake) to complete, in milliseconds.
   ##
   # handshake_timeout = 10000

   ## Set to 'true' to perform reverse DNS lookups when accepting a
   ## connection. rabbitmqctl and management UI will then display hostnames
   ## instead of IP addresses. Default value is `false`.
   ##
   # reverse_dns_lookups = false

   ##
   ## Security, Access Control
   ## ==============
   ##

   ## Related doc guide: https://rabbitmq.com/access-control.html.

   ## The default "guest" user is only permitted to access the server
   ## via a loopback interface (e.g. localhost).
   ## {loopback_users, [<<"guest">>]},
   ##
   # loopback_users.guest = true

   ## Uncomment the following line if you want to allow access to the
   ## guest user from anywhere on the network.
   # loopback_users.guest = false

   ## TLS configuration.
   ##
   ## Related doc guide: https://rabbitmq.com/ssl.html.
   ##
   # listeners.ssl.1                  = 5671
   # 
   # ssl_options.verify               = verify_peer
   # ssl_options.fail_if_no_peer_cert = false
   # ssl_options.cacertfile           = /path/to/cacert.pem
   # ssl_options.certfile             = /path/to/cert.pem
   # ssl_options.keyfile              = /path/to/key.pem
   #
   # ssl_options.honor_cipher_order   = true
   # ssl_options.honor_ecc_order      = true
   #
   ## These are highly recommended for TLSv1.2 but cannot be used
   ## with TLSv1.3. If TLSv1.3 is enabled, these lines MUST be removed.
   # ssl_options.client_renegotiation = false
   # ssl_options.secure_renegotiate   = true
   #
   ## Limits what TLS versions the server enables for client TLS
   ## connections. See https://www.rabbitmq.com/ssl.html#tls-versions for details.
   ##
   ## Cutting edge TLS version which requires recent client runtime
   ## versions and has no cipher suite in common with earlier TLS versions.
   # ssl_options.versions.1 = tlsv1.3
   ## Enables TLSv1.2 for best compatibility
   # ssl_options.versions.2 = tlsv1.2
   ## Older TLS versions have known vulnerabilities and are being phased out
   ## from wide use.

   ## Limits what cipher suites the server will use for client TLS
   ## connections. Narrowing this down can prevent some clients
   ## from connecting.
   ## If TLSv1.3 is enabled and cipher suites are overridden, TLSv1.3-specific
   ## cipher suites must also be explicitly enabled.
   ## See https://www.rabbitmq.com/ssl.html#cipher-suites and https://wiki.openssl.org/index.php/TLS1.3#Ciphersuites
   ## for details.
   #
   ## The example below uses TLSv1.3 cipher suites only
   #
   # ssl_options.ciphers.1  = TLS_AES_256_GCM_SHA384
   # ssl_options.ciphers.2  = TLS_AES_128_GCM_SHA256
   # ssl_options.ciphers.3  = TLS_CHACHA20_POLY1305_SHA256
   # ssl_options.ciphers.4  = TLS_AES_128_CCM_SHA256
   # ssl_options.ciphers.5  = TLS_AES_128_CCM_8_SHA256
   #
   ## The example below uses TLSv1.2 cipher suites only
   #
   # ssl_options.ciphers.1  = ECDHE-ECDSA-AES256-GCM-SHA384
   # ssl_options.ciphers.2  = ECDHE-RSA-AES256-GCM-SHA384
   # ssl_options.ciphers.3  = ECDHE-ECDSA-AES256-SHA384
   # ssl_options.ciphers.4  = ECDHE-RSA-AES256-SHA384
   # ssl_options.ciphers.5  = ECDH-ECDSA-AES256-GCM-SHA384
   # ssl_options.ciphers.6  = ECDH-RSA-AES256-GCM-SHA384
   # ssl_options.ciphers.7  = ECDH-ECDSA-AES256-SHA384
   # ssl_options.ciphers.8  = ECDH-RSA-AES256-SHA384
   # ssl_options.ciphers.9  = DHE-RSA-AES256-GCM-SHA384
   # ssl_options.ciphers.10 = DHE-DSS-AES256-GCM-SHA384
   # ssl_options.ciphers.11 = DHE-RSA-AES256-SHA256
   # ssl_options.ciphers.12 = DHE-DSS-AES256-SHA256
   # ssl_options.ciphers.13 = ECDHE-ECDSA-AES128-GCM-SHA256
   # ssl_options.ciphers.14 = ECDHE-RSA-AES128-GCM-SHA256
   # ssl_options.ciphers.15 = ECDHE-ECDSA-AES128-SHA256
   # ssl_options.ciphers.16 = ECDHE-RSA-AES128-SHA256
   # ssl_options.ciphers.17 = ECDH-ECDSA-AES128-GCM-SHA256
   # ssl_options.ciphers.18 = ECDH-RSA-AES128-GCM-SHA256
   # ssl_options.ciphers.19 = ECDH-ECDSA-AES128-SHA256
   # ssl_options.ciphers.20 = ECDH-RSA-AES128-SHA256
   # ssl_options.ciphers.21 = DHE-RSA-AES128-GCM-SHA256
   # ssl_options.ciphers.22 = DHE-DSS-AES128-GCM-SHA256
   # ssl_options.ciphers.23 = DHE-RSA-AES128-SHA256
   # ssl_options.ciphers.24 = DHE-DSS-AES128-SHA256
   # ssl_options.ciphers.25 = ECDHE-ECDSA-AES256-SHA
   # ssl_options.ciphers.26 = ECDHE-RSA-AES256-SHA
   # ssl_options.ciphers.27 = DHE-RSA-AES256-SHA
   # ssl_options.ciphers.28 = DHE-DSS-AES256-SHA
   # ssl_options.ciphers.29 = ECDH-ECDSA-AES256-SHA
   # ssl_options.ciphers.30 = ECDH-RSA-AES256-SHA
   # ssl_options.ciphers.31 = ECDHE-ECDSA-AES128-SHA
   # ssl_options.ciphers.32 = ECDHE-RSA-AES128-SHA
   # ssl_options.ciphers.33 = DHE-RSA-AES128-SHA
   # ssl_options.ciphers.34 = DHE-DSS-AES128-SHA
   # ssl_options.ciphers.35 = ECDH-ECDSA-AES128-SHA
   # ssl_options.ciphers.36 = ECDH-RSA-AES128-SHA

   # ssl_options.bypass_pem_cache = true

   ## Select an authentication/authorisation backend to use.
   ##
   ## Alternative backends are provided by plugins, such as rabbitmq-auth-backend-ldap.
   ##
   ## NB: These settings require certain plugins to be enabled.
   ##
   ## Related doc guides:
   ##
   ##  * https://rabbitmq.com/plugins.html
   ##  * https://rabbitmq.com/access-control.html
   ##

   # auth_backends.1   = rabbit_auth_backend_internal

   ## uses separate backends for authentication and authorisation,
   ## see below.
   # auth_backends.1.authn = rabbit_auth_backend_ldap
   # auth_backends.1.authz = rabbit_auth_backend_internal

   ## The rabbitmq_auth_backend_ldap plugin allows the broker to
   ## perform authentication and authorisation by deferring to an
   ## external LDAP server.
   ##
   ## Relevant doc guides:
   ##
   ## * https://rabbitmq.com/ldap.html
   ## * https://rabbitmq.com/access-control.html
   ##
   ## uses LDAP for both authentication and authorisation
   # auth_backends.1 = rabbit_auth_backend_ldap

   ## uses HTTP service for both authentication and
   ## authorisation
   # auth_backends.1 = rabbit_auth_backend_http

   ## uses two backends in a chain: HTTP first, then internal
   # auth_backends.1   = rabbit_auth_backend_http
   # auth_backends.2   = rabbit_auth_backend_internal

   ## Authentication
   ## The built-in mechanisms are 'PLAIN',
   ## 'AMQPLAIN', and 'EXTERNAL' Additional mechanisms can be added via
   ## plugins.
   ##
   ## Related doc guide: https://rabbitmq.com/authentication.html.
   ##
   # auth_mechanisms.1 = PLAIN
   # auth_mechanisms.2 = AMQPLAIN

   ## The rabbitmq-auth-mechanism-ssl plugin makes it possible to
   ## authenticate a user based on the client's x509 (TLS) certificate.
   ## Related doc guide: https://rabbitmq.com/authentication.html.
   ##
   ## To use auth-mechanism-ssl, the EXTERNAL mechanism should
   ## be enabled:
   ##
   # auth_mechanisms.1 = PLAIN
   # auth_mechanisms.2 = AMQPLAIN
   # auth_mechanisms.3 = EXTERNAL

   ## To force x509 certificate-based authentication on all clients,
   ## exclude all other mechanisms (note: this will disable password-based
   ## authentication even for the management UI!):
   ##
   # auth_mechanisms.1 = EXTERNAL

   ## This pertains to both the rabbitmq-auth-mechanism-ssl plugin and
   ## STOMP ssl_cert_login configurations. See the RabbitMQ STOMP plugin
   ## configuration section later in this file and the README in
   ## https://github.com/rabbitmq/rabbitmq-auth-mechanism-ssl for further
   ## details.
   ##
   ## To use the TLS cert's CN instead of its DN as the username
   ##
   # ssl_cert_login_from   = common_name

   ## TLS handshake timeout, in milliseconds.
   ##
   # ssl_handshake_timeout = 5000


   ## Cluster name
   ##
   # cluster_name = dev3.eng.megacorp.local
   cluster_name = rabbit.ngiam.local

   ## Password hashing implementation. Will only affect newly
   ## created users. To recalculate hash for an existing user
   ## it's necessary to update her password.
   ##
   ## To use SHA-512, set to rabbit_password_hashing_sha512.
   ##
   # password_hashing_module = rabbit_password_hashing_sha256

   ## When importing definitions exported from versions earlier
   ## than 3.6.0, it is possible to go back to MD5 (only do this
   ## as a temporary measure!) by setting this to rabbit_password_hashing_md5.
   ##
   # password_hashing_module = rabbit_password_hashing_md5

   ##
   ## Default User / VHost
   ## ====================
   ##

   ## On first start RabbitMQ will create a vhost and a user. These
   ## config items control what gets created.
   ## Relevant doc guide: https://rabbitmq.com/access-control.html
   ##
   # default_vhost = /
   # default_user = guest
   # default_pass = guest

   # default_permissions.configure = .*
   # default_permissions.read = .*
   # default_permissions.write = .*

   ## Tags for default user
   ##
   ## For more details about tags, see the documentation for the
   ## Management Plugin at https://rabbitmq.com/management.html.
   ##
   # default_user_tags.administrator = true

   ## Define other tags like this:
   # default_user_tags.management = true
   # default_user_tags.custom_tag = true

   ##
   ## Additional network and protocol related configuration
   ## =====================================================
   ##

   ## Set the server AMQP 0-9-1 heartbeat timeout in seconds.
   ## RabbitMQ nodes will send heartbeat frames at roughly
   ## the (timeout / 2) interval. Two missed heartbeats from
   ## a client will close its connection.
   ##
   ## Values lower than 6 seconds are very likely to produce
   ## false positives and are not recommended.
   ##
   ## Related doc guides:
   ##
   ## * https://rabbitmq.com/heartbeats.html
   ## * https://rabbitmq.com/networking.html
   ##
   # heartbeat = 60

   ## Set the max permissible size of an AMQP frame (in bytes).
   ##
   # frame_max = 131072

   ## Set the max frame size the server will accept before connection
   ## tuning occurs
   ##
   # initial_frame_max = 4096

   ## Set the max permissible number of channels per connection.
   ## 0 means "no limit".
   ##
   # channel_max = 128

   ## Customising TCP Listener (Socket) Configuration.
   ##
   ## Related doc guides:
   ##
   ## * https://rabbitmq.com/networking.html
   ## * https://www.erlang.org/doc/man/inet.html#setopts-2
   ##

   # tcp_listen_options.backlog = 128
   # tcp_listen_options.nodelay = true
   # tcp_listen_options.exit_on_close = false
   #
   # tcp_listen_options.keepalive = true
   # tcp_listen_options.send_timeout = 15000
   #
   # tcp_listen_options.buffer = 196608
   # tcp_listen_options.sndbuf = 196608
   # tcp_listen_options.recbuf = 196608

   ##
   ## Resource Limits & Flow Control
   ## ==============================
   ##
   ## Related doc guide: https://rabbitmq.com/memory.html.

   ## Memory-based Flow Control threshold.
   ##
   # vm_memory_high_watermark.relative = 0.4

   ## Alternatively, we can set a limit (in bytes) of RAM used by the node.
   ##
   # vm_memory_high_watermark.absolute = 1073741824

   ## Or you can set absolute value using memory units (with RabbitMQ 3.6.0+).
   ## Absolute watermark will be ignored if relative is defined!
   ##
   # vm_memory_high_watermark.absolute = 2GB
   ##
   ## Supported unit symbols:
   ##
   ## k, kiB: kibibytes (2^10 - 1,024 bytes)
   ## M, MiB: mebibytes (2^20 - 1,048,576 bytes)
   ## G, GiB: gibibytes (2^30 - 1,073,741,824 bytes)
   ## kB: kilobytes (10^3 - 1,000 bytes)
   ## MB: megabytes (10^6 - 1,000,000 bytes)
   ## GB: gigabytes (10^9 - 1,000,000,000 bytes)



   ## Fraction of the high watermark limit at which queues start to
   ## page message out to disc in order to free up memory.
   ## For example, when vm_memory_high_watermark is set to 0.4 and this value is set to 0.5,
   ## paging can begin as early as when 20% of total available RAM is used by the node.
   ##
   ## Values greater than 1.0 can be dangerous and should be used carefully.
   ##
   ## One alternative to this is to use durable queues and publish messages
   ## as persistent (delivery mode = 2). With this combination queues will
   ## move messages to disk much more rapidly.
   ##
   ## Another alternative is to configure queues to page all messages (both
   ## persistent and transient) to disk as quickly
   ## as possible, see https://rabbitmq.com/lazy-queues.html.
   ##
   # vm_memory_high_watermark_paging_ratio = 0.5

   ## Selects Erlang VM memory consumption calculation strategy. Can be `allocated`, `rss` or `legacy` (aliased as `erlang`),
   ## Introduced in 3.6.11. `rss` is the default as of 3.6.12.
   ## See https://github.com/rabbitmq/rabbitmq-server/issues/1223 and rabbitmq/rabbitmq-common#224 for background.
   # vm_memory_calculation_strategy = rss

   ## Interval (in milliseconds) at which we perform the check of the memory
   ## levels against the watermarks.
   ##
   # memory_monitor_interval = 2500

   ## The total memory available can be calculated from the OS resources
   ## - default option - or provided as a configuration parameter.
   # total_memory_available_override_value = 2GB

   ## Set disk free limit (in bytes). Once free disk space reaches this
   ## lower bound, a disk alarm will be set - see the documentation
   ## listed above for more details.
   ##
   ## Absolute watermark will be ignored if relative is defined!
   # disk_free_limit.absolute = 50000

   ## Or you can set it using memory units (same as in vm_memory_high_watermark)
   ## with RabbitMQ 3.6.0+.
   # disk_free_limit.absolute = 500KB
   # disk_free_limit.absolute = 50mb
   # disk_free_limit.absolute = 5GB

   ## Alternatively, we can set a limit relative to total available RAM.
   ##
   ## Values lower than 1.0 can be dangerous and should be used carefully.
   # disk_free_limit.relative = 2.0

   ##
   ## Clustering
   ## =====================
   ##
   # cluster_partition_handling = ignore

   ## Pauses all nodes on the minority side of a partition. The cluster
   ## MUST have an odd number of nodes (3, 5, etc)
   # cluster_partition_handling = pause_minority

   ## pause_if_all_down strategy require additional configuration
   # cluster_partition_handling = pause_if_all_down

   ## Recover strategy. Can be either 'autoheal' or 'ignore'
   # cluster_partition_handling.pause_if_all_down.recover = ignore

   ## Node names to check
   # cluster_partition_handling.pause_if_all_down.nodes.1 = rabbit@localhost
   # cluster_partition_handling.pause_if_all_down.nodes.2 = hare@localhost

   ## Mirror sync batch size, in messages. Increasing this will speed
   ## up syncing but total batch size in bytes must not exceed 2 GiB.
   ## Available in RabbitMQ 3.6.0 or later.
   ##
   # mirroring_sync_batch_size = 4096

   ## Make clustering happen *automatically* at startup. Only applied
   ## to nodes that have just been reset or started for the first time.
   ##
   ## Relevant doc guide: https://rabbitmq.com//cluster-formation.html
   ##


   ## DNS-based peer discovery. This backend will list A records
   ## of the configured hostname and perform reverse lookups for
   ## the addresses returned.

   # cluster_formation.peer_discovery_backend = rabbit_peer_discovery_dns
   # cluster_formation.dns.hostname = discovery.eng.example.local

   ## This node's type can be configured. If you are not sure
   ## what node type to use, always use 'disc'.
   cluster_formation.node_type = disc

   ## Interval (in milliseconds) at which we send keepalive messages
   ## to other cluster members. Note that this is not the same thing
   ## as net_ticktime; missed keepalive messages will not cause nodes
   ## to be considered down.
   ##
   # cluster_keepalive_interval = 10000

   ##
   ## Statistics Collection
   ## =====================
   ##

   ## Statistics collection interval (in milliseconds). Increasing
   ## this will reduce the load on management database.
   ##
   # collect_statistics_interval = 5000

   ## Fine vs. coarse statistics
   #
   # This value is no longer meant to be configured directly.
   #
   # See https://www.rabbitmq.com/management.html#fine-stats.

   ##
   ## Ra Settings
   ## =====================
   ##
   # raft.segment_max_entries = 65536
   # raft.wal_max_size_bytes = 1048576
   # raft.wal_max_batch_size = 4096
   # raft.snapshot_chunk_size = 1000000

   ##
   ## Misc/Advanced Options
   ## =====================
   ##
   ## NB: Change these only if you understand what you are doing!
   ##

   ## Timeout used when waiting for Mnesia tables in a cluster to
   ## become available.
   ##
   # mnesia_table_loading_retry_timeout = 30000

   ## Retries when waiting for Mnesia tables in the cluster startup. Note that
   ## this setting is not applied to Mnesia upgrades or node deletions.
   ##
   # mnesia_table_loading_retry_limit = 10

   ## Size in bytes below which to embed messages in the queue index.
   ## Related doc guide: https://rabbitmq.com/persistence-conf.html
   ##
   # queue_index_embed_msgs_below = 4096

   ## You can also set this size in memory units
   ##
   # queue_index_embed_msgs_below = 4kb

   ## Whether or not to enable background periodic forced GC runs for all
   ## Erlang processes on the node in "waiting" state.
   ##
   ## Disabling background GC may reduce latency for client operations,
   ## keeping it enabled may reduce median RAM usage by the binary heap
   ## (see https://www.erlang-solutions.com/blog/erlang-garbage-collector.html).
   ##
   ## Before trying this option, please take a look at the memory
   ## breakdown (https://www.rabbitmq.com/memory-use.html).
   ##
   # background_gc_enabled = false

   ## Target (desired) interval (in milliseconds) at which we run background GC.
   ## The actual interval will vary depending on how long it takes to execute
   ## the operation (can be higher than this interval). Values less than
   ## 30000 milliseconds are not recommended.
   ##
   # background_gc_target_interval = 60000

   ## Whether or not to enable proxy protocol support.
   ## Once enabled, clients cannot directly connect to the broker
   ## anymore. They must connect through a load balancer that sends the
   ## proxy protocol header to the broker at connection time.
   ## This setting applies only to AMQP clients, other protocols
   ## like MQTT or STOMP have their own setting to enable proxy protocol.
   ## See the plugins documentation for more information.
   ##
   # proxy_protocol = false

   ## Overriden product name and version.
   ## They are set to "RabbitMQ" and the release version by default.
   # product.name = RabbitMQ
   # product.version = 1.2.3

   ## "Message of the day" file.
   ## Its content is used to expand the logged and printed banners.
   ## Default to /etc/rabbitmq/motd on Unix, %APPDATA%\RabbitMQ\motd.txt
   ## on Windows.
   # motd_file = /etc/rabbitmq/motd

   ## Consumer timeout
   ## If a message delivered to a consumer has not been acknowledge before this timer
   ## triggers the channel will be force closed by the broker. This ensure that
   ## faultly consumers that never ack will not hold on to messages indefinitely.
   ##
   # consumer_timeout = 900000

   ## ----------------------------------------------------------------------------
   ## Advanced Erlang Networking/Clustering Options.
   ##
   ## Related doc guide: https://rabbitmq.com/clustering.html
   ## ----------------------------------------------------------------------------

   # ======================================
   # Kernel section
   # ======================================

   ## Timeout used to detect peer unavailability, including CLI tools.
   ## Related doc guide: https://www.rabbitmq.com/nettick.html.
   ##
   # net_ticktime = 60

   ## Inter-node communication port range.
   ## The parameters inet_dist_listen_min and inet_dist_listen_max
   ## can be configured in the classic config format only.
   ## Related doc guide: https://www.rabbitmq.com/networking.html#epmd-inet-dist-port-range.


   ## ----------------------------------------------------------------------------
   ## RabbitMQ Management Plugin
   ##
   ## Related doc guide: https://rabbitmq.com/management.html.
   ## ----------------------------------------------------------------------------

   # =======================================
   # Management section
   # =======================================

   ## Preload schema definitions from the following JSON file.
   ## Related doc guide: https://rabbitmq.com/management.html#load-definitions.
   ##
   # management.load_definitions = /path/to/exported/definitions.json

   ## Log all requests to the management HTTP API to a file.
   ##
   # management.http_log_dir = /path/to/access.log

   ## HTTP listener and embedded Web server settings.
   # ## See https://rabbitmq.com/management.html for details.
   #
   # management.tcp.port = 15672
   # management.tcp.ip   = 0.0.0.0
   #
   # management.tcp.shutdown_timeout   = 7000
   # management.tcp.max_keepalive      = 120
   # management.tcp.idle_timeout       = 120
   # management.tcp.inactivity_timeout = 120
   # management.tcp.request_timeout    = 120
   # management.tcp.compress           = true

   ## HTTPS listener settings.
   ## See https://rabbitmq.com/management.html and https://rabbitmq.com/ssl.html for details.
   ##
   # management.ssl.port       = 15671
   # management.ssl.cacertfile = /path/to/ca_certificate.pem
   # management.ssl.certfile   = /path/to/server_certificate.pem
   # management.ssl.keyfile    = /path/to/server_key.pem

   ## More TLS options
   # management.ssl.honor_cipher_order   = true
   # management.ssl.honor_ecc_order      = true

   ## These are highly recommended for TLSv1.2 but cannot be used
   ## with TLSv1.3. If TLSv1.3 is enabled, these lines MUST be removed.
   # management.ssl.client_renegotiation = false
   # management.ssl.secure_renegotiate   = true

   ## Supported TLS versions
   # management.ssl.versions.1 = tlsv1.2

   ## Cipher suites the server is allowed to use
   # management.ssl.ciphers.1 = ECDHE-ECDSA-AES256-GCM-SHA384
   # management.ssl.ciphers.2 = ECDHE-RSA-AES256-GCM-SHA384
   # management.ssl.ciphers.3 = ECDHE-ECDSA-AES256-SHA384
   # management.ssl.ciphers.4 = ECDHE-RSA-AES256-SHA384
   # management.ssl.ciphers.5 = ECDH-ECDSA-AES256-GCM-SHA384
   # management.ssl.ciphers.6 = ECDH-RSA-AES256-GCM-SHA384
   # management.ssl.ciphers.7 = ECDH-ECDSA-AES256-SHA384
   # management.ssl.ciphers.8 = ECDH-RSA-AES256-SHA384
   # management.ssl.ciphers.9 = DHE-RSA-AES256-GCM-SHA384

   ## URL path prefix for HTTP API and management UI
   # management.path_prefix = /a-prefix

   ## One of 'basic', 'detailed' or 'none'. See
   ## https://rabbitmq.com/management.html#fine-stats for more details.
   # management.rates_mode = basic

   ## Configure how long aggregated data (such as message rates and queue
   ## lengths) is retained. Please read the plugin's documentation in
   ## https://rabbitmq.com/management.html#configuration for more
   ## details.
   ## Your can use 'minute', 'hour' and 'day' keys or integer key (in seconds)
   # management.sample_retention_policies.global.minute    = 5
   # management.sample_retention_policies.global.hour  = 60
   # management.sample_retention_policies.global.day = 1200

   # management.sample_retention_policies.basic.minute   = 5
   # management.sample_retention_policies.basic.hour = 60

   # management.sample_retention_policies.detailed.10 = 5

   ## ----------------------------------------------------------------------------
   ## RabbitMQ Shovel Plugin
   ##
   ## Related doc guide: https://rabbitmq.com/shovel.html
   ## ----------------------------------------------------------------------------

   ## See advanced.config.example for a Shovel plugin example


   ## ----------------------------------------------------------------------------
   ## RabbitMQ STOMP Plugin
   ##
   ## Related doc guide: https://rabbitmq.com/stomp.html
   ## ----------------------------------------------------------------------------

   # =======================================
   # STOMP section
   # =======================================

   ## See https://rabbitmq.com/stomp.html for details.

   ## TCP listeners.
   ##
   # stomp.listeners.tcp.1 = 127.0.0.1:61613
   # stomp.listeners.tcp.2 = ::1:61613

   ## TCP listener settings
   ##
   # stomp.tcp_listen_options.backlog   = 2048
   # stomp.tcp_listen_options.recbuf    = 131072
   # stomp.tcp_listen_options.sndbuf    = 131072
   #
   # stomp.tcp_listen_options.keepalive = true
   # stomp.tcp_listen_options.nodelay   = true
   #
   # stomp.tcp_listen_options.exit_on_close = true
   # stomp.tcp_listen_options.send_timeout  = 120

   ## Proxy protocol support
   ##
   # stomp.proxy_protocol = false

   ## TLS listeners
   ## See https://rabbitmq.com/stomp.html and https://rabbitmq.com/ssl.html for details.
   # stomp.listeners.ssl.default = 61614
   #
   # ssl_options.cacertfile = path/to/cacert.pem
   # ssl_options.certfile   = path/to/cert.pem
   # ssl_options.keyfile    = path/to/key.pem
   # ssl_options.verify     =  verify_peer
   # ssl_options.fail_if_no_peer_cert = true


   ## Number of Erlang processes that will accept connections for the TCP
   ## and TLS listeners.
   ##
   # stomp.num_acceptors.tcp = 10
   # stomp.num_acceptors.ssl = 1

   ## Additional TLS options

   ## Extract a name from the client's certificate when using TLS.
   ##
   # stomp.ssl_cert_login = true

   ## Set a default user name and password. This is used as the default login
   ## whenever a CONNECT frame omits the login and passcode headers.
   ##
   ## Please note that setting this will allow clients to connect without
   ## authenticating!
   ##
   # stomp.default_user = guest
   # stomp.default_pass = guest

   ## If a default user is configured, or you have configured use TLS client
   ## certificate based authentication, you can choose to allow clients to
   ## omit the CONNECT frame entirely. If set to true, the client is
   ## automatically connected as the default user or user supplied in the
   ## TLS certificate whenever the first frame sent on a session is not a
   ## CONNECT frame.
   ##
   # stomp.implicit_connect = true

   ## Whether or not to enable proxy protocol support.
   ## Once enabled, clients cannot directly connect to the broker
   ## anymore. They must connect through a load balancer that sends the
   ## proxy protocol header to the broker at connection time.
   ## This setting applies only to STOMP clients, other protocols
   ## like MQTT or AMQP have their own setting to enable proxy protocol.
   ## See the plugins or broker documentation for more information.
   ##
   # stomp.proxy_protocol = false

   ## ----------------------------------------------------------------------------
   ## RabbitMQ MQTT Adapter
   ##
   ## See https://github.com/rabbitmq/rabbitmq-mqtt/blob/stable/README.md
   ## for details
   ## ----------------------------------------------------------------------------

   # =======================================
   # MQTT section
   # =======================================

   ## TCP listener settings.
   ##
   # mqtt.listeners.tcp.1 = 127.0.0.1:61613
   # mqtt.listeners.tcp.2 = ::1:61613

   ## TCP listener options (as per the broker configuration).
   ##
   # mqtt.tcp_listen_options.backlog = 4096
   # mqtt.tcp_listen_options.recbuf  = 131072
   # mqtt.tcp_listen_options.sndbuf  = 131072
   #
   # mqtt.tcp_listen_options.keepalive = true
   # mqtt.tcp_listen_options.nodelay   = true
   #
   # mqtt.tcp_listen_options.exit_on_close = true
   # mqtt.tcp_listen_options.send_timeout  = 120

   ## TLS listener settings
   ## ## See https://rabbitmq.com/mqtt.html and https://rabbitmq.com/ssl.html for details.
   #
   # mqtt.listeners.ssl.default = 8883
   #
   # ssl_options.cacertfile = /path/to/tls/ca_certificate_bundle.pem
   # ssl_options.certfile   = /path/to/tls/server_certificate.pem
   # ssl_options.keyfile    = /path/to/tls/server_key.pem
   # ssl_options.verify     = verify_peer
   # ssl_options.fail_if_no_peer_cert  = true
   #


   ## Number of Erlang processes that will accept connections for the TCP
   ## and TLS listeners.
   ##
   # mqtt.num_acceptors.tcp = 10
   # mqtt.num_acceptors.ssl = 10

   ## Whether or not to enable proxy protocol support.
   ## Once enabled, clients cannot directly connect to the broker
   ## anymore. They must connect through a load balancer that sends the
   ## proxy protocol header to the broker at connection time.
   ## This setting applies only to STOMP clients, other protocols
   ## like STOMP or AMQP have their own setting to enable proxy protocol.
   ## See the plugins or broker documentation for more information.
   ##
   # mqtt.proxy_protocol = false

   ## Set the default user name and password used for anonymous connections (when client
   ## provides no credentials). Anonymous connections are highly discouraged!
   ##
   # mqtt.default_user = guest
   # mqtt.default_pass = guest

   ## Enable anonymous connections. If this is set to false, clients MUST provide
   ## credentials in order to connect. See also the mqtt.default_user/mqtt.default_pass
   ## keys. Anonymous connections are highly discouraged!
   ##
   # mqtt.allow_anonymous = true

   ## If you have multiple vhosts, specify the one to which the
   ## adapter connects.
   ##
   # mqtt.vhost = /

   ## Specify the exchange to which messages from MQTT clients are published.
   ##
   # mqtt.exchange = amq.topic

   ## Specify TTL (time to live) to control the lifetime of non-clean sessions.
   ##
   # mqtt.subscription_ttl = 1800000

   ## Set the prefetch count (governing the maximum number of unacknowledged
   ## messages that will be delivered).
   ##
   # mqtt.prefetch = 10

   ## Sets the durable queue type to be used for QoS 1 subscriptions.
   ##
   ## Supported types are:
   ## 
   ## * classic
   ## * quorum
   ##
   ## IMPORTANT: changing this setting requires all existing queues used by
   ## the MQTT plugin to be DELETED or clients will fail to subscribe.
   ## So this setting should be used for new clusters.
   ##
   # mqtt.durable_queue_type = classic



   ## ----------------------------------------------------------------------------
   ## RabbitMQ AMQP 1.0 Support
   ##
   ## See https://github.com/rabbitmq/rabbitmq-amqp1.0/blob/stable/README.md.
   ## ----------------------------------------------------------------------------

   # =======================================
   # AMQP 1.0 section
   # =======================================


   ## Connections that are not authenticated with SASL will connect as this
   ## account. See the README for more information.
   ##
   ## Please note that setting this will allow clients to connect without
   ## authenticating!
   ##
   # amqp1_0.default_user = guest

   ## Enable protocol strict mode. See the README for more information.
   ##
   # amqp1_0.protocol_strict_mode = false

   ## Logging settings.
   ##
   ## See https://rabbitmq.com/logging.html for details.
   ##

   ## Log directory, taken from the RABBITMQ_LOG_BASE env variable by default.
   ##
   # log.dir = /var/log/rabbitmq

   ## Logging to file. Can be false or a filename.
   ## Default:
   # log.file = rabbit.log

   ## To disable logging to a file
   # log.file = false

   ## Log level for file logging
   ##
   # log.file.level = info

   ## File rotation config. No rotation by default.
   ## DO NOT SET rotation date to ''. Leave the value unset if "" is the desired value
   # log.file.rotation.date = $D0
   # log.file.rotation.size = 0

   ## Logging to console (can be true or false)
   ##
   # log.console = false

   ## Log level for console logging
   ##
   # log.console.level = info

   ## Logging to the amq.rabbitmq.log exchange (can be true or false)
   ##
   # log.exchange = false

   ## Log level to use when logging to the amq.rabbitmq.log exchange
   ##
   # log.exchange.level = info



   ## ----------------------------------------------------------------------------
   ## RabbitMQ LDAP Plugin
   ##
   ## Related doc guide: https://rabbitmq.com/ldap.html.
   ##
   ## ----------------------------------------------------------------------------

   # =======================================
   # LDAP section
   # =======================================

   ##
   ## Connecting to the LDAP server(s)
   ## ================================
   ##

   ## Specify servers to bind to. You *must* set this in order for the plugin
   ## to work properly.
   ##
   # auth_ldap.servers.1 = your-server-name-goes-here

   ## You can define multiple servers
   # auth_ldap.servers.2 = your-other-server

   ## Connect to the LDAP server using TLS
   ##
   # auth_ldap.use_ssl = false

   ## Specify the LDAP port to connect to
   ##
   # auth_ldap.port = 389

   ## LDAP connection timeout, in milliseconds or 'infinity'
   ##
   # auth_ldap.timeout = infinity

   ## Or number
   # auth_ldap.timeout = 500

   ## Enable logging of LDAP queries.
   ## One of
   ##   - false (no logging is performed)
   ##   - true (verbose logging of the logic used by the plugin)
   ##   - network (as true, but additionally logs LDAP network traffic)
   ##
   ## Defaults to false.
   ##
   # auth_ldap.log = false

   ## Also can be true or network
   # auth_ldap.log = true
   # auth_ldap.log = network

   ##
   ## Authentication
   ## ==============
   ##

   ## Pattern to convert the username given through AMQP to a DN before
   ## binding
   ##
   # auth_ldap.user_dn_pattern = cn=${username},ou=People,dc=example,dc=com

   ## Alternatively, you can convert a username to a Distinguished
   ## Name via an LDAP lookup after binding. See the documentation for
   ## full details.

   ## When converting a username to a dn via a lookup, set these to
   ## the name of the attribute that represents the user name, and the
   ## base DN for the lookup query.
   ##
   # auth_ldap.dn_lookup_attribute = userPrincipalName
   # auth_ldap.dn_lookup_base      = DC=gopivotal,DC=com

   ## Controls how to bind for authorisation queries and also to
   ## retrieve the details of users logging in without presenting a
   ## password (e.g., SASL EXTERNAL).
   ## One of
   ##  - as_user (to bind as the authenticated user - requires a password)
   ##  - anon    (to bind anonymously)
   ##  - {UserDN, Password} (to bind with a specified user name and password)
   ##
   ## Defaults to 'as_user'.
   ##
   # auth_ldap.other_bind = as_user

   ## Or can be more complex:
   # auth_ldap.other_bind.user_dn  = User
   # auth_ldap.other_bind.password = Password

   ## If user_dn and password defined - other options is ignored.

   # -----------------------------
   # Too complex section of LDAP
   # -----------------------------

   ##
   ## Authorisation
   ## =============
   ##

   ## The LDAP plugin can perform a variety of queries against your
   ## LDAP server to determine questions of authorisation.
   ##
   ## Related doc guide: https://rabbitmq.com/ldap.html#authorisation.

   ## Following configuration should be defined in advanced.config file
   ## DO NOT UNCOMMENT THESE LINES!

   ## Set the query to use when determining vhost access
   ##
   ## {vhost_access_query, {in_group,
   ##                       "ou=${vhost}-users,ou=vhosts,dc=example,dc=com"}},

   ## Set the query to use when determining resource (e.g., queue) access
   ##
   ## {resource_access_query, {constant, true}},

   ## Set queries to determine which tags a user has
   ##
   ## {tag_queries, []}
   #   ]},
   # -----------------------------
   EOF
   ```

   c. /etc/rabbitmq/rabbitmq-env.conf

   ```conf
   tee /etc/rabbitmq/rabbitmq-env.conf << EOF 
   # 节点名
   NODENAME=rabbit@sdp-server
   # 家目录(.erlang.cookie文件所在目录)
   HOME=/var/lib/rabbitmq
   # 指定配置文件路径
   CONFIG_FILE=/etc/rabbitmq/rabbitmq.conf
   # RABBITMQ_MNESIA_DIR 的父目录
   MNESIA_BASE=/var/lib/rabbitmq/mnesia
   # 插件所在路径
   PLUGINS_DIR=/usr/lib/rabbitmq/lib/rabbitmq_server-3.12.0/plugins
   # 已启动插件列表文件
   ENABLED_PLUGINS_FILE=/etc/rabbitmq/enabled_plugins
   # RabbitMQ 服务日志所在基础目录
   LOG_BASE=/var/log/rabbitmq
   EOF
   ```
8. 修改服务配置 /lib/systemd/system/rabbitmq-server.service

```ini
tee /lib/systemd/system/rabbitmq-server.service << EOF
# systemd unit example
[Unit]
Description=RabbitMQ broker
After=network.target <EMAIL>
Wants=network.target <EMAIL>

[Service]
Type=notify
User=rabbitmq
Group=rabbitmq
UMask=0027
NotifyAccess=all
TimeoutStartSec=600

# To override LimitNOFILE, create the following file:
#
# /etc/systemd/system/rabbitmq-server.service.d/limits.conf
#
# with the following content:
#
# [Service]
# LimitNOFILE=65536

LimitNOFILE=32768

# The following setting will automatically restart RabbitMQ
# in the event of a failure. systemd service restarts are not a
# replacement for service monitoring. Please see
# https://www.rabbitmq.com/monitoring.html
Restart=on-failure
RestartSec=10
WorkingDirectory=/var/lib/rabbitmq
Environment="RABBITMQ_CONF_ENV_FILE=/etc/rabbitmq/rabbitmq-env.conf"
ExecStart=/usr/lib/rabbitmq/bin/rabbitmq-server
ExecStop=/usr/lib/rabbitmq/bin/rabbitmqctl shutdown
# See rabbitmq/rabbitmq-server-release#51
SuccessExitStatus=69

[Install]
WantedBy=multi-user.target
EOF
```

### elasticsearch && logstash

1. 安装

```shell
export JAVA_HOME=/opt/jdk1.8.0_321

curl -fsSL https://artifacts.elastic.co/GPG-KEY-elasticsearch |sudo gpg --dearmor -o /usr/share/keyrings/elastic.gpg
echo "deb [signed-by=/usr/share/keyrings/elastic.gpg] https://artifacts.elastic.co/packages/6.x/apt stable main" | sudo tee -a /etc/apt/sources.list.d/elastic-6.x.list
apt update
apt install elasticsearch -y
apt install logstash -y
```

2. 修改配置文件
   a. /etc/elasticsearch/elasticsearch.yml

   ```yaml
   tee /etc/elasticsearch/elasticsearch.yml << EOF
   # ======================== Elasticsearch Configuration =========================
   #
   # NOTE: Elasticsearch comes with reasonable defaults for most settings.
   #       Before you set out to tweak and tune the configuration, make sure you
   #       understand what are you trying to accomplish and the consequences.
   #
   # The primary way of configuring a node is via this file. This template lists
   # the most important settings you may want to configure for a production cluster.
   #
   # Please consult the documentation for further information on configuration options:
   # https://www.elastic.co/guide/en/elasticsearch/reference/index.html
   #
   # ---------------------------------- Cluster -----------------------------------
   #
   # Use a descriptive name for your cluster:
   #
   cluster.name: ngiam
   #
   # ------------------------------------ Node ------------------------------------
   #
   # Use a descriptive name for the node:
   #
   node.name: ngiam-01
   #
   # Add custom attributes to the node:
   #
   #node.attr.rack: r1
   #
   # ----------------------------------- Paths ------------------------------------
   #
   # Path to directory where to store the data (separate multiple locations by comma):
   #
   path.data: /var/lib/elasticsearch
   #
   # Path to log files:
   #
   path.logs: /var/log/elasticsearch
   #
   # ----------------------------------- Memory -----------------------------------
   #
   # Lock the memory on startup:
   #
   #bootstrap.memory_lock: true
   #
   # Make sure that the heap size is set to about half the memory available
   # on the system and that the owner of the process is allowed to use this
   # limit.
   #
   # Elasticsearch performs poorly when the system is swapping the memory.
   #
   # ---------------------------------- Network -----------------------------------
   #
   # Set the bind address to a specific IP (IPv4 or IPv6):
   #
   network.host: 0.0.0.0
   #
   # Set a custom port for HTTP:
   #
   http.port: 9200
   transport.tcp.port: 9300
   #
   # For more information, consult the network module documentation.
   #
   # --------------------------------- Discovery ----------------------------------
   #
   # Pass an initial list of hosts to perform discovery when new node is started:
   # The default list of hosts is ["127.0.0.1", "[::1]"]
   #
   #discovery.zen.ping.unicast.hosts: ["host1", "host2"]
   #
   # Prevent the "split brain" by configuring the majority of nodes (total number of master-eligible nodes / 2 + 1):
   #
   #discovery.zen.minimum_master_nodes: 
   #
   # For more information, consult the zen discovery module documentation.
   #
   # ---------------------------------- Gateway -----------------------------------
   #
   # Block initial recovery after a full cluster restart until N nodes are started:
   #
   #gateway.recover_after_nodes: 3
   #
   # For more information, consult the gateway module documentation.
   #
   # ---------------------------------- Various -----------------------------------
   #
   # Require explicit names when deleting indices:
   #
   #action.destructive_requires_name: true

   http.max_initial_line_length: "8k"
   http.max_header_size: "16k"
   xpack.ml.enabled: false
   EOF
   ```

   b. /etc/logstash/conf.d/audit.conf

   ```conf
   tee /etc/logstash/conf.d/audit.conf << EOF
   input{
   	file{
   		path => ["/data/logs/**/audit*.log"]
   		sincedb_path => "/var/lib/logstash/sincedb/.sincedb_audit"
   		start_position => "beginning"
   		ignore_older => 7776000
   		add_field => {"log_type"=>"audit"}
   		codec => "json"
   	}
   #    file{
   #        path => ["/data/logs/**/message*.log"]
   #        sincedb_path => "/var/lib/logstash/sincedb/.sincedb_message"
   #        start_position => "beginning"
   #        ignore_older => 7776000
   #        add_field => {"log_type"=>"message"}
   #        codec => "json"
   #    }
   	file{
   		path => ["/data/logs/**/record*.log"]
   		sincedb_path => "/var/lib/logstash/sincedb/.sincedb_record"
   		start_position => "beginning"
   		ignore_older => 7776000
   		add_field => {"log_type"=>"record"}
   		codec => "json"
   	}
   	file{
   		path => ["/data/logs/**/async*.log"]
   		sincedb_path => "/var/lib/logstash/sincedb/.sincedb_async"
   		start_position => "beginning"
   		ignore_older => 7776000
   		add_field => {"log_type"=>"async"}
   		codec => "json"
   	}
   	file{
   		path => ["/data/logs/**/asset-statistics*.log"]
   		sincedb_path => "/var/lib/logstash/sincedb/.sincedb_asset-statistics"
   		start_position => "beginning"
   		ignore_older => 7776000
   		add_field => {"log_type"=>"asset-statistics"}
   		codec => "json"
   	}
       file{
   		path => ["/data/logs/**/fortress-assetloginfail*.log"]
   		sincedb_path => "/var/lib/logstash/sincedb/.sincedb_fortress-assetloginfail"
   		start_position => "beginning"
   		ignore_older => 7776000
   		add_field => {"log_type"=>"fortress-assetloginfail"}
   		codec => "json"
   	}
   }
   output{
   	if [log_type] == "audit" {
   		elasticsearch{
   			hosts => ["127.0.0.1:9200"]
   			codec => "json"
   			index => "audit-%{+YYYY.MM}"
   		}
   	}
   	if [log_type] == "message" {
   		elasticsearch{
   			hosts => ["127.0.0.1:9200"]
   			codec => "json"
   			index => "message-%{+YYYY.MM}"
   		}
   	}
   	if [log_type] == "async" {
   		elasticsearch{
   			hosts => ["127.0.0.1:9200"]
   			codec => "json"
   			index => "async-%{+YYYY.MM}"
   		}
   	}
   	if [log_type] == "asset-statistics" {
   		elasticsearch{
   			hosts => ["127.0.0.1:9200"]
   			codec => "json"
   			index => "asset-statistics-%{+YYYY.MM}"
   		}
   	}
   	if [log_type] == "fortress-assetloginfail" {
   		elasticsearch{
   			hosts => ["127.0.0.1:9200"]
   			codec => "json"
   			index => "fortress-assetloginfail-%{+YYYY.MM}"
   		}
   	}
   	if [log_type] == "record" {
   		elasticsearch{
   			hosts => ["127.0.0.1:9200"]
   			codec => "json"
   			document_type => "rest_request_record_entity"
   			template => "/etc/logstash/conf.d/rest_request_record_entity.json"
   			template_name => "rest_request_record_entity"
   			template_overwrite => true
   			index => "rest_request_record_entity"
   		}
   	}
   }
   EOF
   ```

   c. /etc/logstash/conf.d/rest_request_record_entity.json

   ```json
   tee /etc/logstash/conf.d/rest_request_record_entity.json << EOF
   {
   	"template":"rest_request_record_entity",
   	"order":1,
   	"settings":{

   	},
   	"mappings":{
   		"rest_request_record_entity":{
   			"properties":{
   				"classMethodName":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"id":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"requestAppId":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"requestAppName":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"requestParam":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"requestTime":{
   					"type":"long"
   				},
   				"responseTime":{
   					"type":"long"
   				},
   				"restAddress":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"restMethod":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"restName":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"restResult":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				},
   				"restSuccess":{
   					"type":"boolean"
   				},
   				"ip":{
   					"type":"text",
   					"fields":{
   						"keyword":{
   							"type":"keyword",
   							"ignore_above":256
   						}
   					}
   				}
   			}
   		}
   	}
   }
   EOF
   ```
3. 创建es日志目录并授权

   ```shell
   chmod 755 /var/log/elasticsearch
   ```
4. set JAVA_HOME

   ```shell
   echo "JAVA_HOME=/opt/jdk1.8.0_321" >> /etc/default/elasticsearch
   echo "JAVA_HOME=/opt/jdk1.8.0_321" >> /etc/default/logstash
   ```

### mysql

```shell
apt install mysql-server -y
```

get ngiam_db.sql

```shell
scp root@*************:/root/ngiam_db.sql /usr/share/mysql/
```

create mysql_setpass.sh

```shell
tee /usr/share/mysql/mysql_setpass.sh << EOFF
#!/bin/bash

# 提示用户输入新的管理员密码
#read -s -p "Enter new password for MySQL root user: " newPassword
#echo
newPassword=NgiamPwd_123

# restart MySQL 服务
sudo systemctl restart mysql


# 等待 MySQL 服务启动
sleep 5

# 连接到 MySQL 服务器，并修改 root 用户密码
mysql -uroot << EO
FLUSH PRIVILEGES;
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '\$newPassword';

CREATE USER 'ngiam'@'localhost' IDENTIFIED BY '\$newPassword';
CREATE DATABASE ngiam_db CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
GRANT ALL PRIVILEGES ON ngiam_db.* TO 'ngiam'@'localhost';
FLUSH PRIVILEGES;
mysql -ungiam -pNgiamPwd_123 -e "source /usr/share/mysql/ngiam_db.sql" -D ngiam_db
EO

# restart MySQL 服务
sudo systemctl restart mysql

echo "MySQL root password has been set successfully."
EOFF
chmod +x /usr/share/mysql/mysql_setpass.sh

```

### openresty

1. 安装

```shell
wget -O - https://openresty.org/package/pubkey.gpg | sudo gpg --dearmor -o /usr/share/keyrings/openresty.gpg

echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/openresty.gpg] http://openresty.org/package/ubuntu $(lsb_release -sc) main" | sudo tee /etc/apt/sources.list.d/openresty.list > /dev/null

apt update

apt -y install openresty
```

2. 配置

a. /usr/local/openresty/nginx/conf/nginx.conf

```conf
tee /usr/local/openresty/nginx/conf/nginx.conf << EOF
user  nginx;
worker_processes  auto;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"'
		      '"$upstream_response_time" "$upstream_addr" "$upstream_status"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip  on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_types application/javascript text/plain application/x-javascript text/css application/xml text/javascript image/jpeg image/gif image/png;
    gzip_disable "MSIE [1-6]\.";
    gzip_vary on;
    gzip_comp_level 3;

    include /etc/nginx/snippets/upstream*.conf;
    include /etc/nginx/conf.d/*.conf;
}
EOF
```

b. ln -s /usr/local/openresty/nginx/conf /etc/nginx

c. mkdir /usr/local/openresty/nginx/conf/conf.d

d. mkdir /etc/nginx/snippets

e. /etc/nginx/conf.d/web_sso.conf

```conf
tee /etc/nginx/conf.d/web_sso.conf << EOF
map \$sent_http_content_type \$expires {
    default off;
    text/html epoch;
    text/css max;
    application/javascript max;
    ~image/ max;
}

add_header X-Frame-Options SAMEORIGIN;
proxy_cache_path levels=1:3 keys_zone=cache-sso:512m inactive=1h max_size=1g use_temp_path=off;

server {
    listen 11080 default_server;
    server_name _;
    add_header X-Xss-Protection "1;mode=block";
    add_header X-Content-Type-Options nosniff;
    client_max_body_size 64M;
    client_header_buffer_size 128k;
    client_body_buffer_size 1m;
    proxy_buffer_size 32k;
    proxy_buffers 64 32k;
    proxy_busy_buffers_size 1m;
    proxy_temp_file_write_size 512k;
    # 引用通用配置代码块
    include snippets/location_sso.conf;
}
EOF
```

f. /etc/nginx/conf.d/web_admin.conf

```shell
tee  /etc/nginx/conf.d/web_admin.conf << EOF
map \$sent_http_content_type \$expires {
  default                    off;
  text/html                  epoch;
  text/css                   max;
  application/javascript     max;
  ~image/                    max;
 }

add_header X-Frame-Options SAMEORIGIN;
proxy_cache_path  levels=1:2 keys_zone=cache:512m inactive=1h max_size=1g use_temp_path=off;

server {
    listen      11081 default_server;
    server_name _;
    add_header X-Xss-Protection "1;mode=block";
    add_header X-Content-Type-Options nosniff;

    client_max_body_size 0;
    proxy_redirect ~/(\w+)/big/upload/(.*) /$1/big/upload/$2;  #继点续传一定要设置(注意)

    client_header_buffer_size 128k;
    client_body_buffer_size 1m;
    proxy_buffer_size 32k;
    proxy_buffers 64 32k;
    proxy_busy_buffers_size 1m;
    proxy_temp_file_write_size 512k;
    # 引用通用配置代码块
    include snippets/location_admin.conf;
}
EOF
```

g. /etc/nginx/snippets/location_sso.conf

```conf
tee /etc/nginx/snippets/location_sso.conf << EOF
location / {
    expires \$expires;
    root   /data/web-sso;
    index  login.html;
    ssi on;
    ssi_silent_errors on;
}
location /common/ {
    expires \$expires;
    root   /data/web-common/;
    index  index.html index.htm;
}

location /terminal/ {
    alias /data/web-terminal/;
    index index.html;
}

# 登录背景
location = /common-sso/app/preference/login-bg {
# ---------------- 代理层缓存配置 -----------------
    proxy_cache cache-sso; # 指定使用哪个共享内存区存储缓存信息。
    proxy_cache_key \$scheme\$proxy_host\$request_uri; #设置缓存使用的key，默认为完整的访问URL，根据实际情况设置缓存key。
    proxy_cache_valid 30m;
    # 为不同的响应状态码设置缓存时间。如果是proxy_cache_valid 5s，则200、301、302响应都将被缓存。
    proxy_cache_use_stale updating;
    add_header Cache-Status \$upstream_cache_status; # 在响应头中添加缓存命中的状态。
    add_header Cache-Control "no-cache";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
# SSO LOGO
location = /common-sso/app/preference/logo {
    add_header Cache-Control "no-store";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
	proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
# SSO MINILOGO
location = /common-sso/app/preference/minilogo {
    add_header Cache-Control "no-store";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/common-sso/v1/sdk/files/* {
    expires \$expires;
    # ---------------- 代理层缓存配置 -----------------
    proxy_cache cache-sso; # 指定使用哪个共享内存区存储缓存信息。
    proxy_cache_key \$scheme\$proxy_host\$request_uri; #设置缓存使用的key，默认为完整的访问URL，根据实际情况设置缓存key。
    proxy_cache_valid 200 1w;
    # 为不同的响应状态码设置缓存时间。如果是proxy_cache_valid 5s，则200、301、302响应都将被缓存。
    add_header Cache-Status \$upstream_cache_status; # 在响应头中添加缓存命中的状态。
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location = /mfa/v1/sdk/mfa/client/wechat/callback {
    add_header X-Frame-Options "allow-from https://open.weixin.qq.com";
    proxy_pass http://mfa;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
	proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/common-admin/v1/sdk/client/environment/* {
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec ;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_pass http://common-admin;
    proxy_http_version 1.1;
}

location ~ ^/common-admin/setup/* {
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec ;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_pass http://common-admin;
    proxy_http_version 1.1;
}

location ~ ^/common-sso/v1/sdk/group {
    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_types application/json;
    gzip_disable "MSIE [1-6].";
    gzip_vary on;
    gzip_comp_level 4;
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location = /common-sso/v1/sdk/auth/strategy {
    #proxy_pass http://common-sso/common-admin/v1/sdk/auth/strategy;
    rewrite ^(.*)\$ http://\$host:11081/common-admin/v1/sdk/auth/strategy;
}

location ~ ^/common-sso.* {
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/ngiam-sso.* {
    proxy_pass http://ngiam-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/mfa.* {
    proxy_pass http://mfa;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/self/.* {
    proxy_pass http://self;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
	proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/help-sso.* {
    proxy_pass http://help-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/notice-sso.* {
    proxy_pass http://notice-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/mdm.* {
    proxy_pass http://mdm;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/todotask.* {
    proxy_pass http://todo-task;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header TIIIME \$msec;
	proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/accessory.* {
    proxy_pass http://accessory;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
}

location ~ ^/fortress-sso.* {
    proxy_pass http://fortress-sso;
    proxy_set_header       Host \$host:\$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
    client_max_body_size 20480m;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
}

location ~ ^/fortress-admin.* {
    proxy_pass http://fortress-admin;
    proxy_set_header       Host \$host:\$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
    client_max_body_size 20480m;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
}

location /win-rst/api/ext/iam/init {
    proxy_pass http://win-rst;
    proxy_set_header       Host \$host:\$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
	proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
    }

location /win-rst/ {
    proxy_pass http://win-rst/win-rst/;
    proxy_set_header       Host \$host:\$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
}
location ~ ^/warn* {
    proxy_pass http://warn;
    proxy_set_header       Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 2s;
    proxy_read_timeout  3600s;

    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
}
location ~ ^/authentication-gateway* {
    proxy_pass http://authentication-gateway;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

########### godfs ############
proxy_redirect ~/(\w+)/big/upload/(.*) /\$1/big/upload/\$2;  #继点续传一定要设置(注意)

if ( \$request_uri ~ /godfs/fileserver ) {
    # 注意group(fileserver)会随组的前缀改变而改变
    rewrite ^/godfs/(.*)\$ /\$1 last;
}

location ~ /fileserver(\d) {
    client_max_body_size 0;
    #统一在url前增加godfs,以便统一出入口。
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_pass http://go-fastdfs\$1;
}
location ~ /godfs/upload {
    #这是一个横向扩展配置，前期可能只有一个集群group(fileserver1),当fileserver1满后，只需将上传指向fileserver1,
    #也就是将rewrite , proxy_pass 中的fileserver1改为fileserver2即可。
    client_max_body_size 0;
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    rewrite ^/godfs/upload /filerserver1/upload break;
    proxy_pass http://go-fastdfs1;
}
location ~ /godfs/big/upload {
    #以上上类似。
    client_max_body_size 0;
    proxy_set_header Host \$host;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    rewrite ^/godfs/upload /fileserver1/big/upload break;
    proxy_pass http://go-fastdfs1;
}

location ~ ^/sdp/* {
    proxy_pass http://sdp;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/tfa/* {
    proxy_pass http://tfa;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/analysis* {
    proxy_pass http://analysis;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/app-permission-sso.* {
    proxy_set_header Host      \$host:\$server_port;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec ;
    proxy_connect_timeout 2s;
    proxy_read_timeout  3600s;
    proxy_pass http://permission;
}
EOF
```

e. /etc/nginx/snippets/location_admin.conf

```conf
tee /etc/nginx/snippets/location_admin.conf << EOF
location / {
    expires \$expires;
    root   /data/web-admin;
    index  index.html index.htm;
}
location /common {
    expires \$expires;
    root   /data/web-common/;
    index  index.html index.htm;
}

location /terminal/ {
    alias /data/web-terminal/;
    index index.html;
}

location /terminalNew/ {
    alias /data/web-terminalNew/;
    index index.html;
}

# 登录背景
location = /common-admin/app/preference/login-bg {
    add_header Cache-Control "no-cache";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
# ADMIN LOGO
location = /common-admin/app/preference/logo {
    add_header Cache-Control "no-store";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
# ADMIN MINILOGO
location = /common-admin/app/preference/minilogo {
    add_header Cache-Control "no-store";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
# SSO LOGO
location = /common-admin/app/preference/logo/sso {
    add_header Cache-Control "no-store";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

# SSO MINILOGO
location = /common-admin/app/preference/minilogo/sso {
    add_header Cache-Control "no-store";
    # ---------------- 代理层缓存配置 -----------------
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location = /common-sso/v2/swagger-paths {
    rewrite /common-sso/(.+)\$ /common-admin/\$1 break;
    proxy_pass http://common-sso;
    proxy_set_header       Host \$host:\$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
	proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
}

location ~ ^/common-sso/* {
    proxy_pass http://common-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/common-admin/v1/sdk/group/all* {
    gzip on;
    gzip_min_length 1k;
    gzip_buffers 4 16k;
    gzip_types application/json;
    gzip_disable "MSIE [1-6].";
    gzip_vary on;
    gzip_comp_level 4;
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/common-admin* {
    proxy_pass http://common-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
	proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/ngiam-admin* {
    proxy_pass http://ngiam-admin;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/ngiam-sso* {
    proxy_pass http://ngiam-sso;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/warn* {
    proxy_pass http://warn;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}

location ~ ^/analysis* {
    proxy_pass http://analysis;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
	proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/mfa* {
    proxy_pass http://mfa;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/help-admin* {
    proxy_pass http://help-admin;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/notice-admin* {
    proxy_pass http://notice-admin;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/self/.* {
    proxy_pass http://self;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
	proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/mdm* {
    proxy_pass http://mdm;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/openapi* {
    proxy_pass http://openapi;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/todotask* {
    proxy_pass http://todo-task;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/accessory* {
    proxy_pass http://accessory;
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Real-IP  \$remote_addr;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
}
location ~ ^/fortress-admin.* {
    proxy_pass http://fortress-admin;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
    client_max_body_size 20480M;
    proxy_set_header Upgrade \$http_upgrade;
    proxy_set_header Connection "upgrade";
}
location ~ ^/authentication-gateway* {
    proxy_pass http://authentication-gateway;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/sdp/* {
    proxy_pass http://sdp;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/tfa/* {
    proxy_pass http://tfa;
    proxy_set_header  Host \$host:\$server_port;
    proxy_set_header  X-Forwarded-Port \$server_port;
    proxy_set_header  X-Real-IP  \$remote_addr;
    proxy_set_header  X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header  X-Forwarded-Proto  \$scheme;
    proxy_set_header TIIIME \$msec;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_http_version 1.1;
}
location ~ ^/app-permission-admin/* {
    proxy_set_header Host \$host:\$server_port;
    proxy_set_header X-Forwarded-Port \$server_port;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;
    proxy_set_header TIIIME \$msec ;
    proxy_connect_timeout 60s;
    proxy_read_timeout  3600s;
    proxy_pass http://permission;
    proxy_http_version 1.1;
}
EOF
```

f. /etc/nginx/snippets/upstream_common.conf

```conf
tee /etc/nginx/snippets/upstream_common.conf << EOF
upstream sdp {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
 }

upstream common-sso {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream common-admin {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream ngiam-sso {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream ngiam-admin {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream help-sso {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream help-admin {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream notice-sso {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream notice-admin {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream mfa {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream authentication-gateway {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream warn {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream self {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream analysis {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream openapi {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream todo-task {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream accessory {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}
upstream mdm{
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream fortress-admin {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}
upstream permission {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}
EOF
```

g. /etc/nginx/snippets/upstream_sso.conf

```conf
tee /etc/nginx/snippets/upstream_sso.conf << EOF
upstream fortress-sso {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}

upstream win-rst {
    server 127.0.0.1:8080 max_fails=5 fail_timeout=300;
    #balancer_by_lua_file "/etc/nginx/nps/nps_balancer.lua";
    ip_hash; # 弃用原来的lua脚本实现会话保持，改为ip_hash。
}

upstream go-fastdfs1 {
    server 127.0.0.1:48080 max_fails=5 fail_timeout=300;
    ip_hash; #notice:very important(注意)
}
EOF
```

h. /etc/nginx/snippets/upstream_admin.conf

```conf
tee /etc/nginx/snippets/upstream_admin.conf << EOF
upstream tfa {
    server 127.0.0.1:38080 max_fails=5 fail_timeout=300;
}
EOF
```

3. 创建日志目录并授权
   ```shell
   mkdir -p /var/log/nginx
   chmod 755 /var/log/nginx
   ```

### redis

* install redis-server

  ```shell
  apt install redis-server -y
  ```
* add配置文件 `/etc/redis/redis.conf`

```conf
mkdir -p /etc/redis
tee /etc/redis/redis.conf << EOF
bind 0.0.0.0
protected-mode yes
port 6379
tcp-backlog 511
timeout 0
tcp-keepalive 300
daemonize yes
supervised no
pidfile /var/run/redis/redis-server.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
always-show-logo yes
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
rdb-del-sync-files no
dir /var/lib/redis
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-diskless-load disabled
repl-disable-tcp-nodelay no
replica-priority 100
acllog-max-len 128
requirepass NgiamPwd_123
maxmemory 1GB
lazyfree-lazy-eviction yes
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no
lazyfree-lazy-user-del no
oom-score-adj no
oom-score-adj-values 0 200 800
appendonly no
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 0
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
stream-node-max-bytes 4096
stream-node-max-entries 100
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
dynamic-hz yes
aof-rewrite-incremental-fsync yes
rdb-save-incremental-fsync yes
jemalloc-bg-thread yes
EOF
```

* 设置目录权限

```shell
chmod 755 /etc/redis
```

### gofastdfs

1. scp -r root@*************:/opt/gofastdfs /opt
2. service
   ```shell
   tee /etc/systemd/system/gofastdfs.service << EOF
   [Unit]
   Description=go-fastdfs service
   After=syslog.target network.target

   [Service]
   WorkingDirectory=/opt/gofastdfs
   ExecStart=/opt/gofastdfs/fileserver
   ExecReload=/bin/kill -s HUP $MAINPID
   ExecStop=/bin/kill -s QUIT $MAINPID
   PrivateTmp=true
   Restart=always

   [Install]
   WantedBy=multi-user.target
   EOF
   ```

### docker

1. install source

```
sudo apt install apt-transport-https ca-certificates curl gnupg-agent software-properties-common
```

2. add gpg key

```
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -
```

3. add docker apt source

```
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"
```

4. update && install docker

```
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io
```

## 业务包

### web-sso

```shell
scp -r root@*************:/data/web-sso /data
```

```shell
tar -zxf web-sso.tar.gz -C /data
```

### web-admin

```shell
scp -r root@*************:/data/web-admin /data
```

```shell
tar -zxf web-admin.tar.gz -C /data
```

### web-common

```shell
scp -r root@*************:/data/web-common /data

```

```shell
tar -zxf web-common.tar.gz -C /data/web-common && mv /data/web-common/web-commmon /data/web-common/common
```

### web-terminal

```shell
scp -r root@*************:/data/web-terminal /data
```

### web-terminalNew

```shell
scp -r root@*************:/data/web-terminalNew /data
```

### all-boot

1. 将 `server-all-boot.jar`放到 `/data/ngiam`

   ```shell
   mkdir /data/ngiam
   scp -r root@*************:/data/ngiam/server-all-boot.jar /data/ngiam
   ```
2. 添加配置文件

   ```shell
   mkdir /data/config
   scp -r root@*************:/data/config/base /data/config
   scp -r root@*************:/data/config/all-boot /data/config

   ```
3. ngiam-rst.service

   ```shell
   tee /usr/lib/systemd/system/ngiam-rst.service << EOF
   [Unit]
   Description=NGIAM Service
   Documentation=http://www.jingantech.com
   Wants=network-online.target
   After=network-online.target

   [Service]
   Environment=JAVA_HOME=/opt/jdk1.8.0_321

   WorkingDirectory=/data/ngiam

   User=ngiam
   Group=ngiam

   ExecStart=/opt/jdk1.8.0_321/bin/java \\
               -Xms4g -Xmx4g -XX:MetaspaceSize=512m -XX:MaxMetaspaceSize=512m \\
                           -Dsun.jnu.encoding=UTF-8 \\
               -Dfile.encoding=UTF-8 \\
               -Duser.timezone=Asia/Shanghai \\
               -Dspring.config.location=/data/config/base/application-common.properties,/data/config/all-boot/application.properties \\
               -Dspring.cloud.bootstrap.location=/data/config/all-boot/bootstrap.yml \\
               -Dcustom.module.location=/data/config/base/custom.module.json \\
               -Dcustom.server.location=/data/config/all-boot/run.config.json \\
               -Dlogging.config=/data/config/all-boot/log4j2-spring.xml \\
               -jar /data/ngiam/server-all-boot.jar

   StandardOutput=journal
   StandardError=inherit

   LimitNOFILE=65535

   LimitNPROC=4096

   LimitAS=infinity

   LimitFSIZE=infinity

   TimeoutStopSec=0

   KillSignal=SIGTERM

   KillMode=process

   SendSIGKILL=no

   SuccessExitStatus=143

   [Install]
   WantedBy=multi-user.target
   EOF
   ```
4. create log directory

   ```
   mkdir -p /data/logs
   chmod 755 /data/logs
   ```
5. create tomcat directory

   ```shell
   mkdir -p /opt/ngiam-rst/tomcat
   chmod 755 /opt/ngiam-rst/tomcat
   ```
6. krb5.conf

   ```shell
   tee /etc/krb5.conf << EOF
   # Configuration snippets may be placed in this directory as well
   includedir /etc/krb5.conf.d/

   [logging]
    default = FILE:/var/log/krb5libs.log
    kdc = FILE:/var/log/krb5kdc.log
    admin_server = FILE:/var/log/kadmind.log

   [libdefaults]
    dns_lookup_realm = false
    ticket_lifetime = 24h
    renew_lifetime = 7d
    forwardable = true
    rdns = false
   # default_realm = EXAMPLE.COM
    default_ccache_name = KEYRING:persistent:%{uid}

   [realms]
   # EXAMPLE.COM = {
   #  kdc = kerberos.example.com
   #  admin_server = kerberos.example.com
   # }

   [domain_realm]
   # .example.com = EXAMPLE.COM
   # example.com = EXAMPLE.COM
   EOF
   ```
7. mdm_push.p12

   ```shell
   mkdir -p /var/ngiam-message/mdm/cert
   sudo scp root@*************:/var/ngiam-message/mdm/cert/mdm_push.p12 /var/ngiam-message/mdm/cert
   ```

a. /data/config/base/application-common.properties

```properties
tee /data/config/base/application-common.properties << EOF
# 设置默认的数据源或者数据源组,默认值即为main
spring.datasource.dynamic.primary=main
# 严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
spring.datasource.dynamic.strict=true
# 格式化你的sql语句
spring.datasource.dynamic.p6spy=false

# iam-base数据源配置
spring.datasource.dynamic.datasource.base_master.url=***********************************************************************************
spring.datasource.dynamic.datasource.base_master.username=ngiam
spring.datasource.dynamic.datasource.base_master.password=NgiamPwd_123
spring.datasource.dynamic.datasource.base_master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.base_master.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.dynamic.datasource.base_slave.url=***********************************************************************************
spring.datasource.dynamic.datasource.base_slave.username=ngiam
spring.datasource.dynamic.datasource.base_slave.password=NgiamPwd_123
spring.datasource.dynamic.datasource.base_slave.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.base_slave.type=com.zaxxer.hikari.HikariDataSource

# 项目数据源配置
spring.datasource.dynamic.datasource.main_master.url=***********************************************************************************
spring.datasource.dynamic.datasource.main_master.username=ngiam
spring.datasource.dynamic.datasource.main_master.password=NgiamPwd_123
spring.datasource.dynamic.datasource.main_master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.main_master.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.dynamic.datasource.main_slave.url=***********************************************************************************
spring.datasource.dynamic.datasource.main_slave.username=ngiam
spring.datasource.dynamic.datasource.main_slave.password=NgiamPwd_123
spring.datasource.dynamic.datasource.main_slave.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.main_slave.type=com.zaxxer.hikari.HikariDataSource


# mybatis配置
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.configuration.default-enum-type-handler=org.apache.ibatis.type.EnumTypeHandler
mybatis-plus.configuration.map-underscore-to-camel-case=true

mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.nologging.NoLoggingImpl
mybatis-plus.global-config.prefix=iam
# 忽略租户隔离表
mybatis.multi-tenant.ignoreTableNames[0]=system_license
mybatis.multi-tenant.ignoreTableNames[1]=custom_express_ql_config
mybatis.multi-tenant.ignoreTableNames[2]=tenant

# mybatis-enhance 配置
mybatis.ddl.history-table=ddl_history
mybatis.ddl.config[0].schema=ngiam_db
mybatis.ddl.config[0].ds=main
mybatis.ddl.config[0].generator=com.jingantech.mybatis.enhance.ddl.generator.MysqlDdlGenerator
mybatis.ddl.config[0].sql-files[0]=init/db.sql
mybatis.translate.schemas.base=ngiam_db
mybatis.translate.schemas.main=ngiam_db

# redis 配置
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=NgiamPwd_123

# RabbitMQ 配置
spring.rabbitmq.host=127.0.0.1
spring.rabbitmq.port=5672
spring.rabbitmq.username=dev
spring.rabbitmq.password=dev
spring.rabbitmq.virtual-host=/
spring.rabbitmq.listener.type=direct
spring.rabbitmq.template.exchange=ngiam.exchange

# SESSION存储类型
spring.session.store-type=redis
# SESSION存储命名空间
spring.session.redis.namespace=iam
# Redis缓存前缀
spring.cache.redis.key-prefix=iam:caches:
# 缓存类型
spring.cache.type=redis

# ES集群名称
spring.data.elasticsearch.cluster-name=ngiam
# ES集群地址
spring.data.elasticsearch.cluster-nodes=127.0.0.1:9300
# 是否启动ES存储仓库
spring.data.elasticsearch.repositories.enabled=false
# ES Restful地址
spring.elasticsearch.rest.uris=127.0.0.1:9200

# 是否开启thymeleaf模板缓存
spring.thymeleaf.cache=false
# thymeleaf模板路径前缀
spring.thymeleaf.prefix=classpath:/templates/
# thymeleaf模板编码
spring.thymeleaf.encoding=UTF-8
# thymeleaf模板模式
spring.thymeleaf.mode=HTML
# thymeleaf模板后缀
spring.thymeleaf.suffix=.html
# thymeleaf模板Content-Type
spring.thymeleaf.servlet.content-type=text/html

# 配置feign的日志打印级别
feign.client.config.default.logger-level=basic
# 配置feign的连接超时时间
feign.client.config.default.connect-timeout=10000
# 配置feign的数据读取超时时间
feign.client.config.default.read-timeout=30000
# 启用httpClient，通过增加连接池优化性能
feign.httpclient.enabled=true
# 配置最大连接数量
feign.httpclient.max-connections=200
# 配置每次能并行处理的请求数量
feign.httpclient.max-connections-per-route=50
logging.level.com.jingantech.ngiam=debug

# SESSION cookie名称
server.servlet.session.cookie.name=IAM_SID
# SESSION cookie path
server.servlet.session.cookie.path=/

# info.app 配置
info.app.encoding=UTF-8
info.app.java.source=1.8
info.app.java.target=1.8
info.build.name=\${project.name:}
info.build.artifact=\${project.arfitactId:}
info.build.version=\${project.version:}
info.build.time=\${build.time:}

management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=loggers

#接口调用计时
iam.base.controller-statistics.time-enable=false
# 服务license文件
iam.base.license-path=/data/config/license/license.txt
# 系统邮件通知主题
iam.base.email-subject=景安系统通知
# 移动端加密方式
iam.base.client-aes-bit=aes128
# 服务器时间允许的最大时间差(单位: 秒)
iam.base.deviation=3600
# 操作通知短信ID
iam.base.sms.operate-id=SMS_OPERATE
# 重置密码短信ID
iam.base.sms.reset-pwd-id=SMS_RESET_PWD
# 验证码短信ID
iam.base.sms.verify-code-id=SMS_VERIFY_CODE
# 国际化配置文件
iam.base.i18n-extra-files[]=i18n/DomainMessages,\
  i18n/CoreMessages
# 默认语言
iam.base.language=zh
# 是否中心节点
iam.base.distribute.center=true
# 当前节点名称
iam.base.distribute.label=center
# 开启服务通知消息处理失败通知
iam.base.data-change.enable=false
# 服务通知消息处理失败通知邮件主题
iam.base.data-change.subject=数据变更处理失败
# 服务通知消息处理失败通知邮件地址
iam.base.data-change.emails=
# 服务通知消息处理失败通知邮件内容 service: 表示服务  timestamp: 消息处理时间  reason: 处理失败异常信息
iam.base.data-change.content=服务【{{service.name}}】在【{{mills timestamp format="yyyy-MM-dd HH:mm:ss.SSS"}}】有一条通>知消息处理失败.<br/>备注：{{reason}}

# app 配置信息
iam.base.app.data[0].code=common-sso
iam.base.app.data[0].name=个人门户
iam.base.app.data[1].code=common-admin
iam.base.app.data[1].name=管理平台
iam.base.app.data[2].code=desktop_client
iam.base.app.data[2].name=客户端

# swagger 配置
swagger.enabled=true
#springfox.documentation.swagger.v2.path=/swagger/v2/api-docs

jpush-masterSecret=5a0a2b6ce0c2166b39b6a799
jpush-appKey=3d3f06acf8c92bded41a2a97

# mfa服务数据库名称
server.mfa.db.name=ngiam_db
# 堡垒机服务数据库名称
server.fortress.db.name=ngiam_db
# 通用数据库名称
server.common.db.name=ngiam_db

# 邮箱配置
spring.mail.host=smtp.exmail.qq.com
spring.mail.username=<EMAIL>
spring.mail.nickname=景安云信
spring.mail.password=ajSmVq3jVfiHGFZd
spring.mail.default-encoding=utf-8
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.debug=debug

# 容联云配置
sms.serverIp=sandboxapp.cloopen.com
sms.serverPort=8883
sms.accountSid=8aaf070858230abd015823b8b88700cf
sms.accountToken=9b0201f9eebf404eb9888e0330ad492f
sms.appid=8aaf070858230abd015823b8ba0400d6
sms.templateList[0].name=SMS_OPERATE
sms.templateList[0].value=162482
sms.templateList[1].name=SMS_VERIFY_CODE
sms.templateList[1].value=159175
sms.templateList[2].name=SMS_RESET_PWD
sms.templateList[2].value=159171

server.tomcat.protocol-header=X-Forwarded-Proto
server.tomcat.remote-ip-header=X-Real-IP
server.tomcat.port-header=X-Forwarded-Port
server.tomcat.uri-encoding=UTF-8
server.use-forward-headers=true

# 客户端描述
iam.base.client.describe=景安全面数字身份管理客户端
# 基础跳转协议
iam.base.jump.protocol=http

# license 白名单
license.url.check.whiteUrlPrefixList[0]=/swagger
license.url.check.whiteUrlPrefixList[1]=/v2
license.url.check.whiteUrlPrefixList[2]=/webjars
license.url.check.whiteUrlPrefixList[3]=/api
license.url.check.whiteUrlPrefixList[4]=/ngiam-admin/app/preference/theme
license.url.check.whiteUrlPrefixList[5]=/ngiam-admin/app/preference/logo
license.url.check.whiteUrlPrefixList[6]=/ngiam-admin/cas/SSO/alias/ngiamadmin
license.url.check.whiteUrlPrefixList[7]=/app-permission-sso

# 厂商全称
iam.base.company.full-name=北京景安云信科技有限公司
# 厂商简称
iam.base.company.simple-name=景安云信
# 系统名称
iam.base.system-name=统一数字身份认证
# 产品英文名称
iam.base.product.english.name=Comprehensive digital identity management platform
EOF
```

b. /data/config/base/custom.module.json

```json
tee /data/config/base/custom.module.json << EOF
[
    {
        "packageName":"com.jingantech.server.common.sso",
        "beanNamePrefix":"common-sso",
        "mvcUrlPrefix":"common-sso",
        "configKeyPrefix":"common.sso.",
        "secret":"common-sso",
        "name":"通用门户服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/common-sso",
                "http://127.0.0.1:11080/common-sso"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":false,
            "order":1
        }
    },
    {
        "packageName":"com.jingantech.server.ngiam.sso",
        "beanNamePrefix":"ngiam-sso",
        "mvcUrlPrefix":"ngiam-sso",
        "configKeyPrefix":"ngiam.sso.",
        "secret":"ngiam-sso",
        "name":"IAM门户服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/ngiam-sso",
                "http://127.0.0.1:11080/ngiam-sso"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":false,
            "order":3
        }
    },
    {
        "packageName":"com.jingantech.server.common.admin",
        "beanNamePrefix":"common-admin",
        "mvcUrlPrefix":"common-admin",
        "configKeyPrefix":"common.admin.",
        "secret":"common-admin",
        "name":"通用管理门户",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/common-admin",
                "http://127.0.0.1:11081/common-admin"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":false,
            "order":0,
            "admin":true
        }
    },
    {
        "packageName":"com.jingantech.server.ngiam.admin",
        "beanNamePrefix":"ngiam-admin",
        "mvcUrlPrefix":"ngiam-admin",
        "configKeyPrefix":"ngiam.admin.",
        "secret":"ngiam-admin",
        "name":"IAM管理门户",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/ngiam-admin",
                "http://127.0.0.1:11081/ngiam-admin"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":false,
            "order":2
        }
    },
    {
        "packageName":"com.jingantech.server.mfa",
        "beanNamePrefix":"mfa",
        "mvcUrlPrefix":"mfa",
        "configKeyPrefix":"mfa.",
        "secret":"mfa",
        "name":"多因子认证服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/mfa",
                "http://127.0.0.1:11081/mfa"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":4
        }
    },
    {
        "packageName":"com.jingantech.server.accessory",
        "beanNamePrefix":"accessory",
        "mvcUrlPrefix":"accessory",
        "configKeyPrefix":"accessory.",
        "secret":"accessory",
        "name":"附件中心",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/accessory",
                "http://127.0.0.1:11081/accessory"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":5
        }
    },
    {
        "packageName":"com.jingantech.server.todotask",
        "beanNamePrefix":"todotask",
        "mvcUrlPrefix":"todotask",
        "configKeyPrefix":"todotask.",
        "secret":"todo-task",
        "name":"待办服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/todotask",
                "http://127.0.0.1:11081/todotask"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":6
        }
    },
    {
        "packageName":"com.jingantech.server.openapi",
        "beanNamePrefix":"openapi",
        "mvcUrlPrefix":"openapi",
        "configKeyPrefix":"openapi.",
        "secret":"data-center",
        "name":"数据中台服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/openapi",
                "http://127.0.0.1:11081/openapi"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":false,
            "order":7
        }
    },
    {
        "packageName":"com.jingantech.server.analysis",
        "beanNamePrefix":"analysis",
        "mvcUrlPrefix":"analysis",
        "configKeyPrefix":"analysis.",
        "secret":"analysis",
        "name":"报表服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/analysis",
                "http://127.0.0.1:11081/analysis"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":8
        }
    },
    {
        "packageName":"com.jingantech.server.mdm",
        "beanNamePrefix":"mdm",
        "mvcUrlPrefix":"mdm",
        "configKeyPrefix":"mdm.",
        "secret":"mdm",
        "name":"MDM服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/mdm",
                "http://127.0.0.1:11081/mdm"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":9
        }
    },
    {
        "packageName":"com.jingantech.server.self",
        "beanNamePrefix":"self",
        "mvcUrlPrefix":"self",
        "configKeyPrefix":"self.",
        "secret":"self",
        "name":"自助服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/self",
                "http://127.0.0.1:11081/self"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":10
        }
    },
    {
        "packageName":"com.jingantech.server.warn",
        "beanNamePrefix":"warn",
        "mvcUrlPrefix":"warn",
        "configKeyPrefix":"warn.",
        "secret":"warn",
        "name":"审计预警服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/warn",
                "http://127.0.0.1:11081/warn"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":11
        }
    },
    {
        "packageName":"com.jingantech.server.schedule",
        "beanNamePrefix":"schedule",
        "mvcUrlPrefix":"schedule",
        "configKeyPrefix":"schedule.",
        "secret":"schedule",
        "name":"定时任务服务",
        "extendsInfo":{
            "baseUrls":[

            ],
            "builtin":true,
            "enabled":true,
            "initialized":true,
            "changeable":true,
            "order":12
        }
    },
    {
        "packageName":"com.jingantech.server.authgateway",
        "beanNamePrefix":"authgateway",
        "mvcUrlPrefix":"authgateway",
        "configKeyPrefix":"authgateway.",
        "secret":"auth-gateway",
        "name":"认证网关服务",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/auth-gateway",
                "http://127.0.0.1:11081/auth-gateway"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":true,
            "changeable":true,
            "order":13
        }
    },
    {
        "packageName":"com.jingantech.server.help.sso",
        "beanNamePrefix":"help-sso",
        "mvcUrlPrefix":"help-sso",
        "configKeyPrefix":"help.sso.",
        "secret":"help-sso",
        "name":"帮助中心-sso",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/help-sso",
                "http://127.0.0.1:11080/help-sso"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":14
        }
    },
    {
        "packageName":"com.jingantech.server.help.admin",
        "beanNamePrefix":"help-admin",
        "mvcUrlPrefix":"help-admin",
        "configKeyPrefix":"help.admin.",
        "secret":"help-admin",
        "name":"帮助中心-admin",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/help-admin",
                "http://127.0.0.1:11081/help-admin"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":15
        }
    },
    {
        "packageName":"com.jingantech.server.notice.sso",
        "beanNamePrefix":"notice-sso",
        "mvcUrlPrefix":"notice-sso",
        "configKeyPrefix":"notice.sso.",
        "secret":"notice-sso",
        "name":"公告中心-sso",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/notice-sso",
                "http://127.0.0.1:11080/notice-sso"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":16
        }
    },
    {
        "packageName":"com.jingantech.server.notice.admin",
        "beanNamePrefix":"notice-admin",
        "mvcUrlPrefix":"notice-admin",
        "configKeyPrefix":"notice.admin.",
        "secret":"notice-admin",
        "name":"公告中心-admin",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/notice-admin",
                "http://127.0.0.1:11081/notice-admin"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":17
        }
    },
    {
        "packageName":"com.jingantech.server.message",
        "beanNamePrefix":"message",
        "mvcUrlPrefix":"message",
        "configKeyPrefix":"message.",
        "secret":"message",
        "name":"消息服务",
        "extendsInfo":{
            "baseUrls":[

            ],
            "builtin":false,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":18
        }
    },
    {
        "packageName":"com.jingantech.server.fortress.sso",
        "beanNamePrefix":"fortress-sso",
        "mvcUrlPrefix":"fortress-sso",
        "configKeyPrefix":"fortress.sso.",
        "secret":"fortress-sso",
        "name":"堡垒机-sso",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/fortress-sso",
                "http://127.0.0.1:11080/fortress-sso"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":19
        }
    },
    {
        "packageName":"com.jingantech.server.fortress.admin",
        "beanNamePrefix":"fortress-admin",
        "mvcUrlPrefix":"fortress-admin",
        "configKeyPrefix":"fortress.admin.",
        "secret":"fortress-admin",
        "name":"堡垒机-admin",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/fortress-admin",
                "http://127.0.0.1:11081/fortress-admin"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":20
        }
    },
    {
        "packageName":"com.jingantech.server.sdp",
        "beanNamePrefix":"sdp",
        "mvcUrlPrefix":"sdp",
        "configKeyPrefix":"sdp.",
        "secret":"sdp",
        "name":"SDP",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/sdp",
                "http://127.0.0.1:11081/sdp"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":21
        }
    },
    {
        "packageName":"com.jingantech.server.tfa",
        "beanNamePrefix":"tfa",
        "mvcUrlPrefix":"tfa",
        "configKeyPrefix":"tfa.",
        "secret":"tfa",
        "name":"双因数",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/tfa",
                "http://127.0.0.1:11081/tfa"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":21
        }
    },
    {
        "packageName":"com.jingantech.server.app.permission.admin",
        "beanNamePrefix":"app-permission-admin",
        "mvcUrlPrefix":"app-permission-admin",
        "configKeyPrefix":"app-permission-admin",
        "secret":"app-permission-admin",
        "name":"权限网关-admin",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11081/app-permission",
                "http://127.0.0.1:11081/app-permission"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":23
        }
    },
    {
        "packageName":"com.jingantech.server.app.permission.sso",
        "beanNamePrefix":"app-permission-sso",
        "mvcUrlPrefix":"app-permission-sso",
        "configKeyPrefix":"app-permission-sso",
        "secret":"app-permission-sso",
        "name":"权限网关-sso",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/app-permission",
                "http://127.0.0.1:11080/app-permission"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":24
        }
    },
    {
        "packageName":"com.jingantech.server.app.permission",
        "beanNamePrefix":"app-permission",
        "mvcUrlPrefix":"app-permission",
        "configKeyPrefix":"app-permission",
        "secret":"app-permission",
        "name":"权限网关",
        "extendsInfo":{
            "baseUrls":[
                "http://127.0.0.1:11080/app-permission",
                "http://127.0.0.1:11080/app-permission"
            ],
            "builtin":true,
            "enabled":true,
            "initialized":false,
            "changeable":true,
            "order":25
        }
    }
]
EOF
```

add license file

```shell
mkdir -p /data/config/license
scp root@*************:/data/config/license/license.txt /data/config/license/
```

### win-rst

1. file
   ```shell
   scp -r root@*************:/data/web-terminalNew /data
   ```

### sdp

1. copy server

   ```shell
   scp -r root@*************:/opt/sdp_server /opt
   ```
2. edit server.toml.BASE

   ```
   tee /opt/sdp_server/config/server.toml.BASE << EOF
   # SPA 允许的时间误差(秒). 为0时, 不验证时间. 默认不验证时间差。
   spa_time_deviation = 0
   # SPA 端口
   spa_port = 65323
   # DNS 地址
   # dns = "*********"

   # SPA 解密私钥
   sec_key = """-----BEGIN PRIVATE KEY-----
   MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgTITLdgYONqYEc6YW
   E4H+/JPCVJNeyshHbDryCkFYIPugCgYIKoEcz1UBgi2hRANCAAR7NuNVoiyVhWTh
   peEk139qk5xz2G6IdvTGt0MeqGmBzW+04Us2FDiNLULuryDRpM3L+kkZ4oMmXJX3
   q0LIeD/k
   -----END PRIVATE KEY-----"""

   # 白名单 (网关/Nginx地址/控制器地址/Redis/MQ)
   # 端口为0时, 为IP白名单
   whitelist = ['127.0.0.1:0', '127.0.0.1:6379', '127.0.0.1:5672', '127.0.0.1:35672', '**********:0']

   # 本机开放端口
   pub_ports = [11080, 11081, 22, 27882]
   # 授权信息加载间隔时间
   authorization_interval = 5

   # Keepalive
   [keepalive]
       # 空闲时间. 单位(秒)
       idle = 300
       # 间隔时间. 单位(秒)
       interval = 15
       # 重复次数
       retries = 4

   # 后端服务配置
   [backend]
       # 后端服务地址
       url = "http://127.0.0.1:11081"
       # 认证服务context path
       auth_ctx = "/common-admin"
       # 认证服务context path
       sso_ctx = "/common-sso"
       # 资源服务context path
       resource_ctx = "/sdp"
       # 多因子context path
       mfa_ctx = "/mfa"
       # HTTPS 忽略证书CA校验
       ignore_unkown_ca = true

   # Redis 配置
   [redis]
       database = 0
       # Redis密码
       password = "NgiamPwd_123"
       # Redis协议版本
       version = "RESP2"
       # 日志
       tracing = false
   # Redis服务配置
   [redis.server]
       type = "Centralized"
       host = "127.0.0.1"
       port = 6379

   # RabbitMQ 配置
   [mq]
       # MQ地址
       url = "amqp://dev:dev@127.0.0.1:5672//"

   [server]
       # 服务绑定地址
       host = "0.0.0.0"
       # TCP隧道端口
       port = 3030
       # ca证书
       ca = "/opt/sdp_server/cert/ca.crt"
       # cert 证书
       cert = "/opt/sdp_server/cert/ctrl.crt"
       # key 秘钥
       key = "/opt/sdp_server/cert/ctrl.key"

   [xdp_config]
       # 网卡名称  
       iface = "\${NETWORK_NAME}"
       # cpu核心数, 需要与xdp内核程序编译时指定(环境变量 SDP_CORES )的核心数保持一致
       cores = 8

   EOF
   ```
3. edit set_network.sh

   ```
   tee /usr/share/set_network.sh << EOF
   #!/bin/bash

   # 收集当前系统所有启动的网卡名称（排除docker和lo）
   network_cards=\$(ls /sys/class/net | grep -v -e '^docker.*\$' -e '^lo\$')

   # 检查是否只有一个可用的网卡
   if [ \$(echo "\$network_cards" | wc -l) -eq 1 ]; then
     selected_card=\$network_cards
   else
     # 打印网卡序号、名称和IP地址  
     index=1
     for card in \$network_cards; do
       ip_address=\$(ip addr show dev \$card | awk '/inet /{print \$2}')
       echo "\$index. 网卡名：(\$card) IP地址：(\$ip_address)"
       ((index++))
     done

     # 提示用户输入网卡序号
     read -p "请输入使用的网卡序号: " selected_index

     # 根据用户输入的网卡序号打印对应的网卡名称和IP地址
     selected_card=\$(echo "\$network_cards" | awk "NR==\$selected_index")
   fi

   config_file="/opt/sdp_server/config/server.toml.BASE"
   network_name_placeholder="\\\${NETWORK_NAME}"
   network_name_replacement=\$selected_card

   if [ -f "\$config_file" ]; then
     sed "s|\$network_name_placeholder|\$network_name_replacement|g" "\$config_file" > /opt/sdp_server/config/server.toml
     echo "已成功替换配置文件中的占位符。"
   else
     echo "配置文件 \$config_file 不存在。"
   fi
   EOF
   ```
4. edit set_network_default.sh

   ```
   tee /usr/share/set_network_default.sh << EOF
   #!/bin/bash

   # 收集当前系统所有启动的网卡名称（排除docker和lo）
   network_cards=\$(ls /sys/class/net | grep -v -e '^docker\$' -e '^lo\$')

   # 选择序号为 0 的网卡
   selected_card=\$(echo "\$network_cards" | awk "NR==1")

   config_file="/opt/sdp_server/config/server.toml.BASE"
   network_name_placeholder="\\\${NETWORK_NAME}"
   network_name_replacement=\$selected_card

   if [ -f "\$config_file" ]; then
     sed "s|\$network_name_placeholder|\$network_name_replacement|g" "\$config_file" > /opt/sdp_server/config/server.toml
     echo "已成功替换配置文件中的占位符。"
   else
     echo "配置文件 \$config_file 不存在。"
   fi
   EOF
   ```
5. edit sdp_server.service

   ```shell
   tee /usr/lib/systemd/system/sdp_server.service << EOF
   [Unit]
   Description=SDP Service
   After=syslog.target network.target

   [Service]
   WorkingDirectory=/opt/sdp_server
   Environment="RUST_BACKTRACE=1"
   ExecStart=/opt/sdp_server/bin/server --config /opt/sdp_server/config/server.toml --filter error,server=trace --log-dir logs
   ExecReload=/bin/kill -s HUP $MAINPID
   ExecStop=/bin/kill -s QUIT $MAINPID
   ExecStopPost=/usr/bin/ip link set ens192 xdp off
   PrivateTmp=true
   Restart=always

   [Install]
   WantedBy=multi-user.target
   EOF
   ```
6. chmod set_network.sh

   ```
   chmod +x /usr/share/set_network.sh
   ```
7. chmod set_network_default.sh

   ```
   chmod +x set_network_default.sh
   ```
8. install libssl

   ```
   wget http://nz2.archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.0g-2ubuntu4_amd64.deb
   dpkg -i libssl1.1_1.1.0g-2ubuntu4_amd64.deb

   ```

## clear

1. clear apt cache

   ```shell
   apt clean
   ```
2. bak sources.list

   ```shell
   cp /etc/apt/sources.list /etc/apt/sources.list.1
   > /etc/apt/sources.list
   mv /etc/apt/sources.list.d/elastic-6.x.list /etc/apt/sources.list.d/elastic-6.x.list.bak
   mv /etc/apt/sources.list.d/openresty.list /etc/apt/sources.list.d/openresty.list.bak
   mv /etc/apt/sources.list.d/rabbitmq.list /etc/apt/sources.list.d/rabbitmq.list.bak
   ```
3. disable service

   ```shell
   systemctl disable redis-server
   systemctl disable rabbitmq-server
   systemctl disable logstash
   systemctl disable elasticsearch
   systemctl disable mysql
   systemctl disable openresty
   ```

## 添加用户

> 向系统中添加如下用户
> mysqld, ngiam, redis, nginx, elasticsearch, logstash, rabbitmq, sdp

1. 在 `Preseed`Tab中新建一个meta-data文件, 留空
2. 在 `Preseed`Tab中新建一个user-data文件, 输入如下内容

```yaml
#cloud-config
autoinstall:
  version: 1
  interactive-sections:
    # Prompt the user to verify the identity information
    # during installation.
    # - identity
    # Prompt the user to verify the storage information
    # during installation.
    - storage
  ssh:
    allow-pw: true
    install-server: true
  user-data:
    groups:
      - rabbitmq
      - logstash
      - elasticsearch
      - nginx
      - mysql
      - ngiam
      - redis
      - docker
    # Make sure the "user-data" section appears before the "identity"
    # section, otherwise these users will not be created.
    users:
      - name: rabbitmq
        gecos: rabbitmq
        primary_group: rabbitmq
        no_create_home: true
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
      - name: logstash
        gecos: logstash
        primary_group: logstash
        no_create_home: true
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
      - name: elasticsearch
        gecos: elasticsearch
        primary_group: elasticsearch
        no_create_home: true
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
      - name: nginx
        gecos: nginx
        primary_group: nginx
        no_create_home: true
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
      - name: mysql
        gecos: mysql
        primary_group: mysql
        no_create_home: true
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
      - name: ngiam
        gecos: ngiam
        primary_group: ngiam
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
      - name: redis
        gecos: redis
        primary_group: redis
        no_create_home: true
        # Allow the sudo user to execute admin commands without being
        # prompted for the sudo password.
        # sudo: ALL=(ALL) NOPASSWD:ALL
        lock_passwd: true
        shell: /bin/false
        # The password is "123456". This hash was generated using:
        # mkpasswd --method=SHA-512 123456
        passwd: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
    timezone: Asia/Shanghai
    runcmd:
      # 修改redis文件权限
      - chown -R redis:redis /etc/redis
      - chown -R redis:redis /var/log/redis
      - chown -R redis:redis /var/lib/redis
      # mysqld
      - chown -R mysql:mysql /usr/share/mysql
      - chown -R mysql:mysql /var/lib/mysql
      - chown -R mysql:mysql /var/log/mysql
      # elasticsearch
      - chown -R elasticsearch:elasticsearch /var/lib/elasticsearch
      - chown -R elasticsearch:elasticsearch /etc/default/elasticsearch
      - chown -R elasticsearch:elasticsearch /usr/share/elasticsearch
      - chown -R elasticsearch:elasticsearch /var/log/elasticsearch
      - chown -R elasticsearch:elasticsearch /etc/elasticsearch
      # logstash
      - chown -R logstash:logstash /var/log/logstash
      - chown -R logstash:logstash /var/lib/logstash
      - chown logstash:logstash /etc/default/logstash
      # rabbitmq
      - chown -R rabbitmq:rabbitmq /etc/rabbitmq
      - chown -R rabbitmq:rabbitmq /var/lib/rabbitmq
      - chown -R rabbitmq:rabbitmq /var/log/rabbitmq
      # nginx
      - chown -R nginx:nginx /var/log/nginx
      # ngiam
      - chown -R ngiam:ngiam /data/logs
      - chown -R ngiam:ngiam /data/ngiam
      - chown -R ngiam:ngiam /opt/ngiam-rst
      - chown -R ngiam:ngiam /data/config
      - chown ngiam:ngiam /etc/krb5.conf
      - chown -R ngiam:ngiam /var/ngiam-message
      # 启动服务
      - systemctl restart rabbitmq-server.service
      - systemctl restart redis-server.service
      - systemctl restart elasticsearch.service
      - systemctl restart logstash.service
      - systemctl restart openresty.service
      - systemctl restart mysql.service
      - systemctl restart gofastdfs.service
      - systemctl enable rabbitmq-server.service
      - systemctl enable redis-server.service
      - systemctl enable elasticsearch.service
      - systemctl enable logstash.service
      - systemctl enable openresty.service
      - systemctl enable mysql.service
      - systemctl enable gofastdfs.service
      # init mysql
      - /usr/share/mysql/mysql_setpass.sh
      - rm -rf /usr/share/mysql/mysql_setpass.sh
      - /usr/share/set_network_default.sh
      - rm -rf /usr/share/set_network_default.sh
      # after init mysql
      - systemctl start ngiam-rst.service
      - systemctl start sdp_server.service
      - systemctl enable ngiam-rst.service
      - systemctl enable sdp_server.service
      # rabbitmq
      - rabbitmqctl add_user dev dev
      - rabbitmqctl add_user admin admin
      - rabbitmqctl set_user_tags dev policymaker
      - rabbitmqctl set_permissions -p / dev '.*' '.*' '.*'
      - rabbitmqctl set_user_tags admin administrator
      - rabbitmqctl set_permissions -p / admin '.*' '' ''
      # init file
      # - mv /usr/share/init.sh /home/<USER>/init.sh
      # - /home/<USER>/init.sh
  identity:
    hostname: sdp-server
    password: $6$LPSDgjkD7.tgMMn/$QlfY1Q7uD6suiHldUF0wHzF8S0y9bhT0Vhr4jEkLmdiSebKJUpXp0e9lfHkdUUgsGLJk47Fw5A3eLCVvdroTF0
    username: sdp


```

3. 让系统自动创建用户

修改文件 `boot/grub/grub.cfg`

```cfg
set timeout=30

loadfont unicode

set menu_color_normal=white/black
set menu_color_highlight=black/light-gray

menuentry "Try or Install Ubuntu Server" {
	set gfxpayload=keep
	linux	/casper/vmlinuz boot=casper autoinstall "ds=nocloud;s=/cdrom/preseed/" ---
	initrd	/casper/initrd.gz
}
menuentry "Ubuntu Server with the HWE kernel" {
	set gfxpayload=keep
	linux	/casper/vmlinuz  ---
	initrd	/casper/initrd.gz
}
grub_platform
if [ "$grub_platform" = "efi" ]; then
menuentry 'Boot from next volume' {
	exit 1
}
menuentry 'UEFI Firmware Settings' {
	fwsetup
}
else
menuentry 'Test memory' {
	linux16 /boot/memtest86+.bin
}
fi
```

修改文件 `boot/grub/loopback.cfg`

```cfg

menuentry "Try or Install Ubuntu Server" {
	set gfxpayload=keep
	linux	/casper/vmlinuz boot=casper autoinstall "ds=nocloud;s=/cdrom/preseed/" ---
	initrd	/casper/initrd.gz
}
menuentry "Ubuntu Server with the HWE kernel" {
	set gfxpayload=keep
	linux	/casper/vmlinuz  ---
	initrd	/casper/initrd.gz
}

```
