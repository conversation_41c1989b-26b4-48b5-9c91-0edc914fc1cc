use std::{fs, path::PathBuf};

use anyhow::{bail, Context, Result};
use encrypt::{sm4decrypt, sm4encrypt, SM4_IV, SM4_KEY};
use serde::{de::DeserializeOwned, Serialize};

/// read data from yaml as struct T
pub fn read_yaml<T: DeserializeOwned>(path: &PathBuf) -> Result<T> {
    if !path.exists() {
        bail!("file not found \"{}\"", path.display());
    }

    let encrypt_bytes =
        fs::read(path).context(format!("failed to read the file \"{}\"", path.display()))?;

    let yaml_bytes = sm4decrypt(&SM4_KEY, &encrypt_bytes, &SM4_IV)
        .context(format!("failed to decrypt the file \"{}\"", path.display()))?;

    serde_yaml::from_slice::<T>(&yaml_bytes).context(format!(
        "failed to read the file with yaml format \"{}\"",
        path.display()
    ))
}

/// save the data to the file
/// can set `prefix` string to add some comments
pub fn save_yaml<T: Serialize>(path: &PathBuf, data: &T, prefix: Option<&str>) -> Result<()> {
    let data_str = serde_yaml::to_string(data)?;

    let yaml_str = match prefix {
        Some(prefix) => format!("{prefix}\n\n{data_str}"),
        None => data_str,
    };

    let yaml_bytes =
        sm4encrypt(&SM4_KEY, yaml_str.as_bytes(), &SM4_IV).context("failed to encrypt data")?;

    let path_str = path.as_os_str().to_string_lossy().to_string();
    fs::write(path, yaml_bytes).context(format!("failed to save file \"{path_str}\""))
}
