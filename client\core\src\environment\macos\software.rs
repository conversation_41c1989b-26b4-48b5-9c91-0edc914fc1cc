use crate::environment::{running, Software};
use core_foundation::{
    base::{CFType, FromVoid, TCFType},
    bundle::CFBundle,
    string::CFString,
    url::{kCFURLPOSIXPathStyle, CFURL},
};
use core_foundation_sys::{
    base::TCFTypeRef,
    bundle::{CFBundleGetValueForInfoDictionaryKey, CFBundleRef},
};

pub fn list() -> Vec<Software> {
    let mut processes = running::processes(true);
    processes.sort();
    processes.dedup();

    let dirs = vec![
        "/Applications",
        "/System/Applications",
        "/System/Applications/Utilities",
    ];

    let mut software_list = vec![];

    for dir in dirs {
        let cfstr_path = CFString::from_static_string(dir);
        let cfurl_path = CFURL::from_file_system_path(cfstr_path, kCFURLPOSIXPathStyle, true);

        let cfarray = CFBundle::bundles_from_directory(cfurl_path).expect("No applications");
        let values = cfarray.get_all_values();

        unsafe {
            for value in values {
                if value.is_null() {
                    continue;
                }
                let bundle_ref: CFBundleRef =
                    core_foundation_sys::base::TCFTypeRef::from_void_ptr(value);

                if bundle_ref.is_null() {
                    continue;
                }

                let display_name_key = CFString::from_static_string("CFBundleDisplayName");
                let display_name = CFBundleGetValueForInfoDictionaryKey(
                    bundle_ref,
                    display_name_key.as_concrete_TypeRef(),
                );

                if display_name.is_null() {
                    continue;
                }

                let display_name = CFType::from_void(display_name.as_void_ptr());
                if let Some(display_name) = display_name.as_CFType().downcast_into::<CFString>() {
                    let version_key = CFString::from_static_string("CFBundleShortVersionString");
                    let version = CFBundleGetValueForInfoDictionaryKey(
                        bundle_ref,
                        version_key.as_concrete_TypeRef(),
                    );

                    let version = if version.is_null() {
                        None
                    } else {
                        CFType::from_void(version.as_void_ptr())
                            .as_CFType()
                            .downcast_into::<CFString>()
                            .map(|version| version.to_string())
                    };

                    let executable_key = CFString::from_static_string("CFBundleExecutable");
                    let executable = CFBundleGetValueForInfoDictionaryKey(
                        bundle_ref,
                        executable_key.as_concrete_TypeRef(),
                    );

                    let executable = if executable.is_null() {
                        None
                    } else {
                        CFType::from_void(executable.as_void_ptr())
                            .as_CFType()
                            .downcast_into::<CFString>()
                            .map(|version| version.to_string())
                    };

                    let display_name = display_name.to_string();

                    let (executable_location, executable_path) = Some(display_name.clone())
                        .zip(executable.clone())
                        .map(|(path, name)| {
                            (
                                format!("{dir}/{path}.app/Contents/MacOS/{name}"),
                                format!("{dir}/{path}.app/Contents/MacOS"),
                            )
                        })
                        .unzip();

                    software_list.push(Software {
                        run: executable
                            .map(|name| processes.contains(&name))
                            .unwrap_or_default(),
                        software_name: display_name,
                        system_version: version,
                        executable_file_location: executable_location,
                        exe: executable_path,
                    });
                }
            }
        }
    }
    software_list.sort_by(|x, y| x.software_name.cmp(&y.software_name));
    software_list
}
