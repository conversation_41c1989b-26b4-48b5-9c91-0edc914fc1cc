#!/bin/bash

# 读取配置文件路径
CONFIG_FILE="/opt/sdp_server/config/server_tlcp.toml"
SERVICE_NAME="sdp_server_tlcp"
LOG_FILE="/opt/sdp_server/logs/health_check.log"

CLOSE_WAIT_SIZE_FILE="/opt/sdp_server/bin/health_size.txt"

# 默认单位为kB, 默认大小上限为50MB。
log_file_size="51200"

. /etc/profile
usage() {
    echo "Usage: ${0} [-p|--port] [-f|--file] [-n|--name]]" 1>&2
    exit 1
}
#
close_wait_size=$(cat $CLOSE_WAIT_SIZE_FILE)

if [ -z  $close_wait_size ]; then
    close_wait_size=0
fi

function log() {
    local msg=$*
    echo -ne "$(date +['%Y-%m-%d %H:%M:%S']) $msg\n" | tee -a "$LOG_FILE"
}

if du -sk $LOG_FILE 2>/dev/null|awk '{if($1>'${log_file_size}')print "oversized"; else print "normal"}'|grep -q "oversized";then
    log "Clear the log file: $LOG_FILE!"
    : > $LOG_FILE
fi

while [ "$#" -gt 0 ]; do
    case $1 in
        -p|--port)
            log "提取的端口号：$2"
            PORT="$2"
            shift 1
        ;;
        -f|--file)
            CONFIG_FILE="$2"
            shift 1
        ;;
        -n|--name)
            SERVICE_NAME="$2"
            shift 1
        ;;
        *)
            log "Unknown parameter: $1"
            usage
            shift
        ;;
    esac
    shift
done



if [ -z $PORT ]; then
    if [ -f "$CONFIG_FILE" ]; then
        # 使用正则表达式提取最后一个端口号
        ports=$(grep -Po 'port\s*=\s*\K\d+' "$CONFIG_FILE")
        # 获取最后一个端口号
        port=$(echo "$ports" | tail -n1)
    else
        log "$CONFIG_FILE 文件不存在"
        exit 1
    fi

else
    port=$PORT
fi

log "上次的连接状态数量: $close_wait_size"

if [ -n $port ]; then
    log "提取的端口号：$port"
    # 查看正在连接该端口的网络
    connections=$(netstat -an|grep ":$port" | grep CLOSE_WAIT | wc -l)
    log "CLOSE_WAIT 状态的连接数量: $connections"

    new_size=$(($connections -1))
    log "new_size : $new_size"

    if [ $new_size -le $close_wait_size ]; then
        echo 0 >  $CLOSE_WAIT_SIZE_FILE
    else
        echo $connections >  $CLOSE_WAIT_SIZE_FILE
    fi

    # 大于3 重启
    if [ $connections -gt 3 ] || [ $close_wait_size -gt 3 ]; then
        log "执行 systemctl 命令停止服务"
        # 执行 systemctl 命令停止服务
        systemctl stop $SERVICE_NAME
        # 休眠 3 秒
        sleep 3
        log "systemctl 命令启动服务端口的网络"
        # 执行 systemctl 命令启动服务
        systemctl start $SERVICE_NAME
        echo 0 >  $CLOSE_WAIT_SIZE_FILE
    else
        log "未找到连接到该端口的网络"
    fi
else
    log "无法提取端口号"
fi