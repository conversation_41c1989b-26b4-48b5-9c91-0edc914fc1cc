use once_cell::sync::Lazy;
use std::collections::HashMap;
use tokio::sync::RwLock;

use serde_json::Value;

use crate::http;

type PublicIPInfo = HashMap<String, Option<(String, String, String)>>;

static PUBLIC_IP_CACHE: Lazy<RwLock<PublicIPInfo>> = Lazy::new(|| RwLock::new(HashMap::default()));

pub async fn public_ip() -> Option<(String, String, String)> {
    // https://checkip.amazonaws.com
    // https://api.ipify.org
    // https://ifconfig.me/ip
    // https://icanhazip.com
    // https://ipinfo.io/ip
    // https://ipecho.net/plain
    // https://checkipv4.dedyn.io
    let url = "https://ifconfig.me/ip";
    let client = http::client().await;
    let Ok(response) = client.get(url).send().await else {
        return None;
    };
    let bytes = response.bytes().await.unwrap_or_default();
    let internet_ip = String::from_utf8(bytes.to_vec()).unwrap();
    // 获取只读锁
    let hash_map = PUBLIC_IP_CACHE.read().await;

    if let Some(value) = hash_map.get(&internet_ip) {
        let option = value.clone().to_owned();
        drop(hash_map);
        return option;
    } else {
        drop(hash_map);
    }

    let url = "https://www.cz88.net/api/cz88/ip/base?ip=";
    let Ok(response) = client.get(url).send().await else {
        return None;
    };
    let Ok(value) = response.json::<Value>().await else {
        return None;
    };
    if let Some(200) = value["code"].as_u64() {
        let isp = value["data"]["isp"]
            .as_str()
            .map(ToOwned::to_owned)
            .unwrap_or_default();
        let ip = value["data"]["ip"]
            .as_str()
            .map(ToOwned::to_owned)
            .unwrap_or_default();
        let location = value["data"]["country"]
            .as_str()
            .map(ToOwned::to_owned)
            .unwrap_or_default()
            + &value["data"]["province"]
                .as_str()
                .map(ToOwned::to_owned)
                .unwrap_or_default()
            + &value["data"]["city"]
                .as_str()
                .map(ToOwned::to_owned)
                .unwrap_or_default()
            + &value["data"]["districts"]
                .as_str()
                .map(ToOwned::to_owned)
                .unwrap_or_default();
        let some = Some((ip, isp, location));

        // 获取写锁
        let mut hash_map = PUBLIC_IP_CACHE.write().await;
        hash_map.insert(internet_ip, some.clone());
        drop(hash_map);
        log::trace!("public ip {:?}", &some);
        return some;
    }
    None
}
