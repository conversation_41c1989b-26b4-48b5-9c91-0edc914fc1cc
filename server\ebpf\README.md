# Generate vmlinux.rs

## Prerequisites

1. Install bindgen-cli: `cargo install bindgen-cli`

> 安装 `bindgen-cli` 依赖 `clang`
> 
> 参考[https://rust-lang.github.io/rust-bindgen/requirements.html](https://rust-lang.github.io/rust-bindgen/requirements.html)

2. Install aya-tool: `cargo install --git https://github.com/aya-rs/aya -- aya-tool`

## Generate

```shell
aya-tool generate ethhdr icmphdr arphdr iphdr tcphdr udphdr > src/vmlinux.rs
```