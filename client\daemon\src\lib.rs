use std::{fmt::Write, io};

#[cfg(windows)]
mod bio;
pub mod http;
pub mod log;
#[cfg(windows)]
pub mod logon_user;
#[cfg(all(windows, feature = "pki"))]
mod pki;
#[cfg(windows)]
pub mod registry;
pub mod runtime;
pub mod service;
pub mod sm;
pub mod version;

pub type Result<T> = std::result::Result<T, Error>;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Failed to create directory {0}")]
    CreateDirFailed(String, #[source] io::Error),

    #[error("Failed to set directory permissions on {0}")]
    SetDirPermissionFailed(String, #[source] io::Error),

    #[cfg(any(windows, target_os = "macos"))]
    #[error("Not able to find requested directory")]
    FindDirError,

    #[cfg(windows)]
    #[error("Missing %ALLUSERSPROFILE% environment variable")]
    NoProgramDataDir,
}

#[cfg(any(target_os = "linux", target_os = "macos"))]
const PRODUCT_NAME: &str = "oneid";

#[cfg(windows)]
pub const PRODUCT_NAME: &str = "One ID";

/// Used to generate string representations of error chains.
pub trait ErrorExt {
    /// Creates a string representation of the entire error chain.
    fn display_chain(&self) -> String;

    /// Like [Self::display_chain] but with an extra message at the start of the chain
    fn display_chain_with_msg(&self, msg: &str) -> String;
}

impl<E: std::error::Error> ErrorExt for E {
    fn display_chain(&self) -> String {
        let mut s = format!("Error: {}", self);
        let mut source = self.source();
        while let Some(error) = source {
            write!(&mut s, "\nCaused by: {}", error).expect("formatting failed");
            source = error.source();
        }
        s
    }

    fn display_chain_with_msg(&self, msg: &str) -> String {
        let mut s = format!("Error: {}\nCaused by: {}", msg, self);
        let mut source = self.source();
        while let Some(error) = source {
            write!(&mut s, "\nCaused by: {}", error).expect("formatting failed");
            source = error.source();
        }
        s
    }
}
