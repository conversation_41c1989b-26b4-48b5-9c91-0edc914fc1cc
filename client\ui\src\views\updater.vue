<template>
  <div>
    <el-dialog
      v-model="remoteRelease.shouldUpdate"
      center
      :show-close="false"
      destroy-on-close
      class="updaterBox"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <img class="image" src="/assets/images/update.png" alt="" />
      <div class="dialog">
        <div class="find">发现新版本v{{ remoteRelease.payload.version }}</div>
        <div class="tips">
          <p v-for="item in remoteRelease.notes" :key="item">{{ item }}</p>
        </div>
      </div>
      <div v-if="downloading" style="text-align: center">
        <div style="text-align: right; width: 90%">已更新{{ percentage }}%</div>
        <el-progress
          :stroke-width="15"
          :percentage="percentage"
          :color="customColor"
          :show-text="false"
        ></el-progress>
      </div>
      <template #footer>
        <span class="dialog-footer" v-if="!downloading">
          <el-button v-if="!remoteRelease.force" round @click="close">
            稍后再说
          </el-button>
          <el-button round type="primary" @click="download">
            立即更新
          </el-button>
        </span>
        <el-button
          v-if="downloading && !remoteRelease.force && percentage != 100"
          round
          :disabled="percentage == 100"
          @click="cancelDownload"
        >
          取消下载
        </el-button>
        <slot v-if="!downloading && !remoteRelease.force" />
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { invoke } from "@tauri-apps/api/tauri";
import { emit, listen } from "@tauri-apps/api/event";
import { relaunch } from "@tauri-apps/api/process";

export default {
  data() {
    return {
      downloading: false,
      percentage: 0,
      customColor: "#F87F1A",
      remoteRelease: {
        shouldUpdate: false,
      },
      listenEvent: undefined,
      processEvent: undefined,
    };
  },
  beforeUnmount() {
    if (this.listenEvent) {
      this.listenEvent();
    }
    if (this.processEvent) {
      this.processEvent();
    }
  },
  async created() {
    setTimeout(async () => {
      // 监听更新事件
      this.listenEvent = await listen("update_status", async (event) => {
        let status = event.payload.status;
        switch (status) {
          // 开始下载, 显示进度条
          case "PENDING": {
            this.downloading = true;
            break;
          }
          // 安装完成, 重启
          case "DONE": {
            await relaunch();
            break;
          }
          // 下载完成
          case "DOWNLOADED": {
            // 下载完成后, 直接调用安装, 取消`取消下载`按钮
            // this.reset();
            await invoke("install");
            break;
          }
          // 取消下载
          case "CANCEL": {
            this.reset();
            break;
          }
          // 更新出错
          case "ERROR": {
            this.close();
            await emit("log", {
              level: "Error",
              message: "updater: " + event.payload.error,
            });
            this.$message({
              message: "更新失败",
              type: "error",
            });
            break;
          }
        }
      });
    }, 0);

    setTimeout(async () => {
      // 进度条
      this.processEvent = await listen("download_progress", async (event) => {
        // 已下载大小
        let chunkLength = event.payload.chunkSize;
        // 总大小
        let contentLength = event.payload.contentLength;
        // 原生HTTP分块下载时, 长度未知
        // if (contentLength === 0) {
        //    label.innerHTML = '下载中...';
        //    processBar.style.display = 'none';
        //    loopProcessBar.style.display = 'flex';
        //    return;
        // }
        this.percentage = Math.floor((chunkLength / contentLength) * 100);
      });
    }, 0);
  },
  methods: {
    // 初始化
    init(response) {
      response.notes = response.payload.notes.split("#");
      this.remoteRelease = response;
    },
    // 立即下载
    async download() {
      await invoke("download");
    },
    // 取消下载
    async cancelDownload() {
      await invoke("cancel_update");
    },
    close() {
      this.reset();
      this.remoteRelease.shouldUpdate = false;
    },
    // 重置
    reset() {
      // if (this.listenEvent) {
      //   this.listenEvent();
      // }
      // if (this.processEvent) {
      //   this.processEvent();
      // }
      // this.listenEvent = undefined;
      // this.processEvent = undefined;
      this.downloading = false;
      this.percentage = 0;
    },
  },
};
</script>

<style lang="less" scoped>
.find {
  text-align: center;
  font-weight: normal;
  font-size: 20px;
  color: #000;
  margin: 60px 0 28px 0;
}

.tips {
  min-height: 90px;
  max-height: 156px;
  overflow: auto;
  font-size: 15px;
  color: #666;
  padding-left: 33px;
  // margin-bottom: 38px;
}

.image {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -42%);
}
</style>
