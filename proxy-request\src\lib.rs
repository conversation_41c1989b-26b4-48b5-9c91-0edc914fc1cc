use std::{collections::HashMap, path::PathBuf, time::Duration};

use http::header;
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use serde_json::Value;
use serde_repr::{Deserialize_repr, Serialize_repr};
use url::Url;

pub type ProxyResult<T> = std::result::Result<T, Error>;

/// The error types.
#[derive(thiserror::Error, Debug)]
pub enum Error {
    /// Command error.
    #[error("Command Error: {0}")]
    Command(String),
    /// The path operation error.
    #[error("Path Error: {0}")]
    Path(String),
    /// The path StripPrefixError error.
    #[error("Path Error: {0}")]
    PathPrefix(#[from] std::path::StripPrefixError),
    /// Error showing the dialog.
    #[error("Dialog Error: {0}")]
    Dialog(String),
    /// The dialog operation was cancelled by the user.
    #[error("user cancelled the dialog")]
    DialogCancelled,
    /// The network error.
    #[error("Network Error: {0}")]
    Network(#[from] reqwest::Error),
    /// HTTP method error.
    #[error(transparent)]
    HttpMethod(#[from] http::method::InvalidMethod),
    /// Invalid HTTP header value.
    #[error(transparent)]
    HttpHeaderValue(#[from] http::header::InvalidHeaderValue),
    /// Invalid HTTP header value.
    #[error(transparent)]
    HttpHeader(#[from] http::header::InvalidHeaderName),
    /// Failed to serialize header value as string.
    #[error(transparent)]
    Utf8(#[from] std::string::FromUtf8Error),
    /// HTTP form to must be an object.
    #[error("http form must be an object")]
    InvalidHttpForm,
    /// Semver error.
    // #[error(transparent)]
    // Semver(#[from] semver::Error),
    /// JSON error.
    #[error(transparent)]
    Json(#[from] serde_json::Error),
    /// IO error.
    #[error(transparent)]
    Io(#[from] std::io::Error),
    /// Ignore error.
    // #[error("failed to walkdir: {0}")]
    // Ignore(#[from] ignore::Error),
    /// Url error.
    #[error(transparent)]
    Url(#[from] url::ParseError),
    /// failed to detect the current platform.
    #[error("failed to detect platform: {0}")]
    FailedToDetectPlatform(String),
    /// HTTP error.
    #[error(transparent)]
    Http(#[from] http::Error),
}

#[derive(Serialize_repr, Deserialize_repr, Clone, Debug)]
#[repr(u16)]
/// The HTTP response type.
pub enum ResponseType {
    /// Read the response as JSON
    Json = 1,
    /// Read the response as text
    Text,
    /// Read the response as binary
    Binary,
}

/// A file path or contents.
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum FilePart {
    /// File path.
    Path(PathBuf),
    /// File contents.
    Contents(Vec<u8>),
}

impl TryFrom<FilePart> for Vec<u8> {
    type Error = Error;
    fn try_from(file: FilePart) -> ProxyResult<Self> {
        let bytes = match file {
            FilePart::Path(path) => std::fs::read(path)?,
            FilePart::Contents(bytes) => bytes,
        };
        Ok(bytes)
    }
}

/// [`FormBody`] data types.
#[derive(Debug, Serialize, Deserialize)]
#[serde(untagged)]
pub enum FormPart {
    /// A string value.
    Text(String),
    /// A file value.
    #[serde(rename_all = "camelCase")]
    File {
        /// File path or content.
        file: FilePart,
        /// Mime type of this part.
        /// Only used when the `Content-Type` header is set to `multipart/form-data`.
        mime: Option<String>,
        /// File name.
        /// Only used when the `Content-Type` header is set to `multipart/form-data`.
        file_name: Option<String>,
    },
}

/// Form body definition.
#[derive(Debug, Serialize, Deserialize)]
pub struct FormBody(pub HashMap<String, FormPart>);

impl FormBody {
    /// Creates a new form body.
    pub fn new(data: HashMap<String, FormPart>) -> Self {
        Self(data)
    }
}

/// A body for the request.
#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type", content = "payload")]
pub enum Body {
    /// A form body.
    Form(FormBody),
    /// A JSON body.
    Json(Value),
    /// A text string body.
    Text(String),
    /// A byte array body.
    Bytes(Vec<u8>),
}

/// A set of HTTP headers.
#[derive(Debug, Default)]
pub struct HeaderMap(pub header::HeaderMap);

impl Serialize for HeaderMap {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let mut headers = HashMap::new();
        for (header_name, header_value) in &self.0 {
            let value = match header_value.to_str() {
                Ok(value) => value,
                Err(_) => {
                    let Ok(value) = std::str::from_utf8(header_value.as_bytes()) else {
                        continue;
                    };
                    value
                }
            };

            headers.insert(header_name.as_str().to_string(), value.to_owned());
        }

        HashMap::<String, String>::serialize(&headers, serializer)
    }
}

impl<'de> Deserialize<'de> for HeaderMap {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: Deserializer<'de>,
    {
        let map = HashMap::<String, String>::deserialize(deserializer)?;
        let mut headers = header::HeaderMap::default();
        for (key, value) in map {
            if let (Ok(key), Ok(value)) = (
                header::HeaderName::from_bytes(key.as_bytes()),
                header::HeaderValue::from_str(&value),
            ) {
                headers.insert(key, value);
            } else {
                return Err(serde::de::Error::custom(format!(
                    "invalid header `{key}` `{value}`"
                )));
            }
        }
        Ok(Self(headers))
    }
}

#[derive(Deserialize)]
#[serde(untagged)]
enum SerdeDuration {
    Seconds(u64),
    Duration(Duration),
}

fn deserialize_duration<'de, D: Deserializer<'de>>(
    deserializer: D,
) -> Result<Option<Duration>, D::Error> {
    if let Some(duration) = Option::<SerdeDuration>::deserialize(deserializer)? {
        Ok(Some(match duration {
            SerdeDuration::Seconds(s) => Duration::from_secs(s),
            SerdeDuration::Duration(d) => d,
        }))
    } else {
        Ok(None)
    }
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct HttpRequest {
    /// The request method (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS, CONNECT or TRACE)
    pub method: String,
    /// The request URL
    pub url: Url,
    /// The request query params
    pub query: Option<HashMap<String, String>>,
    /// The request headers
    pub headers: Option<HeaderMap>,
    /// The request body
    pub body: Option<Body>,
    #[serde(deserialize_with = "deserialize_duration", default)]
    pub timeout: Option<Duration>,
    /// The response type (defaults to Json)
    pub response_type: Option<ResponseType>,

    /// 若是multipart/form-data类型, Body是流式数据
    #[serde(skip)]
    pub stream: Option<reqwest::Body>,
}

/// The response data.
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ResponseData {
    /// Response URL. Useful if it followed redirects.
    pub url: Url,
    /// Response status code.
    pub status: u16,
    /// Response headers.
    pub headers: HashMap<String, String>,
    /// Response raw headers.
    pub raw_headers: HashMap<String, Vec<String>>,
    /// Response data.
    pub data: Value,
}
