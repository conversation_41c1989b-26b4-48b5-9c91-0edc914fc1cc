use log::debug;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Runtime, State};
use tokio::sync::RwLock;

use super::super::Result;
use crate::state::AppState;

#[cfg(windows)]
use crate::AppPort;

#[tauri::command]
pub(in crate::plugins) async fn sso_result<R: Runtime>(
    app_handle: AppHandle<R>,
    result: String,
    app_state: State<'_, RwLock<AppState>>,
) -> Result<()> {
    debug!(target: "app", "received single sign on event.");
    let mut app_state = app_state.write().await;
    app_state.sso_result = Some(result);
    drop(app_state);

    // 打开网页
    #[cfg(windows)]
    let port = app_handle.state::<AppPort>().inner().0;
    #[cfg(not(windows))]
    let port = 50031u16;

    let path = format!("http://localhost:{port}");

    if let Err(error) = tauri::api::shell::open(&app_handle.shell_scope(), path, None) {
        log::error!(target: "app", "Failed to open url. {error}");
    }
    Ok(())
}
