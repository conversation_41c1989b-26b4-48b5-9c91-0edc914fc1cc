mod compression;
mod merge;
mod tar;
mod zip;

use clap::Parser;
use std::process::exit;

#[derive(Debug, Parse<PERSON>)]
pub struct Options {
    #[clap(subcommand)]
    command: Command,
}

#[derive(Debug, Parser)]
enum Command {
    Merge(merge::Options),
    Compression(compression::Options),
    Tar(tar::Options),
    Zip(zip::Options),
}

fn main() {
    let opts = Options::parse();

    use Command::*;
    let ret = match opts.command {
        Merge(opts) => merge::run(opts),
        Compression(opts) => compression::run(opts),
        Tar(opts) => tar::create_tar(opts),
        Zip(opts) => zip::create_zip(opts),
    };

    if let Err(e) = ret {
        eprintln!("{e:#}");
        exit(1);
    }
}
