use clap::Parser;
use std::{
    fs,
    fs::File,
    io::{<PERSON>ufWriter, Read, Write},
    path::{Path, PathBuf},
};
use zip::write::FileOptions;

#[derive(Debug, Parser)]
pub struct Options {
    /// Source file path.
    #[clap(long)]
    pub src: PathBuf,
    /// Destination file path.
    #[clap(long)]
    pub dest: PathBuf,
}

pub fn create_zip(opts: Options) -> anyhow::Result<()> {
    if let Some(parent) = opts.dest.parent() {
        fs::create_dir_all(parent)?;
    }
    _ = create_zip_from_src(opts.src.as_path(), opts.dest.as_path())?;
    Ok(())
}

pub fn create_zip_from_src(src_file: &Path, dst_file: &Path) -> anyhow::Result<PathBuf> {
    let parent_dir = dst_file.parent().expect("No data in parent");
    fs::create_dir_all(parent_dir)?;
    let writer = create_file(dst_file)?;

    let file_name = src_file
        .file_name()
        .expect("Can't extract file name from path");

    let mut zip = zip::ZipWriter::new(writer);
    let options = FileOptions::default()
        .compression_method(zip::CompressionMethod::Stored)
        .unix_permissions(0o755);

    zip.start_file(file_name.to_string_lossy(), options)?;
    let mut f = File::open(src_file)?;
    let mut buffer = Vec::new();
    f.read_to_end(&mut buffer)?;
    zip.write_all(&buffer)?;
    buffer.clear();

    Ok(dst_file.to_owned())
}

fn create_file(path: &Path) -> anyhow::Result<BufWriter<File>> {
    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent)?;
    }
    let file = File::create(path)?;
    Ok(BufWriter::new(file))
}
