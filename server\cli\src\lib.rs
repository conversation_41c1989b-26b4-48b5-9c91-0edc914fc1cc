use std::net::SocketAddr;

use clap::Parser;
use serde::{Deserialize, Serialize, Serializer};
use time::{format_description, macros::offset};

#[derive(Debug, Serialize, Deserialize, Parser)]
#[serde(tag = "cmd")]
pub enum Command {
    /// 设置日志级别
    SetLogFilter {
        /// 日志级别
        #[clap(long)]
        filter: String,
    },
    /// 查看客户端列表
    ViewClients,
    /// 查看溯源映射
    ViewTraceMapping,
}

/// 在线客户端信息
#[derive(Serialize, Deserialize)]
pub struct OnlineClient {
    /// 设备ID
    pub device_id: String,
    /// 所在单位
    pub unit: String,
    /// 用户
    pub username: String,
    /// 连接地址
    pub peer_addr: SocketAddr,
    /// 上线时间
    #[serde(serialize_with = "online_serialize")]
    pub online: u64,
}

fn online_serialize<S>(online: &u64, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let time = time::OffsetDateTime::from_unix_timestamp((*online as i64) / 1000).unwrap();
    let time = time.to_offset(offset!(+8));
    let format =
        format_description::parse("[year]-[month]-[day] [hour]:[minute]:[second]").unwrap();
    let res = time.format(&format).unwrap();

    res.serialize(serializer)
}
