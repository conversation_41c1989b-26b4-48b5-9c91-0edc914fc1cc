import { createRouter, createWebHashHistory } from "vue-router";
import routes from "./routes.js";
import store from "@/store/index.js";
import { ElMessageBox } from "element-plus";

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});
//全局路由导航钩子
router.beforeEach((to, from, next) => {
  // 关闭消息框
  ElMessageBox.close();

  if (!to.meta.login) {
    next();
  } else {
    if (store.state.isLogin) {
      next();
    } else {
      next({ name: "login" });
    }
  }
});
export default router;
