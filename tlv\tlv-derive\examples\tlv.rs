use ipnet::{IpAddrRange, IpNet, Ipv4AddrRange, Ipv4Net, Ipv6AddrRange, Ipv6Net};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashSet,
    net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr, SocketAddrV4, SocketAddrV6},
};
use tlv::BAD_MESSAGE;

#[derive(Debug, Serialize, Deserialize, Eq, PartialEq)]
struct A {
    a: String,
}

#[derive(Debug, tlv_derive::Serialize, tlv_derive::Deserialize, Eq, PartialEq)]
#[repr(u8)]
enum B {
    A {
        #[tlv = "json"]
        a: A,
        b: bool,
    } = 0,
    B {
        ip: IpAddr,
        #[tlv = "sequence"]
        ips: HashSet<IpAddr>,
    } = 1,
}

#[derive(Debug, tlv_derive::Serialize, tlv_derive::Deserialize, Eq, PartialEq)]
#[tlv = "untag"]
pub enum Ip {
    Ipv4(Ipv4Addr),
    Ipv6(Ipv6Addr),
}

#[derive(Debug, tlv_derive::Serialize, tlv_derive::Deserialize, Eq, PartialEq)]
struct TestTagStruct {
    /// u8
    u_8: u8,
    /// u16
    u_16: u16,
    /// u32
    u_32: u32,
    /// u64
    u_64: u64,
    /// bool
    b: bool,
    /// 字符串
    s: String,
    /// 序列
    #[tlv = "sequence"]
    sequence1: Vec<IpAddr>,
    /// 序列
    #[tlv = "sequence"]
    sequence2: Vec<Ipv4Addr>,
    /// 序列
    #[tlv = "sequence,json"]
    sequence3: Vec<A>,
    #[tlv = "sequence"]
    sequence4: HashSet<IpAddr>,
    /// json字符串
    #[tlv = "json"]
    json: A,
    /// 原始字节序列
    bytes: Vec<u8>,
    /// ip
    ip: IpAddr,
    /// ipv4
    ipv4: Ipv4Addr,
    /// ipv6
    ipv6: Ipv6Addr,
    /// iprange
    iprange: IpAddrRange,
    /// ipv4range
    ipv4range: Ipv4AddrRange,
    /// ipv6range
    ipv6range: Ipv6AddrRange,
    /// ipnet
    ipnet: IpNet,
    /// ipv4net
    ipv4net: Ipv4Net,
    /// ipv6net
    ipv6net: Ipv6Net,
    /// socket addr
    socket_addr: SocketAddr,
    /// socket addr v4
    socket_addrv4: SocketAddrV4,
    /// socket addr v4
    socket_addrv6: SocketAddrV6,
    /// Option
    op2: Option<IpAddr>,
    /// enum
    em: B,
    /// Option
    op: Option<IpAddr>,
    /// Option with sequence
    #[tlv = "sequence"]
    op_seq: Option<Vec<IpAddr>>,
    /// untag enum
    shelling: Ip,
}

fn main() {
    let mut seq4 = HashSet::default();
    seq4.insert(IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1)));
    let request = TestTagStruct {
        u_8: 8,
        u_16: 16,
        u_32: 32,
        u_64: 64,
        b: false,
        s: "string".to_string(),
        sequence1: vec![IpAddr::V4(Ipv4Addr::new(192, 168, 1, 1))],
        sequence2: vec![Ipv4Addr::new(192, 168, 1, 1)],
        sequence3: vec![A {
            a: String::from("sequence3"),
        }],
        sequence4: seq4.clone(),
        json: A {
            a: "json".to_string(),
        },
        bytes: vec![1, 2, 3],
        ip: IpAddr::V4(Ipv4Addr::from([192, 168, 1, 1])),
        ipv4: Ipv4Addr::from([192, 168, 1, 2]),
        ipv6: Ipv6Addr::LOCALHOST,
        iprange: IpAddrRange::V4(Ipv4AddrRange::new(
            Ipv4Addr::from([192, 168, 1, 3]),
            Ipv4Addr::from([192, 168, 1, 5]),
        )),
        ipv4range: Ipv4AddrRange::new(
            Ipv4Addr::from([192, 168, 1, 6]),
            Ipv4Addr::from([192, 168, 1, 8]),
        ),
        ipv6range: Ipv6AddrRange::new(Ipv6Addr::LOCALHOST, Ipv6Addr::LOCALHOST),
        ipnet: IpNet::new(IpAddr::V4(Ipv4Addr::from([192, 168, 1, 1])), 24).unwrap(),
        ipv4net: Ipv4Net::from(Ipv4Addr::from([192, 168, 1, 10])),
        ipv6net: Ipv6Net::from(Ipv6Addr::LOCALHOST),
        socket_addr: SocketAddr::new(IpAddr::V4(Ipv4Addr::from([192, 168, 1, 1])), 80),
        socket_addrv4: SocketAddrV4::new(Ipv4Addr::from([192, 168, 1, 1]), 80),
        socket_addrv6: SocketAddrV6::new(Ipv6Addr::LOCALHOST, 8080, 0, 0),
        em: B::B {
            ip: IpAddr::V4(Ipv4Addr::from([192, 168, 1, 1])),
            ips: seq4,
        },
        op: None,
        op2: Some(IpAddr::V4(Ipv4Addr::from([192, 168, 1, 1]))),
        op_seq: Some(vec![IpAddr::V4(Ipv4Addr::from([192, 168, 1, 1]))]),
        shelling: Ip::Ipv4(Ipv4Addr::from([192, 168, 1, 1])),
    };

    dbg!(&request);
    let bytes = tlv::Serialize::serialize(&request);

    let input = untrusted::Input::from(&bytes);
    let result: Result<TestTagStruct, &str> =
        input.read_all(BAD_MESSAGE, |reader| tlv::Deserialize::deserialize(reader));

    dbg!(&result);

    assert!(result.is_ok());

    assert_eq!(request, result.unwrap());
}
