//! 推送统计数据

use deadpool_lapin::Pool;
use lapin::{options::BasicPublishOptions, BasicProperties, Channel};
use tokio::sync::mpsc;
use tracing::{error, info, warn, Instrument};

use crate::{
    message::{SESSION_RESOURCE_TRAFFIC_QUEUE, SESSION_TRAFFIC_QUEUE, TRAFFIC_EXCHANGE},
    Error,
};

fn do_spawn_producer(
    pool: Pool,
    channel: Channel,
    mut session_traffic_rx: mpsc::Receiver<Vec<u8>>,
    mut session_resource_traffic_rx: mpsc::Receiver<Vec<u8>>,
) {
    tokio::spawn(
        async move {
            loop {
                tokio::select! {
                    Some(traffic) = session_traffic_rx.recv() => {
                        if let Err(err) = channel
                            .basic_publish(
                                TRAFFIC_EXCHANGE,
                                SESSION_TRAFFIC_QUEUE,
                                BasicPublishOptions::default(),
                                &traffic,
                                BasicProperties::default(),
                            )
                            .await {
                                error!("session traffic publish error: {err}");
                                break;
                            }
                    },
                    Some(traffic) = session_resource_traffic_rx.recv() => {
                        if let Err(err) = channel
                            .basic_publish(
                                TRAFFIC_EXCHANGE,
                                SESSION_RESOURCE_TRAFFIC_QUEUE,
                                BasicPublishOptions::default(),
                                &traffic,
                                BasicProperties::default(),
                            )
                            .await {
                                error!("resource traffic publish error: {err}");
                                break;
                            }
                    }
                }
            }

            tokio::spawn(async move {
                info!("recreate channel");

                let mut timeout = 1;
                loop {
                    let channel = get_channel!(pool, timeout);

                    do_spawn_producer(
                        pool,
                        channel,
                        session_traffic_rx,
                        session_resource_traffic_rx,
                    );
                    break;
                }
            });
        }
        .instrument(tracing::error_span!(parent: None, "TrafficProducer")),
    );
}

pub(super) async fn spawn_producer(
    pool: Pool,
    session_traffic_rx: mpsc::Receiver<Vec<u8>>,
    session_resource_traffic_rx: mpsc::Receiver<Vec<u8>>,
) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqProducerError(error.to_string()))?;

    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqProducerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("traffic channel status is abnormal: {error}");
    });

    do_spawn_producer(
        pool,
        channel,
        session_traffic_rx,
        session_resource_traffic_rx,
    );

    Ok(())
}
