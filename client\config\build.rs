use cfg_if::cfg_if;

fn main() {
    // sdp = 0x01
    // iam = 0x02
    // cluster = 0x80

    cfg_if! {
        if #[cfg(all(feature="sdp", feature = "iam", feature = "cluster"))] {
            println!("cargo:rustc-env=SDP_MODEL=3C")
        } else if #[cfg(all(feature="sdp", feature = "iam"))] {
            println!("cargo:rustc-env=SDP_MODEL=3S")
        } else if #[cfg(all(feature="sdp", feature = "cluster"))] {
            println!("cargo:rustc-env=SDP_MODEL=1C")
        } else if #[cfg(all(feature="iam", feature = "cluster"))] {
            println!("cargo:rustc-env=SDP_MODEL=2C")
        }else if #[cfg(all(feature="sdp"))] {
            println!("cargo:rustc-env=SDP_MODEL=1S")
        } else {
           println!("cargo:rustc-env=SDP_MODEL=2S")
        }
    };
}
