#[cfg(windows)]
pub mod power;

#[cfg(windows)]
pub fn is_windows_seven() -> bool {
    use windows::Win32::System::SystemInformation::{GetVersionExA, OSVERSIONINFOA};

    let mut info: OSVERSIONINFOA = Default::default();
    info.dwOSVersionInfoSize = std::mem::size_of::<OSVERSIONINFOA>() as u32;

    unsafe {
        if let Err(err) = GetVersionExA(&mut info) {
            log::error!(target: "app", "GetVersionExA: {err}");
            return false;
        }
    }

    info.dwMajorVersion == 6 && info.dwMinorVersion == 1
}
