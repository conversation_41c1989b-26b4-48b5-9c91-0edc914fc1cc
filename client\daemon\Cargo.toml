[package]
description = "One ID Service"
edition = "2021"
name = "oneidservice"
version = "1.0.0"

[package.metadata.winresource]
FileDescription = "安全令服务"
LegalCopyright = "© 2022 - 2024 景安云信. All rights reserved."
OriginalFilename = "oneiddaemon.exe"
ProductName = "安全令服务"

[dependencies]
actix-cors = "0.7.0"
actix-web = {version = "4.9.0", features = ["rustls", "rustls-0_23"]}
base64 = "0.21.0"
chrono = "0.4.23"
clap = {version = "4.0.26", features = ["cargo"]}
exception_logging = {git = "https://git.jingantech.net/pub/exception_logging.git", branch = "master"}
fern = {version = "0.6", features = ["colored"]}
flume = "0.10.14"
hex = "0.4.3"
libc = "0.2.135"
libsm = {git = "https://github.com/luoffei/libsm.git", branch = "speed_table"}
log = "0.4.21"
log-panics = "2.1.0"
num-bigint = "0.4.6"
once_cell = "1.19"
paths = {git = "https://git.jingantech.net/pub/paths.git", branch = "master"}
picky = "6.3.0"
picky-asn1 = "0.9.0"
regex = "1.8.1"
reqwest = {version = "0.12.6", default-features = false, features = ["charset", "http2", "json", "macos-system-configuration", "rustls-tls"]}
rustls = {version = "0.23.19", default-features = false, features = ["logging", "std", "tls12"]}
rustls-pemfile = "2.2.0"
serde = {version = "1.0.158", features = ["derive"]}
serde_json = "1.0.88"
thiserror = "2.0.4"
tokio = {version = "1.22.0", features = ["rt-multi-thread", "macros", "signal", "parking_lot"]}
uniqueid = {git = "https://git.jingantech.net/pub/uniqueid.git", branch = "master"}

[target.'cfg(windows)'.dependencies]
biometric = {git = "https://git.jingantech.net/pub/biometric.git", branch = "master"}
persistent-port = {git = "https://git.jingantech.net/pub/persistent-port.git", branch = "master"}
skf_lib = {git = "https://git.jingantech.net/pub/skf_lib.git", branch = "master", optional = true}
whoami = {version = "1.4.0", optional = true}
widestring = "1.0.2"
winapi = {version = "0.3", features = ["winnt", "excpt", "winuser", "userenv", "lmaccess", "libloaderapi", "wincrypt"]}
windows-service = "0.5.0"
winreg = "0.10.1"

[target.'cfg(windows)'.dependencies.windows-sys]
features = [
  "Win32_Foundation",
  "Win32_Security",
  "Win32_Security_Authorization",
  "Win32_Security_Authentication_Identity",
  "Win32_System_Diagnostics_Debug",
  "Win32_System_Kernel",
  "Win32_System_Memory",
  "Win32_System_Threading",
  "Win32_System_Diagnostics_ToolHelp",
]
version = "0.42.0"

[target.'cfg(target_os = "linux")'.dependencies]
service-manager = "0.2.0"
systemctl = {git = "https://github.com/luoffei/systemctl.git"}

[target.'cfg(target_os = "macos")'.dependencies]
core-foundation = {git = "https://github.com/luoffei/core-foundation-rs", branch = "core-foundation-0.9.3"}
security-framework = {version = "2.9.1", default-features = false, features = ["OSX_10_15"]}
security-framework-sys = {version = "2.9.0", default-features = false, features = ["OSX_10_15"]}

[target.'cfg(unix)'.dependencies]
nix = "0.23"

[target.'cfg(windows)'.build-dependencies]
semver = "1.0.20"
winresource = "0.1.17"

[target.'cfg(windows)'.build-dependencies.windows-sys]
features = [
  "Win32_System_SystemServices",
]
version = "0.42.0"

[features]
# 双因素
2fa = []
default = ["pki", "oneid"]
# 域控
domain = []
# ONEID
oneid = []
pki = ["dep:whoami", "dep:skf_lib"]
# SDP
sdp = []
