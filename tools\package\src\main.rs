#[cfg(target_os = "linux")]
mod build_ebpf;
mod client;
#[macro_use]
mod common;
#[cfg(target_os = "linux")]
mod server;
#[allow(dead_code)]
mod utils;

use std::{collections::HashMap, env};

use crate::client::{prompt, Module};

use clap::Parser;

use tokio::sync::{broadcast, broadcast::Sender};

#[derive(Debug, Parser)]
pub struct Options {
    #[clap(subcommand)]
    command: Command,
}

#[derive(Debug, Parser)]
enum Command {
    /// 客户端
    BuildClient(client::Options),
    /// 服务端
    #[cfg(target_os = "linux")]
    BuildServer(server::Options),
}

/// cargo package --client --modules sdp,iam --release --all
#[tokio::main]
async fn main() {
    let (tx, _rx) = broadcast::channel::<()>(1);
    tokio::spawn({
        let tx = tx.clone();
        async move {
            _ = tokio::signal::ctrl_c().await;
            _ = tx.send(());
        }
    });

    let opts = Options::parse();

    use Command::*;
    let ret = match opts.command {
        BuildClient(options) => build_client(tx, options).await,
        #[cfg(target_os = "linux")]
        BuildServer(opts) => server::run(tx, opts).await,
    };

    if let Err(e) = ret {
        eprintln!("{e:#}");
        std::process::exit(1);
    }
}

async fn build_client(tx: Sender<()>, mut options: client::Options) -> anyhow::Result<()> {
    let env_vars = if options.use_env {
        let vars = env::vars()
            .into_iter()
            .filter(|(k, _v)| k.starts_with("SDP"))
            .collect::<HashMap<String, String>>();

        vars
    } else {
        HashMap::new()
    };

    if options.run || !options.all {
        if options.modules.is_empty() {
            options.modules = vec![Module::SDP, Module::IAM, Module::TLCP];
        }

        let build_envs = if options.use_env {
            env_vars
        } else {
            prompt(&options.modules)?
        };
        if options.verbose {
            println!("Build envs: {:#?}", build_envs)
        }

        #[cfg(not(windows))]
        let platform = options
            .target
            .clone()
            .unwrap_or(rustc_host::from_cli().unwrap());
        #[cfg(windows)]
        let platform = rustc_host::from_cli().unwrap();
        client::run(tx, options.clone(), &platform, &build_envs).await?;
    } else {
        let (standalone_envs, cluster_envs) = if options.use_env {
            (env_vars.clone(), env_vars.clone())
        } else {
            let modules = vec![Module::SDP, Module::TLCP];
            let standalone_envs = prompt(&modules)?;
            let modules = vec![Module::SDP, Module::TLCP, Module::CLUSTER];
            let cluster_envs = prompt(&modules)?;
            (standalone_envs, cluster_envs)
        };

        #[cfg(target_os = "macos")]
        let platforms = ["x86_64-apple-darwin", "aarch64-apple-darwin"];
        #[cfg(target_os = "linux")]
        let platforms = ["x86_64-unknown-linux-gnu", "aarch64-unknown-linux-gnu"];
        #[cfg(windows)]
        let platforms = [&rustc_host::from_cli().unwrap()];

        let mut iter = platforms.iter();

        while let Some(platform) = iter.next() {
            // 景鸿零信任 单机
            let mut opts = options.clone();
            opts.modules = vec![Module::SDP, Module::TLCP];
            client::run(tx.clone(), opts.clone(), platform, &standalone_envs).await?;

            // 景鸿零信任 集群
            let mut opts = options.clone();
            opts.modules = vec![Module::SDP, Module::TLCP, Module::CLUSTER];
            client::run(tx.clone(), opts.clone(), platform, &cluster_envs).await?;

            // 安全令 单机
            let mut opts = options.clone();
            opts.modules = vec![Module::SDP, Module::IAM, Module::TLCP];
            client::run(tx.clone(), opts.clone(), platform, &standalone_envs).await?;

            // 安全令 集群
            let mut opts = options.clone();
            opts.modules = vec![Module::SDP, Module::IAM, Module::TLCP, Module::CLUSTER];
            client::run(tx.clone(), opts.clone(), platform, &cluster_envs).await?;

            // IAM 单机
            // let mut opts = options.clone();
            // opts.modules = vec![Module::IAM];
            // let build_envs = prompt(&opts)?;
            // client::run(tx.clone(), opts.clone(), platform, build_envs).await?;
        }
    }

    Ok(())
}
