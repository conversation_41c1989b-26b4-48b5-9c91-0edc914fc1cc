use std::{net::SocketAddr, time::Duration};

use hyper_util::{
    rt::{TokioExecutor, TokioIo},
    server::conn,
};
use log::{error, trace};
use tokio::{
    io::{AsyncRead, AsyncWrite},
    time::timeout,
};

use crate::traceability::{
    constants::TLS_HANDSHAKE_TIMEOUT_SEC, inner::crypto::find_tls_config, Proxy, ProxyError,
};

// TCP Listener Service, i.e., http/2 and http/1.1
pub(super) async fn handshake<IO>(
    proxy: Proxy,
    conn_builder: conn::auto::Builder<TokioExecutor>,
    scheme: &'static str,
    raw_stream: IO,
    client_addr: SocketAddr,
    origin: SocketAddr,
) -> Result<(), ProxyError>
where
    IO: AsyncRead + AsyncWrite + Send + Unpin + 'static,
{
    // spawns async handshake to avoid blocking thread by sequential handshake.
    let handshake_fut = async move {
        let acceptor =
            tokio_rustls::LazyConfigAcceptor::new(rustls::server::Acceptor::default(), raw_stream)
                .await;

        if let Err(e) = acceptor {
            return Err(ProxyError::Proxy(format!("Failed to handshake TLS: {e}")));
        }
        let start = acceptor.unwrap();
        let client_hello = start.client_hello();
        let server_name = client_hello.server_name();
        trace!("HTTP/2 or 1.1: SNI in ClientHello: {:?}", server_name);

        let Some(server_crypto) = find_tls_config(server_name, origin.ip(), origin.port()).await
        else {
            return Err(ProxyError::Proxy(format!(
                "No TLS serving app for {:?} or {:?}",
                server_name.unwrap_or_default(),
                origin
            )));
        };

        let stream = match start.into_stream(server_crypto).await {
            Ok(s) => s,
            Err(e) => {
                return Err(ProxyError::Proxy(format!("Failed to handshake TLS: {e}")));
            }
        };
        proxy
            .client_serve(
                TokioIo::new(stream),
                conn_builder,
                scheme,
                client_addr,
                origin,
            )
            .await;
        Ok(())
    };

    tokio::spawn(async move {
        // timeout is introduced to avoid get stuck here.
        match timeout(
            Duration::from_secs(TLS_HANDSHAKE_TIMEOUT_SEC),
            handshake_fut,
        )
        .await
        {
            Ok(a) => {
                if let Err(e) = a {
                    error!("{:?}", e);
                }
            }
            Err(e) => {
                error!("Timeout to handshake TLS: {}", e);
            }
        };
    });

    Ok(())
}
