package main

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"math/big"
	"net"
	"os"
	"time"
)

func generateCertificate(root string, rootKey string, host string) {
	// 解析CA证书和私钥
	caCertBlock, _ := pem.Decode([]byte(root))
	caKeyBlock, _ := pem.Decode([]byte(rootKey))
	caPrivateKey, err := x509.ParsePKCS8PrivateKey(caKeyBlock.Bytes)
	if err != nil {
		fmt.Println(fmt.Sprintf("%v", err))
		return
	}
	caCrt, err := x509.ParseCertificate(caCertBlock.Bytes)
	if err != nil {
		fmt.Println(fmt.Sprintf("%v", err))
		return
	}
	// 判断host是域名还是IP
	isIP := net.ParseIP(host) != nil
	// 构建服务器证书模板
	serverTemplate := x509Template()
	if isIP {
		serverTemplate.IPAddresses = []net.IP{net.ParseIP(host)}
	} else {
		serverTemplate.DNSNames = []string{host}
	}
	subject := pkix.Name{
		Country:    []string{"CN"},
		Locality:   []string{"Beijing"},
		Province:   []string{"Hai Dian"},
		CommonName: host,
	}
	serverTemplate.KeyUsage = x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment
	serverTemplate.Subject = subject
	serverTemplate.AuthorityKeyId = caCrt.SubjectKeyId
	// 生成服务器私钥
	serverPrivateKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		fmt.Println(fmt.Sprintf("%v", err))
		return
	}
	pkcs8Encoded, _ := x509.MarshalPKCS8PrivateKey(serverPrivateKey)
	// 使用CA私钥对服务器证书模板进行签名
	serverCertDER, err := x509.CreateCertificate(rand.Reader, &serverTemplate, caCrt, &serverPrivateKey.PublicKey, caPrivateKey)
	if err != nil {
		fmt.Println(fmt.Sprintf("%v", err))
		return
	}
	// 将服务器证书和私钥保存到字节数组
	var cert bytes.Buffer
	var key bytes.Buffer
	_ = pem.Encode(&cert, &pem.Block{Type: "CERTIFICATE", Bytes: serverCertDER})
	_ = pem.Encode(&key, &pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	fmt.Println(cert.String())
	fmt.Println(key.String())
}

func x509Template() x509.Certificate {
	// generate a serial number
	serialNumberLimit := new(big.Int).Lsh(big.NewInt(1), 128)
	serialNumber, _ := rand.Int(rand.Reader, serialNumberLimit)
	// 7天
	expiry := 7 * 24 * time.Hour
	// 即时生效
	notBefore := time.Now()
	// basic template to use
	x509 := x509.Certificate{
		SerialNumber:          serialNumber,
		NotBefore:             notBefore,
		NotAfter:              notBefore.Add(expiry).UTC(),
		BasicConstraintsValid: true,
	}
	return x509
}

func main() {
	// 定义命令行参数
	root, exists := os.LookupEnv("ROOT")
	if !exists {
		fmt.Println("Missing `ROOT` field")
		return
	}
	key, exists := os.LookupEnv("KEY")
	if !exists {
		fmt.Println("Missing `KEY` field")
		return
	}
	host, exists := os.LookupEnv("HOST")
	if !exists {
		fmt.Println("Missing `HOST` field")
		return
	}

	generateCertificate(root, key, host)
}
