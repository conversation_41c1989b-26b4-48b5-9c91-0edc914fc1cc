<template>
  <div style="display: flex; height: 100%">
    <headerBar></headerBar>
    <leftNav></leftNav>
    <router-view v-slot="{ Component }" v-if="isRouterAlive">
      <!-- <keep-alive>
         <component
             :is="Component"
             v-if="$route.meta.keepalive"
         />
     </keep-alive>  -->
      <component :is="Component" v-if="$route.meta.keepalive" />
      <component :is="Component" v-if="!$route.meta.keepalive" />
    </router-view>
  </div>
</template>

<script>
import leftNav from "./leftNav.vue";
import headerBar from "@/components/Header";

export default {
  components: {
    leftNav,
    headerBar
  },
  name: "Layout",
  data() {
    return {
      isRouterAlive: true,
    };
  },
  provide() {
    return {
      reload: this.reload,
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false; //先关闭，
      this.$nextTick(function () {
        this.isRouterAlive = true; //再打开
      });
    },
  },
};
</script>