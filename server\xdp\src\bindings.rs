// automatically generated by rust-bindgen 0.65.1

// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

#![allow(non_camel_case_types)]

pub const ETHTOOL_GCHANNELS: u32 = 60;
pub type __u32 = ::std::os::raw::c_uint;
#[repr(C)]
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct ethtool_channels {
    pub cmd: __u32,
    pub max_rx: __u32,
    pub max_tx: __u32,
    pub max_other: __u32,
    pub max_combined: __u32,
    pub rx_count: __u32,
    pub tx_count: __u32,
    pub other_count: __u32,
    pub combined_count: __u32,
}
