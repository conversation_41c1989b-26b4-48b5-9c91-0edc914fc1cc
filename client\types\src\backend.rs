use core::fmt;
use std::{
    fmt::Formatter,
    net::{IpAddr, SocketAddr},
};

use serde::{Deserialize, Serialize};
use serde_json::Value;

/// 基础消息.
#[derive(Serialize, Deserialize, Clone, Eq, PartialEq)]
pub struct ConnectionConfig {
    pub ip: Option<IpAddr>,
    pub host: String,
    pub port: u16,
    pub spa_port: u16,
    pub authorization: Option<SPAAuthorization>,
}

/// 基础消息.
#[derive(Serialize, Deserialize, Clone, Eq, PartialEq)]
pub struct SPAAuthorization {
    pub tenant: String,
    pub secret: String,
    pub username: String,
}

impl fmt::Debug for ConnectionConfig {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ConnectionConfig")
            .field("ip", &self.ip)
            .field("host", &self.host)
            .field("port", &self.port)
            .field("spa_port", &self.spa_port)
            .field("authorization", &self.authorization)
            .finish()
    }
}

impl fmt::Debug for SPAAuthorization {
    fn fmt(&self, f: &mut Formatter<'_>) -> fmt::Result {
        let mut secret = self.secret.clone();
        secret.replace_range(8..24, "******");

        f.debug_struct("SPAAuthorization")
            .field("tenant", &self.tenant)
            .field("secret", &secret)
            .field("username", &self.username)
            .finish()
    }
}

/// 控制器登录消息.
#[derive(Serialize, Deserialize, Clone)]
pub struct Login {
    pub connection_config: ConnectionConfig,

    pub payload: Value,
}

impl fmt::Debug for Login {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        let mut payload = self.payload.clone();
        if payload["password"].as_str().is_some() {
            payload["password"] = Value::String(String::from("******"));
        }
        f.debug_struct("LoginCtl")
            .field("connection_config", &self.connection_config)
            .field("payload", &payload)
            .finish()
    }
}

/// 通过验证码修改密码.
#[derive(Serialize, Deserialize)]
pub struct ChangePwdCode {
    pub connection_config: ConnectionConfig,

    pub payload: Value,
}

impl fmt::Debug for ChangePwdCode {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        let mut payload = self.payload.clone();
        if payload["password"].as_str().is_some() {
            payload["password"] = Value::String(String::from("******"));
        }
        f.debug_struct("ChangePwdCode")
            .field("connection_config", &self.connection_config)
            .field("payload", &payload)
            .finish()
    }
}

/// 通过旧密码修改密码.
#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ChangePwdOldPwd {
    pub username: String,
    pub old_password: String,
    pub new_password: String,
}

impl fmt::Debug for ChangePwdOldPwd {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ChangePwdOldPwd")
            .field("username", &self.username)
            .field("old_password", &"******")
            .field("new_password", &"******")
            .finish()
    }
}

/// 票据
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Ticket {
    pub ticket: Vec<u8>,
    pub ticket_type: TicketType,
}

/// 认证事件
#[derive(Debug, Serialize, Deserialize)]
pub struct AuthEvent {
    pub ip_addr: Option<IpAddr>,
}

/// 认证完成
#[derive(Debug, Serialize, Deserialize)]
pub struct CompleteAuth {
    pub auth_result_id: String,
    pub ip_addr: Option<IpAddr>,
}

/// 取消认证
#[derive(Debug, Serialize, Deserialize)]
pub struct CancelAuth {
    pub ip_addr: Option<IpAddr>,
}

/// Action that will be taken after disconnection is complete.
#[derive(Clone, Copy, Debug, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "net.sdp.backend"))]
pub enum ActionAfterDisconnect {
    Nothing,
    Reconnect,
}

/// Event emitted from the states in `oneidcore::backend_state_machine` when the backend state
/// machine enters a new state.
#[derive(Clone, Debug)]
pub enum BackendStateTransition {
    /// No connection is established and network is unsecured.
    Disconnected(Option<i8>),
    /// Connecting, proxy mode.
    ProxyConnecting,
    /// Connected, proxy mode.
    ProxyConnected,
    /// Network is secured but backend is still connecting.
    Connecting(SocketAddr),
    /// Backend is connected.
    Connected(IpAddr, Option<Vec<IpAddr>>),
    /// Disconnecting tunnel.
    Disconnecting,
    /// Tunnel is disconnected but usually secured by blocking all connections.
    Error(ErrorState),
}

#[derive(Clone, Debug, Serialize, Deserialize)]
pub enum TicketType {
    /// SSO登录
    SSO = 0,
    /// 重连
    Reconnect = 1,
}

/// Represents the tunnel state machine entering an error state during a [`BackendStateTransition`].
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "net.sdp.backend"))]
pub struct ErrorState {
    /// Reason why the tunnel state machine ended up in the error state
    cause: ErrorStateCause,
}

impl ErrorState {
    pub fn new(cause: ErrorStateCause) -> Self {
        Self { cause }
    }

    pub fn cause(&self) -> &ErrorStateCause {
        &self.cause
    }
}

/// Reason for the backend state machine entering an [`ErrorState`].
#[derive(Clone, Debug, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
#[serde(tag = "reason", content = "details")]
#[cfg_attr(target_os = "android", derive(IntoJava))]
#[cfg_attr(target_os = "android", jnix(package = "net.sdp.backend"))]
pub enum ErrorStateCause {
    AuthFailed,
    ConnectTimeout,
    NeedChangePwd,
}

impl fmt::Display for ErrorStateCause {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        use self::ErrorStateCause::*;
        let description = match *self {
            AuthFailed => "Authentication failed",
            ConnectTimeout => "Connect timeout",
            NeedChangePwd => "Need to change password",
        };

        write!(f, "{description}")
    }
}
