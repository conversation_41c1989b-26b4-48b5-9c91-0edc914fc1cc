<!doctype html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="/favicon.ico" />
    <title>统一客户端</title>
  </head>
  <style>
    .titlebar {
      height: 40px;
      /* background: #f9780c; */
      user-select: none;
      display: flex;
      justify-content: flex-end;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
    }

    .titlebar-button {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
    }

    .titlebar-button:hover {
      /* background: #f9780c; */
      cursor: pointer;
    }

    .body-border {
      // #if [platform!=macos]
      border: 1px solid rgb(171, 171, 171);
      height: 100vh;
      box-sizing: border-box;
      width: 100% !important;
      // #endif
    }
  </style>

  <body class="body-border">
    <noscript>
      <strong
        >We're sorry but 统一客户端 doesn't work properly without JavaScript
        enabled. Please enable it to continue.</strong
      >
    </noscript>

    <div id="app"></div>
    <script>
      // localStorage.clear()
    </script>
    <script type="module" src="/src/main.js"></script>
  </body>

  <style>
    body {
      overflow: hidden;
      margin: 0;
      user-select: none;
      -webkit-user-select: none;
    }
  </style>
</html>
