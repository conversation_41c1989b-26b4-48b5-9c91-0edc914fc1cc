use std::net::{Ip<PERSON>ddr, Ipv4Addr, Ipv6<PERSON>dd<PERSON>, SocketAddr, SocketAddrV4, SocketAddrV6};

use ipnet::{IpAddrRange, IpNet, Ipv4AddrRange, Ipv4Net, Ipv6AddrRange, Ipv6Net};
use serde_json::Value;

use crate::{utils, Serialize, Tag};

impl Serialize for u8 {
    fn serialize(&self) -> Vec<u8> {
        vec![Tag::Uint8 as u8, 1, *self]
    }
}

impl Serialize for u16 {
    fn serialize(&self) -> Vec<u8> {
        let bytes = self.to_be_bytes();
        vec![Tag::Uint16 as u8, 2, bytes[0], bytes[1]]
    }
}

impl Serialize for u32 {
    fn serialize(&self) -> Vec<u8> {
        let bytes = self.to_be_bytes();
        vec![Tag::Uint32 as u8, 4, bytes[0], bytes[1], bytes[2], bytes[3]]
    }
}

impl Serialize for u64 {
    fn serialize(&self) -> Vec<u8> {
        let bytes = self.to_be_bytes();
        vec![
            Tag::Uint64 as u8,
            8,
            bytes[0],
            bytes[1],
            bytes[2],
            bytes[3],
            bytes[4],
            bytes[5],
            bytes[6],
            bytes[7],
        ]
    }
}

impl Serialize for IpAddr {
    fn serialize(&self) -> Vec<u8> {
        match self {
            IpAddr::V4(ip) => ip.serialize(),
            IpAddr::V6(ip) => ip.serialize(),
        }
    }
}

impl Serialize for Ipv4Addr {
    fn serialize(&self) -> Vec<u8> {
        let octets = self.octets();
        vec![
            Tag::Ipv4 as u8,
            4,
            octets[0],
            octets[1],
            octets[2],
            octets[3],
        ]
    }
}

impl Serialize for Ipv6Addr {
    fn serialize(&self) -> Vec<u8> {
        let octets = self.octets();
        let mut bytes = Vec::with_capacity(18);

        bytes.push(Tag::Ipv6 as u8);
        bytes.push(16);
        bytes.extend(octets);

        bytes
    }
}

impl Serialize for SocketAddr {
    fn serialize(&self) -> Vec<u8> {
        match self {
            SocketAddr::V4(socket_addr) => socket_addr.serialize(),
            SocketAddr::V6(socket_addr) => socket_addr.serialize(),
        }
    }
}

impl Serialize for SocketAddrV4 {
    fn serialize(&self) -> Vec<u8> {
        let mut bytes = Vec::with_capacity(8);

        bytes.push(Tag::SocketAddrV4 as u8);
        bytes.push(6);

        bytes.extend(self.ip().octets());
        bytes.extend(self.port().to_be_bytes());

        bytes
    }
}

impl Serialize for SocketAddrV6 {
    fn serialize(&self) -> Vec<u8> {
        let mut bytes = Vec::with_capacity(20);

        bytes.push(Tag::SocketAddrV6 as u8);
        bytes.push(18);

        bytes.extend(self.ip().octets());
        bytes.extend(self.port().to_be_bytes());

        bytes
    }
}

impl Serialize for bool {
    fn serialize(&self) -> Vec<u8> {
        vec![Tag::Boolean as u8, 1, *self as u8]
    }
}

impl Serialize for Vec<u8> {
    fn serialize(&self) -> Vec<u8> {
        let len = self.len();
        let length_bytes = utils::len_to_bytes(len);

        let mut bytes = Vec::with_capacity(1 + length_bytes.len() + len);

        bytes.push(Tag::RawBytes as u8);
        bytes.extend(length_bytes);
        bytes.extend_from_slice(self);

        bytes
    }
}

impl Serialize for IpNet {
    fn serialize(&self) -> Vec<u8> {
        match self {
            IpNet::V4(ipnet) => ipnet.serialize(),
            IpNet::V6(ipnet) => ipnet.serialize(),
        }
    }
}

impl Serialize for Ipv4Net {
    fn serialize(&self) -> Vec<u8> {
        let mut bytes = Vec::with_capacity(7);
        bytes.push(Tag::Ipv4Net as u8);
        bytes.push(5);
        bytes.push(self.prefix_len());
        bytes.extend(self.addr().octets());

        bytes
    }
}

impl Serialize for Ipv6Net {
    fn serialize(&self) -> Vec<u8> {
        let mut bytes = Vec::with_capacity(19);
        bytes.push(Tag::Ipv6Net as u8);
        bytes.push(17);
        bytes.push(self.prefix_len());
        bytes.extend(self.addr().octets());
        bytes
    }
}

impl Serialize for String {
    fn serialize(&self) -> Vec<u8> {
        let mut bytes = self.as_bytes().to_vec();
        utils::insert_tag(&mut bytes, Tag::UTF8String);
        bytes
    }
}

impl Serialize for IpAddrRange {
    fn serialize(&self) -> Vec<u8> {
        match self {
            IpAddrRange::V4(iprange) => iprange.serialize(),
            IpAddrRange::V6(iprange) => iprange.serialize(),
        }
    }
}

impl Serialize for Ipv4AddrRange {
    fn serialize(&self) -> Vec<u8> {
        let start = Iterator::min(*self).unwrap();
        let end = Iterator::max(*self).unwrap();

        let mut bytes = Vec::with_capacity(10);
        bytes.push(Tag::Ipv4Range as u8);
        bytes.push(8);
        bytes.extend(start.octets());
        bytes.extend(end.octets());
        bytes
    }
}

impl Serialize for Ipv6AddrRange {
    fn serialize(&self) -> Vec<u8> {
        let start = Iterator::min(*self).unwrap();
        let end = Iterator::max(*self).unwrap();

        let mut bytes = Vec::with_capacity(34);
        bytes.push(Tag::Ipv6Range as u8);
        bytes.push(32);
        bytes.extend(start.octets());
        bytes.extend(end.octets());
        bytes
    }
}

impl Serialize for Value {
    fn serialize(&self) -> Vec<u8> {
        let value_bytes = serde_json::to_vec(self).unwrap();
        let mut len_bytes = utils::len_to_bytes(value_bytes.len());
        len_bytes.insert(0, Tag::RawBytes as u8);
        [len_bytes, value_bytes].concat()
    }
}
