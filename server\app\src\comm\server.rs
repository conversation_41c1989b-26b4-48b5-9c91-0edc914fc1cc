use std::{
    collections::{HashMap, HashSet},
    net::{Ip<PERSON>ddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use base::{
    packet::{Message, MessageCodec, MessageType},
    spa::SPAType,
};
use err_ext::ErrorExt;
use futures::{SinkExt, StreamExt};
use http::HeaderValue;
use moka::future::Cache;
use proxy_request::HttpRequest;
use serde_json::Value;
use tlv::Serialize;
use tlv_types::{AuthPacket, AuthResult, ChangePwdByVerifyCode, ModePayload, ProxyRequest};
use tokio::{net::TcpStream, sync::oneshot};
use tokio_rustls::server::TlsStream;
use tokio_util::codec::Framed;
use tracing::{debug, error, warn};

use crate::{
    backend::{client::BackendCommonClient, proxy},
    cache::<PERSON>ache<PERSON><PERSON><PERSON>,
    message::<PERSON><PERSON><PERSON><PERSON>le,
    packet::client::Client<PERSON><PERSON>etH<PERSON>le,
    EventSender,
};

use super::{
    server_handler::{self, MixHandleArgs},
    AuthU<PERSON>, Error, Session,
};

pub(super) enum ChannelMessage {
    Exit,
    Message {
        message: Message,
        callback: Option<oneshot::Sender<()>>,
    },
}

struct HttpProxySender(flume::Sender<ChannelMessage>);

impl proxy::Sender for HttpProxySender {
    async fn send(&self, message: Message) -> Result<(), proxy_request::Error> {
        if self
            .0
            .send_async(ChannelMessage::Message {
                message,
                callback: None,
            })
            .await
            .is_err()
        {
            error!("connection already close or thread panicked.");
        }

        Ok(())
    }
}

/// 认证
pub(super) struct MixServer {
    pub(super) authorization_interval: u64,
    pub(super) dns_servers: Option<Vec<IpAddr>>,

    pub(super) peer_addr: SocketAddr,
    pub(super) local_addr: SocketAddr,
    pub(super) stream: Option<TlsStream<TcpStream>>,
    pub(super) event_sender: EventSender,
    pub(super) cache_manager: CacheManager,
    pub(super) backend_client: Arc<BackendCommonClient>,
    pub(super) client_packet_handle: ClientPacketHandle,
    pub(super) mq_handle: MqHandle,
    pub(super) resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,
    pub(super) spa_cache: Arc<Cache<IpAddr, HashMap<String, SPAType>>>,
}

impl MixServer {
    #[tracing::instrument(name = "Authenticator", parent = None, skip_all, fields(peer_addr = %self.peer_addr))]
    pub(super) async fn start(mut self) -> Result<(), ()> {
        let stream = self.stream.take();
        if stream.is_none() {
            return Ok(());
        }

        let codec = MessageCodec {};
        let framed = Framed::new(stream.unwrap(), codec);
        let (tx, rx) = flume::bounded::<ChannelMessage>(16);
        let (mut sink, mut stream) = framed.split();

        let write_half = tokio::spawn(async move {
            loop {
                match rx.recv_async().await {
                    Err(_) => break sink,
                    Ok(message) => match message {
                        ChannelMessage::Exit => {
                            break sink;
                        }
                        ChannelMessage::Message { message, callback } => {
                            if let Err(err) = sink.send(message).await {
                                error!("{err}");
                                if let Some(tx) = callback {
                                    _ = tx.send(());
                                }
                                break sink;
                            }
                            if let Some(tx) = callback {
                                _ = tx.send(());
                            }
                        }
                    },
                }
            }
        });

        let timeout = Duration::from_secs(15);
        'CONN_LOOP: loop {
            let next = tokio::time::timeout(timeout, stream.next()).await;
            match next {
                Ok(None) => {
                    break;
                }
                Ok(Some(Err(err))) => {
                    error!("{err}");
                    break;
                }
                Ok(Some(Ok(message))) => {
                    match message.r#type() {
                        MessageType::Authentication => {
                            let input = message.payload();
                            let result: Result<AuthPacket, &'static str> =
                                tlv::read_to_struct!(input);
                            let Ok(auth_pkt) = result else {
                                error!(message_type = ?MessageType::Authentication, "bad message");
                                break;
                            };

                            // 收到认证包时, 在此之前可能已经通过Proxy的SPA与网关建立了连接
                            // 此时, 接收到认证包时, SPA可能还未验证通过
                            //
                            // 补救措施: 等待SPA验证. 暂定两秒延时(公司比较差的服务器,
                            // SPA验证也只需要一点几秒)
                            let mut times = 1;
                            loop {
                                let result = self
                                    .spa_cache
                                    .get(&self.peer_addr.ip())
                                    .await
                                    .map(|devices| {
                                        devices.get(&auth_pkt.device_id).map(|spa_type| *spa_type)
                                    })
                                    .unwrap_or_default();
                                if let Some(SPAType::Auth) = result {
                                    break;
                                }
                                if times >= 10 {
                                    if result.is_none() {
                                        error!(
                                            device.id = auth_pkt.device_id,
                                            "client is not authorized by SPA"
                                        );
                                    } else {
                                        error!(message_type = ?MessageType::Authentication, "message does not match SPA type");
                                    }

                                    break 'CONN_LOOP;
                                }
                                debug!("Waiting for SPA verification");
                                times += 1;
                                _ = tokio::time::sleep(Duration::from_millis(100)).await;
                            }

                            // 执行认证
                            match self.handle_auth_request(tx.clone(), auth_pkt.payload).await {
                                Ok(session) => {
                                    _ = tx.send_async(ChannelMessage::Exit).await;
                                    let Ok(sink) = write_half.await else {
                                        return Err(());
                                    };
                                    let framed = sink.reunite(stream).unwrap();

                                    let backend_client = self.backend_client.new_client(
                                        &session.device_id,
                                        &session.tenant,
                                        &session.env,
                                    );

                                    let args = MixHandleArgs {
                                        authorization_interval: self.authorization_interval,
                                        session,
                                        stream: framed,
                                        cache_manager: self.cache_manager,
                                        event_sender: self.event_sender,
                                        resource_whitelist: self.resource_whitelist,
                                        backend_client: Arc::new(backend_client),
                                        client_packet_handle: self.client_packet_handle,
                                        mq_handle: self.mq_handle,
                                    };

                                    server_handler::handle(args).await;
                                    return Ok(());
                                }
                                Err(err) => {
                                    error!("{err}");
                                    break;
                                }
                            }
                        }
                        MessageType::ProxyRequest => {
                            let input = message.payload();
                            let result: Result<ProxyRequest, &'static str> =
                                tlv::read_to_struct!(input);
                            let Ok(request) = result else {
                                error!(message_type = ?MessageType::ProxyRequest, "bad message");
                                break;
                            };
                            let Ok(mut http_request) =
                                serde_json::from_slice::<HttpRequest>(&request.data)
                            else {
                                error!(message_type = ?MessageType::ProxyRequest, "bad message");
                                break;
                            };

                            if !Self::check_spa_type(
                                self.spa_cache.clone(),
                                self.peer_addr.ip(),
                                &request.device_id,
                                &[SPAType::Proxy, SPAType::Auth],
                                MessageType::ProxyRequest,
                            )
                            .await
                            {
                                break;
                            }

                            tokio::spawn({
                            let tx = tx.clone();
                            let base_url = self.backend_client.base_url();
                            _ = http_request.url.set_scheme(base_url.scheme());
                            _ = http_request.url.set_host(base_url.host_str());
                            _ = http_request.url.set_port(base_url.port());

                            if let Some(headers) = http_request.headers.as_mut() {
                                let ip = self.peer_addr.ip().to_string();
                                _ = headers.0.insert(
                                    "x-forwarded-for",
                                    HeaderValue::from_str(ip.as_str()).unwrap(),
                                );
                            }

                            async move {
                                let response_sender = HttpProxySender(tx);
                                let url = http_request.url.clone();
                                if let Err(err) = proxy::execute(
                                    &request.device_id,
                                    request.seq,
                                    http_request,
                                    response_sender,
                                )
                                .await
                                {
                                    error!(%url, "http proxy fail. {}", err.display_chain());
                                }
                            }
                        }.in_current_span());
                        }
                        MessageType::ChangePwdByVerifyCode => {
                            let input = message.payload();
                            let result: Result<ChangePwdByVerifyCode, &'static str> =
                                tlv::read_to_struct!(input);
                            let Ok(request) = result else {
                                error!(message_type = ?MessageType::ProxyRequest, "bad message");
                                break;
                            };
                            if !Self::check_spa_type(
                                self.spa_cache.clone(),
                                self.peer_addr.ip(),
                                &request.device_id,
                                &[SPAType::ChangePwd],
                                MessageType::ChangePwdByVerifyCode,
                            )
                            .await
                            {
                                break;
                            }

                            // 修改密码
                            if let Err(err) = super::common::handle_change_password_request(
                                self.peer_addr,
                                self.backend_client,
                                tx.clone(),
                                request.data,
                            )
                            .await
                            {
                                error!("{err}");
                            }
                            break;
                        }
                        message_type => {
                            error!(?message_type, "received unexpected message",);
                            break;
                        }
                    }
                }
                Err(_) => {
                    warn!("Wait timeout, close connection");
                    break;
                }
            }
        }

        drop(tx);
        let Ok(mut sink) = write_half.await else {
            return Err(());
        };

        if let Err(err) = sink.close().await {
            error!("{err}");
        }

        Err(())
    }

    /// 在经过SNAT后, 同一网络中, 某一个设备经过SPA认证后,
    /// 即端口针对这个网络已放开
    /// 其他设备在认证时, SPA可能还未验证完成, TCP连接先进来了,
    /// 会导致验证失败 睡眠100ms, 等待SPA验证完成
    async fn check_spa_type(
        spa_cache: Arc<Cache<IpAddr, HashMap<String, SPAType>>>,
        ip: IpAddr,
        device_id: &str,
        expected_type: &[SPAType],
        message_type: MessageType,
    ) -> bool {
        let result = match spa_cache
            .get(&ip)
            .await
            .map(|devices| devices.get(device_id).map(|spa_type| *spa_type))
            .unwrap_or_default()
        {
            None => {
                // 等待100ms
                tokio::time::sleep(Duration::from_millis(100)).await;
                match spa_cache
                    .get(&ip)
                    .await
                    .map(|devices| devices.get(device_id).map(|spa_type| *spa_type))
                    .unwrap_or_default()
                {
                    None => Ok(None),
                    Some(spa_type) if expected_type.contains(&spa_type) => Ok(Some(spa_type)),
                    Some(_) => Err(()),
                }
            }
            Some(spa_type) => {
                if !expected_type.contains(&spa_type) {
                    // 等待100ms
                    tokio::time::sleep(Duration::from_millis(100)).await;
                    match spa_cache
                        .get(&ip)
                        .await
                        .map(|devices| devices.get(device_id).map(|spa_type| *spa_type))
                        .unwrap_or_default()
                    {
                        None => Ok(None),
                        Some(spa_type) if expected_type.contains(&spa_type) => Ok(Some(spa_type)),
                        Some(_) => Err(()),
                    }
                } else {
                    Ok(Some(spa_type))
                }
            }
        };

        match result {
            Ok(None) => {
                error!(device.id = device_id, "client is not authorized by SPA");
                false
            }
            Err(_) => {
                error!(?message_type, "message does not match SPA type");
                false
            }
            Ok(Some(_)) => true,
        }
    }

    async fn handle_auth_request(
        &self,
        tx: flume::Sender<ChannelMessage>,
        auth_pkt: Value,
    ) -> Result<Session, Error> {
        debug!("received authentication packet");

        let auth_result = super::auth::auth(
            self.peer_addr,
            auth_pkt,
            &self.cache_manager,
            self.backend_client.clone(),
            tx.clone(),
        )
        .await?;

        // 填充重连票据
        let token = hex::to_string!(&auth_result.token);
        let user = match self
            .cache_manager
            .get::<AuthUser>(&format!("{}|{token}", &auth_result.tenant))
            .await
        {
            Some(user) => user,
            None => {
                error!("the token does not exist or has expired. {token}");
                return Err(Error::AuthenticationFailed);
            }
        };

        let response = AuthResult {
            token,
            expire_tip_day: auth_result.expire_tip_day as u32,
            expire_day: auth_result.expire_day as u32,
            dns: self.dns_servers.clone(),
            data: ModePayload::Mix {
                reconnect_token: user.ticket,
                expire: user.expire,
            },
            client_ip: Some(self.peer_addr.ip()),
        };

        let auth_response = Message::new(MessageType::AuthSuccess, &response.serialize());
        _ = tx
            .send_async(ChannelMessage::Message {
                message: auth_response,
                callback: None,
            })
            .await;
        debug!("authentication successfully");
        Ok(Session {
            peer_addr: self.peer_addr,
            local_addr: self.local_addr,
            device_id: auth_result.device_id,
            tenant: auth_result.tenant,
            username: auth_result.username,
            user_id: user.user_id,
            env: auth_result.env,
            id: user.session_id,
        })
    }
}
