use crate::environment;
use futures_channel::mpsc::UnboundedSender;
use libcore::{proxy_request::Proxy<PERSON><PERSON>le, EventListener};
use types::{backend::TicketType, net::Connectivity};

#[derive(Clone)]
pub struct Listener {
    pub proxy_handle: Proxy<PERSON>and<PERSON>,
    pub reconnect_tx: UnboundedSender<()>,
}

#[allow(unused_variables)]
impl EventListener for Listener {
    fn notify_new_tunnel_state(&self, new_state: types::states::TunnelState) {}

    fn notify_new_backend_state(&self, new_state: types::states::BackendState) {}

    fn notify_connectivity(&self, connectivity: Connectivity) {
        // 网络准备好
        if connectivity.is_online() {
            _ = self.reconnect_tx.unbounded_send(());
        }
    }

    fn notify_close_reason(&self, reason: u8) {}

    fn notify_notification_reason(&self, reason: u8) {}

    fn notify_change_pwd_result(&self, bytes: &[u8]) {}

    fn notify_need_auth(&self) {}

    fn notify_ticket_result(&self, bytes: &[u8], ticket_type: TicketType) {}

    fn notify_denied_access(&self, bytes: &[u8]) {}

    fn notify_environment(&self, bytes: Vec<u8>) {
        let env = serde_json::from_slice::<serde_json::Value>(&bytes).unwrap();
        environment::upload(self.proxy_handle.clone(), env);
    }

    fn notify_update_status(&self, status: &str, msg: Option<String>) {}

    fn notify_download_progress(&self, chunk_size: usize, content_length: u64) {}
}
