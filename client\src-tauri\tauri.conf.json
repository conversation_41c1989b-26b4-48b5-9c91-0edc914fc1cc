{"build": {"beforeBuildCommand": {"cwd": "../ui", "script": "vite build"}, "beforeDevCommand": {"cwd": "../ui", "script": "vite"}, "devPath": "http://localhost:8081", "distDir": "../ui/dist", "withGlobalTauri": true}, "package": {"productName": "安全令", "version": "8.4.0"}, "tauri": {"allowlist": {"all": false, "fs": {"scope": ["$RESOURCE/*"]}, "http": {"all": true, "request": true, "scope": ["http://**", "https://**"]}, "process": {"all": true}, "shell": {"all": false, "open": true}, "window": {"all": true}}, "bundle": {"active": true, "category": "Productivity", "copyright": "© 2022 - 2024 景安云信. All rights reserved.", "externalBin": ["bin/oneidcore", "bin/config"], "icon": ["icons/oneid_256x256.png", "icons/oneid_16x16.png", "icons/oneid_24x24.png", "icons/oneid_32x32.png", "icons/oneid_48x48.png", "icons/oneid_64x64.png", "icons/oneid_96x96.png", "icons/oneid_128x128.png", "icons/oneid_icon.icns", "icons/oneid_icon.ico"], "identifier": "com.jingantech.oneid", "longDescription": "该客户端可辅助用户进行远程办公", "macOS": {"entitlements": null, "exceptionDomain": "", "frameworks": [], "providerShortName": null, "signingIdentity": null}, "publisher": "北京景安云信科技有限公司", "resources": ["./certs/tls/**", "../attachments/oneid.crt"], "shortDescription": "该客户端可辅助用户进行远程办公", "targets": ["deb", "dmg", "nsis"], "windows": {"certificateThumbprint": "61204F34847A6A5C1910EC1AA964384623A4EA9B", "digestAlgorithm": "sha256", "nsis": {"customLanguageFiles": {"simpchinese": "nsis/locales/SimpChinese.nsh"}, "headerImage": "icons/oneid_header.bmp", "installMode": "perMachine", "installerIcon": "icons/oneid_installer.ico", "languages": ["simpchinese"], "sidebarImage": "icons/oneid_sidebar.bmp", "template": "nsis/installer.nsi"}, "timestampUrl": "http://timestamp.comodoca.com", "webviewInstallMode": {"silent": false, "type": "embed<PERSON><PERSON><PERSON><PERSON>"}, "wix": {"language": {"zh-CN": {"localePath": "wix/locales/zh-CN.wxl"}}, "template": "wix/main.wxs"}}, "deb": {"desktopTemplate": "./oneid.desktop", "files": {"/usr/share/polkit-1/actions/com.jingantech.oneid.policy": "./com.jingantech.oneid.policy", "/usr/bin/oneid_auth.sh": "../attachments/linux/deb/auth", "/opt/jingantech/oneid/product_logo_128.svg": "./icons/oneid_128.svg", "/opt/jingantech/oneid/product_logo_256.svg": "./icons/oneid_256.svg"}, "depends": ["ca-certificates", "libnss3-tools", "xdg-utils (>= 1.0.2)"], "preInstallScript": "./deb_preinst", "postInstallScript": "./deb_postinst", "preRemoveScript": "./deb_prerm"}}, "security": {"dangerousRemoteDomainIpcAccess": [{"domain": "localhost", "enableTauriAPI": true, "scheme": null, "windows": ["user_agreement"]}]}, "systemTray": {"iconAsTemplate": false, "iconPath": "icons/oneid_icon.png"}}}