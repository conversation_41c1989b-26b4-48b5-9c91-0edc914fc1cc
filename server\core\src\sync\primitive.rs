// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

mod core_primitive {
    pub use ::core::sync::atomic::*;
    pub use alloc::sync::Arc;
    pub use atomic_waker::AtomicWaker;
}

pub use self::core_primitive::*;

/// Indicates if the type is a zero-sized type
///
/// This can be used to optimize the code to avoid needless calculations.
pub trait IsZst {
    const IS_ZST: bool;
}

impl<T> IsZst for T {
    const IS_ZST: bool = ::core::mem::size_of::<T>() == 0;
}
