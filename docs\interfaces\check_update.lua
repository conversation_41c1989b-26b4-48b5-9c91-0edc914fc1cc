---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by luo<PERSON>i.
--- DateTime: 2023/12/1 14:46
---

-- 引入JSON库
local json = require "cjson"
local v = require "semver"

-- 读取本地JSON文件
local file, err = io.open("/data/config/update.json", "r")
if not file then
    ngx.status = 500
    --ngx.say("Failed to open JSON file: " .. err)
    ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
end

-- 读取文件内容
local content = file:read("*all")
file:close()

-- 解析JSON数据
local data, err = json.decode(content)
if not data then
    ngx.status = 500
    --ngx.say("Failed to parse JSON data: " .. err)
    ngx.exit(ngx.HTTP_INTERNAL_SERVER_ERROR)
end

-- 获取请求URI
local uri = ngx.var.uri
-- 更新包扩展名
local ext = ngx.var.http_ext

-- 使用正则表达式从URI中提取参数
local captures, err = ngx.re.match(uri, "/check_update/([^/]+)/([^/]+)/([^/]+)/([^/]+)(?:/([^/]+))?")
if not captures then
    ngx.status = 404
    -- ngx.say("Invalid URI")
    ngx.exit(ngx.HTTP_NOT_FOUND)
end

-- 解析提取的参数
local product = captures[1]
local platform = captures[2]
local arch = captures[3]
local version = captures[4]
local model = captures[5]

-- linux上安装包有多种格式, 通过扩展名区分
if platform == "linux" and ext and ext ~= "" then
    model = model .. "_" .. ext
end

-- 对应平台更新内容
local platforms = data[product]
if not platforms then
    ngx.status = 204
    ngx.exit(ngx.HTTP_NO_CONTENT)
end
local target = platform .. "-" .. arch
-- 如果没有型号
local update_content = {}
if not model then
    update_content = platforms[target]
    if not update_content then
        ngx.status = 204
        ngx.exit(ngx.HTTP_NO_CONTENT)
    end
else
    local models = platforms[target]
    if not models then
        ngx.status = 204
        ngx.exit(ngx.HTTP_NO_CONTENT)
    end
    update_content = models[model];
    if not update_content then
        ngx.status = 204
        ngx.exit(ngx.HTTP_NO_CONTENT)
    end
end

-- 检查是否需要更新
local current_v = v(version)                    -- 客户端当前版本
local latest_v = v(update_content["version"])   -- 客户端最新版本
if current_v < latest_v then
    -- 设置响应头
    ngx.header.content_type = "application/json"

    -- 返回JSON数据
    ngx.say(json.encode(update_content))

    ngx.exit(ngx.HTTP_OK)
end

ngx.status = 204
ngx.exit(ngx.HTTP_NO_CONTENT)
