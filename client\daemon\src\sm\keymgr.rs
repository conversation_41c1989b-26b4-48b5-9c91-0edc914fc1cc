use base64::prelude::*;
use picky::{
    key::{PrivateKey, PublicKey},
    pem::Pem,
};
use regex::Regex;
use std::error::Error;
// use picky_asn1_x509::PublicKey as InnerPublicKey;
// use picky_asn1::wrapper::{BitStringAsn1, BitStringAsn1Container, IntegerAsn1, OctetStringAsn1};

fn extract_data_field(input: &str) -> Result<Vec<u8>, Box<dyn Error>> {
    // Define the regular expression to extract the data field
    let re = Regex::new(r"data: \[(.*?)\]")?;

    // Find the first match
    let caps = re.captures(input).ok_or("No match found")?;

    // Get the matched group (the content inside the brackets)
    let data_str = caps.get(1).ok_or("No data found")?.as_str();

    println!("data_str:{}", data_str);
    // Split the string by commas and convert to Vec<u8>
    let data_vec: Vec<u8> = data_str
        .split(',')
        .map(|s| s.trim().parse::<u8>())
        .collect::<Result<Vec<u8>, _>>()?;

    Ok(data_vec)
}

pub fn get_public_key(public_key_base64: &str) -> Result<String, Box<dyn Error>> {
    // Decode the Base64 string into bytes
    let bytes_base64 = BASE64_STANDARD.decode(public_key_base64)?;

    // Parse the public key using picky
    let pem = Pem::new("PUBLIC KEY", bytes_base64);
    let public_key = PublicKey::from_pem(&pem)?;

    println!("public_key:{:?}", &public_key);
    let public_key_str = format!("{:?}", public_key);
    let pubkey1 = extract_data_field(&public_key_str)?;
    // PublicKey(SubjectPublicKeyInfo { algorithm: AlgorithmIdentifier { algorithm:
    // ObjectIdentifierAsn1(ObjectIdentifier { root: Iso, first_node: 2, child_nodes: [840, 10045,
    // 2, 1] }), parameters: Ec(NamedCurve(ObjectIdentifierAsn1(ObjectIdentifier { root: Iso,
    // first_node: 2, child_nodes: [156, 10197, 1, 301] }))) }, subject_public_key:
    // Ec(BitStringAsn1(BitString { data: [0, 4, 185, 161, 168, 178, 0, 36, 187, 29, 216, 99, 236,
    // 134, 109, 25, 185, 14, 174, 99, 83, 64, 64, 91, 237, 223, 124, 233, 29, 175, 132, 200, 167,
    // 165, 92, 93, 169, 224, 189, 177, 72, 13, 230, 206, 129, 46, 193, 248, 134, 146, 38, 202, 115,
    // 123, 201, 156, 39, 180, 32, 31, 238, 232, 237, 26, 232, 194] })) })

    // let pubkey1:Vec<u8> = vec![0, 4, 185, 161, 168, 178, 0, 36, 187, 29, 216, 99, 236, 134, 109,
    // 25, 185, 14, 174, 99, 83, 64, 64, 91, 237, 223, 124, 233, 29, 175, 132, 200, 167, 165, 92,
    // 93, 169, 224, 189, 177, 72, 13, 230, 206, 129, 46, 193, 248, 134, 146, 38, 202, 115, 123,
    // 201, 156, 39, 180, 32, 31, 238, 232, 237, 26, 232, 194];

    // Get the encoded public key bytes
    // let pubkey1 = match public_key {
    //     PublicKey::Ec(ec_key) => ec_key.to_encoded_point(false).as_bytes().to_vec(),
    //     _ => return Err("Unsupported key type".into()),
    // };
    // let pubkey1 = public_key.to_der()?;
    // let str_ans1 = BitStringAsn1::from(pubkey1);
    // let integer_asn1 = IntegerAsn1::from_bytes_be_unsigned(pubkey1);
    // println!("asn1:{:?}",&str_ans1.0);
    // let decode_pubkey = elliptic_curve::pkcs8::DecodePublicKey(public_key);
    // Remove the first byte (0x04) and convert the rest to a hexadecimal string
    let pubkey2 = hex::encode(&pubkey1[1..]).to_uppercase();

    Ok(pubkey2)
}

pub fn get_private_key(private_key_base64: &str) -> Result<String, Box<dyn Error>> {
    // Decode the Base64 string into bytes
    let bytes_base64 = BASE64_STANDARD.decode(private_key_base64)?;

    println!("pk len {} is {:?}", bytes_base64.len(), bytes_base64);
    // Parse the private key using picky
    let pem = Pem::new("PRIVATE KEY", bytes_base64);
    let private_key = PrivateKey::from_pem(&pem)?;

    let private_key_str = format!("{:?}", &private_key);
    println!("private_key_str is {}", &private_key_str);
    let private_key_bytes = extract_data_field(&private_key_str)?;
    // Get the encoded private key bytes
    // let private_key_bytes = private_key.to_der()?;
    // Convert the private key bytes to a hexadecimal string
    let private_key_hex = hex::encode(private_key_bytes).to_uppercase();

    Ok(private_key_hex)
}
