use std::net::IpAddr;

use deadpool_lapin::Pool;
use lapin::Channel;
use tokio::sync::mpsc;
use tracing::{error, info, warn, Instrument};

use super::{SDP_EXCHANGE, USER_STATE_QUEUE};

use crate::Error;

fn do_spawn_producer(pool: Pool, channel: Channel, mut rx: mpsc::Receiver<(IpAddr, String)>) {
    tokio::spawn(
        async move {
            loop {
                match rx.recv().await {
                    Some((ip, device_id)) => {
                        let mut payload = vec![];
                        payload.push(100);
                        match ip {
                            IpAddr::V4(ip) => {
                                payload.push(4);
                                payload.extend_from_slice(&ip.octets());
                            }
                            IpAddr::V6(ip) => {
                                payload.push(6);
                                payload.extend_from_slice(&ip.octets());
                            }
                        }
                        payload.extend_from_slice(device_id.as_bytes());
                        if let Err(err) = channel
                            .basic_publish(
                                SDP_EXCHANGE,
                                USER_STATE_QUEUE,
                                Default::default(),
                                &payload,
                                Default::default(),
                            )
                            .await
                        {
                            error!("publish error: {err}");
                            break;
                        }
                    }
                    None => break,
                }
            }

            tokio::spawn(async move {
                info!("recreate channel");

                let mut timeout = 1;
                loop {
                    let channel = get_channel!(pool, timeout);

                    do_spawn_producer(pool, channel, rx);
                    break;
                }
            });
        }
        .instrument(tracing::error_span!(parent: None, "OnlineNotifyProducer")),
    );
}

/// 集群环境下, 广播通知上线的消息
pub(super) async fn spawn_producer(
    pool: Pool,
    rx: mpsc::Receiver<(IpAddr, String)>,
) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqProducerError(error.to_string()))?;
    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqProducerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("online_notify channel status is abnormal: {error}");
    });

    do_spawn_producer(pool, channel, rx);

    Ok(())
}
