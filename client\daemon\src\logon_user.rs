use std::{
    ffi::{CStr, OsStr},
    iter::once,
    os::windows::prelude::OsStrExt,
    path::PathBuf,
    ptr,
};

use serde::Deserialize;
use winapi::um::lmaccess::PUSER_INFO_1;
use winreg::{
    enums::{HKEY_CLASSES_ROOT, HKEY_LOCAL_MACHINE},
    RegKey,
};

pub fn is_correct_logon() -> bool {
    // log::info!("is_correct_logon");
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret = hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\Credential Providers\\{E8881F18-2CCB-4251-9F88-FEB046AA2563}");
    match open_ret {
        Ok(rk) => {
            let Ok(current_logon) = rk.get_value::<String, &str>("currentlogon") else {
                return false;
            };
            let lastlogon = get_lastlogin();
            log::info!("lastlogon:{},currentlogon:{}", &lastlogon, &current_logon);
            lastlogon.eq(&current_logon)
        }
        Err(err) => {
            log::error!("error is {:?}", &err);
            false
        }
    }
}

pub fn is_2faenabled() -> bool {
    let hklm = RegKey::predef(HKEY_CLASSES_ROOT);
    let open_ret = hklm.open_subkey("Jingan");
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("2fa");
            match value_ret {
                Ok(v) => {
                    log::trace!("2fa is {}", &v);
                    println!("2fa is {}", &v);
                    v.trim().contains("1")
                }
                Err(_err) => false,
            }
        }
        Err(_err) => false,
    }
}

// 获取刚登录的账户
fn get_lastlogin() -> String {
    // HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\LogonUI\\
    // LastLoggedOnUser
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret =
        hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\LogonUI");
    let mut username = String::new();
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("LastLoggedOnUser");
            match value_ret {
                Ok(v) => {
                    username = v.clone();
                }
                Err(err) => {
                    log::error!("error is {}", &err);
                }
            }
        }
        Err(err) => {
            log::error!("error2 is {}", &err);
        }
    }
    log::info!("username is {}", &username);
    if username.contains("\\") {
        let arr: Vec<&str> = username.split('\\').collect();
        if arr.len() > 1 {
            username = arr[1].to_owned();
        }
    }
    username
}

// 获取用户账户根目录
fn get_userprofiledir(userhome_name: &str) -> String {
    #[cfg(windows)]
    {
        log::trace!("userhome_name is {}", userhome_name);
        unsafe {
            let mut lpcch_size: Vec<u32> = vec![256];
            let mut lp_profile_dir: Vec<i8> = Vec::with_capacity(256);
            let lp_profile_dir = lp_profile_dir.as_mut_ptr();
            let ret =
                winapi::um::userenv::GetProfilesDirectoryA(lp_profile_dir, lpcch_size.as_mut_ptr());
            log::trace!("GetProfilesDirectoryA result is {}", &ret);
            if ret > 0 {
                let c_str = CStr::from_ptr(lp_profile_dir);
                let homedirret = c_str.to_str();
                let homedir = match homedirret {
                    Ok(dir) => dir,
                    Err(err) => {
                        log::error!("error in find profiles directory {:?}", &err);
                        ""
                    }
                };
                if homedir != "" {
                    let directory = format!("{}\\{}\\", homedir, &userhome_name);
                    log::trace!("User profile directory:{}", &directory);
                    // env::set_current_dir(&directory);
                    return directory;
                }
            }
        };
    }
    String::new()
}

/// u16 数组转u8
#[allow(dead_code)]
fn as_u8_slice(slice: &[u16]) -> &[u8] {
    let len = 2 * slice.len();
    let ptr = slice.as_ptr().cast::<u8>();
    unsafe { std::slice::from_raw_parts(ptr, len) }
}
// 通过api 获取用户的真实用户目录的名称,例如 用户原名为Administrator 目录名Administrator
// ,但是Administrator 可以修改成Admin,但是Administrator 这个目录名没变
pub fn get_userhomedir_name(username: &str) -> String {
    unsafe {
        let dwlevel: u32 = 1;
        let wideusername: Vec<u16> = OsStr::new(username).encode_wide().chain(once(0)).collect();
        let st_len: usize = 1024;
        let mut lpuser1: Vec<u8> = Vec::with_capacity(st_len);
        let mut ptruser1 = lpuser1.as_mut_ptr();
        let empty_ptr = ptr::null_mut();
        let ret_getuserinfo = winapi::um::lmaccess::NetUserGetInfo(
            empty_ptr,
            wideusername.as_ptr(),
            dwlevel,
            &mut ptruser1,
        );
        if ret_getuserinfo != 0 {
            return String::new();
        }
        log::info!("getuserinfo:{:?}", &ret_getuserinfo);
        let puserinfo = ptruser1 as PUSER_INFO_1;
        let userinfo = *puserinfo;
        let puser_home_dir = userinfo.usri1_home_dir;
        log::trace!("looking for the home dir");
        if puser_home_dir.is_null() {
            log::trace!("puser_home_dir is null");
            return String::new();
        }
        let mut vec_u16: Vec<u16> = Vec::new();
        let mut i: isize = 0;
        loop {
            let n = *(puser_home_dir.offset(i));
            if n == 0 && i > 0 {
                break;
            }
            if n > 0 {
                vec_u16.push(n);
            }
            i = i + 1;
        }

        let ustr = widestring::ustring::U16String::from_vec(vec_u16);
        println!("user home:{:?}", &ustr);
        let home_diretory = ustr.to_string().unwrap_or_default();
        log::info!("home directory:{}", &home_diretory);
        return home_diretory;
    }
}

// 映射配置文件路径
pub fn map_config(project: &str, main_config: &str) -> Result<PathBuf, String> {
    let mut config_path = PathBuf::new();
    let username = get_lastlogin();
    if &username == "" {
        return Err(String::from("username is empty"));
    }
    let mut homedirname = get_userhomedir_name(&username);
    if homedirname == "" {
        homedirname = username.to_string().clone();
    }
    println!("homedirname:{}", &homedirname);
    let userdir = get_userprofiledir(&homedirname);
    let user_home_dir = format!("{}", &userdir);
    println!("user_home_dir:{}", &userdir);
    if &user_home_dir == "" || &username == "" {
        return Err(String::from("Error in reading path"));
    }

    let str_path = format!("{}.config\\{}\\{}", &user_home_dir, project, main_config);
    log::info!("str_path:{}", &str_path);
    config_path.push(&str_path);
    log::info!("the config file is {:?}", &config_path);
    Ok(config_path)
}

#[allow(dead_code)]
#[derive(Deserialize)]
struct Params {
    is_sdp: Option<String>,
}

pub fn get_lastssousername() -> String {
    log::info!("get_lastssousername");
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret =
        hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\Credential Providers\\{E8881F18-2CCB-4251-9F88-FEB046AA2563}");
    let mut username = String::new();
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("CURRENTSSOUSERNAME");
            match value_ret {
                Ok(v) => {
                    username = v.clone();
                }
                Err(err) => {
                    log::error!("error is {}", &err);
                }
            }
        }
        Err(err) => {
            log::error!("error2 is {}", &err);
        }
    }
    log::info!("username is {}", &username);
    username
}

fn get_lastlogin_sid() -> String {
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret =
        hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\LogonUI");
    let mut user_sid = String::new();
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("LastLoggedOnUserSID");
            match value_ret {
                Ok(v) => {
                    user_sid = v.clone();
                }
                Err(err) => {
                    log::error!("error is {}", &err);
                }
            }
        }
        Err(err) => {
            log::error!("error2 is {}", &err);
        }
    }
    log::info!("user_sid is {}", &user_sid);
    user_sid
}

fn get_lastlogin_sso_sid() -> String {
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret =
        hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\Credential Providers\\{E8881F18-2CCB-4251-9F88-FEB046AA2563}");
    let mut user_sid = String::new();
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("SID_USERID");
            match value_ret {
                Ok(v) => {
                    user_sid = v.clone();
                }
                Err(err) => {
                    log::error!("error is {}", &err);
                }
            }
        }
        Err(err) => {
            log::error!("error2 is {}", &err);
        }
    }
    log::info!("user_sid is {}", &user_sid);
    user_sid
}

// 是否是本地登录，本地登录不会写入SID_USERID 这样与系统登录的sid 不同
pub fn is_localuser_logon() -> bool {
    let sys_last_login_sid = get_lastlogin_sid();
    let sso_last_login_sid = get_lastlogin_sso_sid();
    sys_last_login_sid != sso_last_login_sid
}

pub fn get_server_addr() -> String {
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret =
        hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\Credential Providers\\{E8881F18-2CCB-4251-9F88-FEB046AA2563}");
    let mut server_addr = String::new();
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("server_addr");
            match value_ret {
                Ok(v) => {
                    server_addr = v.clone();
                }
                Err(err) => {
                    log::error!("error is {}", &err);
                }
            }
        }
        Err(err) => {
            log::error!("error2 is {}", &err);
        }
    }
    log::info!("server_addr is {}", &server_addr);
    server_addr
}

#[cfg(test)]
mod tests {
    use crate::logon_user::{get_userhomedir_name, is_2faenabled};

    /// cargo test -- test_home_dir --nocapture
    #[test]
    fn test_home_dir() {
        let homedir = get_userhomedir_name("测试账户");
        println!("homedir is {}", &homedir);
    }

    //  cargo test -- test_is_2faenabled --nocapture
    #[test]
    fn test_is_2faenabled() {
        let flag = is_2faenabled();
        println!("flag is {}", flag);
    }
}
