[package]
name = "core"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
default = ["alloc", "std"]
alloc = ["atomic-waker", "bytes", "crossbeam-utils"]
std = ["alloc", "once_cell"]

[dependencies]
atomic-waker = { version = "1", optional = true }
byteorder = { version = "1", default-features = false }
bytes = { version = "1", optional = true, default-features = false }
crossbeam-utils = { version = "0.8", optional = true }
cfg-if = "1"
hex-literal = "0.4"
# used for event snapshot testing - needs an internal API so we require a minimum version
insta = { version = ">=1.12", features = ["json"], optional = true }
num-rational = { version = "0.4", default-features = false }
num-traits = { version = "0.2", default-features = false, features = ["libm"] }
pin-project-lite = { version = "0.2" }
codec = { path = "../codec", default-features = false }
subtle = { version = "2", default-features = false }
zerocopy = { version = "0.7", features = ["derive"] }
futures-test = { version = "0.3", optional = true }                             # For testing Waker interactions
once_cell = { version = "1", optional = true }

[package.metadata.kani]
unstable = { stubbing = true }

[lints.rust.unexpected_cfgs]
level = "warn"
check-cfg = ['cfg(kani)', 'cfg(kani_slow)']
