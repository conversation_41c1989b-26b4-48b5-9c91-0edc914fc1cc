use std::fmt::{Debug, Di<PERSON>lay, Formatter};

use untrusted::Input;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub enum Error {
    BadMessage,
}

/// 通信消息格式
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Message {
    /// 负载长度占用字节数
    len_len: u8,
    /// 消息类型
    message_type: MessageType,
    /// 负载, 包含长度和类型
    payload: Vec<u8>,
}

impl Message {
    pub fn new(message_type: MessageType, payload: &[u8]) -> Self {
        let len_bytes = tlv::utils::len_to_bytes(payload.len());

        let mut data = vec![message_type as u8];
        data.extend(len_bytes.iter());
        data.extend(payload.iter());
        Message {
            len_len: len_bytes.len() as u8,
            message_type,
            payload: data,
        }
    }

    pub fn r#type(&self) -> MessageType {
        self.message_type
    }

    pub fn payload(&self) -> Input {
        Input::from(&self.payload[1 + self.len_len as usize..])
    }

    pub fn as_bytes(self) -> Vec<u8> {
        self.payload
    }

    pub fn len(&self) -> usize {
        self.payload.len() - 1 - self.len_len as usize
    }
}

impl Display for Message {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Message")
            .field("type", &self.message_type)
            .field("len_len", &self.len_len)
            .finish()
    }
}

use tokio_util::codec::{Decoder, Encoder};

pub struct MessageCodec;

impl MessageCodec {
    pub fn new() -> Self {
        MessageCodec {}
    }
}

impl Default for MessageCodec {
    fn default() -> Self {
        Self::new()
    }
}

impl Encoder<Message> for MessageCodec {
    type Error = std::io::Error;

    fn encode(&mut self, item: Message, dst: &mut bytes::BytesMut) -> Result<(), Self::Error> {
        dst.extend(item.as_bytes());
        Ok(())
    }
}

impl Decoder for MessageCodec {
    type Item = Message;

    type Error = std::io::Error;

    fn decode(&mut self, src: &mut bytes::BytesMut) -> Result<Option<Self::Item>, Self::Error> {
        if src.is_empty() {
            return Ok(None);
        }

        if src.len() < 2 {
            return Ok(None);
        }

        let Some((length_len, length)) = crate::utils::extract_len(src) else {
            return Ok(None);
        };

        let packet_len = length as usize + 1 + length_len as usize;

        if src.len() < packet_len {
            return Ok(None);
        }

        let bytes = src.split_to(packet_len);

        let message_type = bytes[0].into();

        Ok(Some(Message {
            len_len: length_len,
            message_type,
            payload: bytes.to_vec(),
        }))
    }
}

#[derive(PartialEq, Eq, Copy, Clone, Debug)]
#[repr(u8)]
#[allow(deprecated)]
pub enum MessageType {
    /// 未知数据包
    Unknown = 0,
    Authentication = 1,
    AuthPassCommand = 2,
    Environment = 3,
    ServerList = 4,
    IncrementalServer = 5,
    ChangePwd = 6,
    #[deprecated]
    _DelAuthPassCommand = 7,
    AuthSuccess = 8,
    AuthFailed = 9,
    AuthToken = 10,
    CloseStream = 11,
    TokenFailed = 12,
    ServerPacket = 13,
    TokenSuccess = 14,
    ForwardPacket = 15,
    ReadyConnect = 16,
    CloseReason = 17,
    RequiredMfa = 18,
    MfaAuthCompleted = 19,
    CancelMfaAuth = 20,
    NeedChangePwd = 30,
    ChangePwdByVerifyCode = 31,
    ChangePwdResult = 32,
    #[deprecated]
    AuthCheckImage = 33,
    #[deprecated]
    AuthCheckImageResult = 34,
    #[deprecated]
    AuthRequireVerifyCode = 35,
    #[deprecated]
    AuthRequireVerifyCodeResult = 36,
    /// 下发策略
    DeliveryStrategy = 37,
    /// 拒绝访问
    #[deprecated]
    DeniedAccess = 38,
    /// 重置票据
    RefreshToken = 39,
    /// 重连票据结果
    RefreshTokenResult = 40,
    /// 资源白名单
    ResourceWhitelist = 41,
    /// WEB应用列表
    WebApps = 42,
    /// 通知消息
    Notification = 43,
    /// WEB应用虚拟IP
    WebAppsVirtualIps = 44,
    /// 生成单点登录票据
    GenerateSSOTicket = 100,
    /// 票据结果
    SSOTicketResult = 101,
    /// 代理客户端接口请求
    ProxyRequest = 102,
    /// 代理客户端请求响应
    ProxyResponse = 103,
    /// 代理请求响应-chunk数据包
    ProxyResponseChunk = 104,
    /// 代理失败, 会返回一个错误消息
    ProxyFail = 105,
    // 资源列表发送完成
    ServerListFinished = 255,
}

#[allow(deprecated)]
impl From<u8> for MessageType {
    fn from(t: u8) -> Self {
        // Safety: u8 必须是已经定义了的
        // unsafe { std::mem::transmute::<u8, Self>(t) }
        match t {
            1 => MessageType::Authentication,
            2 => MessageType::AuthPassCommand,
            3 => MessageType::Environment,
            4 => MessageType::ServerList,
            5 => MessageType::IncrementalServer,
            6 => MessageType::ChangePwd,
            7 => MessageType::_DelAuthPassCommand,
            8 => MessageType::AuthSuccess,
            9 => MessageType::AuthFailed,
            10 => MessageType::AuthToken,
            11 => MessageType::CloseStream,
            12 => MessageType::TokenFailed,
            13 => MessageType::ServerPacket,
            14 => MessageType::TokenSuccess,
            15 => MessageType::ForwardPacket,
            16 => MessageType::ReadyConnect,
            17 => MessageType::CloseReason,
            18 => MessageType::RequiredMfa,
            19 => MessageType::MfaAuthCompleted,
            20 => MessageType::CancelMfaAuth,
            30 => MessageType::NeedChangePwd,
            31 => MessageType::ChangePwdByVerifyCode,
            32 => MessageType::ChangePwdResult,
            33 => MessageType::AuthCheckImage,
            34 => MessageType::AuthCheckImageResult,
            35 => MessageType::AuthRequireVerifyCode,
            36 => MessageType::AuthRequireVerifyCodeResult,
            37 => MessageType::DeliveryStrategy,
            38 => MessageType::DeniedAccess,
            39 => MessageType::RefreshToken,
            40 => MessageType::RefreshTokenResult,
            41 => MessageType::ResourceWhitelist,
            42 => MessageType::WebApps,
            43 => MessageType::Notification,
            44 => MessageType::WebAppsVirtualIps,
            100 => MessageType::GenerateSSOTicket,
            101 => MessageType::SSOTicketResult,
            102 => MessageType::ProxyRequest,
            103 => MessageType::ProxyResponse,
            104 => MessageType::ProxyResponseChunk,
            105 => MessageType::ProxyFail,
            _ => MessageType::Unknown,
        }
    }
}
