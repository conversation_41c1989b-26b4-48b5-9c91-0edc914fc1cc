use std::{
    collections::{HashMap, HashSet},
    io,
    net::{IpAddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use crate::xdp::XdpCommand;
use arp::ArpHandle;
use backend::client::BackendCommonClient;
use base::packet::{Message, MessageType};
use cache::CacheManager;
use clap::Parser;
use cli::AppArgs;
use comm::{ClientHandle, CommCommand, VIRTUAL_IP_MAPPING};
use config::{Config, Segments};
use enums::CloseReason;

use flume::Receiver;
use message::MQCommand;
use moka::{
    future::{Cache, FutureExt},
    notification::{ListenerFuture, RemovalCause},
};
use nat::{LightNat, Nat};
use network::Network;
use once_cell::sync::Lazy;
use packet::server::ServerPacketSender;
use serde_json::Value;
use spa::SPAValidator;
use tlv::Serialize;
use tlv_types::{VirtualApps, VirtualIpAddr};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn, Instrument};
use types::Servers;
use virtual_iface::ReqSender;
use xdp::XdpCommandSender;

use crate::{
    comm::listen::CommArgs,
    message::MqArgs,
    packet::{client::ClientForwardInitArgs, server::ServerForwardInitArgs},
};

mod arp;
#[allow(dead_code)]
mod backend;
mod build_info;
#[allow(dead_code)]
mod cache;
mod cli;
mod comm;
#[allow(dead_code)]
mod config;
mod constants;
mod daemon;
mod enums;
mod logging;
mod message;
mod nat;
mod network;
mod packet;
mod spa;
mod traceablitity;
mod traffic;
mod types;
mod virtual_iface;
mod xdp;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Tun error. {0}")]
    TunError(#[from] tun::Error),

    #[error("Bpf error. {0}")]
    BpfError(#[from] aya::EbpfError),

    #[error("Ebpf program error. {0}")]
    EbpfProgramError(#[from] aya::programs::ProgramError),

    #[error("Ebpf map error. {0}")]
    EbpfMapError(#[from] aya::maps::MapError),

    #[error("IO error. {0}")]
    IoError(#[from] io::Error),

    #[error("Failed to create ARP client for interface {0}. {1}")]
    ArpError(String, #[source] io::Error),

    #[error("Failed to create MQ client. {0}")]
    MqBuildError(#[from] deadpool_lapin::BuildError),

    #[error("Failed to create MQ consumer. {0}")]
    MqConsumerError(String),

    #[error("Failed to create MQ producer. {0}")]
    MqProducerError(String),
}

#[allow(dead_code)]
struct Client {
    tenant: String,
    username: String,
    peer_addr: SocketAddr,
    handle: ClientHandle,
    /// 在线时间
    online: u64,
}

/// 客户端连接缓存, key为设备ID
static CLIENTS: Lazy<RwLock<HashMap<String, Client>>> =
    Lazy::new(|| RwLock::new(HashMap::default()));

pub enum InternalEvent {
    /// MQ消息
    MQMessage(MQCommand),
    // /// 转发数据包
    // ForwardPacket(ForwardPacket),
    /// 客户端接入
    PostClientOnline {
        device_id: String,
        ip: IpAddr,
        tenant: String,
        username: String,
        env: Value,
        peer_addr: SocketAddr,
        handle: ClientHandle,
    },
    /// 关闭客户端
    PostClientOffline {
        device_id: String,
        peer_addr: SocketAddr,
    },
    // /// 通信指令
    // CommCommand {
    //     device_id: String,
    //     command: CommCommand,
    // },
    /// 广播命令
    BroadcastCommand(CommCommand),
    /// 后端服务启动后加载的资源列表
    LoadedResource(HashMap<String, Servers>),
    /// 后端服务启动后加载的web应用列表
    LoadedWebApps(HashMap<String, HashSet<tlv_types::WebApp>>),
}

#[derive(Clone)]
pub struct EventSender(flume::Sender<InternalEvent>);

impl EventSender {
    pub async fn send(&self, event: InternalEvent) {
        if self.0.send_async(event).await.is_err() {
            error!("server already down or thread panicked.");
        }
    }
}

fn main() {
    let start = std::time::Instant::now();
    // 解析参数
    let args = AppArgs::parse();
    let (token, reload_handle) = logging::init(&args.log_dir, &args.filter);

    let future = async {
        let config = match config::load(args.config) {
            Ok(config) => config,
            Err(err) => {
                error!("{err}");
                std::process::exit(1);
            }
        };

        let networks = network::info(&config.interface);
        info!("network interface information: {networks:#?}");

        // 全局事件通道
        let (event_sender, event_rx) = flume::unbounded();
        let event_sender = EventSender(event_sender);

        // 创建网卡
        let segments = config.segments();
        let mut light_nat = None;
        let mut req_sender = None;
        let virtual_ip = if let Some((ip, src_ip)) = segments.virtual_ip() {
            let (sender, nat) =
                match virtual_iface::open(&config.interface.r#virtual.name, ip, src_ip).await {
                    Ok(r) => r,
                    Err(err) => {
                        error!("{err}");
                        std::process::exit(1);
                    }
                };
            req_sender = Some(sender);
            light_nat = Some(nat);
            Some(IpAddr::V4(ip))
        } else {
            None
        };

        // 启动ARP
        let addresses = config.interface.primary.multi_nic_addresses.clone();
        let arp_handle = match arp::spawn(virtual_ip, &networks, addresses.clone()).await {
            Ok(r) => r,
            Err(err) => {
                error!("{err}");
                std::process::exit(1);
            }
        };

        // 等待后台启动
        let backend_client = BackendCommonClient::new(config.backend.clone());
        let backend_client = Arc::new(backend_client);
        backend::setup(
            config.backend_interval,
            backend_client.clone(),
            arp_handle.clone(),
            segments.clone(),
            event_sender.clone(),
        )
        .await;

        let server = match create_server(
            config,
            networks,
            event_sender,
            event_rx,
            req_sender,
            light_nat,
            arp_handle,
            backend_client,
        )
        .await
        {
            Ok(server) => server,
            Err(err) => {
                error!("{err}");
                std::process::exit(1);
            }
        };

        daemon::start(reload_handle).await.unwrap();

        info!(elapsed = ?start.elapsed(), "started.");

        server.run().await;
    };

    tokio::runtime::Builder::new_multi_thread()
        .enable_all()
        .thread_name("sdp")
        .build()
        .unwrap()
        .block_on(future.instrument(tracing::error_span!("Application")));
    drop(token);
}

async fn create_server(
    config: Config,
    networks: Vec<Network>,
    event_sender: EventSender,
    event_rx: Receiver<InternalEvent>,
    req_sender: Option<ReqSender>,
    light_nat: Option<Arc<RwLock<LightNat>>>,
    arp_handle: ArpHandle,
    backend_client: Arc<BackendCommonClient>,
) -> Result<Server, Error> {
    let primary_interface = config.interface.primary.base.iface.clone();
    let addresses = config.interface.primary.multi_nic_addresses.clone();
    let segments = config.segments();
    let pub_ports = config.pub_ports();
    let resource_whitelist = config.resource_whitelist();

    let (server_packet_sender, server_packet_receiver) = ServerPacketSender::new();

    // 启动ebpf程序
    let (client_packet_senders, xdp_command_senders) = xdp::start_ebpf(
        config.interface,
        segments.clone(),
        server_packet_sender,
        config.spa_port,
    );

    info!("creating nat table");
    // NAT
    let nat = {
        let mut used_ports = HashSet::default();
        pub_ports.iter().for_each(|port| match port {
            tlv_types::Port::Single(port) => {
                used_ports.insert(*port);
            }
            tlv_types::Port::Range(range) => {
                used_ports.extend((*range.start())..=(*range.end()));
            }
        });
        // SPA和TCP端口
        used_ports.insert(config.spa_port);
        used_ports.insert(config.server.port);
        let nat = Nat::new(xdp_command_senders.clone(), used_ports);
        Arc::new(nat)
    };

    info!("creating spa cache");
    // SPA 认证缓存
    let primary_xdp_sender = xdp_command_senders.primary_xdp_sender();
    let spa_cache = Arc::new(
        Cache::builder()
            .time_to_idle(Duration::from_secs(15))
            .async_eviction_listener(move |k, _v, cause| -> ListenerFuture {
                let primary_xdp_sender = primary_xdp_sender.clone();
                async move {
                    if cause != RemovalCause::Replaced {
                        debug!(k = ?*k, "evict spa cache");
                        primary_xdp_sender.send(XdpCommand::ClosePort(*k)).await;
                    }
                }
                .boxed()
            })
            .build(),
    );

    // SPA 防重放缓存
    let primary_xdp_sender = xdp_command_senders.primary_xdp_sender();
    let anti_replay = Arc::new(
        Cache::builder()
            .time_to_live(Duration::from_secs(15))
            .async_eviction_listener(move |_k, v: SocketAddr, _cause| -> ListenerFuture {
                let primary_xdp_sender = primary_xdp_sender.clone();
                async move {
                    primary_xdp_sender
                        .send(XdpCommand::DelSPARecord(v.ip(), v.port()))
                        .await;
                }
                .boxed()
            })
            .build(),
    );

    // Redis 缓存
    let cache_manager = CacheManager::new(config.redis).await;

    let spa_validator = SPAValidator {
        sec_key: config.sec_key,
        time_deviation: config.spa_time_deviation,
        xdp_command_sender: xdp_command_senders.primary_xdp_sender(),
        cache_manager: cache_manager.clone(),
        backend_client: backend_client.clone(),
        preauthed: spa_cache.clone(),
        anti_replay: anti_replay.clone(),
    };

    // 服务端数据包
    let args = ServerForwardInitArgs {
        spa_port: config.spa_port,
        spa_validator: Arc::new(spa_validator),
        nat: nat.clone(),
        primary_xdp_sender: xdp_command_senders.primary_xdp_sender(),
        packet_receiver: server_packet_receiver,
    };
    packet::server::spawn(args).await;

    // 客户端数据包
    let args = ClientForwardInitArgs {
        networks,
        client_packet_senders,
        xdp_command_senders: xdp_command_senders.clone(),
        arp_handle: arp_handle.clone(),
        nat: nat.clone(),

        primary_interface,
        addresses,
        segments: segments.clone(),
        req_sender,
    };
    let client_packet_handle = packet::client::spawn(args);

    // MQ消息通知
    let (session_traffic_tx, session_traffic_rx) = tokio::sync::mpsc::channel(16);
    let (session_resource_traffic_tx, session_resource_traffic_rx) = tokio::sync::mpsc::channel(16);
    let args = MqArgs {
        url: config.mq.url,
        event_sender: event_sender.clone(),
        session_traffic_rx,
        session_resource_traffic_rx,
    };

    let mq_handle = message::spawn(args).await?;

    // 推送流量统计信息
    traffic::spawn(
        config.push_traffic_interval,
        session_traffic_tx,
        session_resource_traffic_tx,
    );

    // 启动TLCP服务
    let resource_whitelist = Some(Arc::new(resource_whitelist));
    let comm_args = CommArgs {
        authorization_interval: config.authorization_interval,
        event_sender: event_sender.clone(),
        primary_xdp_sender: xdp_command_senders.primary_xdp_sender(),
        keepalive: config.keepalive,
        server_config: config.server,
        dns_servers: config.dns,
        cache_manager: cache_manager.clone(),
        client_packet_handle,
        backend_client,
        mq_handle,
        resource_whitelist: resource_whitelist.clone(),
        spa_cache,
    };
    comm::listen::spawn(comm_args).await;

    Ok(Server {
        rx: event_rx,
        primary_xdp_sender: xdp_command_senders.primary_xdp_sender(),
        resource_whitelist,
        nat,
        light_nat,
        resources: HashMap::new(),
        webapps: HashMap::new(),
        segments,
        arp_handle,
    })
}

struct Server {
    rx: flume::Receiver<InternalEvent>,
    primary_xdp_sender: XdpCommandSender,
    resource_whitelist: Option<Arc<HashSet<SocketAddr>>>,
    nat: Arc<Nat>,
    light_nat: Option<Arc<RwLock<LightNat>>>,
    /// 资源列表, 包含虚拟映射中的虚拟地址列表
    resources: HashMap<String, Servers>,
    /// WEB应用列表
    webapps: HashMap<String, HashSet<tlv_types::WebApp>>,
    segments: Segments,
    arp_handle: ArpHandle,
}

impl Server {
    async fn run(mut self) {
        loop {
            if let Ok(event) = self.rx.recv_async().await {
                match event {
                    InternalEvent::MQMessage(command) => {
                        debug!(?command, "mqcommand");
                        self.handle_mq_command(command).await;
                    }
                    InternalEvent::PostClientOnline {
                        ip,
                        device_id,
                        tenant,
                        username,
                        env,
                        peer_addr,
                        handle,
                    } => {
                        self.primary_xdp_sender
                            .send(XdpCommand::Connected(peer_addr.ip(), peer_addr.port()))
                            .await;

                        self.post_new_client(ip, &tenant, &username, &env, &handle)
                            .await;

                        let timestamp = coarsetime::Clock::now_since_epoch().as_millis();
                        let mut clients = CLIENTS.write().await;
                        if let Some(client) = clients.insert(
                            device_id.clone(),
                            Client {
                                tenant,
                                username,
                                peer_addr,
                                handle,
                                online: timestamp,
                            },
                        ) {
                            warn!(device.id = device_id, pre_addr = %client.peer_addr, "Repeat connection");
                            client.handle.send(CommCommand::Kill).await;
                        }
                    }
                    InternalEvent::PostClientOffline {
                        device_id,
                        peer_addr,
                    } => {
                        self.primary_xdp_sender
                            .send(XdpCommand::DelSPARecord(peer_addr.ip(), peer_addr.port()))
                            .await;

                        self.primary_xdp_sender
                            .send(XdpCommand::CloseConnect(peer_addr.ip(), peer_addr.port()))
                            .await;

                        let mut clients = CLIENTS.write().await;
                        if let Some(client) = clients.get(&device_id) {
                            if client.peer_addr != peer_addr {
                                continue;
                            }
                        }

                        _ = clients.remove(&device_id);
                        drop(clients);

                        self.nat.delete_by_device_id(&device_id).await;

                        if let Some(light_nat) = &self.light_nat {
                            let mut light_nat = light_nat.write().await;
                            light_nat.delete_by_device_id(&device_id).await;
                            drop(light_nat);
                        }
                    }
                    InternalEvent::BroadcastCommand(command) => {
                        let clients = CLIENTS.read().await;
                        for client in clients.values() {
                            client.handle.send(command.clone()).await;
                        }
                    }
                    InternalEvent::LoadedResource(resources) => {
                        self.resources = resources;
                    }
                    InternalEvent::LoadedWebApps(webapps) => {
                        self.webapps = webapps;
                    }
                }
            }
        }
    }

    async fn handle_mq_command(&mut self, command: MQCommand) {
        match command {
            MQCommand::AddResource { tenant, servers } => {
                self.handle_add_resource(tenant, servers).await;
            }
            MQCommand::DelResource { tenant, servers } => {
                self.handle_del_resource(tenant, servers).await;
            }
            MQCommand::AddWebapp { tenant, webapp } => {
                self.handle_add_webapp(tenant, webapp).await;
            }
            MQCommand::DelWebapp { tenant, webapp } => {
                self.handle_del_webapp(tenant, webapp).await;
            }
            MQCommand::AddVirtualResource {
                tenant,
                resource,
                virtual_ip,
            } => {
                self.handle_add_virtual_resource(tenant, resource, virtual_ip)
                    .await;
            }
            MQCommand::UserState {
                reason,
                tenant,
                username,
            } => {
                let reason = CloseReason::from_mq(reason);
                debug!(username = &username, state = ?reason, "user state change");

                let mut clients = CLIENTS.write().await;

                let message = Message::new(MessageType::CloseReason, &(reason as u8).serialize());
                let mut device_ids = vec![];
                for (device_id, client) in clients.iter() {
                    if client.tenant == tenant && client.username == username {
                        client
                            .handle
                            .send(CommCommand::Message(message.clone()))
                            .await;
                        client.handle.send(CommCommand::Kill).await;

                        device_ids.push(device_id.to_owned());
                    }
                }
                device_ids.iter().for_each(|device_id| {
                    _ = clients.remove(device_id);
                });
            }
            MQCommand::UserPasswordChange {
                device_id,
                username,
                tenant,
            } => {
                debug!(username = &username, "user password change");

                let mut clients = CLIENTS.write().await;
                let message = Message::new(
                    MessageType::CloseReason,
                    &(CloseReason::UserPwdChanged as u8).serialize(),
                );
                let mut device_ids = vec![];
                for (_device_id, client) in clients.iter() {
                    if client.tenant == tenant && client.username == username {
                        if &device_id != _device_id {
                            client
                                .handle
                                .send(CommCommand::Message(message.clone()))
                                .await;
                            client.handle.send(CommCommand::Kill).await;
                            device_ids.push(_device_id.to_owned());
                        }
                    }
                }
                device_ids.iter().for_each(|device_id| {
                    _ = clients.remove(device_id);
                });
            }
            MQCommand::DisableDevice(device_id) => {
                debug!(device.id = &device_id, "device disabled");

                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::DeviceDisabled as u8).serialize(),
                    );
                    client.handle.send(CommCommand::Message(message)).await;
                    client.handle.send(CommCommand::Kill).await;
                }
            }
            MQCommand::EndSession(device_id) => {
                debug!(device.id = &device_id, "end session");

                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::EndSession as u8).serialize(),
                    );
                    client.handle.send(CommCommand::Message(message)).await;
                    client.handle.send(CommCommand::Kill).await;
                }
            }

            MQCommand::DisableTenant(tenant) => {
                debug!(tenant = &tenant, "tenant disabled");
                let mut clients = CLIENTS.write().await;
                let message = Message::new(
                    MessageType::CloseReason,
                    &(CloseReason::TenantDisabled as u8).serialize(),
                );
                let mut device_ids = vec![];
                for (_device_id, client) in clients.iter() {
                    if client.tenant == tenant {
                        client
                            .handle
                            .send(CommCommand::Message(message.clone()))
                            .await;
                        client.handle.send(CommCommand::Kill).await;
                        device_ids.push(_device_id.to_owned());
                    }
                }
                device_ids.iter().for_each(|device_id| {
                    _ = clients.remove(device_id);
                });
            }
            #[allow(unused_variables)]
            MQCommand::LoginElseWhere { ip, device_id } => {
                let mut clients = CLIENTS.write().await;
                if let Some(client) = clients.remove(&device_id) {
                    let message = Message::new(
                        MessageType::CloseReason,
                        &(CloseReason::LoginElseWhere as u8).serialize(),
                    );
                    client.handle.send(CommCommand::Message(message)).await;
                    client.handle.send(CommCommand::Close(ip)).await;
                }
            }
        }
    }

    #[allow(unused_variables)]
    async fn post_new_client(
        &self,
        ip: IpAddr,
        tenant: &str,
        username: &str,
        env: &Value,
        handle: &ClientHandle,
    ) {
        // self.xdp_handle
        //     .send_async(XdpCommand::AllowConnect(false, ip))
        //     .await;

        // 发送资源白名单
        if let Some(ref resource_whitelist) = self.resource_whitelist {
            debug!(resources = ?resource_whitelist, "sending whitelist");
            let whitelist = tlv_types::Whitelist {
                ips: Some(resource_whitelist.iter().map(|addr| addr.ip()).collect()),
            };

            let message = Message::new(MessageType::ResourceWhitelist, &whitelist.serialize());
            handle.send(CommCommand::Message(message)).await;
        }

        // WEB应用列表
        if let Some(webapps) = self.webapps.get(tenant) {
            let webapps = tlv_types::WebApps {
                flag: true,
                apps: webapps.clone().into_iter().collect(),
            };

            let message = Message::new(MessageType::WebApps, &webapps.serialize());
            handle.send(CommCommand::Message(message)).await;
        }

        // WEB应用虚拟IP
        use crate::traceablitity::WEBAPPS_REAL_IP_MAPPING;
        let real_mapping = WEBAPPS_REAL_IP_MAPPING.read().await;

        let mut virtual_apps = VirtualApps {
            flag: true,
            mapping: Default::default(),
        };
        for (addr, virtual_ip) in real_mapping.iter() {
            virtual_apps.mapping.push(tlv_types::VirtualIpAddr {
                real: addr.ip(),
                virtual_ip: *virtual_ip,
            });
        }

        let message = Message::new(MessageType::WebAppsVirtualIps, &virtual_apps.serialize());
        handle.send(CommCommand::Message(message)).await;

        if let Some(servers) = self.resources.get(tenant) {
            debug!(resources = ?servers, "sending resources");
            let bytes = servers.as_bytes();
            let message = Message::new(MessageType::ServerList, &bytes);
            handle.send(CommCommand::Message(message)).await;
        }
    }

    async fn handle_add_resource(&mut self, tenant: String, servers: Servers) {
        debug!("add resources: {:?}", servers);

        let mut payload = vec![tlv::Tag::Boolean as u8, 1, 1];
        payload.append(&mut servers.as_bytes());
        let message = Message::new(MessageType::IncrementalServer, &payload);
        let command = CommCommand::Message(message);

        let new_resources = servers.ips.clone();

        let resources = self.resources.entry(tenant.clone()).or_default();
        *resources += servers;

        for resource in new_resources {
            let iface = self.segments.find_interface(&resource);
            self.arp_handle.send(iface, resource).await;
        }

        let clients = CLIENTS.read().await;
        for client in clients.values().filter(|client| client.tenant == tenant) {
            client.handle.send(command.clone()).await;
        }
    }

    async fn handle_del_resource(&mut self, tenant: String, servers: Servers) {
        debug!("del resources: {:?}", servers);

        let mut payload = vec![tlv::Tag::Boolean as u8, 1, 0];
        payload.append(&mut servers.as_bytes());
        let message = Message::new(MessageType::IncrementalServer, &payload);
        let command = CommCommand::Message(message);

        let resources = self.resources.entry(tenant.clone()).or_default();
        *resources -= servers;

        let clients = CLIENTS.read().await;
        for client in clients.values().filter(|client| client.tenant == tenant) {
            client.handle.send(command.clone()).await;
        }
    }

    async fn handle_add_webapp(&mut self, tenant: String, webapp: tlv_types::WebApp) {
        debug!("add webapp: {:?}", webapp);

        if let Some(mapping) = crate::traceablitity::add_apps([webapp.clone()].iter()).await {
            let virtual_apps = VirtualApps {
                flag: true,
                mapping: mapping
                    .into_iter()
                    .map(|(real, virtual_ip)| VirtualIpAddr { real, virtual_ip })
                    .collect(),
            };
            let message = Message::new(MessageType::WebAppsVirtualIps, &virtual_apps.serialize());
            let command = CommCommand::Message(message);

            let clients = CLIENTS.read().await;
            for client in clients.values().filter(|client| client.tenant == tenant) {
                client.handle.send(command.clone()).await;
            }
        }

        let webapps = self.webapps.entry(tenant).or_default();
        webapps.insert(webapp);
    }

    async fn handle_del_webapp(&mut self, tenant: String, webapp: tlv_types::WebApp) {
        debug!("del webapp: {:?}", webapp);

        if let Some(mapping) = crate::traceablitity::del_app(&webapp).await {
            let virtual_apps = VirtualApps {
                flag: false,
                mapping: mapping
                    .into_iter()
                    .map(|(real, virtual_ip)| VirtualIpAddr { real, virtual_ip })
                    .collect(),
            };
            let message = Message::new(MessageType::WebAppsVirtualIps, &virtual_apps.serialize());
            let command = CommCommand::Message(message);

            let clients = CLIENTS.read().await;
            for client in clients.values().filter(|client| client.tenant == tenant) {
                client.handle.send(command.clone()).await;
            }
        }

        let webapps = self.webapps.entry(tenant).or_default();
        webapps.remove(&webapp);
    }

    #[allow(unused_variables)]
    async fn handle_add_virtual_resource(
        &mut self,
        tenant: String,
        resource: IpAddr,
        virtual_ip: IpAddr,
    ) {
        debug!("add virtual resource: {:?}-{:?}", &resource, &virtual_ip);

        let mut virtual_ip_mapping = VIRTUAL_IP_MAPPING.write().await;
        let virtual_mapping = virtual_ip_mapping.entry(tenant.clone()).or_default();
        virtual_mapping.insert(virtual_ip, resource);
        drop(virtual_ip_mapping);

        // 发送资源
        // 删除原有IP
        let list = tlv_types::IncrResourceList {
            flag: false,
            resources: Some(vec![match resource {
                IpAddr::V4(ip) => tlv_types::Resource::Ipv4(ip),
                IpAddr::V6(ip) => tlv_types::Resource::Ipv6(ip),
            }]),
        };
        let message = Message::new(MessageType::IncrementalServer, &list.serialize());
        let del_origin_command = CommCommand::Message(message);

        // 新增虚拟IP
        let list = tlv_types::IncrResourceList {
            flag: true,
            resources: Some(vec![match virtual_ip {
                IpAddr::V4(ip) => tlv_types::Resource::Ipv4(ip),
                IpAddr::V6(ip) => tlv_types::Resource::Ipv6(ip),
            }]),
        };
        let message = Message::new(MessageType::IncrementalServer, &list.serialize());
        let add_virtual_command = CommCommand::Message(message);

        let clients = CLIENTS.read().await;
        for client in clients.values().filter(|client| client.tenant == tenant) {
            client.handle.send(del_origin_command.clone()).await;
            client.handle.send(add_virtual_command.clone()).await;
        }
    }
}
