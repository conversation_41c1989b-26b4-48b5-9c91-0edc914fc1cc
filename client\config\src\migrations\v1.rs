/// ```json
/// {
//     "endpoint":"https://promotion.jinganiam.com:21000",
//     "tenant":{
//         "code":"DEFAULT",
//         "name":"景安云信"
//     },
//     "user":{
//         "username":"luofei",
//         "password":"Aa123..",
//         "remember_me":true,
//         "auto_connect":false
//     },
//     "lang":"zh",
//     "secrets":{
//         "DEFAULT":""
//     },
//     "payload":{
//         "active":"pwd", // 当前登录方式
//         "activename":"pwd",
//         "controller":{
//             "host":"ctrl-215.sdp.jingantech.com",
//             "ip":null,
//             "port":3031,
//             "spaPort":65321
//         },
//         "data":{
//             "DEFAULT":{
//                 "auto_connect":false,
//                 "bindCode":"",
//                 "password":"Aa123..",
//                 "remember_me":true,
//                 "user":"luofei"
//             }
//         },
//         "subscript":0, // 忽略
//         "tenantvisible":true // 切换租户提示
//     }
// }
/// ```
///
use super::Result;
use serde_json::json;
#[cfg(feature = "cluster")]
use serde_json::Number;

const VERSION: semver::Version = semver::Version::new(7, 5, 0);

pub fn migrate(config: &mut serde_json::Value) -> Result<()> {
    if !super::version_matches(config, VERSION) {
        return Ok(());
    }

    // unwrap is safe
    let config = config.as_object_mut().unwrap();

    log::info!(target: "app", "Migrating settings format to {}", VERSION);

    config.insert(
        String::from("version"),
        serde_json::Value::String(format!("{}", VERSION)),
    );

    // 语言/后端地址 不用转换

    // 删除secrets和user
    _ = config.remove("secrets");
    _ = config.remove("user");

    // 选择的租户
    if let Some(tenant) = config.remove("tenant") {
        if let (Some(code), Some(name)) = (tenant.get("code"), tenant.get("name")) {
            config.insert(
                String::from("selected_tenant"),
                json!({
                    "code": code,
                    "name": name,
                }),
            );
        }
    }

    if let Some(payload) = config.remove("payload") {
        // 切换租户提示
        if let Some(show_change_tenant_tip) = payload["tenantvisible"].as_bool() {
            config.insert(
                String::from("show_change_tenant_tip"),
                serde_json::Value::Bool(show_change_tenant_tip),
            );
        }

        // 认证节点
        #[cfg(feature = "sdp")]
        if let Some(controller) = payload["controller"].as_object() {
            if let (Some(host), ip, Some(port), Some(spa_port)) = (
                controller["host"].as_str(),
                controller["ip"].as_str(),
                controller["port"].as_u64(),
                controller["spaPort"].as_u64(),
            ) {
                config.insert(
                    String::from("node"),
                    json!({
                        "host": host,
                        "ip": ip,
                        "port": port,
                        "spa_port": spa_port,
                    }),
                );
            }
        }

        // 集群地址
        #[cfg(feature = "cluster")]
        if let Some(cluster_config_url) = payload["clusterConfigUrl"].as_str() {
            config.insert(
                String::from("cluster_config_url"),
                serde_json::Value::String(cluster_config_url.to_owned()),
            );
        }

        #[cfg(feature = "cluster")]
        if let Some(cluster_starting_index) = payload["clusterStartingIndex"].as_u64() {
            config.insert(
                String::from("cluster_starting_index"),
                serde_json::Value::Number(Number::from(cluster_starting_index)),
            );
        } else {
            config.insert(
                String::from("cluster_starting_index"),
                serde_json::Value::Number(Number::from(0)),
            );
        }

        #[cfg(feature = "cluster")]
        if let Some(cluster_node_changed_times) = payload["clusterNodeChangedTimes"].as_u64() {
            config.insert(
                String::from("cluster_node_changed_times"),
                serde_json::Value::Number(Number::from(cluster_node_changed_times)),
            );
        } else {
            config.insert(
                String::from("cluster_node_changed_times"),
                serde_json::Value::Number(Number::from(0)),
            );
        }

        // 节点列表
        #[cfg(feature = "cluster")]
        if let Some(controllers) = payload["controllers"].as_array() {
            let mut nodes = vec![];
            for controller in controllers {
                if let (Some(host), ip, Some(port), Some(spa_port)) = (
                    controller["host"].as_str(),
                    controller["ip"].as_str(),
                    controller["port"].as_u64(),
                    controller["spaPort"].as_u64(),
                ) {
                    nodes.push(json!({
                        "host": host,
                        "ip": ip,
                        "port": port,
                        "spa_port": spa_port,
                    }))
                }
            }
            config.insert(
                String::from("cluster_nodes"),
                serde_json::Value::Array(nodes),
            );
        }

        // 用户信息
        let mut users = json!({});
        if let Some(map) = payload["data"].as_object() {
            for (code, user) in map {
                if let (Some(username), password, Some(remember_me), Some(auto_connect)) = (
                    user["username"].as_str(),
                    user["password"].as_str(),
                    user["remember_me"].as_bool(),
                    user["auto_connect"].as_bool(),
                ) {
                    users[code] = json!({
                        "username": username,
                        "password": password,
                        "remember_me": remember_me,
                        "auto_connect": auto_connect,
                    });
                    #[cfg(feature = "sdp")]
                    if let Some(secret) = user["bindCode"].as_str() {
                        users[code]["secret"] = serde_json::Value::String(secret.to_owned());
                    }
                    // 上次认证方式
                    if let Some(login_method) = payload["active"].as_str() {
                        users[code]["login_method"] =
                            serde_json::Value::String(login_method.to_owned());
                    }
                }
            }
        }

        config.insert(String::from("users"), users);
    }

    Ok(())
}
