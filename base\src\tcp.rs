use std::{
    io::{self},
    net::{SocketAddr, ToSocketAddrs},
    sync::Arc,
};

use rustls::{pki_types::pem::PemObject, RootCertStore, SupportedCipherSuite};
use tokio_rustls::TlsAcceptor;

use rustls::{
    crypto::{ring as provider, CryptoProvider},
    pki_types::{CertificateDer, PrivateKeyDer},
};

#[cfg(not(feature = "tlcp"))]
use rustls::{pki_types::ServerName, server::WebPkiClientVerifier};
#[cfg(not(feature = "tlcp"))]
use std::convert::TryFrom;
#[cfg(not(feature = "tlcp"))]
use tokio::net::TcpStream;

#[cfg(not(feature = "tlcp"))]
use rustls::version::{TLS12, TLS13};
#[cfg(not(feature = "tlcp"))]
use tokio_rustls::{client::TlsStream as ClientTlsStream, TlsConnector};

#[cfg(not(feature = "tlcp"))]
static DEFAULT_CIPHER_SUITES: &[SupportedCipherSuite] = &[
    rustls::crypto::ring::cipher_suite::TLS13_AES_256_GCM_SHA384,
    rustls::crypto::ring::cipher_suite::TLS13_AES_128_GCM_SHA256,
    rustls::crypto::ring::cipher_suite::TLS13_CHACHA20_POLY1305_SHA256,
    rustls::crypto::ring::cipher_suite::TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
    rustls::crypto::ring::cipher_suite::TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
    rustls::crypto::ring::cipher_suite::TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
    rustls::crypto::ring::cipher_suite::TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
    rustls::crypto::ring::cipher_suite::TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
    rustls::crypto::ring::cipher_suite::TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
];

#[cfg(feature = "tlcp")]
static TLCP_CIPHER_SUITES: &[SupportedCipherSuite] = &[
    rustls::crypto::ring::cipher_suite::TLCP_ECC_SM4_GCM_SM3,
    rustls::crypto::ring::cipher_suite::TLCP_ECDHE_SM4_GCM_SM3,
];

pub fn load_certs(filename: &str) -> Vec<CertificateDer<'static>> {
    CertificateDer::pem_file_iter(filename)
        .expect("cannot open certificate file")
        .map(|result| result.unwrap())
        .collect()
}

pub fn load_pem_certs(cert: &str) -> Vec<CertificateDer<'static>> {
    CertificateDer::pem_slice_iter(cert.as_bytes())
        .map(|result| result.unwrap())
        .collect()
}

pub fn load_private_key(filename: &str) -> PrivateKeyDer<'static> {
    PrivateKeyDer::from_pem_file(filename).expect("cannot read private key file")
}

pub fn load_pem_private_key(key: &str) -> PrivateKeyDer<'static> {
    PrivateKeyDer::from_pem_slice(key.as_bytes()).expect("cannot read private key file")
}

pub fn lookup_ipaddr(host: &str, port: u16) -> io::Result<SocketAddr> {
    let addrs = (host, port).to_socket_addrs().unwrap();
    for addr in addrs {
        if let SocketAddr::V4(_) = addr {
            return Ok(addr);
        }
        if let SocketAddr::V6(_) = addr {
            return Ok(addr);
        }
    }
    Err(io::Error::new(io::ErrorKind::InvalidInput, "invalid host"))
}

#[cfg(not(feature = "tlcp"))]
pub fn make_client_config_with_certs(
    roots: Option<Vec<Vec<u8>>>,
    certs: Vec<CertificateDer<'static>>,
    key: PrivateKeyDer<'static>,
) -> Arc<rustls::ClientConfig> {
    let mut root_store = RootCertStore::empty();
    if let Some(roots) = roots {
        let roots = roots.iter().map(|pem| CertificateDer::from_slice(&pem));
        root_store.add_parsable_certificates(roots);
    }

    let suites = DEFAULT_CIPHER_SUITES.to_vec();
    let versions = vec![&TLS13, &TLS12];

    let config = rustls::ClientConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suite/versions selected")
    .with_root_certificates(root_store)
    .with_client_auth_cert(certs, key)
    .expect("invalid client auth certs/key");
    Arc::new(config)
}

#[cfg(feature = "tlcp")]
pub fn make_client_config_with_certs(
    roots: Option<Vec<Vec<u8>>>,
    certs: Vec<CertificateDer<'static>>,
    key: PrivateKeyDer<'static>,
    enc_cert: CertificateDer<'static>,
    enc_key: PrivateKeyDer<'static>,
) -> Arc<rustls::ClientConfig> {
    let mut root_store = RootCertStore::empty();
    if let Some(roots) = roots {
        let roots = roots.iter().map(|pem| CertificateDer::from_slice(&pem));
        root_store.add_parsable_certificates(roots);
    }

    let suites = TLCP_CIPHER_SUITES.to_vec();
    let versions = vec![&rustls::version::TLCP11];

    let config = rustls::ClientConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suite/versions selected")
    .with_root_certificates(root_store)
    .with_client_auth_dual_cert(
        certs,
        key,
        enc_cert,
        enc_key,
        rustls::DualCertFormat::SIGN_ENC_CA,
    )
    .expect("invalid client auth certs/key");
    Arc::new(config)
}

#[cfg(not(feature = "tlcp"))]
pub fn make_client_config(
    ca_file: &str,
    certs_file: &str,
    key_file: &str,
) -> Arc<rustls::ClientConfig> {
    let roots = load_certs(ca_file);
    let mut root_store = RootCertStore::empty();
    root_store.add_parsable_certificates(roots);

    let suites = DEFAULT_CIPHER_SUITES.to_vec();
    let versions = vec![&TLS13, &TLS12];

    let certs = load_certs(certs_file);
    let key = load_private_key(key_file);

    let config = rustls::ClientConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suite/versions selected")
    .with_root_certificates(root_store)
    .with_client_auth_cert(certs, key)
    .expect("invalid client auth certs/key");
    Arc::new(config)
}

#[cfg(not(feature = "tlcp"))]
fn make_client_config_with_pem(ca: &str, cert: &str, key: &str) -> Arc<rustls::ClientConfig> {
    let roots = load_pem_certs(ca);
    let mut root_store = RootCertStore::empty();
    root_store.add_parsable_certificates(roots);

    let suites = DEFAULT_CIPHER_SUITES.to_vec();
    let versions = vec![&TLS13, &TLS12];

    let certs = load_pem_certs(cert);
    let key = load_pem_private_key(key);

    let config = rustls::ClientConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suite/versions selected")
    .with_root_certificates(root_store)
    .with_client_auth_cert(certs, key)
    .expect("invalid client auth certs/key");
    Arc::new(config)
}

#[cfg(not(feature = "tlcp"))]
fn make_server_config(roots: &str, certs: &str, key_file: &str) -> Arc<rustls::ServerConfig> {
    let roots = load_certs(roots);
    let certs = load_certs(certs);
    let mut client_auth_roots = RootCertStore::empty();
    client_auth_roots.add_parsable_certificates(roots);

    let client_auth = WebPkiClientVerifier::builder(Arc::new(client_auth_roots))
        .build()
        .unwrap();
    // let client_auth = NoClientAuth::new();

    let privkey = load_private_key(key_file);
    let suites = DEFAULT_CIPHER_SUITES.to_vec();
    let versions = vec![&TLS13, &TLS12];

    let mut config = rustls::ServerConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suites/versions specified")
    .with_client_cert_verifier(client_auth)
    .with_single_cert(certs, privkey)
    .expect("bad certificates/private key");

    config.key_log = Arc::new(rustls::KeyLogFile::new());
    config.session_storage = rustls::server::ServerSessionMemoryCache::new(256);
    Arc::new(config)
}

#[cfg(feature = "tlcp")]
fn make_server_config(
    roots: &str,
    sign_certs: &str,
    sign_key: &str,
    enc_cert: &str,
    enc_key: &str,
) -> Arc<rustls::ServerConfig> {
    use rustls::server::WebPkiClientVerifier;

    let roots = load_certs(roots);
    let sign_certs = load_certs(sign_certs);
    let enc_cert = load_certs(enc_cert);
    let enc_cert = enc_cert.into_iter().next().expect("invalid enc certs");
    let mut client_auth_roots = RootCertStore::empty();
    client_auth_roots.add_parsable_certificates(roots);
    let client_auth = WebPkiClientVerifier::builder(Arc::new(client_auth_roots))
        .build()
        .unwrap();
    // let client_auth = NoClientAuth::new();

    let sign_key = load_private_key(sign_key);
    let enc_key = load_private_key(enc_key);
    let suites = TLCP_CIPHER_SUITES.to_vec();
    let versions = vec![&rustls::version::TLCP11];

    let mut config = rustls::ServerConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suites/versions specified")
    .with_client_cert_verifier(client_auth)
    .with_dual_cert(
        sign_certs,
        sign_key,
        enc_cert,
        enc_key,
        rustls::DualCertFormat::SIGN_ENC_CA,
    )
    .expect("bad certificates/private key");

    config.key_log = Arc::new(rustls::KeyLogFile::new());
    config.session_storage = rustls::server::ServerSessionMemoryCache::new(256);
    Arc::new(config)
}

#[cfg(not(feature = "tlcp"))]
fn make_server_config_with_pem(
    roots: &str,
    certs: &str,
    key_file: &str,
) -> Arc<rustls::ServerConfig> {
    let roots = load_pem_certs(roots);
    let certs = load_pem_certs(certs);
    let mut client_auth_roots = RootCertStore::empty();
    client_auth_roots.add_parsable_certificates(roots);
    let client_auth = WebPkiClientVerifier::builder(Arc::new(client_auth_roots))
        .build()
        .unwrap();

    let privkey = load_pem_private_key(key_file);
    let suites = DEFAULT_CIPHER_SUITES.to_vec();
    let versions = vec![&TLS13, &TLS12];

    let mut config = rustls::ServerConfig::builder_with_provider(
        CryptoProvider {
            cipher_suites: suites,
            ..provider::default_provider()
        }
        .into(),
    )
    .with_protocol_versions(&versions)
    .expect("inconsistent cipher-suites/versions specified")
    .with_client_cert_verifier(client_auth)
    .with_single_cert(certs, privkey)
    .expect("bad certificates/private key");

    config.key_log = Arc::new(rustls::KeyLogFile::new());
    config.session_storage = rustls::server::ServerSessionMemoryCache::new(256);
    Arc::new(config)
}

#[cfg(not(feature = "tlcp"))]
pub async fn new_tls_stream(
    domain: &str,
    addr: SocketAddr,
    ca_file: &str,
    cert_file: &str,
    key_file: &str,
) -> io::Result<ClientTlsStream<TcpStream>> {
    let config = make_client_config(ca_file, cert_file, key_file);

    let connector = TlsConnector::from(config);

    let stream = TcpStream::connect(&addr).await?;
    let server_name = ServerName::try_from(domain)
        .map(|n| n.to_owned())
        .map_err(|_| io::Error::new(io::ErrorKind::InvalidInput, "invalid dnsname"))?;
    let stream = connector.connect(server_name, stream).await?;
    Ok(stream)
}

#[cfg(not(feature = "tlcp"))]
pub async fn new_tls_stream_with_pem(
    domain: &str,
    addr: std::net::SocketAddr,
    ca: &str,
    cert: &str,
    key: &str,
) -> ClientTlsStream<TcpStream> {
    let config = make_client_config_with_pem(ca, cert, key);

    let connector = TlsConnector::from(config);

    let stream = TcpStream::connect(&addr).await.unwrap();
    let server_name = ServerName::try_from(domain)
        .map(|n| n.to_owned())
        .map_err(|_| io::Error::new(io::ErrorKind::InvalidInput, "invalid dnsname"))
        .unwrap();
    connector.connect(server_name, stream).await.unwrap()
}

#[cfg(not(feature = "tlcp"))]
pub fn new_tls_acceptor(ca_file: &str, cert_file: &str, key_file: &str) -> TlsAcceptor {
    let config = make_server_config(ca_file, cert_file, key_file);
    TlsAcceptor::from(config)
}

#[cfg(feature = "tlcp")]
pub fn new_tls_acceptor(
    ca_file: &str,
    sign_cert_file: &str,
    sign_key: &str,
    enc_cert_file: &str,
    enc_key: &str,
) -> TlsAcceptor {
    let config = make_server_config(ca_file, sign_cert_file, sign_key, enc_cert_file, enc_key);
    TlsAcceptor::from(config)
}

#[cfg(not(feature = "tlcp"))]
pub fn new_tls_acceptor_with_pem(ca: &str, cert: &str, key: &str) -> TlsAcceptor {
    let config = make_server_config_with_pem(ca, cert, key);
    TlsAcceptor::from(config)
}

// #[cfg(test)]
// mod tests {
//     use tokio::{
//         io::{AsyncReadExt, AsyncWriteExt},
//         net::TcpListener,
//     };

//     use super::*;

//     const CA_FILE: &str = "/files/cert/tfa.jingantech.com/ca.crt";
//     const CLIENT_CERT_FILE: &str = "/files/cert/tfa.jingantech.com/client.crt";
//     const CLIENT_KEY_FILE: &str = "/files/cert/tfa.jingantech.com/client.key";
//     const SERVER_CERT_FILE: &str = "/files/cert/tfa.jingantech.com/server.crt";
//     const SERVER_KEY_FILE: &str = "/files/cert/tfa.jingantech.com/server.key";

//     #[tokio::test]
//     async fn tls() {
//         let msg = b"Hello World";
//         let mut buf = [0; 11];

//         start_server().await;

//         start_client(msg, &mut buf).await;
//         assert_eq!(&buf, msg);

//         println!("{:?}", String::from_utf8(buf.to_vec()));
//     }

//     async fn start_server() {
//         let tls_acceptor = new_tls_acceptor(CA_FILE, SERVER_CERT_FILE, SERVER_KEY_FILE);
//         let listener = TcpListener::bind("0.0.0.0:5002").await.unwrap();

//         tokio::spawn(async move {
//             let (stream, _peer_addr) = listener.accept().await.unwrap();
//             let mut tls_stream = tls_acceptor.accept(stream).await.unwrap();
//             println!("server: Accepted client conn with TLS");

//             let mut buf = [0; 12];
//             tls_stream.read_exact(&mut buf).await.unwrap();
//             println!("server: got data: {buf:?}");

//             tls_stream.write(&buf).await.unwrap();
//             println!("server: flush the data out");
//         });
//     }

//     async fn start_client(msg: &[u8], buf: &mut [u8]) {
//         let addr = lookup_ipaddr("127.0.0.1", 5002).unwrap();
//         let mut tls_stream = new_tls_stream(
//             "tfa.jingantech.com",
//             addr,
//             CA_FILE,
//             CLIENT_CERT_FILE,
//             CLIENT_KEY_FILE,
//         )
//         .await
//         .unwrap();

//         tls_stream.write(msg).await.unwrap();
//         println!("client: send data");

//         tls_stream.read_exact(buf).await.unwrap();
//         println!("client: read echoed data");
//     }
// }
