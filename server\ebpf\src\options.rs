/// 白名单队列大小
pub(crate) const WHITE_LIST_SIZE: u32 = if let Some(ct) = option_env!("SDP_WHITE_LIST") {
    konst::result::unwrap_or!(konst::primitive::parse_u32(ct), 32)
} else {
    32
};

#[allow(dead_code)]
/// 同时单包认证的客户端队列大小
pub(crate) const SPA_PASSED: u32 = if let Some(ct) = option_env!("SDP_CONCURRENCY_SPA_PASSED") {
    konst::result::unwrap_or!(konst::primitive::parse_u32(ct), 65536)
} else {
    65536
};

#[allow(dead_code)]
/// 最大在线人数
pub(crate) const CONNECTED_CLIENTS: u32 = if let Some(ct) = option_env!("SDP_CONNECTED_CLIENTS") {
    konst::result::unwrap_or!(konst::primitive::parse_u32(ct), 20000)
} else {
    20000
};

/// 客户端访问资源的并发量(允许同时访问资源的最大量)
/// 资源: IP+PORT表示一个资源
pub(crate) const CONCURRENCY_ACCESS: u32 = if let Some(ct) = option_env!("SDP_CONCURRENCY_ACCESS") {
    konst::result::unwrap_or!(konst::primitive::parse_u32(ct), 102400)
} else {
    102400
};
