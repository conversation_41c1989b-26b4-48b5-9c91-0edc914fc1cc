<template>
  <div class="app-content">
    <!-- <headerBar /> -->
    <div
      class="about-content"
      v-loading.fullscreen.lock="loading"
      element-loading-text="loading_text"
      element-loading-background="rgba(0, 0, 0, 0.5)"
    >
      <div class="about-header">
        <img src="/assets/oneid_logo.png" alt="" class="icon" />
        <!-- <div><strong>One ID</strong></div> -->
      </div>
      <div class="about-footer">
        <div>
          <strong class="appstr">软件更新</strong><br />
          <strong
            v-if="hasNewVersion"
            style="font-weight: normal; color: #53565d"
            >发现新版本：v{{ productVersion }}</strong
          >
          <strong v-else style="font-weight: normal; color: #53565d"
            >当前已是最新版本：v{{ productVersion }}</strong
          >
          &nbsp;<el-button
            plain
            @click="check"
            style="border: 1px solid #f87f1a; color: #f87f1a"
          >
            {{ hasNewVersion ? "立即更新" : "检查更新" }}
          </el-button>
        </div>
        <div>
          <strong class="appstr">关于我们</strong><br />
          <strong
            style="
              font-weight: normal;
              display: flex;
              align-items: center;
              padding-left: 2px;
            "
          >
            <el-icon color="#77C16F">
              <CircleCheck />
            </el-icon>
            <span style="text-indent: 5px; color: #9c9ea3"
              >已通过 ISO 27001:2013 信息安全认证</span
            >
          </strong>
          <span
            style="color: #f87f1a; cursor: pointer"
            @click="openUserAgreement"
            >《用户协议》</span
          >
        </div>
        <div>
          <strong class="appstr">设备ID</strong><br />
          <span
            >{{ deviceId }} &nbsp;<el-icon
              style="color: #f9780c; cursor: pointer"
              @click="copyDeviceId()"
            >
              <DocumentCopy /> </el-icon
          ></span>
        </div>
      </div>
    </div>
    <updater ref="updater" />
  </div>
</template>

<script>
import headerBar from "@/components/Header.vue";
import { computed, inject } from "vue";
import { useStore } from "vuex";
import updater from "@/views/updater.vue";

export default {
  name: "About",
  components: {
    updater,
    headerBar,
  },
  setup() {
    const store = useStore();
    const checkUpdate = inject("checkUpdate");
    const openUserAgreement = inject("openUserAgreement");
    return {
      checkUpdate,
      openUserAgreement,
      productVersion: window.PRODUCT_VERSION,
      deviceId: window.DEVICE_ID,
      hasNewVersion: computed(() => store.getters.hasNewVersion),
    };
  },
  data() {
    return {
      loading_text: "正在退出...",
      loading: false,
    };
  },
  created() {},
  methods: {
    // 复制ID
    copyDeviceId() {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(window.DEVICE_ID)
          .then(() => {
            this.alert.success("已复制到剪贴板");
          })
          .catch((error) => {
            this.alert.error("复制失败：" + error);
          });
      } else {
        // 如果不支持 navigator.clipboard，使用 document.execCommand
        const textarea = document.createElement("textarea");
        textarea.value = window.DEVICE_ID;
        textarea.style.position = "absolute";
        textarea.style.left = "-9999px"; // 隐藏在视图之外
        document.body.appendChild(textarea);
        textarea.select(); // 选中内容

        try {
          document.execCommand("copy");
          this.alert.success("已复制到剪贴板");
        } catch (error) {
          this.alert.error("复制失败：" + error);
        } finally {
          // 移除临时创建的 textarea
          document.body.removeChild(textarea);
        }
      }
    },
    async check() {
      this.loading_text = "检查中";
      this.loading = true;
      let response = await this.checkUpdate(true, 5000);

      if (response && response.shouldUpdate) {
        this.$nextTick(() => {
          this.$refs.updater.init(response);
        });
      }

      this.loading = false;
      this.loading_text = "正在退出...";
    },
  },
};
</script>

<style lang="less" scoped>
.about-content {
  padding: 0 20px;
  // text-align: center;
  font-size: 15px;

  .about-header {
    //   margin-top: 30%;
    img {
      width: 60px;
      margin-left: 15px;
      height: auto;
    }
  }

  .appstr {
    font-size: 16px;
    font-weight: 600;
    color: #25282f;
  }

  .about-footer {
    //   margin-top: 40%;
    // line-height: 60px;
    font-size: 14px;
    line-height: 40px;
  }

  .about-footer > div {
    margin-bottom: 25px;
    padding-left: 16px;
  }
}
</style>
<style></style>

