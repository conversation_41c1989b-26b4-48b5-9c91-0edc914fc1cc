use futures::{
    channel::{mpsc, oneshot},
    future::Fuse,
    FutureExt, StreamExt,
};
use std::net::IpAddr;

use err_ext::ErrorExt;
use types::tunnel::{ErrorStateCause, TunnelStateTransition};

use crate::{
    tunnel::{TunioTunnel, TunnelParameters},
    tunnel_state_machine::{
        connected_state::{ConnectedState, ConnectedStateBootstrap, TunnelEventsReceiver},
        disconnecting_state::{AfterDisconnect, DisconnectingState},
        TunnelArgs, TunnelCommand, TunnelEvent,
    },
};

use super::{
    error_state::ErrorState, EventConsequence, EventResult, ResourceEvent, SharedTunnelStateValues,
    TunnelCommandReceiver, TunnelMetadata, TunnelState, TunnelStateWrapper,
};

pub(crate) type TunnelCloseEvent = Fuse<oneshot::Receiver<Option<ErrorStateCause>>>;

pub struct ConnectingState {
    tunnel_events: TunnelEventsReceiver,
    tunnel_metadata: Option<TunnelMetadata>,
    tunnel_close_event: TunnelCloseEvent,
    tunnel_close_tx: oneshot::Sender<()>,
    packet_tx: mpsc::UnboundedSender<Vec<u8>>,
    auth_resource_tx: mpsc::UnboundedSender<ResourceEvent>,
    change_network_tx: mpsc::Sender<(IpAddr, oneshot::Sender<()>)>,
    #[cfg(feature = "traceability")]
    traceability_tx: mpsc::Sender<bool>,
}

impl ConnectingState {
    async fn start_tunnel(parameters: TunnelParameters) -> Self {
        let (event_tx, event_rx) = mpsc::unbounded();

        let (tunnel_close_tx, tunnel_close_rx) = oneshot::channel();
        let (tunnel_close_event_tx, tunnel_close_event_rx) = oneshot::channel();

        let (packet_tx, packet_rx) = mpsc::unbounded();
        let (auth_resource_tx, auth_resource_rx) = mpsc::unbounded();
        let (change_network_tx, change_network_rx) = mpsc::channel(1);
        #[cfg(feature = "traceability")]
        let (traceability_tx, traceability_rx) = mpsc::channel(1);

        tokio::spawn({
            #[cfg(feature = "traceability")]
            let packet_tx = packet_tx.clone();
            async move {
                let on_tunnel_event = move |event| -> std::pin::Pin<
                    Box<dyn std::future::Future<Output = ()> + Send>,
                > {
                    let (tx, rx) = oneshot::channel();
                    let _ = event_tx.unbounded_send((event, tx));
                    Box::pin(async move {
                        let _ = rx.await;
                    })
                };

                let args = TunnelArgs {
                    on_event: on_tunnel_event,
                    tunnel_close_rx,
                };

                let block_reason = match TunioTunnel::start(
                    parameters,
                    args,
                    #[cfg(feature = "traceability")]
                    packet_tx,
                    packet_rx,
                    auth_resource_rx,
                    change_network_rx,
                    #[cfg(feature = "traceability")]
                    traceability_rx,
                )
                .await
                {
                    Ok(tunnel) => {
                        let reason = tunnel.listen().await;
                        log::debug!("Tunnel exited with block reason: {:?}", reason);
                        reason
                    }
                    Err(error) => {
                        log::error!("{}", error.display_chain_with_msg("Failed to start tunnel"));
                        let block_reason = match error {
                            crate::tunnel::Error::EnableIpv6Error => {
                                ErrorStateCause::Ipv6Unavailable
                            }
                            #[cfg(target_os = "android")]
                            tunnel::Error::WireguardTunnelMonitoringError(
                                talpid_wireguard::Error::TunnelError(
                                    talpid_wireguard::TunnelError::SetupTunnelDeviceError(
                                        tun_provider::Error::PermissionDenied,
                                    ),
                                ),
                            ) => ErrorStateCause::VpnPermissionDenied,
                            #[cfg(target_os = "android")]
                            tunnel::Error::WireguardTunnelMonitoringError(
                                talpid_wireguard::Error::TunnelError(
                                    talpid_wireguard::TunnelError::SetupTunnelDeviceError(
                                        tun_provider::Error::InvalidDnsServers(addresses),
                                    ),
                                ),
                            ) => ErrorStateCause::InvalidDnsServers(addresses),
                            _ => ErrorStateCause::StartTunnelError,
                        };
                        Some(block_reason)
                    }
                };
                if tunnel_close_event_tx.send(block_reason).is_err() {
                    log::warn!("Tunnel state machine stopped before receiving tunnel closed event");
                }

                log::trace!("Tunnel monitor thread exit");
            }
        });

        ConnectingState {
            tunnel_events: event_rx.fuse(),
            tunnel_metadata: None,
            tunnel_close_event: tunnel_close_event_rx.fuse(),
            tunnel_close_tx,
            packet_tx,
            auth_resource_tx,
            change_network_tx,
            #[cfg(feature = "traceability")]
            traceability_tx,
        }
    }

    fn into_connected_state_bootstrap(self, metadata: TunnelMetadata) -> ConnectedStateBootstrap {
        ConnectedStateBootstrap {
            metadata,
            tunnel_events: self.tunnel_events,
            tunnel_close_event: self.tunnel_close_event,
            tunnel_close_tx: self.tunnel_close_tx,
            tunnel_packet_tx: self.packet_tx,
            auth_resource_tx: self.auth_resource_tx,
            change_network_tx: self.change_network_tx,
            #[cfg(feature = "traceability")]
            traceability_tx: self.traceability_tx,
        }
    }

    async fn reset_routes(shared_values: &mut SharedTunnelStateValues) {
        if let Err(error) = shared_values.route_manager.clear_routes() {
            log::error!("{}", error.display_chain_with_msg("Failed to clear routes"));
        }
        #[cfg(target_os = "linux")]
        if let Err(error) = shared_values.route_manager.clear_routing_rules().await {
            log::error!(
                "{}",
                error.display_chain_with_msg("Failed to clear routing rules")
            );
        }
    }

    async fn disconnect(
        self,
        shared_values: &mut SharedTunnelStateValues,
        after_disconnect: AfterDisconnect,
    ) -> EventConsequence {
        Self::reset_routes(shared_values).await;

        EventConsequence::NewState(
            DisconnectingState::enter(
                shared_values,
                (
                    self.tunnel_close_tx,
                    self.tunnel_close_event,
                    after_disconnect,
                ),
            )
            .await,
        )
    }

    async fn handle_commands(
        self,
        command: Option<TunnelCommand>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match command {
            Some(TunnelCommand::Dns(servers)) => match shared_values.set_dns_servers(servers) {
                #[cfg(target_os = "android")]
                Ok(true) => self.disconnect(shared_values, AfterDisconnect::Reconnect(0)),
                Ok(_) => SameState(self.into()),
                Err(cause) => {
                    self.disconnect(shared_values, AfterDisconnect::Block(cause))
                        .await
                }
            },
            Some(TunnelCommand::AskIsOffline(tx)) => {
                let connectivity = shared_values.offline_monitor.connectivity().await;
                _ = tx.send(connectivity.is_offline());
                SameState(self.into())
            }
            Some(TunnelCommand::Connect) => {
                self.disconnect(shared_values, AfterDisconnect::Reconnect(0))
                    .await
            }
            Some(TunnelCommand::Disconnect(_)) | None => {
                self.disconnect(shared_values, AfterDisconnect::Nothing)
                    .await
            }
            _ => SameState(self.into()),
        }
    }

    async fn handle_tunnel_events(
        mut self,
        event: Option<(TunnelEvent, oneshot::Sender<()>)>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match event {
            Some((TunnelEvent::Up(metadata), _)) => NewState(
                ConnectedState::enter(shared_values, self.into_connected_state_bootstrap(metadata))
                    .await,
            ),
            Some((TunnelEvent::Down, _)) => {
                self.tunnel_metadata = None;

                SameState(self.into())
            }
            None => {
                // The channel was closed
                log::debug!("The tunnel disconnected unexpectedly");
                self.disconnect(shared_values, AfterDisconnect::Reconnect(0))
                    .await
            }
        }
    }

    async fn handle_tunnel_close_event(
        self,
        block_reason: Option<ErrorStateCause>,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        if let Some(block_reason) = block_reason {
            Self::reset_routes(shared_values).await;
            return NewState(ErrorState::enter(shared_values, block_reason).await);
        }

        log::info!("Tunnel closed. Reconnecting",);
        Self::reset_routes(shared_values).await;
        NewState(ConnectingState::enter(shared_values, ()).await)
    }
}

#[cfg(windows)]
#[allow(dead_code)]
fn is_recoverable_routing_error(error: &crate::routing::Error) -> bool {
    matches!(error, crate::routing::Error::AddRoutesFailed(_))
}

#[async_trait::async_trait]
impl TunnelState for ConnectingState {
    type Bootstrap = ();

    async fn enter(
        shared_values: &mut SharedTunnelStateValues,
        _no_need: (),
    ) -> (TunnelStateWrapper, TunnelStateTransition) {
        match shared_values.tunnel_parameters.clone() {
            None => ErrorState::enter(shared_values, ErrorStateCause::TunnelParameterMissing).await,
            Some(tunnel_parameters) => {
                let connecting_state = Self::start_tunnel(tunnel_parameters).await;
                (
                    TunnelStateWrapper::from(connecting_state),
                    TunnelStateTransition::Connecting,
                )
            }
        }
    }

    async fn handle_event(
        mut self,
        commands: &mut TunnelCommandReceiver,
        shared_values: &mut SharedTunnelStateValues,
    ) -> EventConsequence {
        let result = tokio::select! {
            command = commands.next() => EventResult::Command(command),
            event = self.tunnel_events.next() => EventResult::Event(event),
            result = &mut self.tunnel_close_event => EventResult::Close(result),
        };

        match result {
            EventResult::Command(command) => self.handle_commands(command, shared_values).await,
            EventResult::Event(event) => self.handle_tunnel_events(event, shared_values).await,
            EventResult::Close(result) => {
                if result.is_err() {
                    log::warn!("Tunnel monitor thread has stopped unexpectedly");
                }
                let block_reason = result.unwrap_or(None);
                self.handle_tunnel_close_event(block_reason, shared_values)
                    .await
            }
        }
    }
}
