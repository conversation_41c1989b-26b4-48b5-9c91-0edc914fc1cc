use futures_channel::mpsc;
use futures_util::StreamExt;
use libcore::proxy_request::{ProxyHandle, ProxyHttp, REQUESTS, REQUEST_SEQUENCE};
use serde_json::Value;
use std::sync::atomic::Ordering;
use tracing::{info, trace};
use url::Url;

pub fn upload(proxy_handle: ProxyHandle, env: Value) {
    tokio::spawn(async move {
        info!("Sending environmental information");
        trace!("env: {:?}", env);

        let device_id = uniqueid::device_id().unwrap();

        let inner_request = proxy_request::HttpRequest {
            method: "POST".to_string(),
            url: Url::parse(&format!(
                "https://sso.jingantech.com/identity-sso/v8/web/client/environment/{device_id}/pc"
            ))
            .unwrap(),
            query: None,
            headers: None,
            body: Some(proxy_request::Body::Json(env)),
            timeout: None,
            response_type: Some(proxy_request::ResponseType::Json),
            stream: None,
        };

        // 获取requestID
        let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
        let request = ProxyHttp {
            connection_config: None,
            seq,
            request: serde_json::to_vec(&inner_request).unwrap(),
        };

        let (tx, mut rx) = mpsc::channel(1);
        let (chunk_tx, _chunk_rx) = mpsc::channel(1);

        REQUESTS.insert(seq, (tx, chunk_tx)).await;
        proxy_handle.send_request(request);

        _ = rx.next().await;
    });
}
