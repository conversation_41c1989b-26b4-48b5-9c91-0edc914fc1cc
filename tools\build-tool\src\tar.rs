use clap::Parser;
use std::{
    fs,
    fs::File,
    io::Write,
    path::{Path, PathBuf},
};

#[derive(Debug, Parser)]
pub struct Options {
    /// Source file path.
    #[clap(long)]
    pub src: PathBuf,
    /// Destination file path.
    #[clap(long)]
    pub dest: PathBuf,
}

pub(crate) fn create_tar(opts: Options) -> anyhow::Result<()> {
    if let Some(parent) = opts.dest.parent() {
        fs::create_dir_all(parent)?;
    }
    let dest_file = File::create(opts.dest)?;
    let gzip_encoder = libflate::gzip::Encoder::new(dest_file)?;

    let gzip_encoder = create_tar_from_src(opts.src, gzip_encoder)?;
    let mut dest_file = gzip_encoder.finish().into_result()?;
    dest_file.flush()?;
    Ok(())
}

fn create_tar_from_src<P: AsRef<Path>, W: Write>(src_dir: P, dest_file: W) -> anyhow::Result<W> {
    let src_dir = src_dir.as_ref();
    let mut tar_builder = tar::Builder::new(dest_file);

    // validate source type
    let file_type = fs::metadata(src_dir).expect("Can't read source directory");
    // if it's a file don't need to walkdir
    if file_type.is_file() {
        let mut src_file = fs::File::open(src_dir)?;
        let file_name = src_dir
            .file_name()
            .expect("Can't extract file name from path");

        tar_builder.append_file(file_name, &mut src_file)?;
    } else {
        for entry in walkdir::WalkDir::new(src_dir) {
            let entry = entry?;
            let src_path = entry.path();
            if src_path == src_dir {
                continue;
            }

            // We add the .parent() because example if we send a path
            // /dev/src-tauri/target/debug/bundle/osx/app.app
            // We need a tar with app.app/<...> (source root folder should be included)
            // safe to unwrap: the path has a parent
            let dest_path = src_path.strip_prefix(src_dir.parent().unwrap())?;
            if entry.file_type().is_dir() {
                tar_builder.append_dir(dest_path, src_path)?;
            } else {
                let mut src_file = fs::File::open(src_path)?;
                tar_builder.append_file(dest_path, &mut src_file)?;
            }
        }
    }
    let dest_file = tar_builder.into_inner()?;
    Ok(dest_file)
}
