use crate::types::{proto, FromProtobufTypeError};

impl TryFrom<proto::UpdateResponse> for sdptypes::updater::UpdateResponse {
    type Error = FromProtobufTypeError;

    fn try_from(value: proto::UpdateResponse) -> Result<Self, Self::Error> {
        let payload = value
            .payload
            .map(sdptypes::updater::UpdateResponseInner::try_from)
            .transpose()?;
        Ok(Self {
            should_update: value.should_update,
            payload,
        })
    }
}

impl TryFrom<proto::UpdateResponseInner> for sdptypes::updater::UpdateResponseInner {
    type Error = FromProtobufTypeError;

    fn try_from(value: proto::UpdateResponseInner) -> Result<Self, Self::Error> {
        let version = value
            .version
            .parse()
            .map_err(|_| FromProtobufTypeError::InvalidArgument("Version format error"))?;

        Ok(Self {
            force: value.force,
            version,
            notes: value.notes,
        })
    }
}

impl From<sdptypes::updater::UpdateResponse> for proto::UpdateResponse {
    fn from(value: sdptypes::updater::UpdateResponse) -> Self {
        Self {
            should_update: value.should_update,
            payload: value.payload.map(proto::UpdateResponseInner::from),
        }
    }
}

impl From<sdptypes::updater::UpdateResponseInner> for proto::UpdateResponseInner {
    fn from(value: sdptypes::updater::UpdateResponseInner) -> Self {
        Self {
            force: value.force,
            version: value.version,
            notes: value.notes,
        }
    }
}

impl From<proto::UpdateStatus> for sdptypes::updater::UpdateStatus {
    fn from(value: proto::UpdateStatus) -> Self {
        Self {
            status: value.status,
            msg: value.msg,
        }
    }
}

impl From<proto::DownloadProgress> for sdptypes::updater::DownloadProgress {
    fn from(value: proto::DownloadProgress) -> Self {
        Self {
            chunk_size: value.chunk_size,
            content_length: value.content_length,
        }
    }
}
