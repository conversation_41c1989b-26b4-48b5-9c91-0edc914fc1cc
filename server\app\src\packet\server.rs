use std::sync::Arc;

use base::net::{Addr, IpPacket, Version};
use tracing::{error, info, trace, warn, Instrument};

use crate::{
    comm::{CommCommand, VIRTUAL_IP_MAPPING},
    spa::SPAValidator,
    xdp::{XdpCommand, XdpCommandSender},
    CLIENTS,
};

use crate::nat::Nat;

#[derive(Clone)]
pub struct ServerForwardInitArgs {
    pub spa_port: u16,

    pub spa_validator: Arc<SPAValidator>,
    pub nat: Arc<Nat>,
    pub primary_xdp_sender: XdpCommandSender,

    pub packet_receiver: flume::Receiver<IpPacket>,
}

#[derive(Clone)]
pub struct ServerPacketSender {
    command_tx: flume::Sender<IpPacket>,
}

impl ServerPacketSender {
    pub fn new() -> (Self, flume::Receiver<IpPacket>) {
        let (tx, rx) = flume::unbounded::<IpPacket>();

        (ServerPacketSender { command_tx: tx }, rx)
    }

    pub fn send(&self, packet: IpPacket) {
        if self.command_tx.send(packet).is_err() {
            error!("server-side packet handler thread panicked");
        }
    }
}

pub async fn spawn(args: ServerForwardInitArgs) {
    tokio::spawn(
        async move {
            info!("create a server-side packet handler");
            loop {
                match args.packet_receiver.recv_async().await {
                    Err(_) => break,
                    Ok(packet) => {
                        if packet.next_protocol == 17
                            && is_spa(packet.version, &packet.data, args.spa_port)
                        {
                            tokio::spawn({
                                let spa_validator = args.spa_validator.clone();
                                let primary_xdp_sender = args.primary_xdp_sender.clone();
                                async move {
                                    let src_port = packet
                                        .ports
                                        .map(|(src_port, _)| src_port)
                                        .unwrap_or_default();

                                    let result = spa_validator
                                        .validate(packet.src, src_port, &packet.payload()[8..])
                                        .await;
                                    if result.is_none() {
                                        primary_xdp_sender
                                            .send(XdpCommand::DelSPARecord(packet.src, src_port))
                                            .await;
                                    }
                                }
                            });
                        } else {
                            server_nat(packet, args.nat.clone()).await;
                        }
                    }
                }
            }
        }
        .instrument(tracing::error_span!("ServerPacketProcessor")),
    );
}

/// 判断是否是SPA包
/// TODO IPv6
fn is_spa(_version: Version, buf: &[u8], spa_port: u16) -> bool {
    let ihl = (buf[0] & 0xf) as usize * 4;

    let dst_port = u16::from_be_bytes([buf[ihl + 2], buf[ihl + 3]]);

    dst_port == spa_port
}

/// 设置伪地址
pub async fn set_fake_address(tenant: &str, packet: &mut IpPacket, tracing: bool) {
    use std::net::SocketAddr;

    use crate::traceablitity::WEBAPPS_REAL_IP_MAPPING;

    let mapping = VIRTUAL_IP_MAPPING.read().await;
    if let Some(virtual_servers) = mapping.get(tenant) {
        for (virtual_ip, server) in virtual_servers.iter() {
            if server == &packet.src {
                packet.set_src(*virtual_ip);
                break;
            }
        }
    }
    drop(mapping);

    if tracing {
        if let Some((src_port, _)) = packet.ports {
            let mapping = WEBAPPS_REAL_IP_MAPPING.read().await;
            if let Some(virtual_ip) = mapping.get(&SocketAddr::new(packet.src, src_port)) {
                packet.set_src(*virtual_ip);
            }
        }
    }
}

/// 将服务端返回的消息通过隧道发送到客户端
///
/// 去掉ether包头, 同时添加两个字节的包长度到ip头头部
///
/// - `buf`: 服务端响应的原始数据包
async fn server_nat(mut packet: IpPacket, nat: Arc<Nat>) {
    let (ip, port, mapping) = match packet.next_protocol {
        1 | 58 => {
            let identifier = packet.identifier.unwrap();
            let access_addr = Addr(packet.src, identifier);

            (
                access_addr.0,
                identifier,
                nat.delete_by_access_addr(access_addr, identifier).await,
            )
        }
        6 | 17 => {
            let (src_port, dest_port) = packet.ports.unwrap();
            let access_addr = Addr(packet.src, src_port);
            (
                access_addr.0,
                dest_port,
                nat.get(access_addr, dest_port).await,
            )
        }
        _ => {
            warn!(protocol = packet.next_protocol, "unsupported");
            return;
        }
    };

    let Some(mapping) = mapping else {
        warn!(protocol = packet.next_protocol, "no NAT for {ip}:{port}");
        return;
    };

    let clients = CLIENTS.read().await;
    if let Some(client) = clients.get(&mapping.2) {
        set_fake_address(&client.tenant, &mut packet, mapping.0).await;
        // 设置目标地址
        packet.set_dest(mapping.1 .0);

        // 设置IP校验和
        packet.update_ip_checksum();

        // 设置标识符或目标端口
        packet.set_dest_port(mapping.1 .1);

        // 设置校验和
        packet.update_next_protocol_checksum();

        trace!(peer_addr = %client.peer_addr, "{:?}", &packet.data);
        let command = CommCommand::Packet(packet);
        client.handle.send(command).await;
    } else {
        error!(
            device.id = %mapping.2,
            "client disconnected. {:?}", &packet.data
        );
    }
}
