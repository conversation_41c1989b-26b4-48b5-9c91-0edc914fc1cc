# SDP

## 代码格式化

```shell
# 安装
rustup component add rustfmt --toolchain nightly
# 运行
cargo +nightly fmt
cargo +nightly clippy
```

## 前端格式化

```
VUE: vetur
JS和CSS: Prettier
```

## 网关

**内核要求: 5.11 及以上**

### 环境安装

```shell
# 0. 设置rustup代理
export RUSTUP_DIST_SERVER="https://rsproxy.cn"
export RUSTUP_UPDATE_ROOT="https://rsproxy.cn/rustup"

# 1. 安装rust
curl --proto '=https' --tlsv1.2 -sSf https://rsproxy.cn/rustup-init.sh | sh

# 2. 设置cargo代理
vim $HOME/.cargo/config

# 录入以下内容
[source.crates-io]
replace-with = 'rsproxy-sparse'
[source.rsproxy]
registry = "https://rsproxy.cn/crates.io-index"
[source.rsproxy-sparse]
registry = "sparse+https://rsproxy.cn/index/"
[registries.rsproxy]
index = "https://rsproxy.cn/crates.io-index"
[net]
git-fetch-with-cli = true

# 3. 查看cargo版本
cargo --version

# 4. 安装依赖
# Ubuntu
apt install libssl-dev build-essential pkg-config zlib1g-dev
# openEuler
yum groupinstall 'Development Tools'
yum install openssl-devel

```

#### amd64 架构

```shell
# 5. 安装bpf-linker
 cargo install bpf-linker
```

#### aarch64 架构

> `aarch64`架构中, `ring`库编译时需要先注释掉 `build.rs`中 595-600 行的内容
>
> ```rust
> // let flag = if &target.env != "msvc" {
> //     "-Werror"
> // } else {
> //     "/WX"
> // };
> // let _ = c.flag(flag);
> ```

```shell
# 5. 安装llvm16
wget https://apt.llvm.org/llvm.sh
chmod +x llvm.sh
sudo ./llvm.sh 16 all

# 6. 安装bpf-linker
cargo install --no-default-features bpf-linker
```

---

### 参数设置

内核程序参数设置, 编译前通过环境变量指定.

| 参数名称                       | 说明               | 默认值    |
|----------------------------|------------------|--------|
| SDP_WHITE_LIST             | 白名单队列大小          | 32     |
| SDP_CONCURRENCY_SPA_PASSED | 同一时间通过单包认证的客户端数量 | 65536  |
| SDP_CONNECTED_CLIENTS      | 同时在线人数           | 20000  |
| SDP_CONCURRENCY_ACCESS     | 允许同时访问资源的最大量     | 102400 |

**默认已开启国密 TLCP 协议**

### 编译

```shell
cargo p build-server -r -v
```

查看内核程序的反汇编代码

```shell
cargo p build-server --disasm
```

## **客户端**

### 命令行

参照 `client/cli/config.toml` 修改配置

```shell
sudo cargo run --package oneid-cli -- --config client/cli/config.toml
```

### GUI

#### 环境安装

参考: [https://tauri.app/v1/guides/getting-started/prerequisites](https://tauri.app/v1/guides/getting-started/prerequisites)

##### x86 架构(Ubuntu 18.04)交叉编译 Linux Arm64 客户端

```shell

# 安装rust arm64架构目标
rustup target add aarch64-unknown-linux-gnu

# 安装架构对应的链接器
sudo apt install gcc-aarch64-linux-gnu

# 在 <project-root>/.cargo/config.toml 中添加以下配置
[target.aarch64-unknown-linux-gnu]
linker = "aarch64-linux-gnu-gcc"

# 包管理器中启用对应的架构
sudo dpkg --add-architecture arm64

# 调整包源 /etc/apt/source.list
deb [arch=amd64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ bionic main restricted universe multiverse
deb-src [arch=amd64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ bionic main restricted universe multiverse
deb [arch=amd64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ bionic-updates main restricted universe multiverse
deb-src [arch=amd64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ bionic-updates main restricted universe multiverse
deb [arch=amd64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ bionic-backports main restricted universe multiverse
deb-src [arch=amd64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ bionic-backports main restricted universe multiverse

deb [arch=arm64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ bionic main restricted universe multiverse
deb [arch=arm64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ bionic-updates main restricted universe multiverse
deb [arch=arm64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ bionic-backports main restricted universe multiverse
deb [arch=arm64] https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ bionic-security main restricted universe multiverse

# 更新包信息
sudo apt update

# 安装依赖包
sudo apt install libwebkit2gtk-4.0-dev:arm64 libgtk-3-dev:arm64 libayatana-appindicator3-dev:arm64 libsoup2.4-dev:arm64 gdk-3.0:arm64

# 设置环境变量
export PKG_CONFIG_SYSROOT_DIR=/usr/aarch64-linux-gnu/
export PKG_CONFIG_PATH=/usr/lib/aarch64-linux-gnu/pkgconfig
```

1. 安装 tauri-cli

   ```shell
   cargo install tauri-cli --git https://gitee.com/luoffei/tauri.git --branch 1.x
   ```
2. 安装 node

   **Windows**

   ```powershell
   # 下载nvm
   https://github.com/coreybutler/nvm-windows/releases

   # 安装nodejs
   nvm install 16
   nvm use 16 # 版本号安装完后会输出
   ```

   将 `env/nsis/NSIS.zip` 解压至 `C:\Users\<USER>\AppData\Local\tauri`

   **Linux|MacOS**

   ```shell
   # 安装nvm
   ./env/nvm/install.sh

   # 安装nodejs
   nvm install 16
   nvm use 16 # 版本号安装完后会输出
   ```
3. 设置 npm 代理

   ```shell
   npm config set registry https://registry.npmmirror.com
   ```
4. 安装 vite

   ```shell
   # 安装vite
   npm install -g vite@4
   ```
5. 安装 protoc

   **Windows**

   解压 `env/protoc/protoc-27.1-win64.zip`, 然后将里面的 `bin` 目录加入到环境变量中

   **Linux**

   解压 `env/protoc/protoc-29.2-linux-x86_64.zip`, 然后将里面的 `bin` 目录加入到环境变量中

   **MacOS**

   ```shell
   brew install protobuf
   ```

   其他架构或系统, 点击[protoc](https://github.com/protocolbuffers/protobuf/releases/latest)下载

#### 拉取守护进程代码

```shell
git submodule init
git submodule update
```

#### 安装前端依赖包

##### vite-plugin-conditional-compile

```shell
git clone https://gitee.com/luoffei/vite-plugin-conditional-compile.git
cd vite-plugin-conditional-compile
npm i
npm run build
npm link
```

##### axios-tauri-adapter

```shell
git clone -b master https://git.jingantech.net/pub/axios-tauri-adapter.git
cd axios-tauri-adapter
npm i
npm link
```

##### element-plus

预编译或源码安装选其一即可

###### 预编译

解压 `env/element-plus/element-plus.zip `, 然后在 element-plus 目录下执行 `npm i && npm link`

###### 源码编译

```shell
git clone -b 2.4.4 https://git.jingantech.net/pub/element-plus.git
cd element-plus
# 安装pnpm
npm install -g pnpm@8

# 设置以下环境变量
# 安装过程若碰到 Failed to set up Chromium r1036745! Set "PUPPETEER_SKIP_DOWNLOAD" env variable to skip download.
# powershell
$env:PUPPETEER_SKIP_DOWNLOAD="true"
# bash
export PUPPETEER_SKIP_DOWNLOAD=true
# 安装依赖
pnpm i

# 编译element-plus
pnpm build
# 编译element-plus遇到内存溢出时, 通过以下命令设置node内存大小
# powershell
$env:NODE_OPTIONS="--max_old_space_size=8192"
# bash
export NODE_OPTIONS="--max_old_space_size=8192"
# 链接
cd dist/element-plus
npm link
```

##### 链接 vite-plugin-conditional-compile, axios-tauri-adapter 和 element-plus

```
cd client/ui
npm install

npm link vite-plugin-conditional-compile axios-tauri-adapter element-plus
# 若执行上述语句报错, 则先执行 npm update, 之后再次执行上述语句
```

#### 编译或直接运行

```shell
# 在根目录执行下面语句, 查看具体编译选项
cargo p build-client -h

# 去掉溯源功能
cargo p build-client --traceability false

# 启用DNS服务
cargo p build-client --dns-server true

# 直接运行
cargo p build-client --run -v

# 输出Linux Arm64客户端
cargo p build-client --target aarch64-unknown-linux-gnu
```

编译完成后输出路径为 `build/<target>/<release/debug>`

#### 生产包输出调试日志

使用 release 编译后的客户端默认日志级别为 `Info`, 需要线上调试时, 可通过环境变量动态设置日志级别.

`ONEID_LOG_LEVEL`: 设置客户端日志输出级别. `ERROR`, `WARN`, `INFO`, `DEBUG`, `TRACE`

`ONEID_LOG`: 设置核心程序日志输出级别(_依赖于上面的设置_). 默认值: `error,oneidcore=info`

**在 MacOS 中, GUI 程序的环境变量可通过 `launchctl setenv <NAME> <VALUE>`进行设置.**

设置后, 重启客户端即可.

#### 调试时忽略文件其他文件更改导致客户端重新编译

设置环境变量

```shell
# powershell
$env:TAURI_DEV_WATCHER_IGNORE_FILE=".taurignore"

# bash
export TAURI_DEV_WATCHER_IGNORE_FILE=".taurignore"
```

然后在文件 `client/src-tauri/.taurignore` 中添加需要忽略的文件和目录

---

## SPA 包格式

```rust
/// 0               |   1           |       2       |           3
/// 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+ ---┐
/// |                           Timestamp                           |    │
/// +                                                               +    │
/// |-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-|    │
/// |                                                               |    │
/// +                        Client Device ID                       +    │
/// |                                                               |    │
/// +                                                               +    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+    │
/// |                             Nonce                             |    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+  44 bytes
/// +                        Client Public IP                       +    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+    │
/// |                             HOTP                              |    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+    │
/// |                            Counter                            |    │
/// +                                                               +    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+ ---┤
/// |     Type      |         Tenant|Username (Optional)            |max 99 bytes
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+ ---┘

/// SPA最小长度
/// 原始SPA最小长度 45 = 8(时间戳) + 16(设备ID) + 4(随机数) + 4(客户端IP) + 4(密码) +
/// 8(计数器) + 1(SPA类型)

/// SPA最大长度
/// 原始SPA最大长度 144 = 8(时间戳) + 16(设备ID) + 4(随机数) + 4(客户端IP) + 4(密码) +
/// 8(计数器) + 1(SPA类型) 加上最大的`单位|用户名`字段 144 = 45 + 99

/// 单包认证类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[repr(u8)]
pub enum SPAType {
    /// 认证
    Auth = 0,
    /// 修改密码
    ChangePwd = 1,
    /// 代理访问
    Proxy = 2,
}
```

加密后

```
/// 0               |   1           |       2       |           3
/// 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+ ---┐
/// |                                                               |    │
/// +                        Encrypted Data                         +    │
/// ..                                                             ..  142 [ + 99 ] bytes
/// +                                                               +    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+ ---┤
/// |                                                               |    │
/// +                          Signature                            +  32 bytes
/// ..                                                             ..    │
/// |                                                               |    │
/// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+ ---┘
```

签名算法: SM3 Signature = SM3Hash(spa_packet)

加密算法: SM2 Encrypted Data = SM2Enc(spa_packet)

## 网关 ARP 缓存

7.2.3 之前的版本中, SDP 网关转发的流量中, 目标 mac 地址统一配置的都是网关的 mac 地址. 在某些情况下(待测), 流量转发不成功.

目前 ARP 设计中, 最大缓存容量为 32MB. 过期时间为 8 小时.

独立 IP 资源, 在 SDP 网关启动后, 会异步发送 ARP 请求获取其 mac 地址.

IP 段资源在第一次访问时, 会异步去查询其 mac 地址, 在查询为完成期间, 都会将流量的目标 mac 地址配置为网关的 mac 地址.

> 8.1.0 中, 支持了网卡直连(网卡没有网关地址)模式, 第一次访问时, 会先去查询目标 IP 的 Mac 地址.

## 控制器代理访问客户端接口

参考 [代理客户端接口访问消息定义](./MESSAGE.md#代理客户端接口访问消息定义)

## 会话流量统计

定时将数据推送至 MQ.

消息格式参考[会话流量统计](./MQ消息格式.md#会话流量统计)和[会话访问资源流量统计](./MQ消息格式.md#会话访问资源流量统计)

## 单机部署方案

### 1. 两个物理网卡

两个网卡同时连接到网络中, 一个用于 SDP 网关, 一个用于后台管理服务.

#### 优势

- 可直接访问后台管理服务
  > 内网用户可以不通过 SDP 网关, 即可访问后台管理服务, 同时也可将后台管理服务发布到公网进行访问.
  >

#### 劣势

- 需要修改网关配置
  > 由于单机双网卡之间网络不互通, 访问 SDP 代理后台管理服务时, 流量需要通过网关进行转发, 所以需要**网关进行 NAT 映射**.
  >

#### 示例

网卡 eno1, IP 地址为: *************

网卡 eno2, IP 地址为: *************

SDP 网关配置网卡为 eno1, 后台管理服务绑定到 eno2.
在网关上配置一个固定 NAT, 源 IP 为 *************, 目标 IP 为 *************, 转换后地址为网关地址 ***********.

若要通过 SDP 网关代理后台管理服务, 除了在后台管理服务中设置后台管理服务本身的 IP 地址外,
还需要在 SDP 网关配置 `multi_nic_addresses`
中将后台管理服务的 IP(*************)地址填入.

### 2. 一个物理网卡, 一个虚拟网卡

一个网卡, 用于 SDP 网关, 一个网卡供后台管理服务使用.

#### 流程

1. 创建虚拟网卡
2. 部署后台管理服务
3. 配置 iptables 端口转发规则
4. 初始化后台服务
5. 清理 iptables 端口转发规则
6. 启动 SDP 网关

#### 使用外部创建的虚拟网卡

```shell
# 创建虚拟网卡
tunctl -n backend -i ********

# 创建好后使用 ip a 即可看到创建的虚拟网卡

6: backend: <NO-CARRIER,POINTOPOINT,MULTICAST,NOARP,UP> mtu 1500 qdisc pfifo_fast state DOWN group default qlen 500
    link/none
    inet ********/24 scope global backend
       valid_lft forever preferred_lft forever
    inet6 fe80::3712:3a1b:25c1:c6e5/64 scope link stable-privacy
       valid_lft forever preferred_lft forever


# 配置端口转发  ************* 为物理网卡地址
iptables -t nat -A PREROUTING -d ************* -p tcp --dport 11081 -j DNAT --to-destination ********:11081
iptables -t nat -A POSTROUTING -d ******** -p tcp --dport 11081 -j SNAT --to *************

#清空nat表的所有链
iptables -t nat -F PREROUTING
```

#### 优势

- 硬件要求低

  > 只需要一个网卡即可实现单机部署
  >
- 代理后台访问速度更快

  > 服务器内部流量转发, 不必经过网关
  >

#### 劣势

- 若要初始化后台服务, 需要停止网关服务, 且需要配置 iptables 端口转发

#### 示例

网卡 eno1, IP 地址为 *************

虚拟网卡 backend, IP 地址为 ********

SDP 网关配置网卡为 eno1, 后台管理服务绑定到虚拟网卡 backend.

在后台管理服务中设置后台管理服务本身的 IP 地址为 ********.

## 获取或设置网关运行时状态

通过附带的 `server-cli`程序可以动态调整或查看服务的一些状态.

如动态设置日志级别:

```shell
server-cli set-log-filter --filter error,server=info
```

更多指令通过 `server-cli --help` 进行查看
