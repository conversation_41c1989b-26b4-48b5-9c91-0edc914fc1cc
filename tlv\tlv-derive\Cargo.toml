[package]
description = "TLV消息"
edition = "2021"
name = "tlv-derive"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
proc-macro2 = "1.0.67"
quote = "1.0.33"
syn = { version = "2.0.37", features = ["extra-traits"] }

[lib]
proc-macro = true

[dev-dependencies]
ipnet = "2.8.0"
proxy-request = { path = "../../proxy-request" }
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0.107"
tlv = { path = "../tlv" }
untrusted = "0.9.0"
