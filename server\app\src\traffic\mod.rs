use std::{
    net::IpAddr,
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
    time::Duration,
};

use moka::future::Cache;
use once_cell::sync::Lazy;
use tlv::Tag;
use tokio::sync::mpsc;

/// 会话流量统计
pub static SESSION_TRAFFIC: Lazy<Cache<String, (Arc<AtomicU64>, Arc<AtomicU64>)>> =
    Lazy::new(|| {
        Cache::builder()
            .time_to_idle(Duration::from_secs(30 * 60))
            .build()
    });

/// 会话访问资源流量统计
pub static SESSION_RESOURCE_TRAFFIC: Lazy<Cache<AccessKey, (Arc<AtomicU64>, Arc<AtomicU64>)>> =
    Lazy::new(|| {
        Cache::builder()
            .time_to_idle(Duration::from_secs(30 * 60))
            .build()
    });

#[derive(Debug, PartialEq, Eq, Hash)]
pub struct AccessKey {
    pub session_id: String,
    pub protocol: u8,
    pub ip: IpAddr,
    pub port: Option<u16>,
}

/// 定时推送统计信息
pub fn spawn(
    interval: u64,
    session_traffic_tx: mpsc::Sender<Vec<u8>>,
    session_resource_traffic_tx: mpsc::Sender<Vec<u8>>,
) {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_millis(interval));

        loop {
            _ = interval.tick().await;
            let timestamp = coarsetime::Clock::now_since_epoch().as_millis();

            SESSION_TRAFFIC.run_pending_tasks().await;
            let mut session_traffic =
                Vec::with_capacity(SESSION_TRAFFIC.weighted_size() as usize * 64);

            SESSION_TRAFFIC.iter().for_each(|(session_id, (up, down))| {
                // 会话
                session_traffic.push(Tag::UTF8String as u8);
                session_traffic.push(32);
                session_traffic.extend_from_slice(session_id.as_bytes());

                // 时间
                session_traffic.push(Tag::Uint64 as u8);
                session_traffic.push(8);
                session_traffic.extend(timestamp.to_be_bytes());

                // 上行流量
                session_traffic.push(Tag::Uint64 as u8);
                session_traffic.push(8);
                session_traffic.extend(up.load(Ordering::Relaxed).to_be_bytes());

                // 下行流量
                session_traffic.push(Tag::Uint64 as u8);
                session_traffic.push(8);
                session_traffic.extend(down.load(Ordering::Relaxed).to_be_bytes());
            });

            _ = session_traffic_tx.send(session_traffic).await;

            SESSION_RESOURCE_TRAFFIC.run_pending_tasks().await;
            let mut session_traffic =
                Vec::with_capacity(SESSION_RESOURCE_TRAFFIC.weighted_size() as usize * 89);
            SESSION_RESOURCE_TRAFFIC
                .iter()
                .for_each(|(key, (up, down))| {
                    // 会话
                    session_traffic.push(Tag::UTF8String as u8);
                    session_traffic.push(32);
                    session_traffic.extend_from_slice(key.session_id.as_bytes());

                    // 时间
                    session_traffic.push(Tag::Uint64 as u8);
                    session_traffic.push(8);
                    session_traffic.extend(timestamp.to_be_bytes());

                    // 目标地址
                    session_traffic.push(if key.ip.is_ipv4() {
                        Tag::Ipv4 as u8
                    } else {
                        Tag::Ipv6 as u8
                    });
                    session_traffic.push(if key.ip.is_ipv4() { 4 } else { 16 });
                    session_traffic.extend(base::utils::net::ip_to_bytes(key.ip));

                    // 目标端口
                    session_traffic.push(Tag::Uint16 as u8);
                    if let Some(port) = key.port {
                        session_traffic.push(2);
                        session_traffic.extend(port.to_be_bytes());
                    } else {
                        session_traffic.push(0);
                    }

                    // 协议
                    session_traffic.push(Tag::Uint8 as u8);
                    session_traffic.push(1);
                    session_traffic.push(key.protocol);

                    // 上行流量
                    session_traffic.push(Tag::Uint64 as u8);
                    session_traffic.push(8);
                    session_traffic.extend(up.load(Ordering::Relaxed).to_be_bytes());

                    // 下行流量
                    session_traffic.push(Tag::Uint64 as u8);
                    session_traffic.push(8);
                    session_traffic.extend(down.load(Ordering::Relaxed).to_be_bytes());
                });

            _ = session_resource_traffic_tx.send(session_traffic).await;
        }
    });
}
