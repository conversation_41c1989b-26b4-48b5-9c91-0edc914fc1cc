<template>
  <div :class="type ? 'mfa-contentLogin' : 'mfa-content'" >
    <div v-if="qrValue" class="qrcode-modal" :style="!type && 'margin: 20px;'">
      <qrcode-vue :value="qrValue" :size="size"></qrcode-vue>
    </div>
    <div v-else class="qrcode-loading">正在加载中...</div>
    <slot />
    <div v-show="inshow" :class="type ? 'CambiumLogin' : 'Cambium'" @click="refresh">
      <el-icon>
        <RefreshRight />
      </el-icon>
      <p v-show="iswitch">二维码已过期</p>
    </div>
  </div>
</template>

<script>
import QrcodeVue from "qrcode.vue";
import { requestQrcode, pollingMfa, cancelAuth } from "@/api/service";

export default {
  name: "QRCODE",
  components: {
    // QRCodeVue3
    QrcodeVue,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },

    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      polling: undefined,
      requestId: "",
      qrValue: "",
      inshow: false,
      iswitch: false,
      isactive: true,
      size: 160,
    };
  },
  created() {
    this.qrValue = "";
    this.requestId = "";
    this.timeOut();
  },
  watch: {
    participantGroupId: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.getRequestId();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取requestId
    getRequestId() {
      let data = {
        participantGroupId: this.participantGroupId,
        participantTypes: this.type === "login" ? "" : "01",
        participantKeyword: this.userId,
      };
      requestQrcode(data)
        .then((result) => {
          this.requestId = result.data.requestId;
          this.$emit("requestId", this.requestId);
          this.qrValue = result.data.qrcode;
          this.startPolling();
        })
        .catch((error) => {
          this.$emit("generateRequestIdFailCallbackFunc", error);
        });
    },
    // 轮询验证结果
    startPolling() {
      if (!this.requestId) {
        this.stopPolling();
        return;
      }
      pollingMfa(this.requestId)
        .then((result) => {
          console.log("result: ", result);
          if (result.data === "DOING" && this.isactive) {
            let t = this;
            window.setTimeout(t.startPolling, 1000);
            return;
          }
          switch (result.data) {
            case "FAILED":
              this.alert.error(result.data.errorMsg || "系统错误");
              break;
            case "SUCCESS":
              this.Complete();
              break;
          }
          this.inshow = true;
          this.stopPolling();
          this.requestId = "";
        })
        .catch(() => {
          this.$emit("childEventCancel", this.requestId);
          // this.cancel()
          // this.$emit('cancelMfaCallbackFunc')
        });
    },
    // 停止轮询
    stopPolling() {
      this.isactive = false;
    },
    Complete() {
      this.stopPolling();
      this.$emit("mfaCallbackFunc", {
        requestId: this.requestId,
        type: "qrcode",
      });
    },
    cancel() {
      this.stopPolling();
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
    },
    refresh() {
      this.inshow = false;
      this.isactive = true;
      this.iswitch = false;
      this.getRequestId();
      this.timeOut();
    },
    timeOut() {
      setTimeout(() => {
        this.inshow = true;
        this.iswitch = true;
        this.stopPolling();
      }, 300000);
    },
    // 网络异常或断网时, 直接提示二维码异常
    close() {
      this.stopPolling();
      this.inshow = true;
      this.iswitch = true;
    },
  },
};
</script>

<style scoped lang="less">
.mfa-contentLogin {
  // height: 40vh;
  position: relative;
  width: 40%;
  padding: 20px 12px;
  background: #fff;
  text-align: center;
  box-shadow: 2px 2px 12px 2px rgba(42, 41, 41, 0.5);
  border-radius: 15px;
}

.CambiumLogin {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  cursor: pointer;
}

.Cambium {
  position: absolute;
  top: -28px;
  left: 25%;
  width: 50%;
  height: 215px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  cursor: pointer;
}

.el-icon {
  font-size: 40px;
}

.qrcode-loading{
  width: 180px;
  height: 164px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
