import request from "@/tools/request";

const CTX = "/identity-sso/v8";
const CTX_NGIAM = "/ngiam-sso";
const CTX_MFA = "/mfa/v8";
const CTX_SDP = "/access-sdp-sso/v8";
const CTX_APP = "/access-app-sso/v8";
const BASE_URL = "https://sso.jingantech.com";
const RESOURCE_ADMIN = "/resource-admin/v8";

/**
 * 获取租户列表
 *
 * @returns {*}
 */
export function tenants() {
  return request({
    url: BASE_URL + CTX + "/web/tenant/list",
    method: "get",
  });
}

/**
 * 联动登录接口
 *
 * return {
 *     tenant: <租户CODE>,
 *     username: <用户名>,
 *     type: <登录浏览器>,
 *     ticket: <联动登录票据>
 * }
 */
export function getLinkedLoginToken() {
  return request({
    url: "https://localhost:50021/login-ticket",
    method: "get",
    notProxy: true,
  });
}

/**
 * 获取控制器信息
 *
 * @returns {host: xxx, port: xx, spaPort: xx}
 */
export function getCtlInfo() {
  return request({
    url: BASE_URL + CTX_SDP + "/web/resource/ctrl/configV2",
    method: "get",
  });
}

/**
 * 登录成功调取
 *
 * @returns {host: xxx, port: xx, spaPort: xx}
 */
export function getLogin() {
  return request({
    url: BASE_URL + CTX + "/web/system/version/current",
    method: "get",
    ignoreErrors: true,
  });
}

/**
 * 登录iam
 * @param ticket
 * @returns {Promise<AxiosResponse<any>>}
 */
export function login(ticket) {
  return request({
    url: BASE_URL + CTX + "/web/login/" + ticket + "/ticketAuthIamPc",
    method: "get",
  });
}

/**
 * 获取应用列表
 * @param key
 * @param page
 * @param size
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getApps(key, page, size) {
  return request({
    url:
      BASE_URL +
      CTX_NGIAM +
      "/app/sso/appList?keyword=" +
      encodeURIComponent(key) +
      "&page=" +
      page +
      "&size=" +
      size,
    method: "get",
  });
}

/**
 * 获取应用账号列表
 * @param appId
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getAccounts(appId) {
  return request({
    url: BASE_URL + CTX_APP + "/web/login/sso/accountList/" + appId,
    method: "get",
  });
}

/**
 * 查询mfa信息
 * @param url
 * @param data
 * @returns {*|Promise|Promise<unknown>}
 */
export function strategiesCheckInfo(clientApiKey, accountName) {
  return request({
    url: BASE_URL + CTX_APP + "/web/app/strategies/client/check",
    params: { clientApiKey: clientApiKey, accountName: accountName },
    method: "get",
  });
}

/**
 * mfa 获取requestId
 * @param {Object} data
 * @param {string} data.participantGroupId 参与者组ID
 * @param {string} data.participantTypes 参与者类型
 * @param {string} data.participantKeyword 参与者关键字（userId）
 * @param {string} data.method 多因子认证类型
 * @returns
 */
export function getRequestId(
  participantGroupId,
  participantTypes,
  participantKeyword,
  method
) {
  let data = {
    participantGroupId: participantGroupId,
    participantTypes: participantTypes,
    participantKeyword: participantKeyword,
    method: method,
  };
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/request/generate",
    params: data,
    method: "get",
  });
}

/**
 * mfa 验证code
 * @param {Object} data
 * @param {string} data.requestId 请求id
 * @param {string} data.authCode 令牌口令
 * @returns
 */
export function verifyCode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/otp/verify",
    params: data,
    method: "get",
  });
}

/**
 * mfa 验证opt code
 * @param {Object} data
 * @param {string} data.requestId 请求id
 * @param {string} data.authCode 令牌口令
 * @param {string} data.method 认证方式
 * @returns
 */
export function verifyOtpsCode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/otps/verify",
    params: data,
    method: "get",
  });
}

/**
 * mfa 验证邮箱code
 * @param {Object} data
 * @param {string} data.requestId 请求id
 * @param {string} data.code 认证方式
 * @returns
 */
export function verifyEmailCode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/email/verify",
    params: data,
    method: "get",
  });
}

/**
 * mfa 验证短信code
 * @param {Object} data
 * @param {string} data.requestId 请求id
 * @param {string} data.code 认证方式
 * @returns
 */
export function verifySMSCode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/sms/verify",
    params: data,
    method: "get",
  });
}

/**
 * 获取认证结果信息
 * @param {String} authResultId 认证id
 * @returns
 */
export function clientAuthResult(authResultId) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/authresult",
    params: { authResultId: authResultId },
    method: "get",
  });
}

/**
 * 通知服务器：请求已取消
 * @param {String} requestId 请求id
 * @returns
 */
export function cancelAuth(requestId) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/request/cancel",
    params: { requestId: requestId },
    method: "get",
    ignoreErrors: true,
  });
}

/**
 * 获取邮箱验证码
 * @param {Object} data
 * @param {string} data.participantGroupId 参与者组ID
 * @param {string} data.participantTypes 参与者类型
 * @param {string} data.participantKeyword 参与者关键字（userId）
 * @returns
 */
export function getEmailCode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/email/code",
    params: data,
    method: "get",
  });
}

/**
 * 获取短信验证码
 * @param {Object} data
 * @param {string} data.participantGroupId 参与者组ID
 * @param {string} data.participantTypes 参与者类型
 * @param {string} data.participantKeyword 参与者关键字（userId）
 * @returns
 */
export function getSMSCode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/sms/code",
    params: data,
    method: "get",
  });
}

/**
 * 轮询mfa
 * @param {String} requestId
 * @returns
 */
export function pollingMfa(requestId) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/q",
    params: { requestId: requestId },
    method: "get",
  });
}

/**
 * 请求二维码认证
 * @param {Object} data
 * @param {string} data.participantGroupId 参与者组ID
 * @param {string} data.participantTypes 参与者类型
 * @param {string} data.participantKeyword 参与者关键字（userId）
 * @returns
 */
export function requestQrcode(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/qrcode",
    params: data,
    method: "get",
  });
}

// /**
//  * 请求上海安术A5认证
//  * @param {Object} data
//  * @param {string} data.participantGroupId 参与者组ID
//  * @param {string} data.participantTypes 参与者类型
//  * @param {string} data.participantKeyword 参与者关键字（userId）
//  * @returns
//  */
// export function getgenerate(data) {
//     return request({
//         url: URL + CTX_MFA + 'v1/sdk/mfa/client/request/generate',
//         params: data,
//         method: 'get'
//     })
// }

/**
 * 访问资源时获取认证策略
 * @param {Object} data
 * @param {string} data.username 用户名
 * @param {string} data.deviceId 设备ID
 * @param {string} data.target 访问目标资源(非必填)
 * @returns
 */
export function sdpResourceStrategy(data) {
  return request({
    url: BASE_URL + CTX_SDP + "/web/client/resource/strategy",
    params: data,
    method: "get",
  });
}

/**
 *
 * @param {string} appId 应用id
 * @param {string} accountId 账号id
 * @param {string} authResultId 认证id
 * @returns
 */
export function callonApp(appId, accountId, authResultId) {
  return request({
    url:
      BASE_URL +
      CTX_APP +
      "/v1/callonApps/" +
      appId +
      "/" +
      accountId +
      "?authResultId=" +
      authResultId,
    method: "post",
  });
}

// /common-admin/web/auth/strategy查询登录可以使用的认证策略
export function authStrategy() {
  return request({
    url: BASE_URL + CTX + "/web/auth/strategy",
    method: "get",
  });
}

// /web/mfa/client/request/user?requestId="4654"获取用户名
export function mfaRequestId(requestId) {
  return request({
    url:
      BASE_URL +
      CTX_MFA +
      "/web/mfa/client/request/user?requestId=" +
      requestId,
    method: "get",
  });
}

// /web/mfa/client/password/verify登录接口
export function passwordVerify(data) {
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/password/verify",
    // params: { username: username, password: password },
    params: data,
    method: "get",
  });
}

// /sdp/v1/sdk/server/pageResourceByUser分页查询用户下的资源
export function pageResourceByUser(keyword, page, size) {
  return request({
    url:
      BASE_URL +
      CTX_SDP +
      "/micro/client/pageResourceByUser?page=" +
      page +
      "&size=" +
      size,
    method: "post",
    data: {
      keyword: keyword,
    },
  });
}

// /api/list_cert 获取ukey证书
export function ukeycard(data) {
  return request({
    url: "https://localhost:50021/api/list_cert?ukey_type=" + data,
    method: "get",
    notProxy: true,
  });
}

// /api/verify ukey证书认证
export function ukeyauthentication(data) {
  return request({
    url: "https://localhost:50021/api/verify",
    data: data,
    method: "post",
    notProxy: true,
  });
}

/**
 * mfa 获取随机数
 * @param {Object} data
 * @param {string} data.participantGroupId 参与者组ID
 * @param {string} data.participantTypes 参与者类型
 * @param {string} data.participantKeyword 参与者关键字（userId）
 * @param {string} data.method 多因子认证类型
 * @returns
 */
export function getrandom(
  participantGroupId,
  participantTypes,
  participantKeyword,
  method
) {
  let data = {
    participantGroupId: participantGroupId,
    participantTypes: participantTypes,
    participantKeyword: participantKeyword,
    method: method,
  };
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/random",
    params: data,
    method: "get",
  });
}

/**
 * mfa 验证ukey
 */
export function ukeyverify(requestId, signature, cert) {
  let data = {
    requestId: requestId,
    signature: signature,
    cert: cert,
  };
  return request({
    url: BASE_URL + CTX_MFA + "/web/mfa/client/ukey/verify",
    params: data,
    method: "get",
  });
}

// /v1/sdk/pwdPolicy/tip/user?username获取修改密码规则
export function pwdPolicy(data) {
  return request({
    url: BASE_URL + CTX + "/web/client/tip/userId/web",
    data: data,
    method: "post",
  });
}

// common-sso/v1/sdk/client/userSecretKey/web 无策略拉取绑定码
export function userSecretKeyResultId(data) {
  return request({
    url: BASE_URL + CTX + "/web/client/userSecretKey/web",
    data: data,
    method: "post",
  });
}

// common-sso/v1/sdk/client/userSecretKey/authResultId/web 有策略拉取绑定码
export function userSecretKeyauthResultId(data) {
  return request({
    url: BASE_URL + CTX + "/web/client/userSecretKey/authResultId/web",
    data: data,
    method: "post",
  });
}

// /v1/sdk/my/profile 获取用户信息
export function userinfo() {
  return request({
    url: BASE_URL + CTX + "/web/my/profile",
    method: "get",
  });
}

// /v1/sdk/quick_access/getAppSeriesList获取应用组
export function getAppSeriesList() {
  return request({
    url: BASE_URL + CTX_SDP + "/micro/client/resourceGroup/list",
    method: "post",
  });
}

// app/sso/appListBySeries?keyword=' + encodeURIComponent(key) + '&seriesId=' + seriesId + '&page=' + page + '&size=' + size)
export function appListBySeries(keyword, seriesId, page, size) {
  return request({
    url:
      BASE_URL +
      CTX_SDP +
      `/micro/client/sso/resourceListByResourceGroupId?keyword=${keyword}&resourceGroupId=${seriesId}&page=${page}&size=${size}`,
    method: "get",
  });
}

export function stream(url) {
  return request({
    url: url,
    method: "get",
    responseType: "blob",
  });
}

// common-sso/v1/sdk/client/environment/'+ device_id +'/pc' 监听环境变量日志
export function sendEnvironment(data) {
  return request({
    url: BASE_URL + CTX + "/web/client/environment/" + window.DEVICE_ID + "/pc",
    data: data,
    method: "post",
    ignoreErrors: true,
  });
}

// /v1/sdk/login/getLinkedTicket获取票据
export function getLinkedTicket() {
  return request({
    url: BASE_URL + CTX + "/web/sso/login/getLinkedTicket",
    method: "get",
  });
}

// 未登录获取系统密码策略
export function getPolicy() {
  return request({
    url: BASE_URL + CTX + "/web/pwdPolicy/policy",
    method: "get",
  });
}

// 忘记密码发送验证码
export function sendSms(data) {
  return request({
    url: BASE_URL + CTX + "/web/sso/login/verifyCode/send",
    data: data,
    method: "post",
  });
}

// 更新密码
export function updatePassword(data) {
  return request({
    url: BASE_URL + CTX + "/web/sso/login/pwd/update",
    data: data,
    method: "post",
  });
}

/**
 * 获取用户协议内容
 *
 * @returns {*} 用户协议HTML内容
 */
export function getUserAgreement() {
  return request({
    url: BASE_URL + "/user-agreement.html",
    method: "get",
    responseType: "text",
  });
}

/**
 * 面部识别完成, 记录日志
 */
export function facialCompleteAudit(requestId, message) {
  return request({
    url: BASE_URL + CTX_MFA + `/web/mfa/client/${requestId}/audit`,
    method: "post",
    data: { errorMessage: message },
  });
}

// 调整应用列表接口
export function allResource(keyword) {
  return request({
    url:
      BASE_URL + CTX_SDP + `/micro/client/all/sso/resource?keyword=${keyword}`,
    method: "POST",
  });
}

// 调整资源组接口
export function appGroupId(keyword, seriesId, page, size) {
  return request({
    url:
      BASE_URL +
      CTX_SDP +
      `/micro/client/sso/resource?keyword=${keyword}&resourceGroupId=${seriesId}&page=${page}&size=${size}`,
    method: "post",
  });
}

// 获取轮播图
export function getSlideshow() {
  return request({
    url: BASE_URL + CTX_APP + `/web/resource/preference`,
    method: "get",
  });
}

// 管理平台跳转
export function getServiceUrl(secret) {
  return request({
    url: BASE_URL + CTX + `/web/system/get/services/url/${secret}`,
    method: "get",
  });
}

// 获取token
export function getToken() {
  return request({
    url: BASE_URL + CTX + `/v1/sdk/admin/login/getToken`,
    method: "get",
  });
}

// 解析url地址
export function setUrlParam(paramName, paramValue, url) {
  if (!url) {
    url = window.location.href;
  }
  if (!paramName) {
    return url;
  }
  var str = "";
  if (url.indexOf("?") != -1) str = url.substr(url.indexOf("?") + 1);
  else return url + "?" + paramName + "=" + paramValue;
  var returnurl = "";
  var setparam = "";
  var arr;
  var modify = "0";
  if (str.indexOf("&") != -1) {
    arr = str.split("&");
    $.each(arr, function (i, e) {
      if (arr[i].split("=")[0] == paramName) {
        setparam = paramValue;
        modify = "1";
      } else {
        setparam = arr[i].split("=")[1];
      }
      returnurl = returnurl + arr[i].split("=")[0] + "=" + setparam + "&";
    });
    returnurl = returnurl.substr(0, returnurl.length - 1);
    if (modify == "0")
      if (returnurl == str)
        returnurl = returnurl + "&" + paramName + "=" + paramValue;
  } else {
    if (str.indexOf("=") != -1) {
      arr = str.split("=");
      if (arr[0] == paramName) {
        setparam = paramValue;
        modify = "1";
      } else {
        setparam = arr[1];
      }
      returnurl = arr[0] + "=" + setparam;
      if (modify == "0")
        if (returnurl == str)
          returnurl = returnurl + "&" + paramName + "=" + paramValue;
    } else returnurl = paramName + "=" + paramValue;
  }
  return url.substr(0, url.indexOf("?")) + "?" + returnurl;
}

//  动态加密
export function getPublicKey() {
  return request({
    url: BASE_URL + CTX_APP + `/setup/getPublicKey`,
    method: "get",
  });
}

// 认证失败错误日志/v1/sdk/auth/%7BrequestId%7D/mfa
export function msgrequestId(requestId) {
  return request({
    url: BASE_URL + CTX + "/v1/sdk/auth/" + requestId + "/mfa",
    method: "get",
  });
}
