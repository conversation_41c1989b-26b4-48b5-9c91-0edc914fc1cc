use std::{net::SocketAddr, sync::atomic::Ordering};

use super::tls;
use crate::traceability::{
    get_http_scheme, handler::MessageHandler, inner::socket::bind_tcp_socket, Proxy, ProxyError,
    FORWARD_PROXY_TARGETS, TRACING_LISTEN_PORT,
};
use http::Request;
use hyper::{
    body::Incoming,
    rt::{Read, Write},
    service::service_fn,
};
use hyper_util::{
    rt::{TokioExecutor, TokioIo},
    server::conn,
};
use log::{error, info};
use reqwest::Body;

impl Proxy {
    /// Wrapper function to handle request
    async fn serve(
        handler: MessageH<PERSON><PERSON>,
        req: Request<Incoming>,
        scheme: &'static str,
        origin: SocketAddr,
        client_addr: SocketAddr,
        _listen_addr: SocketAddr,
    ) -> Result<hyper::Response<Body>, ProxyError> {
        handler
            .handle_request(req, client_addr, scheme, origin)
            .await
    }

    /// Serves requests from clients
    pub(super) async fn client_serve<I>(
        self,
        stream: I,
        conn_builder: conn::auto::Builder<TokioExecutor>,
        scheme: &'static str,
        peer_addr: SocketAddr,
        origin: SocketAddr,
    ) where
        I: Read + Write + Send + Unpin + 'static,
    {
        tokio::spawn(async move {
            let fut = conn_builder.serve_connection_with_upgrades(
                stream,
                service_fn(move |req: Request<Incoming>| {
                    Self::serve(
                        self.msg_handler.clone(),
                        req,
                        scheme,
                        origin,
                        peer_addr,
                        self.listening_on,
                    )
                }),
            );

            fut.await.ok();
        });
    }

    /// Start without TLS (HTTP cleartext)
    pub(crate) async fn start_without_tls(
        self,
        conn_builder: conn::auto::Builder<TokioExecutor>,
    ) -> Result<(), ProxyError> {
        let tcp_socket = bind_tcp_socket(&self.listening_on)?;
        let listening_addr = tcp_socket.local_addr().unwrap();
        let tcp_listener = tcp_socket.listen(1024)?;
        info!(
            "Start TCP proxy serving with HTTP request: {}",
            listening_addr
        );
        // 广播端口
        TRACING_LISTEN_PORT.store(listening_addr.port(), Ordering::Relaxed);

        while let Ok((stream, client_addr)) = tcp_listener.accept().await {
            // 判断目标是否是HTTPS
            let Some(origin) = FORWARD_PROXY_TARGETS.get(&client_addr).await else {
                // 本地访问, 忽略
                continue;
            };

            let Some(scheme) = get_http_scheme(origin.ip(), origin.port()).await else {
                // 没有scheme, 忽略
                continue;
            };

            if scheme == "https" {
                if let Err(e) = tls::handshake(
                    self.clone(),
                    conn_builder.clone(),
                    scheme,
                    stream,
                    client_addr,
                    origin,
                )
                .await
                {
                    error!("TLS handshake failed. {e}");
                }
                continue;
            }

            self.clone()
                .client_serve(
                    TokioIo::new(stream),
                    conn_builder.clone(),
                    scheme,
                    client_addr,
                    origin,
                )
                .await;
        }
        Ok(())
    }
}
