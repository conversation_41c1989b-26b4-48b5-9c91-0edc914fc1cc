#[macro_export]
macro_rules! print_command_output {
    ($verbose: expr, $command: expr, $envs: expr, $rx: expr) => {
        if $verbose {
            println!("{}", format!("{:?}", $command).blue());
        }
        let mut command = $command.stdout(Stdio::piped()).envs($envs).spawn()?;
        tokio::spawn(async move {
            if let Some(stdout) = command.stdout.take() {
                let reader = BufReader::new(stdout);
                let mut lines = reader.lines();

                loop {
                    tokio::select! {
                        _ = $rx.recv() => {
                            _ = command.kill().await;
                            return Err(anyhow::anyhow!("Exit"));
                        }
                        Ok(next_line) = lines.next_line() => {
                            if let Some(line) = &next_line {
                                println!("{}", line);
                            }
                        }
                        Ok(exit_code) = command.wait() => {
                            if !exit_code.success() {
                                return Err(anyhow::anyhow!(format!("=== {} ===", "Command execution failed".red())));
                            }
                            break; // child process exited
                        }
                    }
                }
            }

            Ok(())
        })
        .await??;
    };
}
