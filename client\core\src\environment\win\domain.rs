use windows::{
    core::PCWSTR,
    // Networking::Connectivity::NetworkInformation,
    Win32::{
        Foundation::ERROR_SUCCESS,
        NetworkManagement::NetManagement::{NetApiBufferFree, NetWkstaGetInfo, WKSTA_INFO_100},
        Networking::ActiveDirectory::{
            DsRoleFreeMemory, DsRoleGetPrimaryDomainInformation, DsRolePrimaryDomainInfoBasic,
            DSROLE_PRIMARY_DOMAIN_INFO_BASIC,
        },
    },
};

use std::ffi::c_void;

/// 获取域
pub fn get_domain_name() -> Option<(String, bool)> {
    let mut domain_info = std::ptr::null_mut::<DSROLE_PRIMARY_DOMAIN_INFO_BASIC>();
    let mut info = std::ptr::null_mut::<WKSTA_INFO_100>();
    unsafe {
        let errno = DsRoleGetPrimaryDomainInformation(
            PCWSTR::null(),
            DsRolePrimaryDomainInfoBasic,
            &mut domain_info as *mut _ as *mut *mut u8,
        );

        if ERROR_SUCCESS.0 != errno {
            return None;
        }

        let deref = &*domain_info;
        if deref.DomainNameDns.is_null() {
            DsRoleFreeMemory(domain_info as *mut u8 as *mut c_void);
            return None;
        }

        let domain = String::from_utf16_lossy(deref.DomainNameFlat.as_wide());
        DsRoleFreeMemory(domain_info as *mut u8 as *mut c_void);
        let errno = NetWkstaGetInfo(
            PCWSTR::null(),
            101,
            Some(&mut info as *mut _ as *mut *mut u8),
        );

        if ERROR_SUCCESS.0 != errno {
            return None;
        }

        let deref = &*info;
        if deref.wki100_langroup.is_null() {
            NetApiBufferFree(Some(info as *mut u8 as *mut c_void));
            return None;
        }

        let group = String::from_utf16_lossy(deref.wki100_langroup.as_wide());
        NetApiBufferFree(Some(info as *mut u8 as *mut c_void));
        let is_domain_user = domain.eq_ignore_ascii_case(&group);
        Some((domain.to_ascii_uppercase(), is_domain_user))
    }
}
