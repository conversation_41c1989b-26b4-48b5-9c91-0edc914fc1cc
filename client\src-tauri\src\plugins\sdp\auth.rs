extern crate rand;

use chrono::Local;
use serde_json::Value;
use tauri::{async_runtime::Mutex, command, State};

use communicate_interface::ControlServiceClient;
use tokio::sync::RwLock;
use types::backend::Login;

use crate::{
    plugins::{ErrorResponse, Result},
    state::{AppState, LOGIN_LOCK},
};

/// 登录控制器
#[command(async)]
pub async fn login(
    request: Login,
    rpc: State<'_, Mutex<ControlServiceClient>>,
    app_state: State<'_, RwLock<AppState>>,
) -> Result<Value> {
    if request.connection_config.authorization.is_none() {
        log::error!(target: "app", "Missing SPA authorization");
        return Err(ErrorResponse::new(600, None));
    }
    let authorization = request.connection_config.authorization.clone().unwrap();
    log::debug!(target: "app", "Receive login controller event: {:?}", &authorization.username);

    let Ok(_lock) = LOGIN_LOCK.try_lock() else {
        return Err(ErrorResponse::new(600, None));
    };

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    match rpc.login(tonic::Request::new(request.into())).await {
        Ok(response) => match serde_json::from_str::<Value>(&response.into_inner()) {
            Ok(result) => {
                log::debug!(target: "app", "Login controller result: {:?}", result);

                if let Some("Mix") = result["mode"].as_str() {
                    let mut app_state = app_state.write().await;
                    app_state.user = Some(crate::state::User {
                        unit: authorization.tenant,
                        username: authorization.username,
                        secret: authorization.secret,
                    });
                    app_state.last_login_time = Some(Local::now().timestamp_millis() as u64);
                }

                Ok(result)
            }
            Err(err) => {
                log::error!(target: "app", "Failed to deserialize login ctl result. {:?}", err);
                Err(ErrorResponse::new(600, Some("连接服务失败")))
            }
        },
        Err(status) => {
            if status.code() == tonic::Code::Unauthenticated {
                let metadata = status.metadata();
                let code = metadata.get("error-code").unwrap();
                let code: i32 = code.to_str().unwrap().parse().unwrap();

                log::error!(target: "app", "Login controller result code: {}", code);
                let result = String::from_utf8(status.details().to_vec()).ok();
                return Err(ErrorResponse::new(code, result.as_deref()));
            }

            log::error!(target: "app", "Failed to login controller. {}", status.message());
            Err(ErrorResponse::new(600, Some("连接服务失败")))
        }
    }
}

/// 退出
#[command(async)]
pub async fn logout(
    rpc: State<'_, Mutex<ControlServiceClient>>,
    app_state: State<'_, RwLock<AppState>>,
) -> Result<()> {
    log::trace!(target: "app", "Receive logout event.");

    let mut app_state = app_state.write().await;
    app_state.reset();
    drop(app_state);

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);
    _ = rpc.disconnect(tonic::Request::new(())).await;

    Ok(())
}

/// 代理是否准备好
#[command(async)]
pub(in crate::plugins) async fn proxy_ready(
    app_state: State<'_, RwLock<AppState>>,
) -> Result<bool> {
    let app_state = app_state.read().await;
    Ok(app_state.proxy_ready)
}
