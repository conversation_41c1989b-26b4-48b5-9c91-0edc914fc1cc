use super::Result;

const VERSION: semver::Version = semver::Version::new(8, 2, 2);

/// 支持内外网配置不同的节点获取地址, 仅升级配置文件版本号
pub fn migrate(config: &mut serde_json::Value) -> Result<()> {
    if !super::version_matches(config, VERSION) {
        return Ok(());
    }

    // unwrap is safe
    let config = config.as_object_mut().unwrap();

    log::info!(target: "app", "Migrating settings format to {}", VERSION);

    config.insert(
        String::from("version"),
        serde_json::Value::String(format!("{}", VERSION)),
    );
    Ok(())
}
