#[cfg(feature = "dns_server")]
use dns_server::{<PERSON><PERSON><PERSON><PERSON><PERSON>, DnsServer};
use futures::{
    channel::{
        mpsc::{self as f_mpsc, UnboundedReceiver},
        oneshot,
    },
    future::LocalBoxFuture,
    StreamExt,
};
#[cfg(feature = "traceability")]
use ipnetwork::IpNetwork;
#[cfg(feature = "traceability")]
use std::collections::HashSet;
use std::{
    collections::HashMap,
    future::Future,
    io,
    marker::PhantomData,
    net::{IpAddr, Ipv4Addr},
    pin::Pin,
    sync::{Arc, Weak},
};

use base::packet::{Message, MessageType};
#[cfg(feature = "dns_server")]
use err_ext::ErrorExt;
use once_cell::sync::Lazy;
#[cfg(feature = "traceability")]
use tlv::Sequence;
#[cfg(feature = "traceability")]
use tokio::task::Jo<PERSON><PERSON><PERSON>le;
use types::{
    backend::{BackendStateTransition, TicketType},
    net::Connectivity,
    states::{BackendState, TunnelState},
    tunnel::TunnelStateTransition,
};

#[cfg(feature = "traceability")]
use crate::routing::Node;
#[cfg(feature = "sdp")]
use crate::{
    backend_state_machine::{BackendCommand, BackendParameters, BackendStateMachineHandle},
    routing::RequiredRoute,
    tunnel::TunnelParameters,
    tunnel_state_machine::{TunnelCommand, TunnelStateMachineHandle},
};

use crate::{
    mpsc::Sender,
    setting::DeviceIdentity,
    strategy::Condition,
    tasks::{TaskCommand, TaskHandle},
};

#[cfg(feature = "sdp")]
pub mod backend;
#[cfg(feature = "sdp")]
pub mod backend_proxy;
#[cfg(feature = "sdp")]
pub mod backend_state_machine;
#[cfg(feature = "sdp")]
pub mod dns;
#[cfg(feature = "dns_server")]
pub mod dns_server;
pub mod environment;
#[cfg(all(windows, feature = "traceability"))]
mod firewall;
pub mod http;
#[cfg_attr(target_os = "linux", allow(dead_code))]
#[cfg(target_os = "linux")]
mod linux;
#[cfg(target_os = "macos")]
pub mod macos;
pub mod mpsc;
#[cfg(feature = "sdp")]
pub mod offline;
#[cfg(feature = "sdp")]
pub mod proxy_request;
#[cfg(feature = "sdp")]
pub mod routing;
pub mod setting;
pub mod shutdown;
pub mod strategy;
pub mod structs;
pub mod tasks;
#[cfg(feature = "traceability")]
pub mod traceability;
#[cfg(feature = "sdp")]
pub mod tunnel;
#[cfg(feature = "sdp")]
pub mod tunnel_state_machine;
pub mod updater;
#[cfg(windows)]
pub mod windows;

#[cfg(feature = "sdp")]
const PRODUCT_NAME: &str = "JA-SDP";

#[cfg(all(feature = "iam", not(feature = "sdp")))]
const PRODUCT_NAME: &str = "NGIAM";

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Missing authorization: {0}")]
    MissingAuthorization(&'static str),

    #[error("Missing domain name certificate configuration: {0}")]
    MissingDomain(String),

    #[error("Wrong domain name: {0}")]
    DomainError(String),

    #[cfg(feature = "sdp")]
    #[error("{0}")]
    BackendError(#[from] backend::Error),

    #[error("{0}")]
    ConnectError(#[from] io::Error),

    #[error("Connection closed")]
    ConnectionClosed,

    #[error("Proxy connection closed")]
    ProxyConnectionClosed,

    #[error("Auth failed")]
    AuthFailed(String),

    #[error("Need change pwd")]
    NeedChangePwd(String),

    #[error("Failed to send command to core because it is not running")]
    CoreUnavailable,

    #[cfg(feature = "sdp")]
    #[error("Tunnel state machine error")]
    TunnelError(#[source] tunnel_state_machine::Error),
}

/// Tunnel packet direction.
pub enum Direction {
    /// Traffic written to the channel.
    In,
    /// The traffic set aside by the channel.
    Out,
}

/// Sent from various threads and exposed interfaces.
pub enum InternalCoreEvent {
    /// Return backend state.
    #[cfg(feature = "sdp")]
    GetBackendState(oneshot::Sender<BackendState>),
    /// Tunnel has changed state.
    #[cfg(feature = "sdp")]
    TunnelStateTransition(TunnelStateTransition),
    /// Backend has changed state.
    #[cfg(feature = "sdp")]
    BackendStateTransition(BackendStateTransition),
    /// A command sent to the backend.
    #[cfg(feature = "sdp")]
    BackendCommand(BackendCommand),
    /// A command sent to the tunnel.
    #[cfg(feature = "sdp")]
    TunnelCommand(TunnelCommand),
    /// Client reconnect token.
    #[cfg(feature = "sdp")]
    ReconnectToken(String, u64),
    /// Client local strategy.
    Strategy(Option<HashMap<String, Vec<Condition>>>),
    /// Client notification message.
    #[cfg(feature = "sdp")]
    Notification(u8),
    /// Core shutdown triggered by a signal, ctrl-c or similar.
    /// The boolean should indicate whether the shutdown was user-initiated.
    TriggerShutdown,
    /// Tunnel packets.
    #[cfg(feature = "sdp")]
    TunnelPacket(Direction, Vec<u8>),
    /// Add DNS record.
    #[cfg(feature = "dns_server")]
    DnsRecord(String, IpAddr),
    /// Remove DNS record.
    #[cfg(feature = "dns_server")]
    RemoveDnsRecord(String),
}

#[cfg(feature = "sdp")]
impl From<TunnelStateTransition> for InternalCoreEvent {
    fn from(tunnel_state_transition: TunnelStateTransition) -> Self {
        InternalCoreEvent::TunnelStateTransition(tunnel_state_transition)
    }
}

#[cfg(feature = "sdp")]
impl From<BackendStateTransition> for InternalCoreEvent {
    fn from(backend_state_transition: BackendStateTransition) -> Self {
        InternalCoreEvent::BackendStateTransition(backend_state_transition)
    }
}

#[cfg(feature = "sdp")]
impl From<BackendCommand> for InternalCoreEvent {
    fn from(command: BackendCommand) -> Self {
        InternalCoreEvent::BackendCommand(command)
    }
}

pub struct CoreCommandChannel {
    sender: CoreCommandSender,
    receiver: f_mpsc::UnboundedReceiver<InternalCoreEvent>,
}

impl Default for CoreCommandChannel {
    fn default() -> Self {
        Self::new()
    }
}

impl CoreCommandChannel {
    pub fn new() -> Self {
        let (untracked_sender, receiver) = f_mpsc::unbounded();
        let sender = CoreCommandSender(Arc::new(untracked_sender));

        Self { sender, receiver }
    }

    pub fn sender(&self) -> CoreCommandSender {
        self.sender.clone()
    }

    pub fn destructure(
        self,
    ) -> (
        CoreEventSender,
        f_mpsc::UnboundedReceiver<InternalCoreEvent>,
    ) {
        let event_sender = CoreEventSender::new(Arc::downgrade(&self.sender.0));

        (event_sender, self.receiver)
    }
}

#[derive(Clone)]
pub struct CoreCommandSender(Arc<f_mpsc::UnboundedSender<InternalCoreEvent>>);

impl CoreCommandSender {
    pub fn send(&self, event: InternalCoreEvent) -> Result<(), Error> {
        self.0
            .unbounded_send(event)
            .map_err(|_| Error::CoreUnavailable)
    }

    #[cfg(feature = "sdp")]
    pub fn send_to_backend(&self, command: BackendCommand) -> Result<(), Error> {
        self.send(InternalCoreEvent::BackendCommand(command))
    }

    #[cfg(feature = "sdp")]
    pub fn send_to_tunnel(&self, command: TunnelCommand) -> Result<(), Error> {
        self.send(InternalCoreEvent::TunnelCommand(command))
    }
}

pub struct CoreEventSender<E = InternalCoreEvent> {
    sender: Weak<f_mpsc::UnboundedSender<InternalCoreEvent>>,
    _event: PhantomData<E>,
}

impl<E> Clone for CoreEventSender<E>
where
    InternalCoreEvent: From<E>,
{
    fn clone(&self) -> Self {
        CoreEventSender {
            sender: self.sender.clone(),
            _event: PhantomData,
        }
    }
}

impl CoreEventSender {
    pub fn new(sender: Weak<f_mpsc::UnboundedSender<InternalCoreEvent>>) -> Self {
        CoreEventSender {
            sender,
            _event: PhantomData,
        }
    }

    pub fn to_specialized_sender<E>(&self) -> CoreEventSender<E>
    where
        InternalCoreEvent: From<E>,
    {
        CoreEventSender {
            sender: self.sender.clone(),
            _event: PhantomData,
        }
    }
}

impl<E> CoreEventSender<E>
where
    InternalCoreEvent: From<E>,
{
    pub fn is_closed(&self) -> bool {
        self.sender
            .upgrade()
            .map(|sender| sender.is_closed())
            .unwrap_or(true)
    }
}

impl<E> Sender<E> for CoreEventSender<E>
where
    InternalCoreEvent: From<E>,
{
    fn send(&self, event: E) -> Result<(), mpsc::Error> {
        if let Some(sender) = self.sender.upgrade() {
            sender
                .unbounded_send(InternalCoreEvent::from(event))
                .map_err(|_| mpsc::Error::ChannelClosed)
        } else {
            Err(mpsc::Error::ChannelClosed)
        }
    }
}

/// Trait representing something that can broadcast core events.
pub trait EventListener: Sync + Send + Clone + 'static {
    /// Notify that the tunnel state changed.
    fn notify_new_tunnel_state(&self, new_state: TunnelState);

    /// Notify that the backend state changed.
    fn notify_new_backend_state(&self, new_state: BackendState);

    /// 通知网络状态
    fn notify_connectivity(&self, connectivity: Connectivity);

    fn notify_close_reason(&self, reason: u8);

    fn notify_notification_reason(&self, reason: u8);

    fn notify_change_pwd_result(&self, bytes: &[u8]);

    fn notify_need_auth(&self);

    fn notify_ticket_result(&self, bytes: &[u8], ticket_type: TicketType);

    fn notify_denied_access(&self, bytes: &[u8]);

    fn notify_environment(&self, bytes: Vec<u8>);

    /// 通知更新状态
    fn notify_update_status(&self, status: &str, msg: Option<String>);

    /// 通知下载进度
    fn notify_download_progress(&self, chunk_size: usize, content_length: u64);
}

pub struct Core<L: EventListener> {
    device_id: String,
    #[cfg(feature = "sdp")]
    tunnel_state: TunnelState,
    #[cfg(feature = "sdp")]
    backend_state: BackendState,
    offline_state_rx: UnboundedReceiver<Connectivity>,
    rx: UnboundedReceiver<InternalCoreEvent>,
    tx: CoreEventSender,
    event_listener: L,
    shutdown_tasks: Vec<Pin<Box<dyn Future<Output = ()>>>>,
    #[cfg(feature = "sdp")]
    tunnel_state_machine_handle: TunnelStateMachineHandle,
    #[cfg(feature = "sdp")]
    backend_state_machine_handle: BackendStateMachineHandle,
    task_handle: TaskHandle,
    #[cfg(feature = "traceability")]
    trace_proxy_handle: Option<JoinHandle<()>>,
    #[cfg(feature = "dns_server")]
    dns_handler: DnsHandler,
    #[cfg(feature = "dns_server")]
    dns_server: Option<DnsServer>,
}

impl<L> Core<L>
where
    L: EventListener + Clone + Send + 'static,
{
    pub async fn start(
        device_id: String,
        identities: HashMap<String, DeviceIdentity>,
        event_listener: L,
        internal_event_tx: CoreEventSender,
        internal_event_rx: UnboundedReceiver<InternalCoreEvent>,
    ) -> Result<Self, Error> {
        #[cfg(target_os = "macos")]
        macos::bump_filehandle_limit();

        let (offline_state_tx, offline_state_rx) = f_mpsc::unbounded();

        #[cfg(feature = "sdp")]
        let tunnel_parameters = TunnelParameters {
            core_tx: internal_event_tx.clone(),
            interface_name: INTERFACE_NAME.clone(),
            enable_ipv6: false,
            #[cfg(target_os = "linux")]
            fwmark: Some(types::TUNNEL_FWMARK),
        };

        #[cfg(feature = "sdp")]
        let (tunnel_state_machine_handle, route_manager_handle) = tunnel_state_machine::spawn(
            tunnel_parameters,
            internal_event_tx.to_specialized_sender(),
            offline_state_tx,
            #[cfg(target_os = "linux")]
            tunnel_state_machine::LinuxNetworkingIdentifiers {
                fwmark: types::TUNNEL_FWMARK,
                table_id: types::TUNNEL_TABLE_ID,
            },
        )
        .await
        .map_err(Error::TunnelError)?;

        #[cfg(feature = "sdp")]
        let backend_parameters = BackendParameters {
            device_id: device_id.clone(),
            local_ip: Ipv4Addr::new(10, 6, 7, 7),
            event_listener: event_listener.clone(),
            interface_name: INTERFACE_NAME.clone(),
            route_manager_handle,
            identities,
        };

        #[cfg(feature = "sdp")]
        let backend_state_machine_handle = backend_state_machine::spawn(
            internal_event_tx.to_specialized_sender(),
            backend_parameters,
            internal_event_tx.to_specialized_sender(),
        )
        .await;

        let task_handle = tasks::spawn().await;

        Ok(Core {
            device_id,
            #[cfg(feature = "sdp")]
            tunnel_state: TunnelState::Disconnected,
            #[cfg(feature = "sdp")]
            backend_state: BackendState::Disconnected(None),
            offline_state_rx,
            rx: internal_event_rx,
            tx: internal_event_tx,
            event_listener,
            shutdown_tasks: vec![],
            #[cfg(feature = "sdp")]
            tunnel_state_machine_handle,
            #[cfg(feature = "sdp")]
            backend_state_machine_handle,
            task_handle,
            #[cfg(feature = "traceability")]
            trace_proxy_handle: None,
            #[cfg(feature = "dns_server")]
            dns_handler: DnsHandler::default(),
            #[cfg(feature = "dns_server")]
            dns_server: None,
        })
    }

    pub async fn run(mut self) -> Result<(), Error> {
        self.connect_tunnel();
        #[cfg(feature = "traceability")]
        traceability::init();

        loop {
            tokio::select! {
                event = self.rx.next() => match event {
                    Some(event) => if self.handle_event(event).await {
                        break;
                    }
                    None => break,
                },
                is_offline = self.offline_state_rx.next() => match is_offline {
                    Some(connectivity) => {
                        if connectivity.is_offline() {
                            log::error!("Network connection abnormality");
                        } else {
                            log::info!("Network connection restored");
                        }
                        self.event_listener.notify_connectivity(connectivity);

                        // close backend
                        #[cfg(feature = "sdp")]
                        if connectivity.is_offline() && (self.backend_state == BackendState::Connected
                            || self.backend_state == BackendState::ProxyConnected
                            || self.backend_state == BackendState::Connecting
                            || self.backend_state == BackendState::ProxyConnecting) {
                            self.send_backend_command(BackendCommand::Disconnect(None));
                        }

                        // reconnect tunnel
                        #[cfg(feature = "sdp")]
                        if connectivity.is_online() && (self.tunnel_state != TunnelState::Connected && self.tunnel_state != TunnelState::Connecting) {
                            self.reconnect_tunnel();
                        }
                    }
                    None => break,
                }
            }
        }

        self.disconnect_tunnel_and_wait().await;
        self.finalize().await;
        Ok(())
    }

    /// Begin disconnecting and wait for the tunnel state machine to be disconnected
    async fn disconnect_tunnel_and_wait(&mut self) {
        if self.tunnel_state.is_disconnected() {
            return;
        }

        self.disconnect_tunnel();

        while let Some(event) = self.rx.next().await {
            if let InternalCoreEvent::TunnelStateTransition(transition) = event {
                self.handle_tunnel_state_transition(transition).await;
            } else {
                log::trace!("Ignoring event because the core is shutting down");
            }

            if self.tunnel_state.is_disconnected() {
                break;
            }
        }
    }

    async fn finalize(self) {
        let (
            event_listener,
            shutdown_tasks,
            tunnel_state_machine_handle,
            backend_state_machine_handle,
            task_handle,
        ) = self.shutdown();
        for future in shutdown_tasks {
            future.await;
        }

        tunnel_state_machine_handle.try_join().await;
        backend_state_machine_handle.try_join().await;
        task_handle.try_join().await;

        drop(event_listener);

        #[cfg(any(target_os = "macos", target_os = "linux"))]
        if let Err(err) = tokio::fs::remove_file(paths::get_rpc_socket_path()).await {
            if err.kind() != std::io::ErrorKind::NotFound {
                log::error!("Failed to remove old RPC socket: {}", err);
            }
        }
    }

    /// Shuts down the core without shutting down the underlying event listener and the shutdown
    /// callbacks
    fn shutdown<'a>(
        self,
    ) -> (
        L,
        Vec<LocalBoxFuture<'a, ()>>,
        TunnelStateMachineHandle,
        BackendStateMachineHandle,
        TaskHandle,
    ) {
        let Core {
            event_listener,
            shutdown_tasks,
            tunnel_state_machine_handle,
            backend_state_machine_handle,
            task_handle,
            ..
        } = self;

        (
            event_listener,
            shutdown_tasks,
            tunnel_state_machine_handle,
            backend_state_machine_handle,
            task_handle,
        )
    }

    async fn handle_event(&mut self, event: InternalCoreEvent) -> bool {
        use self::InternalCoreEvent::*;
        let mut should_stop = false;
        match event {
            #[cfg(feature = "sdp")]
            GetBackendState(tx) => {
                _ = tx.send(self.backend_state.clone());
            }
            #[cfg(feature = "sdp")]
            TunnelStateTransition(transition) => {
                self.handle_tunnel_state_transition(transition).await
            }
            #[cfg(feature = "sdp")]
            BackendStateTransition(transition) => {
                self.handle_backend_state_transition(transition).await
            }
            #[cfg(feature = "sdp")]
            BackendCommand(command) => {
                self.send_backend_command(command);
            }
            #[cfg(feature = "sdp")]
            TunnelCommand(command) => {
                self.send_tunnel_command(command);
            }
            TriggerShutdown => {
                self.on_trigger_shutdown();
                should_stop = true;
            }
            #[cfg(feature = "sdp")]
            TunnelPacket(direction, packet) => {
                self.handle_tunnel_packet_event(direction, packet).await
            }
            Strategy(strategy) => {
                let command = TaskCommand::AddTask((
                    tasks::environment::TASK_NAME,
                    Box::pin(tasks::environment::environment_task(
                        self.device_id.clone(),
                        self.event_listener.clone(),
                        strategy,
                    )),
                ));
                self.send_task_command(command).await;
            }
            #[cfg(feature = "sdp")]
            ReconnectToken(ticket, expire) => {
                log::trace!("Reconnect token. {ticket}/{expire}");
                let command = TaskCommand::AddTask((
                    tasks::refresh_ticket::TASK_NAME,
                    Box::pin(tasks::refresh_ticket::delay_refresh_reconnect_ticket(
                        self.tx.clone(),
                        ticket.clone(),
                        expire,
                    )),
                ));
                self.send_task_command(command).await;
                let value = format!("{}@{}", ticket, expire);
                self.event_listener
                    .notify_ticket_result(value.as_bytes(), TicketType::Reconnect)
            }
            #[cfg(feature = "sdp")]
            Notification(info) => {
                log::trace!("Notify message. {info}");
                self.event_listener.notify_notification_reason(info)
            }
            #[cfg(feature = "dns_server")]
            DnsRecord(domain, ip) => {
                _ = self.dns_handler.upsert(domain.as_str(), ip).await;
            }
            #[cfg(feature = "dns_server")]
            RemoveDnsRecord(domain) => {
                _ = self.dns_handler.remove(domain.as_str()).await;
            }
        }
        should_stop
    }

    #[cfg(feature = "sdp")]
    async fn handle_tunnel_packet_event(&self, direction: Direction, packet: Vec<u8>) {
        match direction {
            Direction::In => {
                self.send_tunnel_command(TunnelCommand::Packet(packet));
            }
            Direction::Out => {
                let message = Message::new(MessageType::ForwardPacket, &packet);
                self.send_backend_command(BackendCommand::Packet(message));
            }
        }
    }

    #[cfg(feature = "sdp")]
    async fn handle_tunnel_state_transition(
        &mut self,
        tunnel_state_transition: TunnelStateTransition,
    ) {
        let tunnel_state = match tunnel_state_transition {
            TunnelStateTransition::Disconnected => TunnelState::Disconnected,
            TunnelStateTransition::Connecting => TunnelState::Connecting,
            TunnelStateTransition::Connected => TunnelState::Connected,
            TunnelStateTransition::Disconnecting(after_disconnect) => {
                TunnelState::Disconnecting(after_disconnect)
            }
            TunnelStateTransition::Error(error_state) => TunnelState::Error(error_state),
        };

        log::debug!("New tunnel state: {:?}", tunnel_state);

        if let TunnelState::Error(ref error_state) = tunnel_state {
            log::error!(
                "FAILED TO BLOCK NETWORK CONNECTIONS, ENTERED ERROR STATE BECAUSE: {}",
                error_state.cause()
            );
        }

        self.tunnel_state = tunnel_state.clone();
        self.event_listener.notify_new_tunnel_state(tunnel_state);
    }

    #[cfg(feature = "sdp")]
    async fn handle_backend_state_transition(
        &mut self,
        backend_state_transition: BackendStateTransition,
    ) {
        #[inline(always)]
        #[cfg_attr(not(feature = "traceability"), allow(unused_variables))]
        async fn do_connected_after<L: EventListener>(
            this: &mut Core<L>,
            ip: IpAddr,
            dns_servers: Option<Vec<IpAddr>>,
        ) {
            // TODO: 设置虚拟网卡IP
            // let (tx, rx) = oneshot::channel();
            // this.send_tunnel_command(TunnelCommand::Network {
            //     ip,
            //     callback_tx: tx,
            // });
            // _ = rx.await;
            #[cfg(feature = "traceability")]
            {
                #[cfg(windows)]
                let traceability_available = firewall::allow_self_in_windows_defender().is_ok();
                #[cfg(not(windows))]
                let traceability_available = true;
                if traceability_available {
                    this.trace_proxy_handle =
                        Some(traceability::start_trace_proxy(this.device_id.clone(), ip));
                    // 添加溯源路由
                    let mut routes = HashSet::new();
                    routes.push(RequiredRoute::new(
                        IpNetwork::new(IpAddr::V4(Ipv4Addr::new(10, 7, 0, 0)), 16).unwrap(),
                        Node::device(INTERFACE_NAME.clone().clone()),
                    ));
                    this.send_tunnel_command(TunnelCommand::Routes((Some(routes), None)));
                } else {
                    log::warn!("Traceability function is not available");
                }
                this.send_tunnel_command(TunnelCommand::TraceabilityFunction(
                    traceability_available,
                ));
            }

            #[cfg(feature = "dns_server")]
            {
                // 将DNS设置为localhost
                this.send_tunnel_command(TunnelCommand::Dns(Some(vec![IpAddr::V4(
                    Ipv4Addr::LOCALHOST,
                )])));

                // 设置DNS上游服务
                _ = this.dns_handler.set_upstream(dns_servers).await;

                match DnsServer::spawn(this.dns_handler.clone()).await {
                    Ok(server) => {
                        this.dns_server = Some(server);
                    }
                    Err(error) => {
                        log::warn!("{}", error.display_chain_with_msg("DNS server"));
                    }
                }
            }

            #[cfg(not(feature = "dns_server"))]
            {
                if dns_servers.is_some() {
                    this.send_tunnel_command(TunnelCommand::Dns(dns_servers));
                }
            }

            // 启动环境扫描任务
            let command = TaskCommand::AddTask((
                tasks::environment::TASK_NAME,
                Box::pin(tasks::environment::environment_task(
                    this.device_id.clone(),
                    this.event_listener.clone(),
                    None,
                )),
            ));
            this.send_task_command(command).await;
        }

        #[inline(always)]
        async fn do_disconnected_after<L: EventListener>(this: &mut Core<L>) {
            #[cfg(feature = "traceability")]
            if let Some(handle) = this.trace_proxy_handle.take() {
                handle.abort();
            }
            #[cfg(feature = "dns_server")]
            {
                _ = this.dns_handler.reset().await;
                if let Some(dns_server) = this.dns_server.take() {
                    _ = dns_server.shutdown().await;
                }
            }
            this.send_tunnel_command(TunnelCommand::BackendOffline);

            // 取消定时检测环境任务
            let command = TaskCommand::CancelTask(tasks::environment::TASK_NAME);
            this.send_task_command(command).await;

            // 取消重连票据刷新任务
            let command = TaskCommand::CancelTask(tasks::refresh_ticket::TASK_NAME);
            this.send_task_command(command).await;
        }

        let backend_state = match backend_state_transition {
            BackendStateTransition::Disconnected(reason) => {
                do_disconnected_after(self).await;
                BackendState::Disconnected(reason)
            }
            BackendStateTransition::ProxyConnecting => BackendState::ProxyConnecting,
            BackendStateTransition::ProxyConnected => BackendState::ProxyConnected,
            BackendStateTransition::Connecting(_) => BackendState::Connecting,
            BackendStateTransition::Connected(ip, dns_servers) => {
                do_connected_after(self, ip, dns_servers).await;
                BackendState::Connected
            }
            BackendStateTransition::Disconnecting => BackendState::Disconnecting,
            BackendStateTransition::Error(error_state) => {
                do_disconnected_after(self).await;
                BackendState::Error(error_state)
            }
        };

        log::info!("New backend state: {:?}", backend_state);

        self.backend_state = backend_state.clone();
        self.event_listener.notify_new_backend_state(backend_state);
    }

    fn on_trigger_shutdown(&mut self) {
        #[cfg(feature = "sdp")]
        {
            self.disconnect_backend();
            self.disconnect_tunnel();
        }
    }

    #[cfg(feature = "sdp")]
    fn connect_tunnel(&mut self) {
        self.send_tunnel_command(TunnelCommand::Connect);
    }

    #[cfg(feature = "sdp")]
    fn disconnect_tunnel(&mut self) {
        self.send_tunnel_command(TunnelCommand::Disconnect(None));
    }

    #[cfg(feature = "sdp")]
    fn disconnect_backend(&mut self) {
        self.send_backend_command(BackendCommand::Disconnect(None));
    }

    #[cfg(feature = "sdp")]
    fn reconnect_tunnel(&mut self) {
        self.connect_tunnel();
    }

    #[cfg(feature = "sdp")]
    fn send_tunnel_command(&self, command: TunnelCommand) {
        self.tunnel_state_machine_handle
            .command_tx()
            .unbounded_send(command)
            .expect("Tunnel state machine has stopped");
    }

    #[cfg(feature = "sdp")]
    fn send_backend_command(&self, command: BackendCommand) {
        self.backend_state_machine_handle
            .command_tx()
            .unbounded_send(command)
            .expect("Backend state machine has stopped");
    }

    async fn send_task_command(&self, command: TaskCommand) {
        self.task_handle
            .command_tx()
            .send(command)
            .await
            .expect("Task runner has stopped");
    }

    pub fn shutdown_handle(&self) -> ShutdownHandle {
        ShutdownHandle {
            tx: self.tx.clone(),
        }
    }
}

pub struct ShutdownHandle {
    tx: CoreEventSender,
}

impl ShutdownHandle {
    pub fn shutdown(&self) {
        let _ = self.tx.send(InternalCoreEvent::TriggerShutdown);
    }
}

static INTERFACE_NAME: Lazy<String> = Lazy::new(|| {
    #[cfg(target_os = "macos")]
    {
        let interfaces = netdev::get_interfaces();
        let mut serials = interfaces
            .iter()
            .filter(|interface| interface.name.starts_with("utun"))
            .map(|interface| {
                interface
                    .name
                    .replace("utun", "")
                    .parse::<usize>()
                    .unwrap_or_default()
            })
            .collect::<Vec<usize>>();

        if serials.is_empty() {
            return String::from("utun9");
        }

        serials.sort();

        let max = serials[serials.len() - 1];
        format!("utun{}", max + 1)
    }

    #[cfg(windows)]
    {
        String::from("SDP")
    }
    #[cfg(target_os = "linux")]
    String::from("sdp")
});

/// Remove any old RPC socket (if it exists).
#[cfg(not(windows))]
pub async fn cleanup_old_rpc_socket(rpc_socket_path: impl AsRef<std::path::Path>) {
    if let Err(err) = tokio::fs::remove_file(rpc_socket_path).await {
        if err.kind() != std::io::ErrorKind::NotFound {
            log::error!("Failed to remove old RPC socket: {}", err);
        }
    }
}
