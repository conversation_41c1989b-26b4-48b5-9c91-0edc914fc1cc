use crate::get_unnamed_enum_type;
use proc_macro2::TokenStream;
use quote::quote;
use syn::{spanned::Spanned, Data, Error, ExprLit, Lit};

mod em;
mod fie;

pub fn expand_derive_deserialize(input: &mut syn::DeriveInput) -> syn::Result<TokenStream> {
    let type_name = &input.ident;
    match &input.data {
        Data::Struct(data_struct) => {
            if !matches!(&data_struct.fields, syn::Fields::Named(_)) {
                return Err(Error::new(
                    proc_macro2::Span::call_site(),
                    "can only work on named fields",
                ));
            }
            fie::expand_struct(&data_struct.fields, type_name)
        }
        Data::Enum(em) => {
            let untag = crate::get_tlv_value(&input.attrs)?;
            let untag = match untag.as_deref() {
                Some("untag") => true,
                None => false,
                _ => {
                    return Err(Error::new(
                        proc_macro2::Span::call_site(),
                        "#[tlv = \"untag\"] is only defined for enums",
                    ));
                }
            };

            let mut match_arms = vec![];
            for (ordinal, variant) in em.variants.iter().enumerate() {
                let enum_name = &variant.ident;

                let ordinal = if let Some((
                    _,
                    syn::Expr::Lit(ExprLit {
                        lit: Lit::Int(lit_int),
                        ..
                    }),
                )) = &variant.discriminant
                {
                    lit_int.base10_digits().parse().unwrap()
                } else {
                    ordinal as u8
                };

                match &variant.fields {
                    syn::Fields::Named(_) => {
                        if untag {
                            return Err(Error::new(
                                proc_macro2::Span::call_site(),
                                "#[tlv = \"untag\"] is only defined for unit enums",
                            ));
                        }
                        let token_stream = em::expand_named_fields(&variant.fields)?;
                        match_arms.push(quote! {
                            #ordinal => {
                                Ok(#type_name::#enum_name {
                                    #token_stream
                                })
                            },
                        });
                    }
                    syn::Fields::Unnamed(fields) => {
                        if fields.unnamed.len() != 1 {
                            return Err(Error::new(
                                variant.fields.span(),
                                "unnamed enums must be tuples of single values",
                            ));
                        }
                        let field = fields.unnamed.first().unwrap();
                        // Ipv4(Ipv4Addr)
                        let ident = get_unnamed_enum_type(field)?;

                        match_arms.push(quote! {
                            if reader.peek(#ident::FIXED_TAG as u8) {
                                return Ok(#type_name::#enum_name(tlv::Deserialize::deserialize(reader)?));
                            }
                        });
                    }
                    _ => {
                        return Err(Error::new(
                            proc_macro2::Span::call_site(),
                            "can only work on named fields",
                        ));
                    }
                };
            }

            let match_arms = TokenStream::from_iter(match_arms);

            if untag {
                Ok(quote! {
                    impl tlv::Deserialize for #type_name {
                        const FIXED_TAG: tlv::Tag = tlv::Tag::Unknown;

                        fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
                            #match_arms

                            Err(tlv::BAD_MESSAGE)
                        }
                    }
                })
            } else {
                Ok(quote! {
                    impl tlv::Deserialize for #type_name {
                        const FIXED_TAG: tlv::Tag = tlv::Tag::RawBytes;

                        fn deserialize(reader: &mut untrusted::Reader) -> Result<Self, &'static str> {
                            let inner = tlv::utils::except_tag_and_get_value(reader, Self::FIXED_TAG)?;
                            let ordinal = inner.as_slice_less_safe()[0];

                            match ordinal {
                                #match_arms
                                _ => Err(tlv::BAD_MESSAGE),
                            }
                        }
                    }
                })
            }
        }
        Data::Union(_) => Err(Error::new(
            input.span(),
            "#[derive(Serialize)] is only defined for structs or enums",
        )),
    }
}
