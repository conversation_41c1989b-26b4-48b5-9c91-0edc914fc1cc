#[cfg(unix)]
use once_cell::sync::Lazy;
#[cfg(unix)]
use std::{env, fs, os::unix::fs::PermissionsExt};
use std::{
    future::Future,
    io,
    pin::Pin,
    task::{Context, Poll},
};

use parity_tokio_ipc::Endpoint as IpcEndpoint;
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};
use tonic::transport::{
    server::Connected, Certificate, ClientTlsConfig, Endpoint, Identity, Server, ServerTlsConfig,
    Uri,
};
pub use tonic::{async_trait, transport::Channel, Code, Request, Response, Status};
use tower::service_fn;

pub use types::communicate_service_server::{CommunicateService, CommunicateServiceServer};

pub mod types;

pub type ControlServiceClient =
    types::communicate_service_client::CommunicateServiceClient<Channel>;

#[cfg(unix)]
static SDP_COMMUNICATE_SOCKET_GROUP: Lazy<Option<String>> =
    Lazy::new(|| env::var("SDP_COMMUNICATE_SOCKET_GROUP").ok());

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Communicate RPC server or client error")]
    GrpcTransportError(#[source] tonic::transport::Error),

    #[error("RPC ca error")]
    GrpcCaError(#[source] tonic::transport::Error),

    #[error("Failed to start IPC pipe/socket")]
    StartServerError(#[source] io::Error),

    #[error("Failed to initialize pipe/socket security attributes")]
    SecurityAttributes(#[source] io::Error),

    #[error("Unable to set permissions for IPC endpoint")]
    PermissionsError(#[source] io::Error),

    #[cfg(unix)]
    #[error("Group not found")]
    NoGidError,

    #[cfg(unix)]
    #[error("Failed to obtain group ID")]
    ObtainGidError(#[source] nix::Error),

    #[cfg(unix)]
    #[error("Failed to set group ID")]
    SetGidError(#[source] nix::Error),
}

pub async fn new_rpc_client() -> Result<ControlServiceClient, Error> {
    use futures::TryFutureExt;

    let ipc_path = paths::get_rpc_socket_path();
    let ca = include_bytes!("../../attachments/ca.crt");
    let cert = include_bytes!("../../attachments/client.crt");
    let key = include_bytes!("../../attachments/client.key");
    // The URI will be ignored
    let channel = Endpoint::from_static("https://[::]:50041")
        .tls_config(
            ClientTlsConfig::new()
                .domain_name("localhost".to_string())
                .ca_certificate(Certificate::from_pem(ca))
                .identity(Identity::from_pem(cert, key)),
        )
        .map_err(Error::GrpcCaError)?
        .connect_with_connector(service_fn(move |_: Uri| {
            IpcEndpoint::connect(ipc_path.clone()).map_ok(hyper_util::rt::tokio::TokioIo::new)
        }))
        .await
        .map_err(Error::GrpcTransportError)?;

    Ok(ControlServiceClient::new(channel))
}

pub type ServerJoinHandle = tokio::task::JoinHandle<Result<(), Error>>;

pub async fn spawn_rpc_server<T, F>(
    service: T,
    abort_rx: F,
    rpc_socket_path: impl AsRef<std::path::Path>,
) -> Result<ServerJoinHandle, Error>
where
    T: CommunicateService,
    F: Future<Output = ()> + Send + 'static,
{
    use futures::stream::TryStreamExt;
    use parity_tokio_ipc::SecurityAttributes;

    let mut endpoint = IpcEndpoint::new(rpc_socket_path.as_ref().to_string_lossy().to_string());
    endpoint.set_security_attributes(
        SecurityAttributes::allow_everyone_create()
            .map_err(Error::SecurityAttributes)?
            .set_mode(0o766)
            .map_err(Error::SecurityAttributes)?,
    );
    let incoming = endpoint.incoming().map_err(Error::StartServerError)?;

    #[cfg(unix)]
    if let Some(group_name) = &*SDP_COMMUNICATE_SOCKET_GROUP {
        let group = nix::unistd::Group::from_name(group_name)
            .map_err(Error::ObtainGidError)?
            .ok_or(Error::NoGidError)?;
        nix::unistd::chown(rpc_socket_path.as_ref(), None, Some(group.gid))
            .map_err(Error::SetGidError)?;
        fs::set_permissions(rpc_socket_path, PermissionsExt::from_mode(0o760))
            .map_err(Error::PermissionsError)?;
    }

    Ok(tokio::spawn(async move {
        let ca = include_bytes!("../../attachments/ca.crt");
        let cert = include_bytes!("../../attachments/server.crt");
        let key = include_bytes!("../../attachments/server.key");
        Server::builder()
            .tls_config(
                ServerTlsConfig::new()
                    .client_auth_optional(false)
                    .client_ca_root(Certificate::from_pem(ca))
                    .identity(Identity::from_pem(cert, key)),
            )
            .map_err(Error::GrpcCaError)?
            .add_service(CommunicateServiceServer::new(service))
            .serve_with_incoming_shutdown(incoming.map_ok(StreamBox), abort_rx)
            .await
            .map_err(Error::GrpcTransportError)
    }))
}

#[derive(Debug)]
struct StreamBox<T: AsyncRead + AsyncWrite>(pub T);
impl<T: AsyncRead + AsyncWrite> Connected for StreamBox<T> {
    type ConnectInfo = Option<()>;

    fn connect_info(&self) -> Self::ConnectInfo {
        None
    }
}
impl<T: AsyncRead + AsyncWrite + Unpin> AsyncRead for StreamBox<T> {
    fn poll_read(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<io::Result<()>> {
        Pin::new(&mut self.0).poll_read(cx, buf)
    }
}
impl<T: AsyncRead + AsyncWrite + Unpin> AsyncWrite for StreamBox<T> {
    fn poll_write(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<io::Result<usize>> {
        Pin::new(&mut self.0).poll_write(cx, buf)
    }

    fn poll_flush(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<io::Result<()>> {
        Pin::new(&mut self.0).poll_flush(cx)
    }

    fn poll_shutdown(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<io::Result<()>> {
        Pin::new(&mut self.0).poll_shutdown(cx)
    }
}
