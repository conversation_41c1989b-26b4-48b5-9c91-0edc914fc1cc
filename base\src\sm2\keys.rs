use asn1::{BitString, ObjectIdentifier};

#[derive(asn1::Asn1Read, asn1::Asn1Write)]
pub struct AlgorithmIdentifier<'a> {
    pub algorithm: ObjectIdentifier,
    pub parameters: asn1::Tlv<'a>,
}

#[derive(asn1::Asn1Read, asn1::Asn1Write)]
pub struct PkixPublicKey<'a> {
    pub algo: AlgorithmIdentifier<'a>,
    pub pub_bytes: BitString<'a>,
}

#[derive(asn1::Asn1Read, asn1::Asn1Write)]
struct Pkcs8<'a> {
    version: i32,
    algo: AlgorithmIdentifier<'a>,
    private_key: &'a [u8],
}

#[derive(asn1::Asn1Read, asn1::Asn1Write)]
struct EcPrivateKey<'a> {
    version: i32,
    private_key: &'a [u8],
    #[explicit(0)]
    named_curve_oid: Option<ObjectIdentifier>,
    #[explicit(1)]
    publick_key: Option<BitString<'a>>,
}

#[cfg(test)]
mod tests {
    use gm_sm2::key::{Sm2Model, Sm2PrivateKey, Sm2PublicKey};
    use pkcs8::{DecodePrivateKey, DecodePublicKey};

    #[test]
    fn test_load_key() {
        let pem_pubkey = "-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEezbjVaIslYVk4aXhJNd/apOcc9hu
iHb0xrdDHqhpgc1vtOFLNhQ4jS1C7q8g0aTNy/pJGeKDJlyV96tCyHg/5A==
-----END PUBLIC KEY-----"
            .to_string();

        let pem_prikey = "-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgTITLdgYONqYEc6YW
E4H+/JPCVJNeyshHbDryCkFYIPugCgYIKoEcz1UBgi2hRANCAAR7NuNVoiyVhWTh
peEk139qk5xz2G6IdvTGt0MeqGmBzW+04Us2FDiNLULuryDRpM3L+kkZ4oMmXJX3
q0LIeD/k
-----END PRIVATE KEY-----"
            .to_string();

        let pubkey = Sm2PublicKey::from_public_key_pem(&pem_pubkey).unwrap();
        let prikey = Sm2PrivateKey::from_pkcs8_pem(&pem_prikey).unwrap();

        let msg = b"luofei";

        let encrypted_text = pubkey.encrypt(msg, false, Sm2Model::C1C2C3).unwrap();

        let result = prikey
            .decrypt(&encrypted_text, false, Sm2Model::C1C2C3)
            .unwrap();
        assert_eq!(msg, &result[..]);
    }
}
