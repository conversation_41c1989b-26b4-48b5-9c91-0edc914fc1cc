import axios from "axios";
import { ElMessage, ElMessageBox } from "element-plus";
// import store from '@/store'
import axiosTauriAdapter from "axios-tauri-adapter";
import { emit } from "@tauri-apps/api/event";
// import Cookies from 'js-cookie'
import router from "../router";
import { invoke } from "@tauri-apps/api/tauri";

// create an axios instance
const service = axios.create({
  // baseURL: localStorage.getItem('endpoint'), // url = base url + request url
  baseURL: "",
  // withCredentials: true, // send cookies when cross-domain requests
  adapter: axiosTauriAdapter,
  timeout: 5000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  (config) => {
    // do something before request is sent

    // 格式化POST传递参数为Format Data格式
    if (config.method === "post") {
      config.headers["Content-Type"] = "application/json";
    }

    if (window.CONFIG.selected_tenant) {
      config.headers["TENANT"] = window.CONFIG.selected_tenant.code;
    }

    config.headers["user-agent"] = window.USER_AGENT;
    delete config.headers["origin"];

    if (config.url.indexOf("/v1/sdk/client/login/") === -1) {
      config.headers["Cookie"] += ";" + localStorage.getItem("responseCookie");
    }

    if (config.url.indexOf("/login-ticket") !== -1) {
      config.headers["origin"] =
        localStorage.getItem("ctl") || "http://sso.beta.jingantech.com";
    }

    config.headers["DEVICE-ID"] = window.DEVICE_ID;

    config.headers["APP-TYPE"] = "DESKTOP";

    config.withCredentials = true;

    return config;
  },
  (error) => {
    // do something with request error
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const res = response.data;
    // console.log("成功回调:", response);
    if (response.headers["set-cookie"]) {
      localStorage.setItem("responseCookie", response.headers["set-cookie"]);
    }

    // Blob
    if (response.status === 200 && res instanceof Blob) {
      return res;
    }

    // Text
    if (response.status === 200 && typeof res === "string") {
      return res;
    }

    // if the custom code is not 20000, it is judged as an error.
    if (
      res !== null &&
      res.code !== "SUCCESS" &&
      res.code !== 200 &&
      res.code !== 0 &&
      response.config.url.indexOf("/login-ticket") === -1
    ) {
      if (response.config && response.config.ignoreErrors) {
        return res;
      }
      if (res.data && res.data.messageKey === "MFA.MFA.AUTH.TIMEOUT") {
        return Promise.reject(res);
      }
      if (res.data && res.data.errorMsg) {
        ElMessage({
          message: res.data.errorMsg || "服务器连接失败",
          type: "error",
          duration: 3 * 1000,
        });
      }
      return Promise.reject(res || "服务器连接失败");
    } else {
      return res;
    }
  },
  (error) => {
    console.log("失败回调:", error);
    if (typeof error === "string") {
      return Promise.reject(error);
    } else {
      // 忽略错误
      if (error.config && error.config.ignoreErrors) {
        return Promise.reject(error);
      }

      if (
        (error.data && error.data.errorCode === "SYSTEM-0002") ||
        (error.response.data &&
          error.response.data.data &&
          error.response.data.data.errorCode === "SYSTEM-0002")
      ) {
        emit("log", { level: "Warn", message: "Session expired." });

        // #if [includeSDP]
        if (
          window.previousIAMAutoLogin ||
          window.previousUserLifecycleInvalid
        ) {
          return Promise.reject();
        }
        window.previousIAMAutoLogin = true;

        // 重新获取票据，自动登录
        window.loadResources();
        // #endif
        return Promise.reject(error.data || error.response.data);
      }

      if (
        error.response &&
        error.response.data &&
        error.response.data.data &&
        (error.response.data.data.messageKey === "MFA.CHECK.DENY" ||
          error.response.data.data.messageKey ===
            "NGIAM.SSO.APP.SSOCONFIG.NOT.FOUND")
      ) {
        return Promise.reject(error.response.data.data);
      }

      if (
        error.response.data.data &&
        (error.response.data.data.messageKey ===
          "BASE.SECURITY.USER.LIFECYCLE.NOT.ACTIVE" ||
          error.response.data.data.messageKey ===
            "BASE.SECURITY.USER.LIFECYCLE.EXPIRED")
      ) {
        if (
          ElMessageBox.count &&
          ElMessageBox.count() > 0 &&
          window.previousUserLifecycleInvalid
        ) {
          return Promise.reject(error.response.data.data);
        }

        window.previousUserLifecycleInvalid = true;
        ElMessageBox.confirm(error.response.data.data.errorMsg, "提示", {
          confirmButtonText: "确定",
          roundButton: true,
          center: true,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
        }).finally(() => {
          window.previousUserLifecycleInvalid = false;
          // #if [includeSDP]
          invoke("plugin:sdp|logout").then(() => {
            router.push("/login");
          });
          // #else
          router.push("/login");
          // #endif
        });
      } else {
        let msg =
          (error.data && error.data.errorMsg) ||
          (error.response.data.data && error.response.data.data.errorMsg);
        let statusCode = error.response.status;
        let url = error.config.url;
        if (msg) {
          emit("log", {
            level: "Error",
            message:
              `An error occurred while requesting API ${url}: ` +
              JSON.stringify(msg),
          });
          ElMessage({
            message: msg,
            type: "error",
            duration: 3 * 1000,
          });
        } else {
          emit("log", {
            level: "Error",
            message: `An error occurred while requesting API ${url}: ${statusCode}`,
          });
          return Promise.reject(error.data || error.response.statusText);
        }
      }
      return Promise.reject(error);
    }
  }
);

export default service;
