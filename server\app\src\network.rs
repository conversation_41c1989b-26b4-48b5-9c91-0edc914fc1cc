use std::net::IpAddr;

use ipnet::{Ipv4Net, Ipv6Net};
use pnet_base::MacAddr;

use crate::config::Interface;

#[allow(dead_code)]
#[derive(<PERSON>lone, Debug)]
pub struct Network {
    pub iface: String,
    pub ipv4net: Ipv4Net,
    pub ipv6net: Option<Ipv6Net>,
    pub mac: MacAddr,
    pub gateway: Option<Gateway>,
}

#[allow(dead_code)]
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug)]
pub struct Gateway {
    pub ip: IpAddr,
    pub mac: MacAddr,
}

pub fn info(interface: &Interface) -> Vec<Network> {
    let primary_network = get_network_info(&interface.primary.base.iface);

    let mut networks = vec![];
    networks.push(primary_network);

    if let Some(ref secondary) = interface.secondary {
        for interface in secondary {
            networks.push(get_network_info(&interface.base.iface));
        }
    }

    networks
}

fn get_network_info(iface: &str) -> Network {
    let interfaces = netdev::get_interfaces();
    let interface = interfaces
        .into_iter()
        .find(|interface| interface.name == iface)
        .unwrap_or_else(|| panic!("interface `{}` not found.", iface));

    let ipv4net = interface
        .ipv4
        .into_iter()
        .next()
        .unwrap_or_else(|| panic!("interface `{}` has no IPv4 address.", iface));

    let ipv6net = interface.ipv6.into_iter().next();

    let mac = interface
        .mac_addr
        .map(|mac| {
            let mac = mac.octets();
            MacAddr(mac[0], mac[1], mac[2], mac[3], mac[4], mac[5])
        })
        .unwrap_or_else(|| panic!("interface `{}` has no mac address.", iface));

    let gateway = interface.gateway.map(|network_device| {
        let mac = network_device.mac_addr.octets();
        Gateway {
            ip: IpAddr::V4(network_device.ipv4.first().unwrap().to_owned()),
            mac: MacAddr(mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]),
        }
    });

    Network {
        iface: iface.to_owned(),
        ipv4net,
        ipv6net,
        mac,
        gateway,
    }
}
