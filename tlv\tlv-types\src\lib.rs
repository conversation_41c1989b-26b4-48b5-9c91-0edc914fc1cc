use ipnet::{IpNet, Ipv4AddrRange, Ipv4Net, Ipv6AddrRange, Ipv6Net};
use serde_json::Value;
use std::{
    collections::HashSet,
    fmt::{Debug, Formatter},
    hash::Hash,
    net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddrV4, SocketAddrV6},
    ops::RangeInclusive,
};
use tlv::{Deserialize, Serialize};
use url::Url;

/// 认证数据包
#[derive(Debug, Serialize, Deserialize)]
pub struct AuthPacket {
    /// 设备ID
    #[tlv = "hex"]
    pub device_id: String,
    #[tlv = "json"]
    pub payload: Value,
}

/// 网关认证包
#[derive(Debug, Serialize, Deserialize)]
pub struct AuthGatewayPacket {
    /// Token.
    pub token: Vec<u8>,
    /// 租户编码
    pub tenant: String,
    /// 环境数据
    #[tlv = "json"]
    pub env: Value,
}

/// 认证结果
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AuthResult {
    /// Token.
    #[tlv = "hex"]
    pub token: String,
    /// 密码过期提醒时间.
    pub expire_tip_day: u32,
    /// 密码过期时间.
    pub expire_day: u32,
    /// DNS列表.
    #[tlv = "sequence"]
    pub dns: Option<Vec<IpAddr>>,
    /// 运行模式.
    pub data: ModePayload,
    /// 客户端IP地址(将来可能是分配给客户端的虚拟IP地址)
    pub client_ip: Option<IpAddr>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[repr(u8)]
pub enum ModePayload {
    Mix {
        /// 重连票据
        reconnect_token: String,
        /// 重连票据过期时间
        expire: u64,
    } = 0,
    Standard {
        /// 网关信息
        #[tlv = "json"]
        gateway: Value,
    } = 1,
}

/// 修改密码
#[derive(Debug, Serialize, Deserialize)]
pub struct ChangePwdByOldPwd {}

/// 验证码修改密码
#[derive(Debug, Serialize, Deserialize)]
pub struct ChangePwdByVerifyCode {
    /// 设备ID
    #[tlv = "hex"]
    pub device_id: String,
    /// 修改密码数据.
    pub data: Value,
}

/// MFA认证完成
#[derive(Debug, Serialize, Deserialize)]
pub struct MfaCompleteAuth {
    /// 认证结果ID
    pub auth_result_id: String,
    /// 访问资源
    pub resource: AccessResource,
}

/// MFA 认证时资源
#[derive(Debug, Serialize, Deserialize)]
#[tlv = "untag"]
pub enum AccessResource {
    Ipv4(Ipv4Addr),
    Ipv6(Ipv6Addr),
    SocketAddrV4(SocketAddrV4),
    SocketAddrV6(SocketAddrV6),
}

impl AccessResource {
    pub fn ip(&self) -> IpAddr {
        match self {
            AccessResource::Ipv4(ip) => IpAddr::V4(*ip),
            AccessResource::Ipv6(ip) => IpAddr::V6(*ip),
            AccessResource::SocketAddrV4(addr) => IpAddr::V4(*addr.ip()),
            AccessResource::SocketAddrV6(addr) => IpAddr::V6(*addr.ip()),
        }
    }
}

/// 白名单列表
#[derive(Debug, Serialize, Deserialize)]
pub struct Whitelist {
    #[tlv = "sequence"]
    pub ips: Option<Vec<IpAddr>>,
}

/// 资源列表
#[derive(Debug, Serialize, Deserialize)]
pub struct ResourceList {
    #[tlv = "sequence"]
    pub resources: Option<Vec<Resource>>,
}

/// 增量资源列表
#[derive(Debug, Serialize, Deserialize)]
pub struct IncrResourceList {
    /// 是否删除.
    pub flag: bool,
    #[tlv = "sequence"]
    pub resources: Option<Vec<Resource>>,
}

#[derive(Debug, Serialize, Deserialize)]
#[tlv = "untag"]
pub enum Resource {
    Ipv4(Ipv4Addr),
    Ipv6(Ipv6Addr),
    Ipv4Net(Ipv4Net),
    Ipv6Net(Ipv6Net),
    Ipv4Range(Ipv4AddrRange),
    Ipv6Range(Ipv6AddrRange),
}

impl From<Resource> for Vec<IpNet> {
    fn from(resource: Resource) -> Self {
        match resource {
            Resource::Ipv4(ip) => vec![IpNet::V4(Ipv4Net::from(ip))],
            Resource::Ipv6(ip) => vec![IpNet::V6(Ipv6Net::from(ip))],
            Resource::Ipv4Net(ip) => vec![IpNet::V4(ip).trunc()],
            Resource::Ipv6Net(ip) => vec![IpNet::V6(ip).trunc()],
            Resource::Ipv4Range(range) => {
                let start = Iterator::min(range).unwrap();
                let end = Iterator::max(range).unwrap();

                tlv::utils::range_to_cidr(start, end)
            }
            Resource::Ipv6Range(range) => {
                let start = Iterator::min(range).unwrap();
                let end = Iterator::max(range).unwrap();

                tlv::utils::range_to_cidr(start, end)
            }
        }
    }
}

/// 代理请求
#[derive(Debug, Serialize, Deserialize)]
pub struct ProxyRequest {
    /// 设备ID
    #[tlv = "hex"]
    pub device_id: String,
    /// 请求序号
    pub seq: u64,
    /// HTTP 请求
    pub data: Vec<u8>,
}

/// 代理响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ProxyResponse {
    /// 请求序号
    pub seq: u64,
    /// 响应消息
    pub data: Vec<u8>,
    /// 是否分块
    pub is_chunk: bool,
}

/// 响应chunk数据包包
#[derive(Debug, Serialize, Deserialize)]
pub struct ResponseChunk {
    /// 请求序号
    pub seq: u64,
    /// 响应消息
    pub data: Vec<u8>,
}

/// 代理请求失败
#[derive(Debug, Serialize, Deserialize)]
pub struct ProxyFail {
    /// 请求序号
    pub seq: u64,
    /// 失败信息
    pub message: String,
}

/// WEB应用列表
#[derive(Debug, Serialize, Deserialize)]
pub struct WebApps {
    /// 新增还是删除.
    pub flag: bool,
    /// 应用列表
    #[tlv = "json"]
    pub apps: Vec<WebApp>,
}

#[derive(serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone)]
pub struct WebApp {
    pub url: Url,
    pub ips: Option<Vec<BackendIp>>,
    pub cidrs: Option<Vec<Cidr>>,
    pub ranges: Option<Vec<Range>>,
}

impl WebApp {
    pub fn only_one_ip(&self) -> Option<IpAddr> {
        if self.cidrs.as_deref().is_some_and(|cidrs| !cidrs.is_empty()) {
            return None;
        }

        if self
            .ranges
            .as_deref()
            .is_some_and(|ranges| !ranges.is_empty())
        {
            return None;
        }

        if self.ips.is_none() {
            return None;
        }

        let ips = self.ips.as_deref().unwrap();
        if ips.len() == 1 {
            return Some(ips[0].ip);
        }

        None
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone)]
pub struct BackendIp {
    pub ip: IpAddr,
}

impl Hash for WebApp {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.url.hash(state);
    }
}

impl Debug for WebApp {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("WebApp")
            .field("url", &format!("{}", self.url))
            .field("ips", &self.ips)
            .field("cidrs", &self.cidrs)
            .field("ranges", &self.ranges)
            .finish()
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone)]
pub struct Range {
    pub start: IpAddr,
    pub end: IpAddr,

    #[serde(skip_serializing)]
    pub ports: Option<HashSet<Port>>,
}

impl Range {
    pub fn contains(&self, target: &IpAddr) -> bool {
        *target >= self.start && *target <= self.end
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize, PartialEq, Eq, Clone)]
pub struct Cidr {
    pub prefix: u8,
    pub ip: IpAddr,

    #[serde(skip_serializing)]
    pub ports: Option<HashSet<Port>>,
}

impl Cidr {
    pub fn contains(&self, target: &IpAddr) -> bool {
        let ip_net = IpNet::new(self.ip, self.prefix).unwrap();
        ip_net.contains(target)
    }
}

#[derive(Debug, serde::Deserialize, Eq, PartialEq, Hash, Clone)]
#[serde(untagged)]
pub enum Port {
    Single(u16),
    Range(RangeInclusive<u16>),
}

impl Port {
    pub fn contains(&self, target: u16) -> bool {
        match self {
            Port::Single(port) => *port == target,
            Port::Range(range) => range.contains(&target),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VirtualApps {
    pub flag: bool,
    #[tlv = "sequence"]
    pub mapping: Vec<VirtualIpAddr>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VirtualIpAddr {
    pub real: IpAddr,
    pub virtual_ip: IpAddr,
}
