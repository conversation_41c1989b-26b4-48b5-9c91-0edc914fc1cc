use crate::types::{proto, FromProtobufTypeError};

impl From<sdptypes::states::TunnelState> for proto::TunnelState {
    fn from(state: sdptypes::states::TunnelState) -> Self {
        use proto::error_state::Cause;
        use sdptypes::states::TunnelState as SdpTunnelState;

        use sdptypes::tunnel as sdp_tunnel;

        let state = match state {
            SdpTunnelState::Disconnected => {
                proto::tunnel_state::State::Disconnected(proto::tunnel_state::Disconnected {})
            }
            SdpTunnelState::Connecting => {
                proto::tunnel_state::State::Connecting(proto::tunnel_state::Connecting {})
            }
            SdpTunnelState::Connected => {
                proto::tunnel_state::State::Connected(proto::tunnel_state::Connected {})
            }
            SdpTunnelState::Disconnecting(after_disconnect) => {
                proto::tunnel_state::State::Disconnecting(proto::tunnel_state::Disconnecting {
                    after_disconnect: match after_disconnect {
                        sdp_tunnel::ActionAfterDisconnect::Nothing => {
                            i32::from(proto::AfterDisconnect::Nothing)
                        }
                        sdp_tunnel::ActionAfterDisconnect::Reconnect => {
                            i32::from(proto::AfterDisconnect::Reconnect)
                        }
                        sdp_tunnel::ActionAfterDisconnect::Block => {
                            i32::from(proto::AfterDisconnect::Nothing)
                        }
                    },
                })
            }
            SdpTunnelState::Error(error_state) => {
                proto::tunnel_state::State::Error(proto::tunnel_state::Error {
                    error_state: Some(proto::ErrorState {
                        cause: match error_state.cause() {
                            sdp_tunnel::ErrorStateCause::Ipv6Unavailable => {
                                i32::from(Cause::Ipv6Unavailable)
                            }
                            sdp_tunnel::ErrorStateCause::SetDnsError => {
                                i32::from(Cause::SetDnsError)
                            }
                            sdp_tunnel::ErrorStateCause::StartTunnelError => {
                                i32::from(Cause::StartTunnelError)
                            }
                            sdp_tunnel::ErrorStateCause::IsOffline => i32::from(Cause::IsOffline),
                            sdp_tunnel::ErrorStateCause::TunnelParameterMissing => {
                                i32::from(Cause::TunnelParameterMissing)
                            }
                            sdp_tunnel::ErrorStateCause::SetRouteError => {
                                i32::from(Cause::SetRouteError)
                            }
                        },
                    }),
                })
            }
        };

        proto::TunnelState { state: Some(state) }
    }
}

impl TryFrom<proto::TunnelState> for sdptypes::states::TunnelState {
    type Error = FromProtobufTypeError;

    fn try_from(state: proto::TunnelState) -> Result<Self, FromProtobufTypeError> {
        use sdptypes::{states::TunnelState as SdpState, tunnel as sdp_tunnel};

        let state = match state.state {
            Some(proto::tunnel_state::State::Disconnected(_)) => SdpState::Disconnected,
            Some(proto::tunnel_state::State::Connecting(proto::tunnel_state::Connecting {})) => {
                SdpState::Connecting {}
            }
            Some(proto::tunnel_state::State::Connected(proto::tunnel_state::Connected {})) => {
                SdpState::Connected {}
            }
            Some(proto::tunnel_state::State::Disconnecting(
                proto::tunnel_state::Disconnecting { after_disconnect },
            )) => {
                SdpState::Disconnecting(match proto::AfterDisconnect::try_from(after_disconnect) {
                    Ok(proto::AfterDisconnect::Nothing) => {
                        sdp_tunnel::ActionAfterDisconnect::Nothing
                    }
                    Ok(proto::AfterDisconnect::Reconnect) => {
                        sdp_tunnel::ActionAfterDisconnect::Reconnect
                    }
                    _ => {
                        return Err(FromProtobufTypeError::InvalidArgument(
                            "invalid \"after_disconnect\" action",
                        ))
                    }
                })
            }
            Some(proto::tunnel_state::State::Error(proto::tunnel_state::Error {
                error_state: Some(proto::ErrorState { cause }),
            })) => {
                let cause = match proto::error_state::Cause::try_from(cause) {
                    Ok(proto::error_state::Cause::Ipv6Unavailable) => {
                        sdp_tunnel::ErrorStateCause::Ipv6Unavailable
                    }
                    Ok(proto::error_state::Cause::IsOffline) => {
                        sdp_tunnel::ErrorStateCause::IsOffline
                    }
                    Ok(proto::error_state::Cause::SetDnsError) => {
                        sdp_tunnel::ErrorStateCause::SetDnsError
                    }
                    Ok(proto::error_state::Cause::StartTunnelError) => {
                        sdp_tunnel::ErrorStateCause::StartTunnelError
                    }
                    _ => {
                        return Err(FromProtobufTypeError::InvalidArgument(
                            "invalid error cause",
                        ))
                    }
                };

                SdpState::Error(sdp_tunnel::ErrorState::new(cause))
            }
            _ => {
                return Err(FromProtobufTypeError::InvalidArgument(
                    "invalid tunnel state",
                ))
            }
        };

        Ok(state)
    }
}

impl From<sdptypes::states::BackendState> for proto::BackendState {
    fn from(state: sdptypes::states::BackendState) -> Self {
        use proto::backend_error_state::Cause;
        use sdptypes::{backend as sdp_backend, states::BackendState};

        match state {
            BackendState::Disconnected(reason) => proto::BackendState {
                state: Some(proto::backend_state::State::Disconnected(
                    proto::backend_state::Disconnected {
                        reason: reason.map(|reason| reason as i32),
                    },
                )),
            },
            BackendState::ProxyConnecting => proto::BackendState {
                state: Some(proto::backend_state::State::ProxyConnecting(
                    proto::backend_state::ProxyConnecting {},
                )),
            },
            BackendState::ProxyConnected => proto::BackendState {
                state: Some(proto::backend_state::State::ProxyConnected(
                    proto::backend_state::ProxyConnected {},
                )),
            },
            BackendState::Connected => proto::BackendState {
                state: Some(proto::backend_state::State::Connected(
                    proto::backend_state::Connected {},
                )),
            },
            BackendState::Connecting => proto::BackendState {
                state: Some(proto::backend_state::State::Connecting(
                    proto::backend_state::Connecting {},
                )),
            },
            BackendState::Disconnecting => proto::BackendState {
                state: Some(proto::backend_state::State::Disconnecting(
                    proto::backend_state::Disconnecting {},
                )),
            },
            BackendState::Error(error_state) => {
                let state = proto::backend_state::State::Error(proto::backend_state::Error {
                    error_state: Some(proto::BackendErrorState {
                        cause: match error_state.cause() {
                            sdp_backend::ErrorStateCause::AuthFailed => {
                                i32::from(Cause::AuthFailed)
                            }
                            sdp_backend::ErrorStateCause::ConnectTimeout => {
                                i32::from(Cause::ConnectTimeout)
                            }
                            sdp_backend::ErrorStateCause::NeedChangePwd => {
                                i32::from(Cause::NeedChangePwd)
                            }
                        },
                    }),
                });
                proto::BackendState { state: Some(state) }
            }
        }
    }
}

impl TryFrom<proto::BackendState> for sdptypes::states::BackendState {
    type Error = FromProtobufTypeError;

    fn try_from(state: proto::BackendState) -> Result<Self, FromProtobufTypeError> {
        use sdptypes::{backend as sdp_backend, states::BackendState};

        match state.state {
            Some(proto::backend_state::State::Disconnected(state)) => Ok(
                BackendState::Disconnected(state.reason.map(|reason| reason as i8)),
            ),
            Some(proto::backend_state::State::ProxyConnecting(_)) => {
                Ok(BackendState::ProxyConnecting)
            }
            Some(proto::backend_state::State::ProxyConnected(_)) => {
                Ok(BackendState::ProxyConnected)
            }
            Some(proto::backend_state::State::Connected(_)) => Ok(BackendState::Connected),
            Some(proto::backend_state::State::Connecting(_)) => Ok(BackendState::Connecting),
            Some(proto::backend_state::State::Disconnecting(_)) => Ok(BackendState::Disconnecting),
            Some(proto::backend_state::State::Error(proto::backend_state::Error {
                error_state: Some(proto::BackendErrorState { cause }),
            })) => {
                let cause = match proto::backend_error_state::Cause::try_from(cause) {
                    Ok(proto::backend_error_state::Cause::AuthFailed) => {
                        sdp_backend::ErrorStateCause::AuthFailed
                    }
                    Ok(proto::backend_error_state::Cause::ConnectTimeout) => {
                        sdp_backend::ErrorStateCause::ConnectTimeout
                    }
                    Ok(proto::backend_error_state::Cause::NeedChangePwd) => {
                        sdp_backend::ErrorStateCause::NeedChangePwd
                    }
                    _ => {
                        return Err(FromProtobufTypeError::InvalidArgument(
                            "invalid error cause",
                        ))
                    }
                };

                Ok(BackendState::Error(sdp_backend::ErrorState::new(cause)))
            }
            _ => Err(FromProtobufTypeError::InvalidArgument(
                "invalid backend state",
            )),
        }
    }
}
