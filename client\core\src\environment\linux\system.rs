use serde_json::{json, Value};
use sysinfo::System;

pub fn info(device_id: &str) -> Value {
    let mac = crate::environment::interface::list()
        .unwrap_or_default()
        .into_iter()
        .map(|mut value| value["mac"].take())
        .filter(|f| f.is_string())
        .map(|mac| mac.as_str().map(String::from).unwrap_or_default())
        .collect::<Vec<String>>()
        .join(",");

    let hw_info = systeminfo::from_system_hardware();

    json!({
        "deviceId": device_id,
        "sysType": "Linux",
        "kernelVersion": System::kernel_version().unwrap_or_default(),
        "operatingSystem": System::long_os_version().unwrap_or_default(),
        "systemVersion": System::os_version().unwrap_or_default(),
        "clientVersion": version::FILE_VERSION,
        "mac": mac,
        "name": System::host_name().unwrap_or_default(),
        "manufacturer": hw_info.system_manufacturer,
    })
}
