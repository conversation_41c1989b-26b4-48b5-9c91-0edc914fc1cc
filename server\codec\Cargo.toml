[package]
name = "codec"
version = "0.0.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
default = ["std", "bytes"]
alloc = []
std = ["alloc"]

[dependencies]
byteorder = { version = "1.1", default-features = false }
bytes = { version = "1", default-features = false, optional = true }
zerocopy = { version = "0.7", features = ["derive"] }

[package.metadata.kani]
flags = { tests = true }
unstable = { stubbing = true }

[lints.rust.unexpected_cfgs]
level = "warn"
check-cfg = ['cfg(kani)']
