<template>
  <div class="mfa-content" style="margin-top: 61px; padding-bottom: 61px">
    <div class="auth-code-content border">
      <el-input
        v-model="authCode"
        class="auth-input"
        placeholder="请输入微信动态口令"
      />
    </div>
    <el-button
      ref="confirmBtn"
      type="primary"
      class="mfa-confirm-btn"
      :disabled="!authCode || submitting"
      @click="submitAuth"
      >确定</el-button
    >
  </div>
</template>

<script>
import { getRequestId, verifyOtpsCode, cancelAuth } from "@/api/service";

export default {
  name: "WECHATOTP",
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      requestId: "",
      authCode: "",
      code: "wechat_otp",
      reg: /^\d+$/,
      submitting: false,
    };
  },
  created() {
    this.getRequestId();
  },
  methods: {
    // 获取requestId
    getRequestId() {
      getRequestId(this.participantGroupId, "01", this.userId, this.code).then(
        (result) => {
          this.requestId = result.data;
          this.$emit("requestId", this.requestId);
        }
      );
    },
    /**
     * 提交认证
     */
    async submitAuth() {
      try {
        this.subbmitting = true;
        await this.Complete();
      } finally {
        this.subbmitting = false;
      }
    },
    Complete() {
      if (!this.authCode) {
        this.alert.error("请输入微信动态口令");
        return;
      }

      if (this.authCode && !this.reg.test(this.authCode)) {
        this.alert.error("微信动态口令格式错误");
        return;
      }

      return verifyOtpsCode({
        requestId: this.requestId,
        authCode: this.authCode,
        method: this.code,
      }).then(() => {
        this.$emit("mfaCallbackFunc", "wechat_code 认证完成啦");
      });
    },
    cancel() {
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
    },
  },
};
</script>

<style scoped lang="less">
.mfa-content {
  .auth-code-content {
    width: 100%;
    margin: auto;
    text-align: center;

    .auth-code-input {
      width: 100%;
      margin-bottom: 30px;
      height: 40px;
    }
  }

  :deep(.el-input__wrapper) {
    border: 0 !important;
    padding: 1px 1px;
    border-radius: 20px;
  }
}
</style>
