import { getAll } from "@tauri-apps/api/window";
import { getUserAgreement } from "@/api/service";
import { invoke } from "@tauri-apps/api";

export const openUserAgreement = async () => {
  let windows = getAll();
  for (let window of windows) {
    if (window.label === "user_agreement") {
      await window.unminimize();
      await window.show();
      await window.setFocus();
      return;
    }
  }

  let content = await getUserAgreement();

  await invoke("user_agreement", {
    url: "data:text/html," + content,
  });
};
