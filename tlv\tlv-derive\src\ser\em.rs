use proc_macro2::TokenStream;
use quote::quote;
use syn::{spanned::Spanned, <PERSON><PERSON><PERSON>, Fields};

pub(super) fn expand_named_fields(fields: &Fields) -> syn::Result<(TokenStream, TokenStream)> {
    if !matches!(fields, Fields::Named(_)) {
        return Err(Error::new(
            proc_macro2::Span::call_site(),
            "can only work on named fields",
        ));
    }

    let mut enum_fields = vec![];
    let mut struct_fields = vec![];
    for field in fields {
        let field_name = field.ident.as_ref().unwrap();

        let token_streams = if let Some(r#type) = crate::get_tlv_value(&field.attrs)? {
            match r#type.as_str() {
                "json" => {
                    quote! {
                        let mut field_bytes = serde_json::to_vec(&#field_name).unwrap();
                        tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);

                        bytes.extend(field_bytes);
                    }
                }
                "hex" => {
                    quote! {
                        let mut field_bytes = hex::decode!(#field_name.as_bytes());
                        tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);

                        bytes.extend(field_bytes);
                    }
                }
                "sequence" => {
                    quote! {
                        let mut seq_bytes = vec![];
                        for ele in #field_name.iter() {
                            seq_bytes.extend(tlv::Serialize::serialize(ele));
                        }

                        let len = seq_bytes.len();
                        let mut len_bytes = tlv::utils::len_to_bytes(len);
                        len_bytes.insert(0, tlv::Tag::Sequence as u8);

                        bytes.extend(len_bytes);
                        bytes.extend(seq_bytes);
                    }
                }
                "sequence,json" => {
                    quote! {
                        let mut seq_bytes = vec![];
                        for ele in #field_name.iter() {
                            let mut field_bytes = serde_json::to_vec(ele).unwrap();
                            tlv::utils::insert_tag(&mut field_bytes, tlv::Tag::RawBytes);
                            seq_bytes.extend(field_bytes);
                        }

                        let len = seq_bytes.len();
                        let mut len_bytes = tlv::utils::len_to_bytes(len);
                        len_bytes.insert(0, tlv::Tag::Sequence as u8);

                        bytes.extend(len_bytes);
                        bytes.extend(seq_bytes);
                    }
                }
                _ => {
                    // never happen
                    return Err(Error::new(
                        field.span(),
                        format!("unknown value `{}`", r#type),
                    ));
                }
            }
        } else {
            quote! {
                let field_bytes = tlv::Serialize::serialize(#field_name);
                bytes.extend(field_bytes);
            }
        };
        enum_fields.push(quote!(#field_name,));
        struct_fields.push(token_streams);
    }

    let enum_fields = TokenStream::from_iter(enum_fields);
    let struct_fields = TokenStream::from_iter(struct_fields);

    Ok((enum_fields, struct_fields))
}
