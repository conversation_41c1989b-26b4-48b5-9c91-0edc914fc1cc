use futures::StreamExt;

use types::backend::{BackendStateTransition, ErrorStateCause};

use crate::{
    backend_state_machine::{
        connecting_state::ConnectingState, disconnected_state::DisconnectedState,
        proxy_connecting_state::ProxyConnectingState, BackendCommand, BackendCommandReceiver,
        BackendState, BackendStateWrapper, SharedBackendStateValues,
    },
    EventListener,
};

use super::EventConsequence;

#[allow(dead_code)]
pub struct ErrorState {
    block_reason: ErrorStateCause,
}

#[async_trait::async_trait]
impl<L> BackendState<L> for ErrorState
where
    L: EventListener + Send + Clone + 'static,
{
    type Bootstrap = ErrorStateCause;

    async fn enter(
        _shared_values: &mut SharedBackendStateValues<L>,
        block_reason: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition) {
        (
            BackendStateWrapper::from(ErrorState {
                block_reason: block_reason.clone(),
            }),
            BackendStateTransition::Error(types::backend::ErrorState::new(block_reason)),
        )
    }

    async fn handle_event(
        self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        use self::EventConsequence::*;

        match commands.next().await {
            Some(BackendCommand::Disconnect(tx)) => {
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                NewState(DisconnectedState::enter(shared_values, None).await)
            }
            Some(BackendCommand::ProxyConnect {
                tx,
                connection_config,
            }) => {
                NewState(ProxyConnectingState::enter(shared_values, (tx, connection_config)).await)
            }
            Some(BackendCommand::Login {
                tx,
                system_info,
                payload,
            }) => NewState(ConnectingState::enter(shared_values, (tx, system_info, payload)).await),
            Some(BackendCommand::ChangePwdByCode(tx, system_info, payload)) => {
                shared_values
                    .change_password_by_verify_code(tx, system_info, payload)
                    .await;
                NewState(DisconnectedState::enter(shared_values, None).await)
            }
            None => Finished,
            Some(_) => SameState(self.into()),
        }
    }
}
