#![no_std]
#![no_main]

#[allow(non_upper_case_globals)]
#[allow(non_snake_case)]
#[allow(non_camel_case_types)]
#[allow(dead_code)]
mod protocols;

#[allow(non_upper_case_globals)]
#[allow(non_snake_case)]
#[allow(non_camel_case_types)]
#[allow(dead_code)]
mod vmlinux;

mod options;

use core::mem;

use aya_ebpf::{
    bindings::xdp_action,
    macros::{map, xdp},
    maps::{Array, HashMap, XskMap},
    programs::XdpContext,
};
use options::*;
use protocols::{IPPROTO_ICMP, IPPROTO_TCP, IPPROTO_UDP};
use share::Addr;
use vmlinux::{arphdr, ethhdr, icmphdr, iphdr, tcphdr, udphdr};

/// SPA最小长度
/// 原始SPA最小长度 45 = 8(timestamp) + 16(device id) + 4(nonce) + 4(client ip) + 4(hotp) +
/// 8(counter) + 1(type) 加密后 142 = 45 + 97
/// 加上签名后的总长度 174 = 142 + 32
const MIN_SPA_PACKET_LEN: u16 = 174;

/// SPA最大长度
/// 原始SPA最小长度 45 = 8(timestamp) + 16(device id) + 4(nonce) + 4(client ip) + 4(hotp) +
/// 8(counter) + 1(type) 加上最大的`单位|用户名`字段 144 = 45 + 99
/// 加密后 241 = 144 + 97
/// 加上签名后的总长度 273 = 241 + 32
const MAX_SPA_PACKET_LEN: u16 = 273;

/// 本机对外开放端口
#[map(name = "PUB_PORTS")]
static mut PUB_PORTS: Array<u32> = Array::with_max_entries(10, 0);

/// 白名单
#[map(name = "WHITELIST")]
static mut WHITELIST: HashMap<Addr, u8> = HashMap::with_max_entries(WHITE_LIST_SIZE, 0);

/// 收到单包的客户端
#[map(name = "RECEIVED")]
static mut RECEIVED_SPA: HashMap<u64, u8> = HashMap::with_max_entries(CONNECTED_CLIENTS, 0);

/// 通过单包认证的客户端
/// 控制器网关分离时, 网关的连接需要先通过控制器认证
#[map(name = "PREAUTHED")]
static mut ALLOW_CONNECT: HashMap<u32, u8> = HashMap::with_max_entries(SPA_PASSED, 0);

/// 已经连接的客户端
#[map(name = "CONNECTED")]
static mut CONNECTED: HashMap<Addr, u8> = HashMap::with_max_entries(CONNECTED_CLIENTS, 0);

/// 将包转到用户程序处理
#[map(name = "SOCKETS")]
static mut PACKETS: XskMap = XskMap::with_max_entries(1024, 0);

/// 边界内资源列表
// #[cfg(any(feature = "gateway", feature = "mix"))]
// #[map(name = "RESOURCES")]
// static mut SERVER_LIST: HashMap<u32, u8> = HashMap::with_max_entries(RESOURCES_SIZE, 0);

// /// 控制器网关分离时, 网关通过SPA认证的客户端
// #[cfg(feature = "gateway")]
// #[map(name = "AUTHENTICATED")]
// static mut AUTHENTICATED: HashMap<u32, u8> = HashMap::with_max_entries(SPA_PASSED, 0);

/// NAT表
#[map(name = "NAT")]
static mut NAT_TABLE: HashMap<Addr, u8> = HashMap::with_max_entries(CONCURRENCY_ACCESS, 0);

/// VRRP 组播地址. **********
const VRRP_BROADCAST: u32 = 3758096402;

/// 本机IP
#[no_mangle]
static LOCAL_IP: u32 = 0;

/// VIP
#[no_mangle]
static VIP: u32 = 0;

/// SPA端口
#[no_mangle]
static SPA_PORT: u16 = 65321;

#[xdp]
pub fn sdp_p(ctx: XdpContext) -> u32 {
    let local_ip = unsafe { core::ptr::read_volatile(&LOCAL_IP) };

    let vip = unsafe { core::ptr::read_volatile(&VIP) };

    let spa_port = unsafe { core::ptr::read_volatile(&SPA_PORT) };

    try_sdp_xdp(ctx, local_ip, vip, spa_port).unwrap_or(xdp_action::XDP_DROP)
}

fn try_sdp_xdp(ctx: XdpContext, local_ip: u32, vip: u32, spa_port: u16) -> Result<u32, ()> {
    // info!(&ctx, "received a packet");

    let ethhdr: *const ethhdr = unsafe { ptr_at(&ctx, 0)? };
    let ether_type = u16::from_be(unsafe { *ethhdr }.h_proto);
    match ether_type {
        protocols::ETH_P_IP => {
            let ipv4hdr: *const iphdr = unsafe { ptr_at(&ctx, 14)? };
            let dst_ip = u32::from_be(unsafe { (*ipv4hdr).__bindgen_anon_1.addrs.daddr });

            // 本机流量, 虚拟IP流量或VRRP流量
            if dst_ip != local_ip && dst_ip != vip && dst_ip != VRRP_BROADCAST {
                return Ok(xdp_action::XDP_DROP);
            }

            return unsafe { handle_ipv4_packet(ctx, spa_port, ipv4hdr, vip) };
        }
        protocols::ETH_P_ARP => {
            let arphdr: *const arphdr = unsafe { ptr_at(&ctx, 14)? };

            if u16::from_be(unsafe { *arphdr }.ar_pro) == protocols::ETH_P_IP as u16 {
                let dst_ip: *const u32 = unsafe { ptr_at(&ctx, 14 + 24)? };
                let dst_ip = u32::from_be(unsafe { *dst_ip });
                if dst_ip == local_ip || dst_ip == vip {
                    return Ok(xdp_action::XDP_PASS);
                }
            }
        }
        _ => {}
    }

    Ok(xdp_action::XDP_DROP)
}

fn is_pub_port(dst_port: u16) -> bool {
    for index in 0..10 {
        match unsafe { PUB_PORTS.get(index) } {
            Some(&port) => {
                let start = (port >> 16) as u16;
                if start == 0 {
                    if port as u16 == dst_port {
                        return true;
                    }
                } else {
                    let end = (port & 0xffff) as u16;
                    if dst_port >= start && dst_port <= end {
                        return true;
                    }
                }
            }
            None => return false,
        }
    }
    false
}

unsafe fn in_whitelist(src_ip: u32, src_port: u16) -> bool {
    if WHITELIST.get(&Addr::new(src_ip, 0)).is_some() {
        return true;
    }

    if WHITELIST.get(&Addr::new(src_ip, src_port)).is_some() {
        return true;
    }
    false
}

fn is_spa_packet(udphdr: *const udphdr, spa_port: u16) -> bool {
    let dst_port = u16::from_be(unsafe { (*udphdr).dest });
    if dst_port != spa_port {
        return false;
    }

    let len = u16::from_be(unsafe { *udphdr }.len) - 8;
    len >= MIN_SPA_PACKET_LEN && len <= MAX_SPA_PACKET_LEN
}

/// fin + ack
fn is_waved_packet(tcphdr: *const tcphdr) -> bool {
    unsafe { *tcphdr }.fin() == 1 || unsafe { *tcphdr }.ack() == 1
}

#[inline(always)]
unsafe fn ptr_at<T>(ctx: &XdpContext, offset: usize) -> Result<*const T, ()> {
    let start = ctx.data();
    let end = ctx.data_end();
    let len = mem::size_of::<T>();

    if start + offset + len > end {
        return Err(());
    }

    let ptr = (start + offset) as *const T;
    Ok(&*ptr)
}

#[panic_handler]
fn panic(_info: &core::panic::PanicInfo) -> ! {
    unsafe { core::hint::unreachable_unchecked() }
}

#[inline]
unsafe fn handle_ipv4_packet(
    ctx: XdpContext,
    spa_port: u16,
    ipv4hdr: *const iphdr,
    vip: u32,
) -> Result<u32, ()> {
    let protocol = unsafe { *ipv4hdr }.protocol;
    let src_ip = u32::from_be((*ipv4hdr).__bindgen_anon_1.addrs.saddr);
    let addr = 14 + ((*ipv4hdr).ihl() * 4) as usize;
    match protocol {
        IPPROTO_UDP => {
            let udphdr: *const udphdr = ptr_at(&ctx, addr)?;
            let src_port = u16::from_be((*udphdr).source);
            let dst_port = u16::from_be((*udphdr).dest);

            // 本机开放端口
            if is_pub_port(dst_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            // 白名单
            if in_whitelist(src_ip, src_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            let socket_addr = (src_ip as u64) | (src_port as u64) << 32;
            // 收到重复的SPA包
            if RECEIVED_SPA.get(&socket_addr).is_some() {
                return Ok(xdp_action::XDP_DROP);
            }

            if NAT_TABLE.get(&Addr::new(src_ip, src_port)).is_some() {
                let queue_id = unsafe { (*ctx.ctx).rx_queue_index };
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }

            // 如果是单包认证
            if is_spa_packet(udphdr, spa_port) {
                _ = RECEIVED_SPA.insert(&socket_addr, &1, 0);
                let queue_id = (*ctx.ctx).rx_queue_index;
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }
            Ok(xdp_action::XDP_DROP)
        }
        IPPROTO_TCP => {
            let tcphdr: *const tcphdr = ptr_at(&ctx, addr)?;
            let src_port = u16::from_be((*tcphdr).source);
            let dst_port = u16::from_be((*tcphdr).dest);

            // 本机开放端口
            if is_pub_port(dst_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            // 白名单
            if in_whitelist(src_ip, src_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            if NAT_TABLE.get(&Addr::new(src_ip, src_port)).is_some() {
                let queue_id = (*ctx.ctx).rx_queue_index;
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }

            if CONNECTED.get(&Addr::new(src_ip, src_port)).is_some() {
                return Ok(xdp_action::XDP_PASS);
            }
            if ALLOW_CONNECT.get(&src_ip).is_some() {
                return Ok(xdp_action::XDP_PASS);
            }
            // 如果不满足上述情况, 则放开挥手的包
            if is_waved_packet(tcphdr) {
                return Ok(xdp_action::XDP_PASS);
            }
            Ok(xdp_action::XDP_DROP)
        }
        IPPROTO_ICMP => {
            let icmphdr: *const icmphdr = ptr_at(&ctx, addr)?;
            let identifier = u16::from_be((*icmphdr).un.echo.id);

            if WHITELIST.get(&Addr::new(src_ip, 0)).is_some() {
                return Ok(xdp_action::XDP_PASS);
            }

            if NAT_TABLE.get(&Addr::new(src_ip, identifier)).is_some() {
                let queue_id = (*ctx.ctx).rx_queue_index;
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }
            Ok(xdp_action::XDP_DROP)
        }
        // VRRP, 虚拟路由器冗余协议 https://protocol.aymar.cn/cd_feature_vrrp_message_format.html
        112 => {
            // 取第一个虚拟IP地址
            let first_vip: *const u32 = ptr_at(&ctx, addr + 8)?;
            let first_vip = u32::from_be(*first_vip);
            if first_vip != vip {
                return Ok(xdp_action::XDP_DROP);
            }
            Ok(xdp_action::XDP_PASS)
        }
        _ => Ok(xdp_action::XDP_DROP),
    }
}
