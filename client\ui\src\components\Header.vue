<template>
  <div class="app-title" data-tauri-drag-region>
    <!-- #if [platform=macos] -->
    <!-- <span
        v-if="isSettings"
        class="title-icon title-span"
        @click="backEvent"
    >返回</span>  -->
    <slot />
    <!-- #else -->
    <div class="app-title-right">
      <slot />
      <el-icon class="title-icon" :size="20" @click="minEvent">
        <Minus />
      </el-icon>
      <el-icon class="title-icon" :size="20" @click="closeEvent">
        <Close />
      </el-icon>
    </div>
    <!-- #endif -->
  </div>
</template>

<script>
import { appWindow } from "@tauri-apps/api/window";
import { invoke } from "@tauri-apps/api/tauri";
import { useStore } from "vuex";
import { computed } from "vue";

export default {
  name: "HeaderBar",
  setup() {
    const store = useStore();
    return {
      isLogin: computed(() => store.getters.isLogin),
    };
  },
  computed: {
    isSettings() {
      return (
        this.$router.currentRoute.value.path == "/setting" ||
        this.$router.currentRoute.value.path == "/about" ||
        this.$router.currentRoute.value.path == "/changePwd"
      );
    },
  },
  methods: {
    minEvent() {
      appWindow.minimize();
    },
    closeEvent() {
      if (this.isLogin) {
        appWindow.hide();
      } else {
        invoke("graceful_exit");
      }
    },
  },
};
</script>
<style lang="less" scoped>
.app-title {
  z-index: 1;
}

.title-icon:focus {
  outline: 0;
}
</style>

