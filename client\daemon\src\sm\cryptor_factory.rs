#[cfg(windows)]
use log::{error, info};
use serde::{Deserialize, Serialize};

#[cfg(windows)]
const TFA_GET_PUBLIC_KEY: &str = "/access-tfa-sso/v8/web/client/getPublicKey";

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ErrorDataDetial {
    pub error_code: Option<String>,
    pub error_msg: Option<String>,
    pub message_key: Option<String>,
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct ErrorInfoData {
    pub code: String,
    pub data: ErrorDataDetial,
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PubkeyInfo {
    pub manufacturer: i32,              // 加密厂商 0-默认，1-三未信安
    pub data_transmit_type: i32,        // 数据传输类型，0默认软加密，1使用全局配置
    pub public_key_str: Option<String>, // 公钥字符串，base64
}

impl PubkeyInfo {
    pub fn new() -> Self {
        PubkeyInfo {
            manufacturer: 0,
            data_transmit_type: 0,
            public_key_str: None,
        }
    }
}

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PubkeyInfoInfoResp {
    pub code: String,
    pub data: Option<PubkeyInfo>,
}

impl ErrorInfoData {
    pub fn new(code: String, error_msg: Option<String>) -> Self {
        ErrorInfoData {
            code,
            data: ErrorDataDetial {
                error_code: None,
                error_msg,
                message_key: None,
            },
        }
    }
}

#[cfg(windows)]
async fn handle_pubkeyinfo_response_2(
    response: reqwest::Response,
) -> Result<PubkeyInfoInfoResp, ErrorInfoData> {
    let text_ret = response.text().await;
    let response_text = match text_ret {
        Ok(text) => text,
        Err(_err) => {
            error!("error in get resp2 {:?}", &_err);
            let err_msg = format!("error in get resp2 {:?}", &_err);
            return Err(ErrorInfoData::new(err_msg.clone(), Some(err_msg.clone())));
        }
    };

    info!("Response text: {}", &response_text);

    let info_response: PubkeyInfoInfoResp =
        serde_json::from_str(&response_text).map_err(|err| {
            error!("Error in parsing JSON: {:?}", err);
            error!("JSON string: {}", response_text); // Log the JSON string
            ErrorInfoData::new(
                "deserialize_error".to_string(),
                Some(format!("Failed to deserialize PubkeyInfoInfoResp: {}", err)),
            )
        })?;
    Ok(info_response)
}

#[cfg(windows)]
pub async fn fetch_pubkey_info(tenant: &str, deviceid: &str) -> Result<PubkeyInfo, ErrorInfoData> {
    let client = crate::http::client_tenant_deviceid(tenant, deviceid).await;
    let endpoint = crate::logon_user::get_server_addr();
    let url = format!("{}{}", endpoint, TFA_GET_PUBLIC_KEY);
    info!("url is {}", &url);
    let response_ret = client.get(&url).send().await;
    match response_ret {
        Ok(response) => {
            if response.status().is_success() {
                info!("response is {:?}", &response);
                let info_response_ret = handle_pubkeyinfo_response_2(response).await;
                let info_response = match info_response_ret {
                    Ok(resp) => resp,
                    Err(_err) => {
                        return Err(_err);
                    }
                };
                if info_response.code.clone() == "SUCCESS" {
                    return Ok(info_response.data.unwrap_or(PubkeyInfo::new()));
                } else {
                    return Err(ErrorInfoData::new(
                        String::from("ERRROR"),
                        Some(String::from("The code is not SUCCESS")),
                    ));
                }
            } else {
                let error_message_ret = response.text().await;
                let error_msg = format!("HTTP error: {:?}", &error_message_ret);
                error!("{}", &error_msg);
                let err_info = ErrorInfoData::new(String::from("ERRROR"), Some(error_msg));
                return Err(err_info);
            }
        }
        Err(_err) => {
            let error_msg = format!("error is {:?}", &_err);
            error!("{}", &error_msg);
            let err_info = ErrorInfoData::new(String::from("ERRROR"), Some(error_msg));
            return Err(err_info);
        }
    }
}
