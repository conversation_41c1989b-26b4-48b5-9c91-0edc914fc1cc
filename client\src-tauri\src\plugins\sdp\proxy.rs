use communicate_interface::{types::ProxyRequest, ControlServiceClient};
use proxy_request::{HttpRequest, ResponseData, ResponseType};
use serde_json::Value;
use std::collections::HashMap;
use tauri::State;
use tokio::sync::Mutex;
use types::backend::ConnectionConfig;

#[tauri::command(async)]
pub(super) async fn http_request(
    request: HttpRequest,
    connection_config: ConnectionConfig,
    rpc: State<'_, Mutex<ControlServiceClient>>,
) -> Result<ResponseData, String> {
    log::trace!(target: "app", "Received http request: {}", &request.url);

    let rpc_guard = rpc.lock().await;
    let mut rpc = rpc_guard.clone();
    drop(rpc_guard);

    let response_type = request
        .response_type
        .clone()
        .unwrap_or(proxy_request::ResponseType::Json);

    let http_request = serde_json::to_vec(&request).map_err(|error| {
        log::error!(target: "app", "serialization failed: {:?}", request);
        tauri::api::Error::Json(error).to_string()
    })?;

    match rpc
        .http_request(tonic::Request::new(ProxyRequest {
            request: http_request,
            connection_config: Some(connection_config.into()),
        }))
        .await
    {
        Ok(response) => {
            let proxy_response = response.into_inner();
            let response = proxy_response.response.unwrap();
            match response {
                communicate_interface::types::proxy_response::Response::Ok(response_data) => {
                    let mut _response_data = serde_json::from_slice::<ResponseData>(
                        &response_data.response,
                    )
                    .map_err(|error| {
                        log::error!(target: "app", "deserialization failed: {:?}", request);
                        tauri::api::Error::Json(error).to_string()
                    })?;

                    if let Some(data) = response_data.data {
                        let data = match response_type {
                            ResponseType::Json => serde_json::from_slice(&data),
                            ResponseType::Text => Ok(Value::String(
                                String::from_utf8(data).map_err(|error| error.to_string())?,
                            )),
                            ResponseType::Binary => serde_json::to_value(&data),
                        };

                        _response_data.data = data.map_err(|error| {
                            log::error!(target: "app", "deserialization failed: {:?}", request);
                            tauri::api::Error::Json(error).to_string()
                        })?;
                    }

                    Ok(_response_data)
                }
                communicate_interface::types::proxy_response::Response::Timeout(_) => {
                    Ok(ResponseData {
                        url: request.url,
                        status: 408,
                        headers: HashMap::default(),
                        raw_headers: HashMap::default(),
                        data: Value::String(String::from("Request timeout")),
                    })
                }
                communicate_interface::types::proxy_response::Response::ConnectionFailed(_) => {
                    Ok(ResponseData {
                        url: request.url,
                        status: 500,
                        headers: HashMap::default(),
                        raw_headers: HashMap::default(),
                        data: Value::String(String::from("Connection failed")),
                    })
                }
                communicate_interface::types::proxy_response::Response::Err(cause) => {
                    Ok(ResponseData {
                        url: request.url,
                        status: 400,
                        headers: HashMap::default(),
                        raw_headers: HashMap::default(),
                        data: Value::String(cause),
                    })
                }
            }
        }
        Err(_) => Err("Operation timeout".to_owned()),
    }
}
