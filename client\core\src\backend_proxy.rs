use std::time::Duration;

use futures::{
    channel::{mpsc, oneshot},
    SinkExt, StreamExt,
};
use socket2::{SockRef, TcpKeepalive};
use tokio::{io::AsyncWriteExt, net::TcpStream};
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use base::packet::{Message, MessageCodec, MessageType};

use crate::backend::common;

pub struct ProxyBackend {
    pub ctl_framed: Framed<TlsStream<TcpStream>, MessageCodec>,

    // 断开连接
    pub backend_close_rx: oneshot::Receiver<()>,
    // 连接断开通知
    pub backend_close_tx: oneshot::Sender<()>,

    pub ctl_packet_rx: mpsc::UnboundedReceiver<Message>,

    pub return_rx:
        mpsc::Receiver<oneshot::Sender<Option<Framed<TlsStream<TcpStream>, MessageCodec>>>>,
}

impl ProxyBackend {
    pub(crate) async fn run(mut self) {
        Self::config_stream(self.ctl_framed.get_ref().get_ref().0);

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = &mut self.backend_close_rx => {
                        _ = self.ctl_framed.into_inner().shutdown().await;
                        _ = self.backend_close_tx.send(());
                        break;
                    }
                    result = self.return_rx.next() => match result {
                        Some(result_tx) => {
                            _ = result_tx.send(Some(self.ctl_framed));
                            break;
                        }
                        None => {
                            _ = self.ctl_framed.into_inner().shutdown().await;
                            _ = self.backend_close_tx.send(());
                            break;
                        }
                    },
                    message = self.ctl_framed.next() => match message {
                        None => {
                            log::debug!("Lost connection with server");
                            _ = self.ctl_framed.into_inner().shutdown().await;
                            _ = self.backend_close_tx.send(());
                            break;
                        }
                        Some(Err(error)) => {
                            if let Some(error_code) = error.raw_os_error() {
                                log::error!("Lost connection with server. os error {error_code}");
                            } else {
                                log::error!("Lost connection with server. {}", error);
                            }
                            _ = self.ctl_framed.into_inner().shutdown().await;
                            _ = self.backend_close_tx.send(());
                            break;
                        }
                        Some(Ok(message)) => match message.r#type() {
                            MessageType::ProxyResponse => {
                                let input = message.payload();
                                if common::handle_proxy_response(input).await.is_err() {
                                    _ = self.ctl_framed.into_inner().shutdown().await;
                                    _ = self.backend_close_tx.send(());
                                    break;
                                }
                            }
                            MessageType::ProxyResponseChunk => {
                                let input = message.payload();
                                if common::handle_proxy_response_chunk(input).await.is_err() {
                                    break;
                                }
                            }
                            MessageType::ProxyFail => {
                                let input = message.payload();
                                if common::handle_proxy_fail(input).await.is_err() {
                                    _ = self.ctl_framed.into_inner().shutdown().await;
                                    _ = self.backend_close_tx.send(());
                                    break;
                                }
                            }
                            message_type => {
                                log::error!(
                                    "Received unexpected message. type = {:?}",
                                    message_type
                                );
                                _ = self.ctl_framed.into_inner().shutdown().await;
                                _ = self.backend_close_tx.send(());
                                break;
                            }
                        },
                    },
                    message = self.ctl_packet_rx.next() => match message {
                        Some(message) => {
                            let ty = message.r#type();
                            if let Err(error) = self.ctl_framed.send(message).await {
                                log::error!("Failed to send message  {:?} {}", ty, error);
                                _ = self.ctl_framed.into_inner().shutdown().await;
                                _ = self.backend_close_tx.send(());
                                break;
                            }
                        }
                        None => {
                            _ = self.ctl_framed.into_inner().shutdown().await;
                            _ = self.backend_close_tx.send(());
                            break;
                        }
                    },
                }
            }
            log::debug!("Backend closed");
        });
    }

    fn config_stream(stream: &TcpStream) {
        let socket = SockRef::from(stream);
        let keepalive = TcpKeepalive::new()
            .with_time(Duration::from_secs(180))
            .with_interval(Duration::from_secs(15));

        socket.set_tcp_keepalive(&keepalive).unwrap();
    }
}
