[package]
build = "build.rs"
edition = "2021"
name = "app"
publish = false
rust-version = "1.75"
version = "0.0.0"

[dependencies]
amq-protocol = "7.1.2"
arp-toolkit = { version = "0.3.2", features = ["sync"] }
aya = "0.13.1"
aya-log = "0.2.1"
base = { path = "../../base" }
bytes = { version = "1.4.0", features = ["serde"] }
clap = { version = "4.1", features = ["derive", "string"] }
coarsetime = { workspace = true }
console-subscriber = { version = "0.1.10", optional = true }
dashmap = { version = "6.1.0", features = ["inline"] }
deadpool-lapin = "0.10.0"
env_logger = "0.10"
err_ext = { path = "../../err_ext" }
faster-hex = "0.8.0"
flume = "0.10.14"
fred = { version = "10.1", features = [
  "serde-json",
  "enable-rustls-ring",
  "sentinel-auth",
  "dynamic-pool",
] }
futures = "0.3.28"
futures-util = "0.3.28"
gm-sm2 = { workspace = true }
hex = { workspace = true }
http = "1.1.0"
ipnet = { version = "2.8.0", features = ["json"] }
lapin = "2.3.1"
log-panics = { version = "2", features = ["with-backtrace"] }
moka = { version = "0.12", features = ["future"] }
netdev = "0.33.0"
once_cell = { workspace = true }
percent-encoding = "2.3.0"
pkcs8 = { workspace = true }
pnet_base = "0.34.0"
pnet_packet = "0.34.0"
proxy-request = { path = "../../proxy-request" }
rand = "0.9"
range = "1.0.0"
reqwest = { version = "0.12.6", default-features = false, features = [
  "charset",
  "http2",
  "json",
  "macos-system-configuration",
  "multipart",
  "rustls-tls",
  "stream",
] }
rustls = { version = "0.23.23", default-features = false, features = [
  "logging",
  "std",
  "tls12",
  "tlcp11",
  "ring",
] }
sdpcore = { package = "core", path = "../core" }
serde = { version = "1.0.176", features = ["derive"] }
serde_json = { version = "1.0.105", features = ["raw_value"] }
serde_repr = "0.1.16"
serde_with = "3.1.0"
server-cli = { path = "../cli" }
share = { path = "../share", features = ["user"] }
socket2 = { version = "0.5.3", features = ["all"] }
thiserror = "2.0.4"
time = { version = "0.3.15", features = ["macros"] }
tlv = { path = "../../tlv/tlv" }
tlv-types = { path = "../../tlv/tlv-types" }
tokio = { version = "1.25", features = ["full"] }
tokio-executor-trait = "2.1.1"
tokio-reactor-trait = "1.1.0"
tokio-rustls = { version = "0.26.2", default-features = false, features = [
  "logging",
  "tls12",
  "ring",
] }
tokio-util = "0.7.8"
toml = "0.8"
tracing = "0.1.37"
tracing-appender = "0.2.2"
tracing-rolling = "0.3.0"
tracing-subscriber = { version = "0.3.17", features = [
  "json",
  "time",
  "env-filter",
  "local-time",
] }
tun = { version = "0.7.2", features = ["async"] }
untrusted = "0.9.0"
url = { version = "2.4.1", features = ["serde"] }
version = { path = "../../version" }
xdp = { path = "../xdp" }

[features]
default = ["tlcp"]
tlcp = ["base/tlcp", "rustls/tlcp11"]
trace = ["tokio/tracing", "console-subscriber"]

[[bin]]
name = "server"
path = "src/main.rs"

[build-dependencies]
built = { version = "0.7", features = ["git2"] }
time = { version = "0.3", features = ["formatting", "local-offset"] }
