<template>
  <div>
    <el-dialog
      :model-value="show"
      title="忘记密码"
      width="80%"
      center
      :before-close="closeDialog"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div v-if="!networkError" style="margin-top: 10px;">
        <el-tabs
          v-model="activeForgetMethodName"
          type="card"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane label="短信重置密码" name="MOBILE"></el-tab-pane>
          <el-tab-pane label="邮箱重置密码" name="EMAIL"></el-tab-pane>
        </el-tabs>
        <div
          v-loading.fullscreen.lock="loading"
          element-loading-text="加载中..."
          element-loading-background="rgba(0, 0, 0, 0.5)"
        >
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            label-width="110px"
            class="changePwd-Form"
          >
            <el-form-item
              v-if="activeForgetMethodName === 'MOBILE'"
              class="form-item"
              label="手机号码 :"
              prop="mobile"
              :error="errorMsg"
            >
              <el-input
                v-model="ruleForm.mobile"
                class="form-input"
                placeholder="请输入手机号码"
                autocomplete="off"
                clearable
              />
            </el-form-item>
            <el-form-item
              v-if="activeForgetMethodName === 'EMAIL'"
              class="form-item"
              label="邮箱号码 :"
              prop="emilCode"
              :error="errorMsg"
            >
              <el-input
                v-model="ruleForm.emilCode"
                class="form-input"
                placeholder="请输入邮箱号码"
                autocomplete="off"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="form-item"
              label="验证码 :"
              prop="smsCode"
            >
              <el-input
                v-model="ruleForm.smsCode"
                placeholder="请输入验证码"
                class="form-input"
                maxlength="6"
              >
                <template #append>
                  <el-button
                    v-show="activeForgetMethodName === 'MOBILE'"
                    :disabled="disabled"
                    size="small"
                    @click="getSMSCode"
                  >
                    {{ sendCode }}
                  </el-button>
                  <el-button
                    v-show="activeForgetMethodName === 'EMAIL'"
                    :disabled="disabledEmail"
                    size="small"
                    @click="getSMSCode"
                  >
                    {{ sendCodeEmail }}
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
            <div
              class="tipStart"
              v-if="tip"
              style="
                max-height: 80px;
                font-size: 12px;
                text-align: left;
                font-weight: normal;
                width: 316px;
                margin-left: 110px;
                background: #ffedde;
                border-radius: 5px;
                margin-top: 15px;
              "
            >
              {{ tip[0] }}
              <!-- <strong style="font-size: 15px;">密码需要满足以下条件：</strong> -->
              <!-- <p v-for="(item) in tip" :key="item">{{ item }}</p> -->
            </div>
            <el-form-item
              class="form-item"
              label="设置新密码 :"
              prop="newPassword"
              :error="newerrorMsg"
            >
              <el-input
                v-model="ruleForm.newPassword"
                class="form-input"
                placeholder="请输入新密码"
                type="password"
                autocomplete="off"
                clearable
                show-password
              />
            </el-form-item>
            <el-form-item
              class="form-item"
              label="新密码确认 :"
              prop="newPassword2"
            >
              <el-input
                v-model="ruleForm.newPassword2"
                class="form-input"
                type="password"
                placeholder="请再次输入新密码"
                autocomplete="off"
                clearable
                show-password
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div v-if="networkError" class="network-error">
        <div>
          <img src="/assets/network_error.png" />
        </div>
        <div>
          <el-button type="primary" v-if="networkError" @click="pwdPolicy">
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </div>
      </div>
      <template #footer v-if="!networkError">
        <span class="dialog-footer">
          <!-- <el-button round @click="closeDialog">取消</el-button> -->
          <el-button
            ref="forgetPwdBtn"
            round
            type="primary"
            @click="VCodeSubmitForm"
          >
            确认修改
          </el-button>
          <!-- <el-button v-else round type="primary" @click="submitForm">
                      发送重置密码邮件
                  </el-button> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getPolicy, sendSms, updatePassword } from "@/api/service";

export default {
  name: "forgetPwd",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      networkError: false,
      loading_text: "加载中...",
      ruleForm: {
        mobile: "",
        newPassword: "",
        newPassword2: "",
        smsCode: "",
        emilCode: "",
      },
      sendCode: "获取验证码",
      sendCodeEmail: "获取验证码",
      disabled: false,
      defaultTime: 60,
      disabledEmail: false,
      defaultTimeEmail: 60,
      verifyCode: "",
      countdown: undefined,
      countdownEmail: undefined,
      tip: "",
      newerrorMsg: "",
      errorMsg: "",
      changePwdListener: null,
      rules: {
        mobile: [
          { required: true, message: "请输入手机号码", trigger: "blur" },
        ],
        emilCode: [
          { required: true, message: "请输入邮箱号码", trigger: "blur" },
        ],
        smsCode: [{ required: true, message: "请输入验证码", trigger: "blur" }],
        newPassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
        ],
        newPassword2: [
          { required: true, validator: validatePass2, trigger: "blur" },
        ],
      },
      activeForgetMethodName: "MOBILE",
    };
  },
  created() {
    this.closeDialog();
  },

  methods: {
    async VCodeSubmitForm() {
      this.newerrorMsg = "";
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          this.loading_text = "提交中...";
          let param = {
            oldVerifyMethod:
              this.activeForgetMethodName === "MOBILE" ? "MOBILE" : "EMAIL",
            oldVerifyAccount:
              this.activeForgetMethodName === "MOBILE"
                ? this.ruleForm.mobile
                : this.ruleForm.emilCode,
            oldVerifyCode: this.ruleForm.smsCode,
            newPwd: this.ruleForm.newPassword2,
          };

          updatePassword(param)
            .then((res) => {
              this.loading = false;
              this.loading_text = "加载中...";
              this.$emit("successCallback", "密码修改成功");
            })
            .catch((err) => {
              this.loading = false;
              this.loading_text = "加载中...";
            });
        } else {
          return false;
        }
      });
    },
    // 获取密码策略
    pwdPolicy() {
      this.loading = true;
      getPolicy({
        userTypes: ["03"],
      })
        .then((res) => {
          this.networkError = false;
          this.tip = res.data.tipKey.split("\n");
        })
        .catch(() => {
          this.networkError = true;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    init() {
      this.pwdPolicy();
    },
    closeDialog() {
      this.networkError = false;
      this.$emit("close");
      this.errorMsg = "";
      this.newerrorMsg = "";
      this.ruleForm.mobile = "";
      this.ruleForm.smsCode = "";
      this.ruleForm.emilCode = "";
      this.ruleForm.newPassword = "";
      this.ruleForm.newPassword2 = "";
      this.sendCode = "获取验证码";
      this.sendCodeEmail = "获取验证码";
      this.disabled = false;
      this.disabledEmail = false;
      this.defaultTime = 60;
      this.defaultTimeEmail = 60;
      this.activeForgetMethodName = "MOBILE";
      clearInterval(this.countdown);
      clearInterval(this.countdownEmail);
      this.countdown = undefined;
      this.countdownEmail = undefined;
    },
    getSMSCode() {
      if (!this.ruleForm.mobile && this.activeForgetMethodName == "MOBILE") {
        this.errorMsg = "请输入手机号码";
        return;
      }
      if (!this.ruleForm.emilCode && this.activeForgetMethodName == "EMAIL") {
        this.errorMsg = "请输入邮箱号码";
        return;
      }
      let data = {
        method: this.activeForgetMethodName == "MOBILE" ? "MOBILE" : "EMAIL",
        account:
          this.activeForgetMethodName == "MOBILE"
            ? this.ruleForm.mobile
            : this.ruleForm.emilCode,
      };
      sendSms(data)
        .then((result) => {
          let data =
            this.activeForgetMethodName == "MOBILE"
              ? this.ruleForm.mobile
              : this.ruleForm.emilCode;
          this.activeForgetMethodName == "MOBILE"
            ? this.alert.success("已发送至手机" + data + "，请注意查收。")
            : this.alert.success("已发送至邮箱" + data + "，请注意查收。");

          if (this.activeForgetMethodName == "MOBILE") {
            this.countdown = setInterval(this.timer, 1000);
          } else {
            this.countdownEmail = setInterval(this.timerEmail, 1000);
          }
        })
        .catch((err) => { });
    },
    timer() {
      this.disabled = true;
      this.sendCode = "重新获取(" + this.defaultTime + ")";
      this.defaultTime--;
      if (this.defaultTime < 0) {
        clearInterval(this.countdown);
        this.disabled = false;
        this.sendCode = "重新获取";
        this.defaultTime = 60;
      }
    },
    timerEmail() {
      this.disabledEmail = true;
      this.sendCodeEmail = "重新获取(" + this.defaultTimeEmail + ")";
      this.defaultTimeEmail--;
      if (this.defaultTimeEmail < 0) {
        clearInterval(this.countdownEmail);
        this.disabledEmail = false;
        this.sendCodeEmail = "重新获取";
        this.defaultTimeEmail = 60;
      }
    },
    handleClick(tab, event) {
      this.activeForgetMethodName = tab.props.name;
      this.errorMsg = "";
      this.newerrorMsg = "";
      this.$refs.ruleForm.resetFields();
    },
  },
};
</script>

<style lang="less" scoped>
.network-error {
  display: flex;
  height: 463px;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.changePwd-Form {
  padding: 10px 60px 0;
  overflow-y: auto;
  text-align: -webkit-center;
  height: 350px;

  :deep(.el-input-group__append) {
    border-left: 1px solid #f9780c;
  }

  :deep(.el-input__inner) {
    height: 35px;
    line-height: 40px;
  }

  .form-btn {
    display: block;
    width: 68px;
    height: 34px;
    line-height: 34px;
    padding: 0;
    margin-top: 10px;
    background-image: linear-gradient(to right, #fbaa66, #f9780c);
  }

  :deep(.el-form-item__error) {
    padding: 0;
  }

  .tipStart {
    // margin-left: 100px;
    padding: 12px;
    font-size: 14px;
    font-weight: normal;
    color: #53565d;
    // max-height: 128px;
    overflow-y: auto;
  }

  :deep(.el-form-item__label) {
    height: 55px;
    align-items: center;
    color: #25282f;
  }

  :deep(.el-form-item__content) {
    // display: block;
    line-height: 55px;
  }

  // .el-button:hover {
  //     color: #fff;
  // }
  .el-form-item {
    width: 70%;
    margin-bottom: 8px;
  }
}

:deep(.el-dialog) {
  margin-top: 12vh;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  margin: 0 35%;
}

:deep(.el-tabs--card > .el-tabs__header) {
  border: 0;
  border-radius: 6px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border: none;
  background-color: #f9f9f9;
  font-size: 14px;
  padding: 0 20px;
  // border-radius: 8px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
  background-color: #ffedde;
  color: #f9780c !important;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: none;
}

.demo-tabs {
  height: 35px;
}
</style>
