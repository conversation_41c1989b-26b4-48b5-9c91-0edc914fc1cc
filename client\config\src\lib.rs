#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
use std::{fmt, ops::Deref};

use anyhow::Result;
use encrypt_files::help;
#[cfg(all(feature = "sdp", not(feature = "cluster")))]
use rand::Rng;
use serde::{Deserialize, Serialize};
#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
use serde::{Deserializer, Serializer};
use serde_json::Value;
#[cfg(all(feature = "sdp", not(feature = "cluster")))]
use std::str::Split;
use std::{collections::HashMap, net::IpAddr, path::PathBuf};
#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
use url::Url;

pub const MAIN_CONFIG: &str = "config.yaml";

pub const CORE_CONFIG: &str = "core.yaml";

/// 域名
#[cfg(all(feature = "sdp", not(feature = "cluster")))]
pub const DOMAIN: Option<&str> = option_env!("SDP_DOMAIN");

/// IP
#[cfg(all(feature = "sdp", not(feature = "cluster")))]
pub const IP: Option<&str> = if let Some(ip) = option_env!("SDP_IP") {
    let ip = konst::string::trim(ip);
    if ip.is_empty() {
        None
    } else {
        Some(ip)
    }
} else {
    None
};

/// tcp端口
#[cfg(all(feature = "sdp", not(feature = "cluster")))]
pub const PORT: Option<&str> = if let Some(port) = option_env!("SDP_PORT") {
    let port = konst::string::trim(port);
    if port.is_empty() {
        None
    } else {
        Some(port)
    }
} else {
    None
};

/// udp端口
#[cfg(all(feature = "sdp", not(feature = "cluster")))]
pub const SPA_PORT: Option<&str> = if let Some(port) = option_env!("SDP_SPA_PORT") {
    let port = konst::string::trim(port);
    if port.is_empty() {
        None
    } else {
        Some(port)
    }
} else {
    None
};

/// 客户端负载均衡内网配置地址
#[cfg(feature = "cluster")]
pub const CLUSTER_CONFIG_URL: Option<&str> = option_env!("SDP_CLUSTER_CONFIG_URL");

/// 客户端负载均衡外网配置地址
#[cfg(feature = "cluster")]
pub const CLUSTER_EXTERNAL_CONFIG_URL: Option<&str> =
    option_env!("SDP_CLUSTER_EXTERNAL_CONFIG_URL");

/// IAM 后端服务地址
#[cfg(not(any(feature = "cluster", feature = "sdp")))]
pub const BACKEND_URL: Option<&str> = option_env!("IAM_BACKEND_URL");

/// 客户端MODEL
pub const MODEL: &str = env!("SDP_MODEL");

pub mod migrations;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 配置版本
    pub version: semver::Version,

    /// 集群内网配置地址
    #[cfg(feature = "cluster")]
    #[serde(
        serialize_with = "url_wrapper_serialize",
        deserialize_with = "url_wrapper_deserialize"
    )]
    pub cluster_config_url: Option<UrlWrapper>,

    /// 集群外网配置地址
    #[cfg(feature = "cluster")]
    #[serde(
        serialize_with = "url_wrapper_serialize",
        deserialize_with = "url_wrapper_deserialize"
    )]
    pub cluster_external_config_url: Option<UrlWrapper>,

    /// 集群节点
    #[cfg(feature = "sdp")]
    pub cluster_nodes: Option<Vec<Node>>,

    /// 集群节点轮询起始索引
    #[serde(default)]
    #[cfg(feature = "sdp")]
    pub cluster_starting_index: usize,

    /// 集群节点切换次数
    #[serde(default)]
    #[cfg(feature = "sdp")]
    pub cluster_node_changed_times: usize,

    /// 节点配置
    #[cfg(feature = "sdp")]
    pub node: Option<Node>,

    /// 后端服务地址
    #[cfg(not(any(feature = "cluster", feature = "sdp")))]
    #[serde(
        serialize_with = "url_wrapper_serialize",
        deserialize_with = "url_wrapper_deserialize"
    )]
    pub endpoint: Option<UrlWrapper>,

    /// 租户列表
    pub tenants: Option<Vec<Tenant>>,

    /// 当前选择的租户
    pub selected_tenant: Option<Tenant>,

    /// 各个租户下的用户信息
    pub users: Option<HashMap<String, User>>,

    /// 加密密钥
    pub secret_key: Option<Value>,

    /// 国际化
    pub lang: Option<String>,

    /// 是否显示单位切换提示
    pub show_change_tenant_tip: bool,
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
#[derive(Clone, PartialEq, Eq)]
pub struct UrlWrapper {
    inner: Url,
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
impl serde::Serialize for UrlWrapper {
    fn serialize<S>(&self, serializer: S) -> std::result::Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(self.inner.as_str())
    }
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
impl fmt::Debug for UrlWrapper {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.inner.as_str())
    }
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
impl Deref for UrlWrapper {
    type Target = Url;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
impl From<Url> for UrlWrapper {
    fn from(value: Url) -> Self {
        Self { inner: value }
    }
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
impl From<UrlWrapper> for Url {
    fn from(value: UrlWrapper) -> Self {
        value.inner
    }
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
fn url_wrapper_deserialize<'de, D>(deserializer: D) -> Result<Option<UrlWrapper>, D::Error>
where
    D: Deserializer<'de>,
{
    Option::<Url>::deserialize(deserializer).map(|url| url.map(Into::into))
}

#[cfg(any(feature = "cluster", not(any(feature = "cluster", feature = "sdp"))))]
fn url_wrapper_serialize<S>(url: &Option<UrlWrapper>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    match url {
        Some(url) => url.inner.serialize(serializer),
        None => serializer.serialize_none(),
    }
}

/// 租户信息
#[derive(Default, Debug, Clone, Deserialize, Serialize)]
pub struct Tenant {
    /// 编码
    pub code: String,
    /// 租户名称
    pub name: String,
}

/// 用户信息
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct User {
    pub username: String,
    pub password: Option<String>,
    pub remember_me: bool,
    pub auto_connect: bool,
    /// 绑定码
    #[cfg(feature = "sdp")]
    pub secret: Option<String>,
    /// 本次认证方式
    pub login_method: Option<String>,
}

/// 节点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Node {
    pub host: String,
    pub ip: Option<IpAddr>,
    pub port: u16,
    pub spa_port: u16,
}

impl Default for AppConfig {
    fn default() -> Self {
        #[cfg(all(feature = "sdp", not(feature = "cluster")))]
        fn to_node(
            domain: &str,
            ip: Option<IpAddr>,
            ports: &mut Split<&str>,
            spa_ports: &mut Split<&str>,
        ) -> Option<Node> {
            if let (Some(port), Some(spa_port)) = (ports.next(), spa_ports.next()) {
                if let (Ok(port), Ok(spa_port)) = (port.parse(), spa_port.parse()) {
                    return Some(Node {
                        host: domain.to_owned(),
                        ip,
                        port,
                        spa_port,
                    });
                }
            }

            None
        }

        Self {
            version: semver::Version::parse(version::PRODUCT_VERSION).unwrap(),
            #[cfg(feature = "cluster")]
            cluster_config_url: CLUSTER_CONFIG_URL
                .and_then(|url| url.parse::<Url>().map(Into::into).ok()),
            #[cfg(feature = "cluster")]
            cluster_external_config_url: CLUSTER_EXTERNAL_CONFIG_URL
                .and_then(|url| url.parse::<Url>().map(Into::into).ok()),
            #[cfg(feature = "sdp")]
            node: None,
            #[cfg(feature = "cluster")]
            cluster_nodes: None,
            #[cfg(all(feature = "sdp", not(feature = "cluster")))]
            cluster_nodes: if let (Some(domain), Some(port), Some(spa_port)) =
                (DOMAIN, PORT, SPA_PORT)
            {
                let mut nodes = vec![];
                let mut ports = port.split(",");
                let mut spa_ports = spa_port.split(",");
                if let Some(ip) = IP {
                    let ips = ip.split(',');
                    let ip_num = ips.clone().count();
                    if ip_num > 1 {
                        for ip in ips {
                            if let Some(node) =
                                to_node(domain, ip.parse().ok(), &mut ports, &mut spa_ports)
                            {
                                nodes.push(node);
                            }
                        }
                    } else {
                        let port_num = spa_ports.clone().count();
                        for _ in 0..port_num {
                            if let Some(node) =
                                to_node(domain, ip.parse().ok(), &mut ports, &mut spa_ports)
                            {
                                nodes.push(node);
                            }
                        }
                    }
                } else {
                    let port_num = spa_ports.clone().count();
                    for _ in 0..port_num {
                        if let Some(node) = to_node(domain, None, &mut ports, &mut spa_ports) {
                            nodes.push(node);
                        }
                    }
                }
                Some(nodes)
            } else {
                None
            },
            #[cfg(feature = "sdp")]
            cluster_starting_index: 0,
            #[cfg(feature = "sdp")]
            cluster_node_changed_times: 0,
            #[cfg(not(any(feature = "cluster", feature = "sdp")))]
            endpoint: BACKEND_URL.and_then(|url| url.parse().ok()),
            tenants: None,
            secret_key: None,
            selected_tenant: None,
            users: None,
            lang: Some("zh".to_owned()),
            show_change_tenant_tip: true,
        }
    }
}

impl AppConfig {
    /// Save App Config
    pub fn save_file(&self) -> Result<()> {
        log::debug!(target: "app", "{:?}", self);
        let res_dir = paths::resource_dir()?;
        let path = res_dir.join(MAIN_CONFIG);
        help::save_yaml(&path, &self, Some("# SDP Config"))
    }
}

pub fn init(resource_dir: PathBuf) -> AppConfig {
    let path = resource_dir.join(MAIN_CONFIG);
    #[cfg_attr(any(feature = "cluster", not(feature = "sdp")), allow(unused_mut))]
    let mut app_config = help::read_yaml::<AppConfig>(&path).unwrap_or_else(|_| {
        let config = AppConfig::default();
        help::save_yaml(&path, &config, Some("# Generated by SDP Client")).unwrap();
        config
    });

    // 初始化选择一个节点
    #[cfg(all(feature = "sdp", not(feature = "cluster")))]
    {
        if let Some(cluster_nodes) = app_config.cluster_nodes.as_mut() {
            let mut rng = rand::rng();
            let index = rng.random_range(0..cluster_nodes.len());
            app_config.node = Some(cluster_nodes.get(index).unwrap().clone());
            app_config.cluster_starting_index = index;
            app_config.cluster_node_changed_times = 0;
        }
    }

    app_config
}
