<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE policyconfig PUBLIC
 "-//freedesktop//DTD PolicyKit Policy Configuration 1.0//EN"
 "http://www.freedesktop.org/standards/PolicyKit/1/policyconfig.dtd">
<policyconfig>
    <vendor>北京景安云信科技有限公司</vendor>
    <vendor_url>https://www.jingantech.com</vendor_url>
    <action id="com.jingantech.oneid.core">
        <description>运行安全令</description>
        <message>Authentication is required to run the One ID</message>
        <!-- <icon_name>accessories-text-editor</icon_name> -->
        <defaults>
            <allow_any>no</allow_any>
            <allow_inactive>no</allow_inactive>
            <allow_active>auth_admin</allow_active>
        </defaults>

        <message xml:lang="zh_CN">安全令需要输入密码</message>
        <annotate key="org.freedesktop.policykit.exec.path">/usr/bin/oneid_auth</annotate>
        <annotate key="org.freedesktop.policykit.exec.allow_gui">true</annotate>
    </action>
</policyconfig>
