use communicate_interface::new_rpc_client;
use err_ext::ErrorExt;

/// Checks if there is another instance of the core running.
///
/// Tries to connect to another core and perform a simple tcp call. If it fails, assumes the
/// other core has stopped.
pub async fn is_another_instance_running() -> bool {
    match new_rpc_client().await {
        Ok(_) => true,
        Err(error) => {
            let msg = "Failed to locate/connect to another core instance, assuming there isn't one";
            if log::log_enabled!(log::Level::Trace) {
                log::trace!("{}\n{}", msg, error.display_chain());
            } else {
                log::debug!("{}", msg);
            }
            false
        }
    }
}
