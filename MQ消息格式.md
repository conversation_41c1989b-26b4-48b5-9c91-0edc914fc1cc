## 服务器列表

交换机名称: `server_list_exchange` 交换机类型: `Fanout`

| 类型 | 说明       | 格式                                                                                   |
| :--- | :--------- | :------------------------------------------------------------------------------------- |
| 0    | 删除服务器 | 首字节为消息类型0, 中间为ip长度+ip字节, 后跟租户长度+租户字节 (UTF-8编码)                              |
| 1    | 新增服务器 | 首字节为消息类型1, 中间为ip长度+ip字节, 后跟租户长度+租户字节 (UTF-8编码)                              |
| 2    | 新增伪IP   | 首字节为消息类型2, 中间为ip长度+ip字节, 后跟租户长度+租户字节 (UTF-8编码) |

### ip数据格式
| 类型 | 说明       | 格式|
| :--- | :--------- | :------------------|
| 0 |IP| 首字节为消息类型0,中间为IP类型(4/6)加上ip值|
| 1 |RANGE|首字节为消息类型1,中间为IP类型(4/6)与开始ip 与结束ip|
| 2 |CIDR| 首字节为消息类型2,中间为IP类型(4/6)与子网掩码位 与ip |


## WEB APP

交换机名称: `web_app_exchange` 交换机类型: `Fanout`

| 类型 | 说明       | 格式                                                                                   |
| :--- | :--------- | :------------------------------------------------------------------------------------- |
| 0    | 删除web_app | 首字节为消息类型0, 中间跟租户长度+租户字节 (UTF-8编码), 后为JSON是序列化数据长度+内容字节                              |
| 1    | 新增web_app | 首字节为消息类型1, ,中间跟租户长度+租户字节 (UTF-8编码) 后为JSON是序列化数据长度+内容字节                              |


## 其他消息

交换机名称: `sdp_exchange` 交换机类型: `Fanout`

| 类型 | 说明           | 数据(utf-8编码)                |
| :--- | :------------- | :----------------------------- |
| 0    | 用户禁用       | 租户编码\|用户名               |
| 1    | 用户被删除     | 租户编码\|用户名               |
| 2    | 设备禁用       | 设备ID                         |
| 3    | 用户未生效     | 租户编码\|用户名               |
| 4    | 用户已失效     | 租户编码\|用户名               |
| 5    | 用户密码变更   | 16字节设备ID, 租户编码\|用户名 |
| 6    | 用户绑定码变更 | 租户编码\|用户名               |
| 7    | 租户禁用       | 租户编码                       |
| 8    | 关闭会话       | 16字节设备ID                    |
| 100  | 用户异地登录   | 设备ID                         |

## 会话流量统计

```
|Tag::UTF8String|32|会话ID|
|Tag::Uint64|8|时间戳|
|Tag::Uint64|8|上行流量|
|Tag::Uint64|8|下行流量|
```

原始数据长度

56 = 32(会话ID) + 8(时间戳) + 8(上行流量) + 8(下行流量)

按TLV进行编码

64 = 34(会话ID) + 10(时间戳) + 10(上行流量) + 10(下行流量)

## 会话访问资源流量统计

```
|Tag::UTF8String|32|会话ID|
|Tag::Uint64|8|时间戳|
|Tag::Ipv4|目标IP长度|目标IP|
|Tag::Uint16|2|目标端口|
|Tag::Uint8|1|协议号|
|Tag::Uint64|8|上行流量大小|
|Tag::Uint64|8|下行流量大小|
```

原始数据长度

32(会话ID) + 8(时间戳) + 4/16(目标IP地址) + 0/2(目标端口) + 1(协议号) + 8(上行流量) + 8(下行流量)

最大值: 75

按TLV进行编码

34(会话ID) + 10(时间戳) + 6/18(目标IP地址) + 2/4(目标端口) + 3(协议号) + 10(上行流量) + 10(下行流量)

最大值: 89
