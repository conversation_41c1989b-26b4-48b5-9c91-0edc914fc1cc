// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

use core::fmt;

#[derive(<PERSON><PERSON>, <PERSON>lone)]
pub struct CheckedRange {
    start: usize,
    end: usize,
}

impl CheckedRange {
    #[inline]
    pub(crate) fn new(start: usize, end: usize) -> Self {
        debug_assert!(
            end >= start,
            "end: {end} must be greater than or equal to start: {start}",
        );

        Self { start, end }
    }

    #[inline]
    pub fn get<'a>(&self, slice: &'a [u8]) -> &'a [u8] {
        &slice[self.start..self.end]
    }

    #[inline]
    pub fn get_mut<'a>(&self, slice: &'a mut [u8]) -> &'a mut [u8] {
        &mut slice[self.start..self.end]
    }

    #[inline]
    pub fn len(&self) -> usize {
        self.end - self.start
    }

    #[inline]
    pub fn is_empty(&self) -> bool {
        self.start == self.end
    }
}

impl fmt::Debug for CheckedRange {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "{}..{}", self.start, self.end)
    }
}
