[package]
edition = "2021"
name = "tlv-types"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
faster-hex = "0.8.1"
hex = { workspace = true }
ipnet = "2.8.0"
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0.107"
tlv = { path = "../tlv", features = ["derive"] }
untrusted = "0.9.0"
url = { version = "2.4.1", features = ["serde"] }
