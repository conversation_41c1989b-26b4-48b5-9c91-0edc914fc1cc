# OpenResty接口配置


## 检查更新接口 `check_update.lua`

接口地址: `/check_update/{product}/{platform}/{arch}/{version}/{model}`

双因素等其他没有型号的产品, 接口地址为: `/check_update/{product}/{platform}/{arch}/{version}`

> 产品名称如下图所示(英文缩写): 
> 
> ![产品名称](product_names.png)

OpenResty配置

```nginx
location /check_update/ {
    content_by_lua_file /etc/nginx/snippets/check_update.lua;
}
```

将`update.json`文件放到`/data/config/`文件下下

将`semver.lua`文件放到openresty安装目录的`lualib`文件夹下. 默认位置`/usr/local/openresty/lualib`

示例: 

```http request
GET http://*************/check_update/JA-SDP/windows/x86_64/7.4.6/3S
Accept: application/json

HTTP/1.1 200 OK
Server: openresty/********
Date: Fri, 01 Dec 2023 07:54:49 GMT
Content-Type: application/json
Transfer-Encoding: chunked
Connection: keep-alive
X-Xss-Protection: 1;mode=block
X-Content-Type-Options: nosniff

{
  "manifest": {
    "signature": "dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIHRhdXJpIHNlY3JldCBrZXkKUlVTVVZ2dVdKYmxUOHlTTlIvSHdGc2tuMGpBTktZQnd6NjlGWWNhR2NYM08rcTNnUTBBSmNCdWdsWTBIdDk4RVBYRk0wWTI5Q1ZmRjBON280dTVhLy81c3B5WnZ3VS9WN2d3PQp0cnVzdGVkIGNvbW1lbnQ6IHRpbWVzdGFtcDoxNjk3NTM1MTU5CWZpbGU65a6J5YWo5LukXzcuNC42X3g2NC1zZXR1cC5uc2lzLnppcAp1UjV0SHBqSUlraHB0VC9SS2E4V3ZLejhnbEZYU3B4NEptZCtpRGFnc1k1Z1p2bjVObi9TSEVQZWtPUS9iUUVZV0V2KzZqU0l2TGpRc1VmN1NIaWRBQT09Cg==",
    "url": "https:\/\/promotion.jinganiam.com:21000\/packages\/JA-SDP\/安全令_7.4.6_x64-setup.nsis.zip"
  },
  "force": true,
  "notes": "<div style=\"padding:0 0 0 15px;text-align:left;font-weight:bold\">v7.4.6<div style=font-weight:normal>修复了一些已知问题<\/div><\/div>",
  "version": "7.4.6",
  "pub_date": "2023-10-17T00:00:00Z"
}
```

## 部署模式接口

> (集群或单机以及包含的功能模块)

接口地址: `/deploy_model`

OpenResty配置

```nginx
location /deploy_model {
    default_type 'text/plain';

    content_by_lua_block {
        ngx.say('3S')
    }
}
```

> sdp = 0x01
> 
> iam = 0x02
> 
> 集群为C, 单机为S.
> 
> 示例: 包含SDP和IAM模块且是单机模式
> 
> ```rust
> let sdp = 0x01u16;
> let iam = 0x02u16;
> 
> let model = sdp | iam; // 3
> 
> let model = format!("{}S", model); // 3S
> ```