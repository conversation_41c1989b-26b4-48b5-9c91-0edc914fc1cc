import Layout from "../layout/index.vue";

export default [
  {
    path: "/login",
    name: "login",
    component: () => import("../views/login"),
  },
  {
    path: "/",
    component: Layout,
    meta: {
      keepalive: true,
      login: true,
    },
    children: [
      { path: "", name: "home", component: () => import("../views/home") },
    ],
  },
  {
    path: "/setting",
    component: Layout,
    children: [
      {
        path: "",
        name: "setting",
        component: () => import("../views/setting"),
      },
    ],
  },
  {
    path: "/about",
    component: Layout,
    children: [
      { path: "", name: "about", component: () => import("../views/about") },
    ],
  },
  {
    path: "/changePwd",
    component: Layout,
    children: [
      {
        path: "",
        name: "change_pwd",
        component: () => import("../views/changePwd"),
      },
    ],
  },
];
