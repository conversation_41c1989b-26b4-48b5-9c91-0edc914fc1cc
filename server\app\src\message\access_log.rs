//! 推送访问日志

use deadpool_lapin::Pool;
use tokio::sync::mpsc;
use tracing::{error, info, warn, Instrument};

use crate::{message::LOG_EXCHANGE, Error};
use lapin::{options::BasicPublishOptions, BasicProperties, Channel};

fn do_spawn_producer(pool: Pool, channel: Channel, mut log_rx: mpsc::Receiver<Vec<u8>>) {
    tokio::spawn(
        async move {
            loop {
                match log_rx.recv().await {
                    Some(log) => {
                        if let Err(err) = channel
                            .basic_publish(
                                LOG_EXCHANGE,
                                LOG_EXCHANGE,
                                BasicPublishOptions::default(),
                                &log,
                                BasicProperties::default(),
                            )
                            .await
                        {
                            error!("access log publish failed: {err}");
                            break;
                        }
                    }
                    None => break,
                }
            }

            tokio::spawn(async move {
                info!("recreate channel");

                let mut timeout = 1;
                loop {
                    let channel = get_channel!(pool, timeout);

                    do_spawn_producer(pool, channel, log_rx);

                    break;
                }
            });
        }
        .instrument(tracing::error_span!(parent: None, "AccessLogProducer")),
    );
}

pub(super) async fn spawn_producer(
    pool: Pool,
    log_rx: mpsc::Receiver<Vec<u8>>,
) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqProducerError(error.to_string()))?;

    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqProducerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("log channel status is abnormal: {error}");
    });

    do_spawn_producer(pool, channel, log_rx);

    Ok(())
}
