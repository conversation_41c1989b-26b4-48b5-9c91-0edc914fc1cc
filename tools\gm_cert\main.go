package main

import (
	"crypto/rand"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"github.com/Hyperledger-TWGC/ccs-gm/sm2"
	"github.com/Hyperledger-TWGC/ccs-gm/x509"
	"io/ioutil"
	"log"
	"math/big"
	"os"
	"time"
)

func publicKey(priv interface{}) interface{} {
	switch k := priv.(type) {
	case *sm2.PrivateKey:
		return &k.<PERSON>
	default:
		return nil
	}
}

// default template for X509 certificates
func x509Template() x509.Certificate {
	// generate a serial number
	serialNumberLimit := new(big.Int).Lsh(big.NewInt(1), 128)
	serialNumber, _ := rand.Int(rand.Reader, serialNumberLimit)

	// set expiry to around 10 years
	expiry := 3650 * 24 * time.Hour
	// round minute and backdate 5 minutes
	notBefore := time.Now().Round(time.Minute).Add(-5 * time.Minute).UTC()

	// basic template to use
	x509 := x509.Certificate{
		SerialNumber:          serialNumber,
		NotBefore:             notBefore,
		NotAfter:              notBefore.Add(expiry).UTC(),
		BasicConstraintsValid: true,
	}
	return x509
}

// default template for X509 subject
func subjectTemplate() pkix.Name {
	return pkix.Name{
		Country:  []string{"CN"},
		Locality: []string{"Beijing"},
		Province: []string{"Beijing"},
	}
}

// Additional for X509 subject
func subjectTemplateAdditional(
	country,
	province,
	locality,
	orgUnit,
	streetAddress,
	postalCode string,
) pkix.Name {
	name := subjectTemplate()
	if len(country) >= 1 {
		name.Country = []string{country}
	}
	if len(province) >= 1 {
		name.Province = []string{province}
	}

	if len(locality) >= 1 {
		name.Locality = []string{locality}
	}
	if len(orgUnit) >= 1 {
		name.OrganizationalUnit = []string{orgUnit}
	}
	if len(streetAddress) >= 1 {
		name.StreetAddress = []string{streetAddress}
	}
	if len(postalCode) >= 1 {
		name.PostalCode = []string{postalCode}
	}
	return name
}

func main() {
	dir := "sdp.com/"

	_, err := os.Stat(dir)
	if os.IsNotExist(err) {
		err := os.MkdirAll(dir, os.ModePerm)
		if err != nil {
			fmt.Printf("目录创建失败: %s\n", err)
			return
		}
	}

	ca_domain := "sdp.com"
	ctl_domain := "controller." + ca_domain
	gateway_domain := "gateway." + ca_domain
	client_domain := "client." + ca_domain

	subject := pkix.Name{
		Country:    []string{"CN"},
		Locality:   []string{"Beijing"},
		Province:   []string{"Hai Dian"},
		CommonName: ca_domain,
	}
	keyUsage := x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment | x509.KeyUsageCertSign

	notBefore := time.Now()
	notAfter := notBefore.Add(365 * 24 * time.Hour * 10)

	serialNumberLimit := new(big.Int).Lsh(big.NewInt(1), 128)
	serialNumber, err := rand.Int(rand.Reader, serialNumberLimit)
	if err != nil {
		log.Fatalf("Failed to generate serial number: %v", err)
	}

	// CA
	template := x509.Certificate{
		IsCA:         true,
		SerialNumber: serialNumber,
		Subject:      subject,
		NotBefore:    notBefore,
		NotAfter:     notAfter,

		KeyUsage: keyUsage,
		ExtKeyUsage: []x509.ExtKeyUsage{
			x509.ExtKeyUsageClientAuth,
			x509.ExtKeyUsageServerAuth,
		},
		BasicConstraintsValid: true,
		DNSNames:              []string{ca_domain},
	}

	ca_priv, err := sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}

	pkcs8Encoded, err := x509.MarshalPKCS8PrivateKey(ca_priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded := pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})

	ioutil.WriteFile(dir+"ca.key", pemEncoded, 0600)

	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, publicKey(ca_priv), ca_priv)
	if err != nil {
		log.Fatalf("Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"ca.crt", pemEncoded, 0644)

	ca_crt, _ := x509.ParseCertificate(derBytes)

	// SDP Controller
	template1 := x509Template()
	template1.DNSNames = []string{ctl_domain}
	template1.Subject = subject
	template1.Subject.CommonName = ctl_domain

	// Controller sign certificate
	template1.KeyUsage = x509.KeyUsageDigitalSignature
	priv, err := sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}
	pkcs8Encoded, err = x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	ioutil.WriteFile(dir+"ctrl_sign.key", pemEncoded, 0600)

	derBytes, err = x509.CreateCertificate(rand.Reader, &template1, ca_crt, publicKey(priv), ca_priv)
	if err != nil {
		log.Fatalf(dir+"Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"ctrl_sign.crt", pemEncoded, 0644)

	// Controller encrypt certificate
	template1.KeyUsage = x509.KeyUsageKeyEncipherment
	priv, err = sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}
	pkcs8Encoded, err = x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	ioutil.WriteFile(dir+"ctrl_enc.key", pemEncoded, 0600)

	derBytes, err = x509.CreateCertificate(rand.Reader, &template1, ca_crt, publicKey(priv), ca_priv)
	if err != nil {
		log.Fatalf(dir+"Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"ctrl_enc.crt", pemEncoded, 0644)

	// SDP Gateway
	template2 := x509Template()
	template2.DNSNames = []string{gateway_domain}
	template2.Subject = subject
	template2.Subject.CommonName = gateway_domain

	// Gateway sign certificate
	template2.KeyUsage = x509.KeyUsageDigitalSignature
	priv, err = sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}
	pkcs8Encoded, err = x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	ioutil.WriteFile(dir+"gateway_sign.key", pemEncoded, 0600)

	derBytes, err = x509.CreateCertificate(rand.Reader, &template2, ca_crt, publicKey(priv), ca_priv)
	if err != nil {
		log.Fatalf("Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"gateway_sign.crt", pemEncoded, 0644)

	// Gateway encrypt certificate
	template2.KeyUsage = x509.KeyUsageKeyEncipherment
	priv, err = sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}
	pkcs8Encoded, err = x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	ioutil.WriteFile(dir+"gateway_enc.key", pemEncoded, 0600)

	derBytes, err = x509.CreateCertificate(rand.Reader, &template2, ca_crt, publicKey(priv), ca_priv)
	if err != nil {
		log.Fatalf("Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"gateway_enc.crt", pemEncoded, 0644)

	// SDP Client
	template3 := x509Template()
	template3.DNSNames = []string{client_domain}
	template3.Subject = subject
	template3.Subject.CommonName = client_domain

	// Client sign certificate
	template3.KeyUsage = x509.KeyUsageDigitalSignature
	priv, err = sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}
	pkcs8Encoded, err = x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	ioutil.WriteFile(dir+"client_sign.key", pemEncoded, 0600)

	derBytes, err = x509.CreateCertificate(rand.Reader, &template3, ca_crt, publicKey(priv), ca_priv)
	if err != nil {
		log.Fatalf("Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"client_sign.crt", pemEncoded, 0644)

	// Client encrypt certificate
	template3.KeyUsage = x509.KeyUsageKeyEncipherment
	priv, err = sm2.GenerateKey(rand.Reader)
	if err != nil {
		log.Fatalf("Failed to generate private key: %v", err)
	}
	pkcs8Encoded, err = x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		log.Fatalf("Failed to marshal private key: %v", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: pkcs8Encoded})
	ioutil.WriteFile(dir+"client_enc.key", pemEncoded, 0600)

	derBytes, err = x509.CreateCertificate(rand.Reader, &template3, ca_crt, publicKey(priv), ca_priv)
	if err != nil {
		log.Fatalf("Failed to create certificate: %s", err)
	}

	pemEncoded = pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	ioutil.WriteFile(dir+"client_enc.crt", pemEncoded, 0644)
}
