[package]
edition = "2021"
name = "xdp"
version = "0.0.0"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
default = ["tokio"]

[dependencies]
aya = { version = "0.12", default-features = false }
bitflags = "2"
codec = { path = "../codec" }
errno = "0.3"
libc = "0.2"
sdpcore = { package = "core", path = "../core" }
tokio = { version = "1", features = ["net"], optional = true }
