<template>
  <div>
    <div class="home-header">
      <div
        class="home-header-left"
        style="display: flex; flex-direction: column; align-items: center"
      >
        <img
          class="userhead"
          src=""
          v-real-img="imageUrl"
          alt=""
          width="50"
          height="50"
          @click="jumpToSettings"
          @error="handleImageError"
          style="cursor: pointer"
        />
        <div class="loginInfo">
          <strong class="f_w_no" :title="displayName.length > 3 ? displayName : ''">
            {{
              displayName.length > 3
                ? displayName.slice(0, 3) + "..."
                : displayName
            }}
          </strong>
        </div>
        <el-menu
          background-color="#F4F5F7"
          :default-active="$route.path"
          class="el-menu-vertical-demo"
        >
          <el-menu-item @click="navigationMenuClick('/')" index="1">
            <img
              :src="
                $route.path == '/'
                  ? '/assets/images/menuicon/workactive.png'
                  : '/assets/images/menuicon/workbench.png'
              "
              width="18"
              height="18"
              alt=""
            />
            <span
              :style="
                $route.path == '/' ? 'color: #F87F1A;' : 'color: #9C9EA3;'
              "
              >工作台</span
            >
          </el-menu-item>
          <el-menu-item @click="navigationMenuClick('/setting')" index="2">
            <img
              :src="
                $route.path == '/setting'
                  ? '/assets/images/menuicon/setactive.png'
                  : '/assets/images/menuicon/setting.png'
              "
              width="18"
              height="18"
              alt=""
            />
            <span
              :style="
                $route.path == '/setting'
                  ? 'color: #F87F1A;'
                  : 'color: #9C9EA3;'
              "
              >设置</span
            >
          </el-menu-item>
          <el-menu-item @click="navigationMenuClick()" index="3">
            <!-- <el-icon color="#9C9EA3" class="el-menu-icon"><SwitchButton /></el-icon> -->
            <img
              src="/assets/images/menuicon/close.png"
              alt=""
              width="16"
              height="16"
            />
            <span style="color: #9c9ea3">退出登录</span>
          </el-menu-item>
        </el-menu>
      </div>
      <div class="logoBottom">
        <img src="/assets/oneid_logo.png" alt="" width="30" />
        <span>v{{ productVersion }}</span>
      </div>
    </div>
    <el-dialog
      v-model="abnormalDisconnectVisible"
      title="异常断开"
      width="30%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="closeAbnormalDisconnectDialog"
    >
      <p style="padding: 30px 0; text-align: center">
        您的连接已断开，请重新登录
      </p>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            round
            type="primary"
            @click="closeAbnormalDisconnectDialog"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { invoke } from "@tauri-apps/api/tauri";
import { computed } from "vue";
import { useStore } from "vuex";
import { userinfo } from "../api/service.js";

export default {
  setup() {
    const store = useStore();
    return {
      productVersion: window.PRODUCT_VERSION,
      isLogin: computed(() => store.getters.isLogin),
    };
  },
  data() {
    return {
      displayName: "",
      item: "",
      imageUrl: "",
      defaultImageUrl: "/assets/user.png", // 默认图片地址
      abnormalDisconnectVisible: false, // 异常断开提示框
      times: 0,
      timer: null,
    };
  },
  created() {
    window.userdata = this.currentUserInfo;
    this.timer = setInterval(() => {
      this.currentUserInfo();
    }, 1000 * 60 * 15);
  },
  beforeUnmount() {
    clearInterval(this.timer);
  },
  methods: {
    // 获取用户信息
    currentUserInfo() {
      userinfo()
        .then((res) => {
          this.displayName = res.data.displayName;
          this.imageUrl =
            "http://sso.jingantech.com/identity-admin/v8/v1/sdk/files/" +
            res.data.avatarId +
            "?TENANT=" +
            CONFIG.selected_tenant.code; // 在线图片地址

          // 缓存用户信息
          this.$store.commit("setCurrentUser", res.data);
        })
        .catch((error) => {
          // 会话过期或用户有效期无效, 不再加载
          if (!error) {
            clearInterval(this.timer);
            return;
          }

          this.times++;
          if (this.times <= 3) {
            setTimeout(() => {
              this.currentUserInfo();
            }, 500);
          }
        });
    },
    handleSelect(key, keyPath) {},
    // 导航菜单点击事件
    navigationMenuClick(pagePath) {
      // 异常断开
      if (!this.isLogin) {
        this.abnormalDisconnectVisible = true;
        return;
      }
      if (pagePath) {
        this.$router.push(pagePath);
      } else {
        this.$confirm("是否立即退出登录?", "退出登录", {
          confirmButtonText: "立即退出",
          cancelButtonText: "取消",
          roundButton: true,
          class: "logout",
          center: true,
          closeOnClickModal: false,
          closeOnPressEscape: false,
        })
          .then(() => {
            this.loading = true;
            invoke("plugin:sdp|logout")
              .then(() => {
                this.$store.commit("updateIsLogin", false);
                window.processMfaAuthState = false;
                this.$router.push({
                  path: "/login",
                  query: { date: new Date().getTime() },
                });
              })
              .finally(() => {
                this.loading = false;
              });
          })
          .catch(() => {
            this.loading = false;
          });
      }
    },
    jumpToSettings() {
      this.$router.push("/setting");
    },
    handleImageError(event) {
      event.target.src = this.defaultImageUrl; // 加载失败时，将图片地址设置为默认图片地址
    },
    closeAbnormalDisconnectDialog() {
      this.abnormalDisconnectVisible = false;
      this.$router.push({
        path: "/login",
        query: { date: new Date().getTime() },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.el-menu {
  border-right: 0;
  margin-top: 15px;
}

.el-menu-item {
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  line-height: 22px;
  margin: 10px 15px 20px 15px;
  width: 52px;
  height: 50px;
  color: #9c9ea3;
}

.el-menu-icon {
  margin-left: 3px;
}

.el-menu-item.is-active {
  color: #f87f1a;
  border-radius: 8px 8px 8px 8px;
}

.logoBottom {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #9c9ea3;
}

.home-header {
  background: #f4f5f7;
  height: 100%;
  // width: 60vw;
  clear: both;
  // overflow: hidden;
  padding: 26px 0px 0 0px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .home-header-left {
    float: left;
    color: #fff;

    .home_icon_user {
      font-size: 60px;
      float: left;
      background: #666;
    }

    .loginInfo {
      margin-top: 5px;
      float: left;
      font-size: 12px;
      // line-height: 30px;
      vertical-align: middle;
      font-weight: 400;
      color: #25282f;

      .loginInfo-bottom {
        line-height: 20px;
        margin-top: 5px;
      }

      .home_icon_check {
        font-size: 18px;
        float: left;
      }

      .name-status {
        float: left;
        font-size: 13px;
        letter-spacing: 1px;
      }
    }

    .el-menu-vertical-demo span {
      font-size: 12px;
      font-weight: 600;
    }
  }
}

// :deep(.el-menu-item.is-active img) {
//     content: url('/assets/images/workactive.png');
// }
// :deep(.el-button .is-round){
//     padding: 0;
//     width: 112px;
//     height: 40;
// }
</style>