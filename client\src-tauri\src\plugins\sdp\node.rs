use crate::plugins;
use config::{AppConfig, Node};
use rand::Rng;
use tauri::State;
use tokio::sync::Mutex;

#[tauri::command(async)]
pub(super) async fn set_nodes(
    app_config: State<'_, Mutex<AppConfig>>,
    cluster_nodes: Vec<Node>,
) -> plugins::Result<AppConfig> {
    let mut config_lock = app_config.lock().await;
    config_lock.cluster_nodes = Some(cluster_nodes);
    if let Some(cluster_nodes) = config_lock.cluster_nodes.as_mut() {
        let mut rng = rand::thread_rng();
        let index = rng.gen_range(0..cluster_nodes.len());
        let node = cluster_nodes.get(index).unwrap().clone();

        log::debug!(target: "app", "Use node: {node:?}");

        config_lock.node = Some(node);
        config_lock.cluster_starting_index = index;
        config_lock.cluster_node_changed_times = 0;

        config_lock.selected_tenant = None;
        config_lock.tenants = None;
        config_lock.users = None;
        config_lock.secret_key = None;
    }

    Ok(config_lock.clone())
}
