use std::net::SocketAddr;

use tokio::net::TcpSocket;

use crate::traceability::ProxyError;

/// Bind TCP socket to the given `SocketAddr`, and returns the TCP socket with `SO_REUSEADDR` and
/// `SO_REUSEPORT` options. This option is required to re-bind the socket address when the proxy
/// instance is reconstructed.
pub(super) fn bind_tcp_socket(listening_on: &SocketAddr) -> Result<TcpSocket, ProxyError> {
    let tcp_socket = if listening_on.is_ipv6() {
        TcpSocket::new_v6()
    } else {
        TcpSocket::new_v4()
    }?;
    tcp_socket.set_reuseaddr(true)?;
    #[cfg(not(windows))]
    tcp_socket.set_reuseport(true)?;
    if let Err(e) = tcp_socket.bind(*listening_on) {
        log::error!("Failed to bind TCP socket `{listening_on}`: {}", e);
        return Err(ProxyError::Io(e));
    };
    Ok(tcp_socket)
}
