use dbus::blocking::{Proxy, SyncConnection};
use std::{sync::Arc, time::Duration};

type Result<T> = std::result::Result<T, Error>;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Failed to create a DBus connection")]
    ConnectError(#[source] dbus::Error),

    #[error("Failed to read defender firewall property")]
    ReadDefenderFirewallStateError(#[source] dbus::Error),
}

const DEFENDER_FIREWALL_BUS: &str = "com.deepin.defender.firewall";
const DEFENDER_FIREWALL_PATH: &str = "/com/deepin/defender/firewall";
const DEFENDER_FIREWALL_INTERFACE: &str = "com.deepin.defender.firewall";
const DEFENDER_FIREWALL_STATE: &str = "GetSwitch";

const RPC_TIMEOUT: Duration = Duration::from_secs(1);

/// Returns true if the firewall is on.
pub fn is_open() -> Result<bool> {
    Defender::new()?.is_open()
}

struct Defender {
    pub dbus_connection: Arc<SyncConnection>,
}

impl Defender {
    fn new() -> Result<Self> {
        Ok(Self {
            dbus_connection: crate::get_connection().map_err(Error::ConnectError)?,
        })
    }

    fn is_open(&self) -> Result<bool> {
        match self
            .as_manager_object()
            .method_call(DEFENDER_FIREWALL_INTERFACE, DEFENDER_FIREWALL_STATE, ())
            .map(|(open,): (bool,)| open)
        {
            Ok(open) => Ok(open),
            Err(error) => {
                if let Some(name) = error.name() {
                    if name.ends_with("ServiceUnknown") {
                        return Ok(false);
                    }
                }
                Err(Error::ReadDefenderFirewallStateError(error))
            }
        }
    }

    fn as_manager_object(&self) -> Proxy<'_, &SyncConnection> {
        Proxy::new(
            DEFENDER_FIREWALL_BUS,
            DEFENDER_FIREWALL_PATH,
            RPC_TIMEOUT,
            &self.dbus_connection,
        )
    }
}
