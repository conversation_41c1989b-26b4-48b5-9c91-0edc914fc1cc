// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

use aya::include_bytes_aligned;

/// The primary BPF program to direct traffic
pub static PRIMARY_BPF_PROGRAM: &[u8] = {
    #[cfg(target_endian = "little")]
    let prog = include_bytes_aligned!(concat!(
        env!("CARGO_MANIFEST_DIR"),
        "/src/bpf/sdp-bpfel-primary.ebpf"
    ));

    #[cfg(target_endian = "big")]
    let prog = include_bytes_aligned!(concat!(
        env!("CARGO_MANIFEST_DIR"),
        "/src/bpf/sdp-bpfeb-primary.ebpf"
    ));

    prog
};

/// The secondary BPF program to direct traffic
pub static SECONDARY_BPF_PROGRAM: &[u8] = {
    #[cfg(target_endian = "little")]
    let prog = include_bytes_aligned!(concat!(
        env!("CARGO_MANIFEST_DIR"),
        "/src/bpf/sdp-bpfel-secondary.ebpf"
    ));

    #[cfg(target_endian = "big")]
    let prog = include_bytes_aligned!(concat!(
        env!("CARGO_MANIFEST_DIR"),
        "/src/bpf/sdp-bpfeb-secondary.ebpf"
    ));

    prog
};

/// The name of the primary XDP program
pub static PRIMARY_PROGRAM_NAME: &str = "sdp_p";

/// The name of the secondary XDP program
pub static SECONDARY_PROGRAM_NAME: &str = "sdp_s";

/// The name of the AF_XDP socket map
pub static XSK_MAP_NAME: &str = "SOCKETS";

/// The name of the pub ports map
pub static PUB_PORTS_MAP_NAME: &str = "PUB_PORTS";

/// The name of the whitelist map
pub static WHITELIST_MAP_NAME: &str = "WHITELIST";

/// The name of the spa authed map
pub static PREAUTHED_MAP_NAME: &str = "PREAUTHED";

/// The name of the received spa clients map
pub static RECEIVED_MAP_NAME: &str = "RECEIVED";

/// The name of the connected map
pub static CONNECTED_MAP_NAME: &str = "CONNECTED";

/// The name of the resources map
// pub static RESOURCES_MAP_NAME: &str = "RESOURCES";

/// The name of the nat map
pub static NAT_MAP_NAME: &str = "NAT";
