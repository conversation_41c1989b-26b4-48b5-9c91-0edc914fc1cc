use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

use sdptypes::backend::SPAAuthorization;

use crate::types::{proto, FromProtobufTypeError};

impl From<sdptypes::backend::ConnectionConfig> for proto::ConnectionConfig {
    fn from(message: sdptypes::backend::ConnectionConfig) -> Self {
        Self {
            ip: message.ip.map(|ip| ip.to_string()),
            host: message.host,
            port: message.port as u32,
            spa_port: message.spa_port as u32,
            tenant: message
                .authorization
                .as_ref()
                .map(|authorization| authorization.tenant.to_owned()),
            secret: message
                .authorization
                .as_ref()
                .map(|authorization| authorization.secret.to_owned()),
            username: message
                .authorization
                .map(|authorization| authorization.username),
        }
    }
}

impl TryFrom<proto::ConnectionConfig> for sdptypes::backend::ConnectionConfig {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::ConnectionConfig) -> Result<Self, Self::Error> {
        let ip = if let Some(ip) = message.ip {
            Some(
                ip.parse::<IpAddr>()
                    .map_err(|_| FromProtobufTypeError::InvalidArgument("invalid ip address"))?,
            )
        } else {
            None
        };

        let authorization = message
            .tenant
            .zip(message.secret.zip(message.username))
            .map(|(tenant, (secret, username))| SPAAuthorization {
                tenant,
                secret,
                username,
            });

        Ok(Self {
            ip,
            host: message.host,
            port: message.port as u16,
            spa_port: message.spa_port as u16,
            authorization,
        })
    }
}

impl From<sdptypes::backend::Login> for proto::Login {
    fn from(message: sdptypes::backend::Login) -> Self {
        Self {
            connection_config: Some(proto::ConnectionConfig::from(message.connection_config)),
            payload: serde_json::to_string(&message.payload).unwrap(),
        }
    }
}

impl TryFrom<proto::Login> for sdptypes::backend::Login {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::Login) -> Result<Self, Self::Error> {
        use sdptypes::backend::ConnectionConfig;
        match message.connection_config {
            Some(connection_config) => Ok(Self {
                connection_config: ConnectionConfig::try_from(connection_config)?,
                payload: serde_json::from_str(&message.payload)
                    .map_err(|_| FromProtobufTypeError::InvalidArgument("invalid login payload"))?,
            }),
            None => Err(FromProtobufTypeError::InvalidArgument(
                "invalid login message",
            )),
        }
    }
}

impl From<sdptypes::backend::ChangePwdCode> for proto::ChangePwdCode {
    fn from(message: sdptypes::backend::ChangePwdCode) -> Self {
        Self {
            connection_config: Some(proto::ConnectionConfig::from(message.connection_config)),
            payload: serde_json::to_string(&message.payload).unwrap(),
        }
    }
}

impl TryFrom<proto::ChangePwdCode> for sdptypes::backend::ChangePwdCode {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::ChangePwdCode) -> Result<Self, Self::Error> {
        use sdptypes::backend::ConnectionConfig;
        match message.connection_config {
            Some(connection_config) => Ok(Self {
                connection_config: ConnectionConfig::try_from(connection_config)?,
                payload: serde_json::from_str(&message.payload)
                    .map_err(|_| FromProtobufTypeError::InvalidArgument("invalid login payload"))?,
            }),
            None => Err(FromProtobufTypeError::InvalidArgument(
                "invalid login message",
            )),
        }
    }
}

impl From<sdptypes::backend::ChangePwdOldPwd> for proto::ChangePwdOldPwd {
    fn from(message: sdptypes::backend::ChangePwdOldPwd) -> Self {
        Self {
            username: message.username,
            old_password: message.old_password,
            new_password: message.new_password,
        }
    }
}

impl From<sdptypes::backend::Ticket> for proto::Ticket {
    fn from(message: sdptypes::backend::Ticket) -> Self {
        Self {
            ticket: message.ticket,
            ticket_type: message.ticket_type as i32,
        }
    }
}

impl TryFrom<proto::Ticket> for sdptypes::backend::Ticket {
    type Error = FromProtobufTypeError;

    fn try_from(ticket: proto::Ticket) -> Result<Self, Self::Error> {
        Ok(Self {
            ticket: ticket.ticket,
            ticket_type: match ticket.ticket_type {
                0 => sdptypes::backend::TicketType::SSO,
                _ => sdptypes::backend::TicketType::Reconnect,
            },
        })
    }
}

impl From<sdptypes::backend::TicketType> for proto::ticket::TicketType {
    fn from(r#type: sdptypes::backend::TicketType) -> Self {
        match r#type {
            sdptypes::backend::TicketType::SSO => Self::Sso,
            sdptypes::backend::TicketType::Reconnect => Self::Reconnect,
        }
    }
}

impl TryFrom<proto::ticket::TicketType> for sdptypes::backend::TicketType {
    type Error = FromProtobufTypeError;

    fn try_from(r#type: proto::ticket::TicketType) -> Result<Self, Self::Error> {
        Ok(match r#type {
            proto::ticket::TicketType::Sso => Self::SSO,
            proto::ticket::TicketType::Reconnect => Self::Reconnect,
        })
    }
}

impl TryFrom<proto::ChangePwdOldPwd> for sdptypes::backend::ChangePwdOldPwd {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::ChangePwdOldPwd) -> Result<Self, Self::Error> {
        Ok(Self {
            username: message.username,
            old_password: message.old_password,
            new_password: message.new_password,
        })
    }
}

impl From<sdptypes::backend::AuthEvent> for proto::AuthEvent {
    fn from(event: sdptypes::backend::AuthEvent) -> Self {
        Self {
            ip_addr: event.ip_addr.map(|ip| match ip {
                IpAddr::V4(ip) => ip.octets().to_vec(),
                IpAddr::V6(ip) => ip.octets().to_vec(),
            }),
        }
    }
}

impl TryFrom<proto::AuthEvent> for sdptypes::backend::AuthEvent {
    type Error = FromProtobufTypeError;

    fn try_from(event: proto::AuthEvent) -> Result<Self, Self::Error> {
        let ip_addr = event
            .ip_addr
            .map(|ip| match ip.len() {
                4 => Ok(IpAddr::V4(Ipv4Addr::new(ip[0], ip[1], ip[2], ip[3]))),
                16 => Ok(IpAddr::V6(Ipv6Addr::from(
                    <Vec<u8> as TryInto<[u8; 16]>>::try_into(ip).unwrap(),
                ))),
                _ => Err(FromProtobufTypeError::InvalidArgument("invalid ip")),
            })
            .transpose()?;

        Ok(Self { ip_addr })
    }
}

impl From<sdptypes::backend::CompleteAuth> for proto::CompleteAuth {
    fn from(message: sdptypes::backend::CompleteAuth) -> Self {
        Self {
            auth_result_id: message.auth_result_id,
            ip_addr: message.ip_addr.map(|ip| match ip {
                IpAddr::V4(ip) => ip.octets().to_vec(),
                IpAddr::V6(ip) => ip.octets().to_vec(),
            }),
        }
    }
}

impl TryFrom<proto::CompleteAuth> for sdptypes::backend::CompleteAuth {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::CompleteAuth) -> Result<Self, Self::Error> {
        let ip_addr = message
            .ip_addr
            .map(|ip| match ip.len() {
                4 => Ok(IpAddr::V4(Ipv4Addr::new(ip[0], ip[1], ip[2], ip[3]))),
                16 => Ok(IpAddr::V6(Ipv6Addr::from(
                    <Vec<u8> as TryInto<[u8; 16]>>::try_into(ip).unwrap(),
                ))),
                _ => Err(FromProtobufTypeError::InvalidArgument("invalid ip")),
            })
            .transpose()?;

        Ok(Self {
            auth_result_id: message.auth_result_id,
            ip_addr,
        })
    }
}

impl From<sdptypes::backend::CancelAuth> for proto::CancelAuth {
    fn from(message: sdptypes::backend::CancelAuth) -> Self {
        Self {
            ip_addr: message.ip_addr.map(|ip| match ip {
                IpAddr::V4(ip) => ip.octets().to_vec(),
                IpAddr::V6(ip) => ip.octets().to_vec(),
            }),
        }
    }
}

impl TryFrom<proto::CancelAuth> for sdptypes::backend::CancelAuth {
    type Error = FromProtobufTypeError;

    fn try_from(message: proto::CancelAuth) -> Result<Self, Self::Error> {
        let ip_addr = message
            .ip_addr
            .map(|ip| match ip.len() {
                4 => Ok(IpAddr::V4(Ipv4Addr::new(ip[0], ip[1], ip[2], ip[3]))),
                16 => Ok(IpAddr::V6(Ipv6Addr::from(
                    <Vec<u8> as TryInto<[u8; 16]>>::try_into(ip).unwrap(),
                ))),
                _ => Err(FromProtobufTypeError::InvalidArgument("invalid ip")),
            })
            .transpose()?;

        Ok(Self { ip_addr })
    }
}
