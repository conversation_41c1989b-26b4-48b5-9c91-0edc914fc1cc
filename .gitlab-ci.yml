### 依赖Gitlib Runner得Docker配置, 需要将本机/data/succeeded/挂载到docker容器中

workflow:
  rules:
    - if: $CI_MERGE_REQUEST_ID
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'dev-7.0'

stages:
  - check_merge_request
  - test
  - report
  - notify
  - clean

checkMergeRequest:
  stage: check_merge_request
  rules:
    - if: '$CI_MERGE_REQUEST_ASSIGNEES == null'
      when: on_success
  script:
    - REQUEST_BODY="{\"projectId\":$CI_MERGE_REQUEST_PROJECT_ID,\"projectUrl\":\"$CI_MERGE_REQUEST_PROJECT_URL\",\"mgIid\":$CI_MERGE_REQUEST_IID,\"authorId\":\"$GITLAB_USER_ID\"}"
    - TOKEN=$(echo -n $REQUEST_BODY | openssl dgst -sha256 -hmac "gitlab_ci_to_ding_talk" | awk -F '=' '{print $2}')
    - curl 'http://sonar.jayxiam.com:9001/mg_invalid' -H 'Content-Type:application/json' -H "X-GitlabCI-Token:${TOKEN:1}" -d "$REQUEST_BODY"
    - exit 1
  tags:
    - shell

统一终端_前端:
  stage: test
  image: node:14.21.1-buster
  script:
    # 切换目录
    - cd client/ui
    # 安装依赖包
    - npm install --registry=https://registry.npm.taobao.org
    # 生成报告
    - npm run lint:report --path=/data/succeeded/${CI_PIPELINE_ID}front-report.json
  tags:
    - node_runner

提交报告_前端:
  stage: report
  needs: [统一终端_前端]
  script:
    # 提交报告
    - sonar-scanner -Dsonar.eslint.reportPaths=/data/succeeded/${CI_PIPELINE_ID}front-report.json -Dsonar.analysis.project_id=$CI_MERGE_REQUEST_PROJECT_ID -Dsonar.analysis.mg_iid=$CI_MERGE_REQUEST_IID -Dsonar.analysis.author_id=$GITLAB_USER_ID -Dsonar.analysis.author_name=$GITLAB_USER_NAME -Dsonar.analysis.mg_title="$CI_MERGE_REQUEST_TITLE" -Dsonar.analysis.msg="$CI_COMMIT_MESSAGE" -Dsonar.analysis.project_url=$CI_MERGE_REQUEST_PROJECT_URL -Dsonar.analysis.assignees=$CI_MERGE_REQUEST_ASSIGNEES
    - touch /data/succeeded/${CI_PIPELINE_ID}front
  tags:
    - shell

失败通知_前端:
  stage: notify
  needs: [提交报告_前端]
  script:
    - if [ -e "/data/succeeded/${CI_PIPELINE_ID}front" ]; then exit 0; fi;
    - REQUEST_BODY="{\"project\":\"统一终端-前端\",\"title\":\"$CI_MERGE_REQUEST_TITLE\",\"mgIid\":$CI_MERGE_REQUEST_IID,\"authorId\":\"$GITLAB_USER_ID\",\"commitMessage\":\"$CI_COMMIT_MESSAGE\",\"branch\":\"$CI_MERGE_REQUEST_SOURCE_BRANCH_NAME\",\"pipelineUrl\":\"$CI_PIPELINE_URL\"}"
    - TOKEN=$(echo -n $REQUEST_BODY | openssl dgst -sha256 -hmac "gitlab_ci_to_webhook" | awk -F '=' '{print $2}')
    - curl 'http://sonar.jayxiam.com:9001/scan_failed' -H 'Content-Type:application/json' -H "X-GitlabCI-Token:${TOKEN:1}" -d "$REQUEST_BODY"
  tags:
    - shell
  when: on_failure

数据清理:
  stage: clean
  script:
    - rm -f /data/succeeded/${CI_PIPELINE_ID}front
    - rm -f /data/succeeded/${CI_PIPELINE_ID}front-report.json
  tags:
    - shell
