use gm_sm3::sm3_hash;

pub struct Hotp<'a> {
    secret: &'a [u8],
}

impl<'a> Hotp<'a> {
    pub fn new(secret: &'a [u8]) -> Self {
        Hotp { secret }
    }

    pub fn generate(&self, counter: u64) -> u32 {
        let msg = counter.to_be_bytes();

        let mut key_msg = Vec::new();
        key_msg.extend_from_slice(self.secret);
        key_msg.extend_from_slice(&msg);
        key_msg.extend_from_slice(&[0u8; 8]);

        let hash = sm3_hash(&key_msg);

        let od = self.truncate_sm3(hash);

        od % 10u32.pow(6)
    }

    fn truncate_sm3(&self, hash: [u8; 32]) -> u32 {
        let s1 =
            self.ml(hash[0], 24) | self.ml(hash[1], 16) | self.ml(hash[2], 8) | self.ml(hash[3], 0);
        let s2 =
            self.ml(hash[4], 24) | self.ml(hash[5], 16) | self.ml(hash[6], 8) | self.ml(hash[7], 0);
        let s3 = self.ml(hash[8], 24)
            | self.ml(hash[9], 16)
            | self.ml(hash[10], 8)
            | self.ml(hash[11], 0);
        let s4 = self.ml(hash[12], 24)
            | self.ml(hash[13], 16)
            | self.ml(hash[14], 8)
            | self.ml(hash[15], 0);
        let s5 = self.ml(hash[16], 24)
            | self.ml(hash[17], 16)
            | self.ml(hash[18], 8)
            | self.ml(hash[19], 0);
        let s6 = self.ml(hash[20], 24)
            | self.ml(hash[21], 16)
            | self.ml(hash[22], 8)
            | self.ml(hash[23], 0);
        let s7 = self.ml(hash[24], 24)
            | self.ml(hash[25], 16)
            | self.ml(hash[26], 8)
            | self.ml(hash[27], 0);
        let s8 = self.ml(hash[28], 24)
            | self.ml(hash[29], 16)
            | self.ml(hash[30], 8)
            | self.ml(hash[31], 0);

        ((s1 + s2 + s3 + s4 + s5 + s6 + s7 + s8) % 2u64.pow(32)) as u32
    }

    fn ml(&self, x: u8, j: u8) -> u64 {
        (x as u64) << j
    }
}

#[cfg(test)]
mod tests {
    use super::Hotp;

    #[test]
    fn test_htop() {
        let secret = hex::decode!("F46BEB5FDAC37060B3ABDE80B73DB7671FFE4079".as_bytes());
        let hotp = Hotp::new(&secret);

        // let counter = Local::now().timestamp_millis() as u64;
        let counter = 27497009;
        let otp = hotp.generate(counter);
        assert_eq!(otp, 760315);

        let counter = 27497010;
        let otp = hotp.generate(counter);
        assert_eq!(otp, 981735);
    }
}
