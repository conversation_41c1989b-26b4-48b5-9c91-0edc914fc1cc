use std::time::Duration;

use reqwest::ClientBuilder;
use serde_json::Value;

pub async fn externet_ip() -> Option<(String, String, String)> {
    let url = "https://www.cz88.net/api/cz88/ip/base?ip=";
    let client = ClientBuilder::new()
        .timeout(Duration::from_secs(15))
        .build()
        .expect("failed to build client");
    let Ok(response) = client.get(url).send().await else {return None};
    let Ok(value) = response.json::<Value>().await else {return None};
    if let Some(200) = value["code"].as_u64() {
        let isp = value["data"]["isp"].as_str().map(ToOwned::to_owned).unwrap_or_default();
        let ip = value["data"]["ip"].as_str().map(ToOwned::to_owned).unwrap_or_default();
        let location = value["data"]["country"].as_str().map(ToOwned::to_owned).unwrap_or_default()
            + &value["data"]["province"].as_str().map(ToOwned::to_owned).unwrap_or_default()
            + &value["data"]["city"].as_str().map(ToOwned::to_owned).unwrap_or_default()
            + &value["data"]["districts"].as_str().map(ToOwned::to_owned).unwrap_or_default();
        return Some((ip, isp, location));
    }
    return None;
}
