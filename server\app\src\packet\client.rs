use std::{
    collections::{HashMap, HashSet},
    net::IpAddr,
    sync::Arc,
};

use base::net::{Addr, IpPacket};

use pnet_packet::ethernet::EtherTypes;
use tokio::sync::oneshot;
use tracing::{error, info, trace, warn, Instrument};

use crate::{
    arp::ArpHandle,
    config::Segments,
    nat::{Nat, NatState},
    network::Network,
    virtual_iface::ReqSender,
    xdp::{ClientPacketSenders, XdpCommand, XdpCommandSenders},
};

pub struct ClientForwardInitArgs {
    pub networks: Vec<Network>,
    pub client_packet_senders: ClientPacketSenders,
    pub xdp_command_senders: XdpCommandSenders,
    pub arp_handle: ArpHandle,
    pub nat: Arc<Nat>,

    pub primary_interface: String,
    /// 本机其他网卡IP地址
    pub addresses: Option<HashSet<IpAddr>>,
    /// 网卡与网卡连接的网段映射, 不包含主网卡
    pub segments: Segments,
    pub req_sender: Option<ReqSender>,
}

#[derive(Clone)]
pub struct ClientPacketHandle {
    command_tx: flume::Sender<(String, ClientPacket)>,
    primary_interface: String,
    addresses: Option<HashSet<IpAddr>>,
    /// 网卡与网卡连接的网段映射, 不包含主网卡
    segments: Segments,
    req_sender: Option<ReqSender>,
}

impl ClientPacketHandle {
    pub async fn send(&self, message: ClientPacket) {
        // 如果是本机其他网卡
        if let Some(ref addresses) = self.addresses {
            if addresses.contains(&message.1.dest) {
                if self
                    .command_tx
                    .send_async((self.primary_interface.clone(), message))
                    .await
                    .is_err()
                {
                    error!("user-side packet handler thread panicked");
                }
                return;
            }
        }

        // 如果是访问本机虚拟IP地址
        if let Some((ip, _src_ip)) = self.segments.virtual_ip() {
            if message.1.dest == ip {
                if let Some(req_sender) = &self.req_sender {
                    _ = req_sender.send_async(message).await;
                }
                return;
            }
        }

        let iface = self.segments.find_interface(&message.1.dest);
        if self
            .command_tx
            .send_async((iface.to_owned(), message))
            .await
            .is_err()
        {
            error!("user-side packet handler thread panicked");
        }
    }
}

pub type ClientPacket = (String, IpPacket, bool);

pub fn spawn(args: ClientForwardInitArgs) -> ClientPacketHandle {
    let (tx, rx) = flume::unbounded::<(String, ClientPacket)>();

    let mut network_map = HashMap::new();
    args.networks.into_iter().for_each(|network| {
        network_map.insert(network.iface.clone(), network);
    });

    tokio::spawn(
        async move {
            info!("create a user-side packet handler");
            loop {
                match rx.recv_async().await {
                    Err(_) => break,
                    Ok((iface, (device_id, packet, tracing))) => {
                        let network = network_map.get(&iface).unwrap();

                        let Some(target_mac) =
                            args.arp_handle.get_target_mac(&iface, packet.dest).await
                        else {
                            error!("Mac address of `{}` was not found.", packet.dest);
                            continue;
                        };

                        let (access_addr, buf) = match client_nat(
                            tracing,
                            device_id.clone(),
                            network,
                            packet,
                            args.nat.clone(),
                            target_mac,
                        )
                        .await
                        {
                            (addr, Some(buf)) => (addr, buf),
                            _ => continue,
                        };

                        // 将访问目标地址写入内核
                        if let Some(access_addr) = access_addr {
                            let (tx, rx) = oneshot::channel();
                            args.xdp_command_senders
                                .send(XdpCommand::Nat((access_addr.0, access_addr.1), Some(tx)))
                                .await;

                            if let Ok(false) = rx.await {
                                continue;
                            }
                        }

                        trace!(device.id = device_id, iface, "{:?}", &buf);

                        args.client_packet_senders.send(&iface, buf).await;
                    }
                }
            }
        }
        .instrument(tracing::error_span!("ClientPacketProcessor")),
    );

    ClientPacketHandle {
        command_tx: tx,
        primary_interface: args.primary_interface,
        addresses: args.addresses,
        segments: args.segments,
        req_sender: args.req_sender,
    }
}

/// 将客户端的消息转发给服务端
///
/// - `tracing`: 是否溯源
/// - `packet`: 客户端数据包
/// - `gateway_mac`: 网关Mac地址
/// - `local_mac`: 本地Mac地址
/// - `local_ip`: 本地IP地址
///
/// ## Returns
///
/// 如果转发成功, 则返回访问目标地址和发送的数据包
async fn client_nat<'a>(
    tracing: bool,
    device_id: String,
    network: &Network,
    mut packet: IpPacket,
    nat: Arc<Nat>,
    target_mac: [u8; 6],
) -> (Option<Addr>, Option<Vec<u8>>) {
    let Some((access_addr, client_local_addr)) = packet.address() else {
        warn!(protocol = packet.next_protocol, "unsupported");
        return (None, None);
    };

    if let Some(nat_state) = nat
        .get_or_map(tracing, device_id, client_local_addr, access_addr)
        .await
    {
        // 设置源地址
        packet.set_src(IpAddr::V4(network.ipv4net.addr()));

        // 设置IP校验和
        packet.update_ip_checksum();

        // 设置标识符或目标端口
        packet.set_src_port(nat_state.port());

        // 设置校验和
        packet.update_next_protocol_checksum();

        // 填充mac地址
        let mut data = packet.data;
        for _ in 0..14 {
            data.insert(0, 0)
        }
        data[0..6].copy_from_slice(&target_mac);
        data[6..12].copy_from_slice(&network.mac.octets());
        data[12..14].copy_from_slice(&EtherTypes::Ipv4.0.to_be_bytes());

        return match packet.next_protocol {
            1 | 58 => (Some(Addr(access_addr.0, nat_state.port())), Some(data)),
            _ => match nat_state {
                NatState::Created(_) => (Some(access_addr), Some(data)),
                NatState::Already(_) => (None, Some(data)),
            },
        };
    }

    warn!(
        protocol = packet.next_protocol,
        "no NAT for {:?}:{:?}", access_addr.0, access_addr.1
    );
    return (None, None);
}
