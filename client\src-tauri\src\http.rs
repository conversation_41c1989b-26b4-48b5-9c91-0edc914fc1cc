use std::time::Duration;

use reqwest::{
    header::{HeaderMap, HeaderValue},
    Client, ClientBuilder,
};
use tokio::sync::OnceCell;

static INSTANCE: OnceCell<Client> = OnceCell::const_new();

fn default_headers() -> HeaderMap {
    let mut headers = HeaderMap::new();
    headers.append("Content-Type", HeaderValue::from_static("application/json"));
    headers.append("Accept", HeaderValue::from_static("application/json"));
    headers.append("APP-TYPE", HeaderValue::from_static("DESKTOP"));
    headers
}

pub async fn client() -> &'static Client {
    INSTANCE
        .get_or_init(|| async {
            ClientBuilder::new()
                .default_headers(default_headers())
                .timeout(Duration::from_secs(15))
                .danger_accept_invalid_certs(true)
                .danger_accept_invalid_hostnames(true)
                .no_proxy()
                .build()
                .expect("Failed to build client")
        })
        .await
}
