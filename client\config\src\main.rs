use config::AppConfig;

fn main() {
    let resource_dir = match paths::resource_dir() {
        Ok(dir) => dir,
        Err(error) => {
            log::error!("{error}");
            std::process::exit(1)
        }
    };

    // 升级配置文件
    let _ = config::migrations::migrate_all(&resource_dir);

    // 配置文件中的控制器地址与程序包默认地址不匹配时, 覆盖默认配置
    #[cfg(feature = "cluster")]
    let matched = {
        // 加载配置文件
        let app_config = config::init(resource_dir);

        if let (Some(cluster_config_url), Some(cluster_exterbak_config_url)) = (
            config::CLUSTER_CONFIG_URL,
            config::CLUSTER_EXTERNAL_CONFIG_URL,
        ) {
            if app_config.cluster_config_url.is_none()
                || app_config.cluster_external_config_url.is_none()
            {
                false
            } else {
                let internal = app_config.cluster_config_url.as_ref().unwrap();
                let external = app_config.cluster_config_url.as_ref().unwrap();
                let internal = internal.to_string();
                let external = external.to_string();
                internal == cluster_config_url && external == cluster_exterbak_config_url
            }
        } else {
            true
        }
    };

    #[cfg(all(feature = "sdp", not(feature = "cluster")))]
    let matched = true;

    #[cfg(any(feature = "cluster", feature = "sdp"))]
    {
        if !matched {
            let default_config = AppConfig::default();
            _ = default_config.save_file();
        }
    }
}
