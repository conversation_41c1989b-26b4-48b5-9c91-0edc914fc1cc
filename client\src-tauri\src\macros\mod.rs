#[macro_export]
macro_rules! notify {
    ($handle: expr, $message: expr) => {
        use notify_rust::Notification;
        let config = $handle.config();
        #[cfg(windows)]
        let identifier = &config.tauri.bundle.identifier;
        let appname = config.package.product_name.as_deref().unwrap();

        let mut toast = Notification::new();
        toast.appname(appname).summary("错误").body($message);
        #[cfg(windows)]
        toast.app_id(identifier);
        #[cfg(target_os = "macos")]
        {
            use notify_rust::{
                error::MacOsError, get_bundle_identifier_or_default, set_application,
            };

            let app_id = get_bundle_identifier_or_default(appname);
            match set_application(&app_id) {
                Ok(_) => {}
                Err(MacOsError::Application(error)) => println!("{}", error),
                Err(MacOsError::Notification(error)) => println!("{}", error),
            }
        }
        _ = toast.show();
    };
    ($handle: expr, $message: literal) => {
        use notify_rust::Notification;
        let config = $handle.config();
        #[cfg(windows)]
        let identifier = &config.tauri.bundle.identifier;
        let appname = config.package.product_name.as_deref().unwrap();

        let mut toast = Notification::new();
        toast.appname(appname).summary("错误").body($message);
        #[cfg(windows)]
        toast.app_id(identifier);
        _ = toast.show();
    };

    ($handle: expr, $title: literal, $message: literal) => {
        use notify_rust::Notification;
        let config = $handle.config();
        #[cfg(windows)]
        let identifier = &config.tauri.bundle.identifier;
        let appname = config.package.product_name.as_deref().unwrap();

        let mut toast = Notification::new();
        toast.appname(appname).summary($title).body($message);
        #[cfg(windows)]
        toast.app_id(identifier);
        _ = toast.show();
    };

    ($handle: expr, $title: literal, $message: literal, $icon: literal) => {
        use notify_rust::Notification;
        let config = $handle.config();
        #[cfg(windows)]
        let identifier = &config.tauri.bundle.identifier;
        let appname = config.package.product_name.as_deref().unwrap();

        let mut toast = Notification::new();
        toast
            .appname(appname)
            .icon($icon)
            .summary($title)
            .body($message);
        #[cfg(windows)]
        toast.app_id(identifier);
        _ = toast.show();
    };
}

#[macro_export]
macro_rules! log_err {
    ($result: expr) => {
        if let Err(err) = $result {
            log::error!(target: "app", "{err}");
        }
    };

    ($result: expr, $err_str: expr) => {
        if let Err(_) = $result {
            log::error!(target: "app", "{}", $err_str);
        }
    };
}
