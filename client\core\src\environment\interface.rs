use netdev::interface::InterfaceType;
use serde_json::{json, Value};

pub fn list() -> Option<Vec<Value>> {
    let mut interfaces = netdev::get_interfaces();
    interfaces.sort_by(|x, y| x.index.cmp(&y.index));
    interfaces
        .iter()
        .filter(|interface| {
            if interface.if_type != InterfaceType::Ethernet && interface.if_type != InterfaceType::Wireless80211 {
                return false;
            }

            !interface.ipv4.is_empty() && interface.gateway.is_some()
        })
        .map(|interface| {
            interface.ipv4.iter().map(|ip|json!({
                    "ip": ip.addr.to_string(),
                    "gateway": interface.gateway.as_ref().map(|network_device| network_device.ipv4.first().map(|ip|ip.to_string())),
                    "interfaceName": interface.friendly_name.clone(),
                    "netmask": ip.netmask.to_string(),
                    "mac": interface.mac_addr.as_ref().map(|mac| mac.to_string()),
                })).collect::<Vec<Value>>()
        })
        .reduce(|mut prev, mut curr| {
            curr.append(&mut prev);
            curr
        })
}
