use windows::Win32::{
    Networking::NetworkListManager::{
        INetworkListManager, NetworkListManager, NLM_ENUM_NETWORK_CONNECTED,
    },
    System::Com::{CoCreateInstance, CoInitialize, CLSCTX_INPROC_SERVER},
};

pub fn connected_internet() -> bool {
    if unsafe { CoInitialize(None) }.is_err() {
        return false;
    }

    let result: Result<INetworkListManager, windows::core::Error> =
        unsafe { CoCreateInstance(&NetworkListManager, None, CLSCTX_INPROC_SERVER) };

    let Ok(nlm) = result else {
        return false;
    };

    if let Ok(enum_networks) = unsafe { nlm.GetNetworks(NLM_ENUM_NETWORK_CONNECTED) } {
        let mut networks = vec![None; 5];
        let mut num = 5;
        if unsafe { enum_networks.Next(&mut networks, Some(&mut num)) }.is_err() {
            return false;
        }
        for network in networks.iter().flatten() {
            unsafe {
                if network
                    .IsConnectedToInternet()
                    .map(|v| v.as_bool())
                    .unwrap_or_default()
                {
                    return true;
                }
            };
        }
    }
    false
}
