#[cfg(target_arch = "wasm32")]
extern crate wasm_bindgen;

use gm_sm4::{CipherMode, Sm4CipherMode, Sm4Result};
#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

/// SQLite 加密密钥
pub const SM4_KEY: [u8; 16] = [
    59, 231, 201, 214, 165, 148, 0, 162, 182, 70, 253, 224, 244, 44, 150, 152,
];
/// SQLite 加密偏移量
pub const SM4_IV: [u8; 16] = [
    94, 144, 122, 202, 172, 91, 233, 123, 104, 206, 67, 170, 23, 19, 216, 4,
];

pub fn sm4encrypt(key: &[u8], data: &[u8], iv: &[u8]) -> Sm4Result<Vec<u8>> {
    let cipher = Sm4CipherMode::new(key, CipherMode::Cbc).unwrap();
    cipher.encrypt(data, iv)
}

pub fn sm4decrypt(key: &[u8], data: &[u8], iv: &[u8]) -> Sm4Result<Vec<u8>> {
    let cipher = Sm4CipherMode::new(key, CipherMode::Cbc).unwrap();
    cipher.decrypt(data, iv)
}

#[allow(non_snake_case)]
#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn sm4Encrypt(key: &str, data: &str, iv: &str) -> Result<String, String> {
    let Ok(key) = hex::decode(key) else {
        return Err("key error".to_owned());
    };

    let Ok(iv) = hex::decode(iv) else {
        return Err("iv error".to_owned());
    };

    sm4encrypt(&key, data.as_bytes(), &iv)
        .map(hex::encode)
        .map_err(|err| err.to_string())
}

#[allow(non_snake_case)]
#[cfg(target_arch = "wasm32")]
#[wasm_bindgen]
pub fn sm4Decrypt(key: &str, data: &str, iv: &str) -> Result<String, String> {
    let Ok(key) = hex::decode(key) else {
        return Err("key error".to_owned());
    };

    let Ok(data) = hex::decode(data) else {
        return Err("iv error".to_owned());
    };

    let Ok(iv) = hex::decode(iv) else {
        return Err("iv error".to_owned());
    };

    sm4decrypt(&key, &data, &iv)
        .map(|data| String::from_utf8(data).unwrap())
        .map_err(|err| err.to_string())
}

#[cfg(test)]
mod tests {
    use crate::{sm4decrypt, sm4encrypt};

    const SM4_KEY: [u8; 16] = [
        59, 231, 201, 214, 165, 148, 0, 162, 182, 70, 253, 224, 244, 44, 150, 152,
    ];

    const SM4_IV: [u8; 16] = [
        94, 144, 122, 202, 172, 91, 233, 123, 104, 206, 67, 170, 23, 19, 216, 4,
    ];

    #[test]
    fn test_encrypt() {
        let result = sm4encrypt(&SM4_KEY, b"123456", &SM4_IV);
        assert!(result.is_ok());
    }

    #[test]
    fn test_decrypt() {
        let data = [
            73, 165, 52, 184, 159, 116, 234, 221, 101, 99, 232, 129, 213, 167, 27, 179,
        ];

        let result = sm4decrypt(&SM4_KEY, &data, &SM4_IV);
        assert!(result.is_ok());

        assert_eq!("123456", String::from_utf8(result.unwrap()).unwrap());
    }
}
