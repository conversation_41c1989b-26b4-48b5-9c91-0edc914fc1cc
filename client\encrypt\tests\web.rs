#![cfg(target_arch = "wasm32")]

extern crate wasm_bindgen_test;

use wasm_bindgen_test::*;

wasm_bindgen_test_configure!(run_in_browser);

extern crate encrypt;
use encrypt::{sm4Decrypt, sm4Encrypt};

const SM4_KEY: &str = "3be7c9d6a59400a2b646fde0f42c9698";

const SM4_IV: &str = "5e907acaac5be97b68ce43aa1713d804";

#[wasm_bindgen_test]
fn test_encrypt() {
    let result = sm4Encrypt(SM4_KEY, "123456", SM4_IV);
    assert!(result.is_ok());
}

#[wasm_bindgen_test]
fn test_decrypt() {
    let data = "49a534b89f74eadd6563e881d5a71bb3";

    let result = sm4Decrypt(SM4_KEY, data, SM4_IV);
    assert!(result.is_ok());

    assert!("123456" == result.unwrap());
}
