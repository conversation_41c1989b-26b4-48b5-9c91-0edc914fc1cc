mod de;
mod ser;

use proc_macro::TokenStream;
use proc_macro2::Ident;
use syn::{
    parse_macro_input, spanned::Spanned, Attribute, DeriveInput, Error, Expr, ExprLit, Field, Lit,
    MetaNameValue, Type,
};

#[proc_macro_derive(Serialize, attributes(tlv))]
pub fn derive_serialize(input: TokenStream) -> TokenStream {
    let mut input = parse_macro_input!(input as DeriveInput);
    ser::expand_derive_serialize(&mut input)
        .unwrap_or_else(syn::Error::into_compile_error)
        .into()
}

#[proc_macro_derive(Deserialize, attributes(tlv))]
pub fn derive_deserialize(input: TokenStream) -> TokenStream {
    let mut input = parse_macro_input!(input as DeriveInput);
    de::expand_derive_deserialize(&mut input)
        .unwrap_or_else(syn::Error::into_compile_error)
        .into()
}

const VALUES: &[&str] = &["json", "sequence", "hex", "sequence,json", "untag"];

fn get_tlv_value(attrs: &[Attribute]) -> syn::Result<Option<String>> {
    for attr in attrs {
        if attr.path().is_ident("tlv") {
            if let syn::Meta::NameValue(MetaNameValue {
                value:
                    Expr::Lit(ExprLit {
                        lit: Lit::Str(lit_str),
                        ..
                    }),
                ..
            }) = &attr.meta
            {
                let value = lit_str.value();
                if !VALUES.contains(&value.as_str()) {
                    return Err(Error::new(
                        lit_str.span(),
                        format!("unknown value `{}`", value),
                    ));
                }
                return Ok(Some(value));
            }
        }
    }

    Ok(None)
}

fn get_unnamed_enum_type(field: &Field) -> syn::Result<&Ident> {
    let Type::Path(type_path) = &field.ty else {
        return Err(Error::new(field.span(), "unsupported type"));
    };
    let inner_type = type_path.path.segments.first().unwrap();
    Ok(&inner_type.ident)
}
