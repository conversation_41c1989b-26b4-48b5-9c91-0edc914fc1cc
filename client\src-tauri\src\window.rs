#[cfg(target_os = "macos")]
use tauri::utils::TitleBarStyle;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Window, Wry};

#[cfg(target_os = "macos")]
use crate::log_err;
#[cfg(target_os = "macos")]
use crate::window_ext::WindowExt;
#[cfg(target_os = "macos")]
use window_vibrancy::{apply_vibrancy, NSVisualEffectMaterial};

pub(super) fn create_start_page(app_handle: &AppHandle<Wry>) {
    let builder = tauri::window::WindowBuilder::new(
        app_handle,
        "splashscreen".to_string(),
        tauri::WindowUrl::App("splashscreen.html#start_page".into()),
    )
    .title(&app_handle.package_info().name)
    .center()
    .fullscreen(false)
    .resizable(false)
    .decorations(false);

    #[cfg(target_os = "macos")]
    let builder = builder
        .decorations(true)
        .hidden_title(true)
        .title_bar_style(TitleBarStyle::Overlay);

    let window = builder.inner_size(974.0, 654.0).build().unwrap();

    // 隐藏所有按钮
    #[cfg(target_os = "macos")]
    {
        log_err!(window.set_button_visible(0, false));
        log_err!(window.set_button_visible(1, false));
    }

    beautify_window(window);
}

pub fn create_main_window(app_handle: &AppHandle<Wry>, init_script: String) {
    if let Some(window) = app_handle.get_window("main") {
        let _ = window.unminimize();
        let _ = window.show();
        let _ = window.set_focus();
        return;
    }

    let builder = tauri::window::WindowBuilder::new(
        app_handle,
        "main".to_string(),
        tauri::WindowUrl::App("index.html".into()),
    )
    .title(&app_handle.package_info().name)
    .center()
    .fullscreen(false)
    .resizable(false)
    .decorations(false);

    #[cfg(target_os = "macos")]
    let builder = builder
        .decorations(true)
        .accept_first_mouse(true)
        .hidden_title(true)
        .title_bar_style(TitleBarStyle::Overlay);

    // 将配置发送给前端
    // NOTE: 前端执行 location.reload() 会重新执行该脚本
    let builder = builder.initialization_script(&init_script);

    let window = builder.inner_size(974.0, 654.0).build().unwrap();

    app_handle
        .run_on_main_thread(|| beautify_window(window))
        .expect("Failed to set window effects.");
}

#[cfg_attr(target_os = "linux", allow(unused_variables))]
fn beautify_window(window: Window<Wry>) {
    #[cfg(not(target_os = "linux"))]
    window_shadows::set_shadow(&window, true).expect("Unsupported platform!");
    // 设置窗口模糊特效
    #[cfg(target_os = "macos")]
    apply_vibrancy(&window, NSVisualEffectMaterial::Sidebar, None, None)
        .expect("Unsupported platform! 'apply_vibrancy' is only supported on macOS");

    #[cfg(target_os = "macos")]
    {
        log_err!(window.position_traffic_lights(9.0, 10.5));

        // 隐藏最大化按钮
        log_err!(window.set_button_visible(2, false));
    }
}
