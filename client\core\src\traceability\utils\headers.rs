use crate::traceability::ProxyError;
use bytes::BufMut;
use hyper::header::{self, HeaderMap, HeaderName, HeaderValue};
use std::{borrow::Cow, net::IpAddr};

////////////////////////////////////////////////////
// Functions to manipulate headers

/// Append header entry with comma according to [RFC9110](https://datatracker.ietf.org/doc/html/rfc9110)
#[allow(dead_code)]
pub(crate) fn append_header_entry_with_comma(
    headers: &mut HeaderMap,
    key: &str,
    value: &str,
) -> Result<(), ProxyError> {
    match headers.entry(HeaderName::from_bytes(key.as_bytes())?) {
        header::Entry::Vacant(entry) => {
            entry.insert(value.parse::<HeaderValue>()?);
        }
        header::Entry::Occupied(mut entry) => {
            // entry.append(value.parse::<HeaderValue>()?);
            let mut new_value =
                Vec::<u8>::with_capacity(entry.get().as_bytes().len() + 2 + value.len());
            new_value.put_slice(entry.get().as_bytes());
            new_value.put_slice(&[b',', b' ']);
            new_value.put_slice(value.as_bytes());
            entry.insert(HeaderValue::from_bytes(&new_value)?);
        }
    }

    Ok(())
}

/// Add header entry if not exist
pub(super) fn add_header_entry_if_not_exist(
    headers: &mut HeaderMap,
    key: impl Into<Cow<'static, str>>,
    value: impl Into<Cow<'static, str>>,
) -> Result<(), ProxyError> {
    match headers.entry(HeaderName::from_bytes(key.into().as_bytes())?) {
        header::Entry::Vacant(entry) => {
            entry.insert(value.into().parse::<HeaderValue>()?);
        }
        header::Entry::Occupied(_) => (),
    };

    Ok(())
}

/// Overwrite header entry if exist
pub(crate) fn add_header_entry_overwrite_if_exist(
    headers: &mut HeaderMap,
    key: impl Into<Cow<'static, str>>,
    value: impl Into<Cow<'static, str>>,
) -> Result<(), ProxyError> {
    match headers.entry(HeaderName::from_bytes(key.into().as_bytes())?) {
        header::Entry::Vacant(entry) => {
            entry.insert(value.into().parse::<HeaderValue>()?);
        }
        header::Entry::Occupied(mut entry) => {
            entry.insert(HeaderValue::from_bytes(value.into().as_bytes())?);
        }
    }

    Ok(())
}

/// Align cookie values in single line
/// Sometimes violates [RFC6265](https://www.rfc-editor.org/rfc/rfc6265#section-5.4) (for http/1.1).
/// This is allowed in RFC7540 (for http/2) as mentioned [here](https://stackoverflow.com/questions/4843556/in-http-specification-what-is-the-string-that-separates-cookies).
pub(crate) fn make_cookie_single_line(headers: &mut HeaderMap) -> Result<(), ProxyError> {
    let cookies = headers
        .iter()
        .filter(|(k, _)| **k == header::COOKIE)
        .map(|(_, v)| v.to_str().unwrap_or(""))
        .collect::<Vec<_>>()
        .join("; ");
    if !cookies.is_empty() {
        headers.remove(header::COOKIE);
        headers.insert(header::COOKIE, HeaderValue::from_bytes(cookies.as_bytes())?);
    }
    Ok(())
}

/// Add forwarding headers like `x-forwarded-for`.
pub(crate) fn add_forwarding_header(
    headers: &mut HeaderMap,
    scheme: &'static str,
    host: &str,
    origin_ip: IpAddr,
    origin_port: u16,
    uri_str: &str,
) -> Result<(), ProxyError> {
    let canonical_client_addr = origin_ip.to_string();

    // https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/Forwarded
    // Forwarded: by=<identifier>; for=<identifier>; host=<host>; proto=<http|https>
    add_header_entry_overwrite_if_exist(
        headers,
        "forwarded",
        format!(
            "for={}:{};host={};proto={}",
            &canonical_client_addr, origin_port, host, scheme
        ),
    )?;

    add_header_entry_overwrite_if_exist(headers, "x-forwarded-for", canonical_client_addr.clone())?;

    // Single line cookie header
    // TODO: This should be only for HTTP/1.1. For 2+, this can be multi-lined.
    make_cookie_single_line(headers)?;

    /////////// As Nginx
    // If we receive X-Forwarded-Proto, pass it through; otherwise, pass along the
    // scheme used to connect to this server
    add_header_entry_if_not_exist(headers, "x-forwarded-proto", scheme)?;
    // If we receive X-Forwarded-Port, pass it through; otherwise, pass along the
    // server port the client connected to
    add_header_entry_if_not_exist(headers, "x-forwarded-port", origin_port.to_string())?;

    /////////// As Nginx-Proxy
    // x-real-ip
    add_header_entry_overwrite_if_exist(headers, "x-real-ip", canonical_client_addr)?;
    // x-original-uri
    add_header_entry_overwrite_if_exist(headers, "x-original-uri", uri_str.to_string())?;
    // proxy
    add_header_entry_overwrite_if_exist(headers, "proxy", "")?;

    Ok(())
}

/// Add device-id header.
pub(crate) fn add_device_id_header(
    headers: &mut HeaderMap,
    device_id: String,
) -> Result<(), ProxyError> {
    add_header_entry_overwrite_if_exist(headers, "device-id", device_id)?;
    Ok(())
}
