use std::{
    ffi::c_void,
    ptr::{self, slice_from_raw_parts},
};

use log::{trace, warn};
use windows_sys::Win32::{
    Foundation::{ERROR_SERVICE_NOT_ACTIVE, ERROR_SUCCESS},
    NetworkManagement::WiFi::{
        wlan_interface_state_connected, WlanCloseHandle, WlanEnumInterfaces, WlanFreeMemory,
        WlanGetAvailableNetworkList, WlanOpenHandle,
        WLAN_AVAILABLE_NETWORK_INCLUDE_ALL_MANUAL_HIDDEN_PROFILES, WLAN_AVAILABLE_NETWORK_LIST,
        WLAN_INTERFACE_INFO, WLAN_INTERFACE_INFO_LIST,
    },
};

pub fn network_ssid() -> Vec<String> {
    // 检查依赖
    if std::fs::File::open("C:\\Windows\\System32\\Wlanapi.dll").is_err() {
        trace!("Wireless LAN Service is not installed and started");
        return vec![];
    }

    let dwclientversion = 2;
    let preserved = ptr::null_mut::<c_void>();
    let mut pdwnegotiatedversion = 0u32;
    let mut phclienthandle = 0isize;
    unsafe {
        let ret = WlanOpenHandle(
            dwclientversion,
            preserved,
            &mut pdwnegotiatedversion as *mut u32,
            &mut phclienthandle as *mut isize,
        );

        // WiFi组件不可用
        if ERROR_SERVICE_NOT_ACTIVE == ret {
            trace!("Wireless LAN Service unavailable");
            return vec![];
        }

        if ERROR_SUCCESS != ret {
            warn!("Failed to open wlan handle. {}", ret);
            return vec![];
        }

        let mut ppinterfacelist = ptr::null_mut::<WLAN_INTERFACE_INFO_LIST>();
        let ret = WlanEnumInterfaces(phclienthandle, preserved, &mut ppinterfacelist);

        if ERROR_SUCCESS != ret {
            warn!("Failed to enum wlan interfaces. {}", ret);
            _ = WlanCloseHandle(phclienthandle, preserved);
            return vec![];
        }

        let deref = &*ppinterfacelist;
        let interfaces = slice_from_raw_parts(
            &deref.InterfaceInfo as *const WLAN_INTERFACE_INFO,
            deref.dwNumberOfItems as usize,
        );

        let interfaces = &*interfaces;
        let mut names: Vec<String> = interfaces
            .iter()
            .filter_map(|interface| {
                if interface.isState != wlan_interface_state_connected {
                    return None;
                }

                let mut ppavailablenetworklist = ptr::null_mut::<WLAN_AVAILABLE_NETWORK_LIST>();

                let ret = WlanGetAvailableNetworkList(
                    phclienthandle,
                    &interface.InterfaceGuid,
                    WLAN_AVAILABLE_NETWORK_INCLUDE_ALL_MANUAL_HIDDEN_PROFILES,
                    preserved,
                    &mut ppavailablenetworklist,
                );
                if ERROR_SUCCESS != ret {
                    let description = String::from_utf16(&interface.strInterfaceDescription);
                    warn!(
                        "Failed to get wlan available network list for {:?}. {}",
                        description, ret
                    );
                    None
                } else {
                    let deref = &*ppavailablenetworklist;
                    let len = deref.Network[0].dot11Ssid.uSSIDLength;
                    let ucssid = deref.Network[0].dot11Ssid.ucSSID;
                    Some(String::from_utf8_lossy(&ucssid[..len as usize]).to_string())
                }
            })
            .collect();
        WlanFreeMemory(ppinterfacelist as *const c_void);
        _ = WlanCloseHandle(phclienthandle, preserved);
        names.sort_by(|x, y| x.cmp(&y));
        names
    }
}
