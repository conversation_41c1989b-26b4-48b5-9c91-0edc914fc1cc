use std::{env, fs, path::PathBuf, process::Command};

use time::{format_description::well_known::Iso8601, OffsetDateTime};

fn main() {
    let out_dir = PathBuf::from(env::var_os("OUT_DIR").unwrap());
    fs::write(out_dir.join("git-commit-date.txt"), commit_date()).unwrap();

    println!(
        "cargo:rustc-env=BUILD_TIME={}",
        OffsetDateTime::now_local()
            .unwrap()
            .format(&Iso8601::DATE_TIME)
            .unwrap()
    );

    built::write_built_file().expect("Failed to acquire build-time information");
}

fn commit_date() -> String {
    let output = Command::new("git")
        .args(["log", "-1", "--date=short", "--pretty=format:%cd"])
        .output()
        .expect("Unable to get git commit date");
    std::str::from_utf8(&output.stdout)
        .unwrap()
        .trim()
        .to_owned()
}
