use std::{fs, io, str::FromStr};

use tokio::{
    io::{AsyncBufReadExt, AsyncWriteExt, BufReader},
    net::UnixListener,
    sync::{
        mpsc,
        oneshot::{self, Sender},
    },
};
use tracing::{error, info, Instrument};
use tracing_subscriber::{reload::<PERSON><PERSON>, EnvFilter, Registry};

use crate::{traceablitity::WEBAPPS_VIRTUAL_IP_MAPPING, CLIENTS};

pub async fn start(reload_handle: Handle<EnvFilter, Registry>) -> io::Result<()> {
    let socket_path = "/tmp/sdp_server.sock";

    // 清理旧的 socket 文件
    if fs::exists(socket_path)? {
        fs::remove_file(socket_path)?;
    }

    let listener = UnixListener::bind(socket_path)?;
    info!("listening on {}", socket_path);

    let (tx, mut rx) = mpsc::channel::<(String, Sender<bool>)>(1);

    tokio::spawn(
        async move {
            while let Some((filter, callback)) = rx.recv().await {
                if let Ok(new_filter) = EnvFilter::from_str(&filter) {
                    let result = reload_handle.modify(|filter| {
                        *filter = new_filter;
                    });

                    _ = callback.send(result.is_ok());

                    if result.is_ok() {
                        info!(r#"apply log level `{}`"#, filter);
                    }
                } else {
                    _ = callback.send(false);
                }
            }
        }
        .in_current_span(),
    );

    tokio::spawn(async move {
        loop {
            match listener.accept().await {
                Ok((mut socket, _addr)) => {
                    let reload_filter_producer = tx.clone();
                    tokio::spawn(async move {
                        let (reader, mut writer) = socket.split();
                        let mut reader = BufReader::new(reader);
                        let mut line = String::new();

                        while let Ok(n) = reader.read_line(&mut line).await {
                            if n == 0 {
                                break;
                            }
                            let command = line.trim();

                            let mut response = String::from("Unknown command");

                            if let Ok(command) =
                                serde_json::from_str::<server_cli::Command>(command)
                            {
                                match command {
                                    // 设置日志级别
                                    server_cli::Command::SetLogFilter { filter } => {
                                        let (tx, rx) = oneshot::channel();
                                        _ = reload_filter_producer
                                            .send((filter.to_string(), tx))
                                            .await;

                                        if rx.await.unwrap_or_default() {
                                            response.clear();
                                            response.push_str("Log level changed");
                                        }
                                    }
                                    // 查看客户端连接信息
                                    server_cli::Command::ViewClients => {
                                        let clients = CLIENTS.read().await;
                                        let times = (clients.len() / 1000) + 1;
                                        for i in 0..times {
                                            let mut values = vec![];
                                            let mut index = 0;
                                            for (device_id, client) in clients.iter().skip(i * 1000)
                                            {
                                                values.push(serde_json::json!( {
                                                    "device_id": device_id.clone(),
                                                    "unit": client.tenant.clone(),
                                                    "username": client.username.clone(),
                                                    "peer_addr": client.peer_addr,
                                                    "online": client.online,
                                                }));
                                                index += 1;
                                                if index == 1000 {
                                                    break;
                                                }
                                            }
                                            response =
                                                serde_json::to_string(&values).unwrap_or_default();
                                            if i + 1 == times {
                                                response.push_str("END");
                                            }
                                            response.push_str("\n");
                                            _ = writer.write_all(response.as_bytes()).await;
                                        }
                                        break;
                                    }
                                    // 查看溯源资源映射信息
                                    server_cli::Command::ViewTraceMapping => {
                                        let mapping = WEBAPPS_VIRTUAL_IP_MAPPING.read().await;
                                        response =
                                            serde_json::to_string(&*mapping).unwrap_or_default();
                                    }
                                }
                            }

                            response.push_str("\n");
                            _ = writer.write_all(response.as_bytes()).await;
                        }
                    });
                }
                Err(err) => {
                    error!("daemon thread exit. {err}");
                    break;
                }
            }
        }
    });

    Ok(())
}
