use colored::Colorize;
use std::{collections::HashMap, path::PathBuf, process::Stdio};
use tokio::{
    io::{AsyncBufReadExt, BufReader},
    process::Command,
    sync::broadcast::Sender,
};

use crate::print_command_output;

pub async fn run(tx: Sender<()>, verbose: bool) -> Result<(), anyhow::Error> {
    for arch in ["bpfel", "bpfeb"] {
        let target = format!("{arch}-unknown-none");
        let target = &target;

        println!(
            "{}",
            "\n========================= 1. eBPF =========================\n".red()
        );

        for bin in ["primary", "secondary"] {
            eprintln!("    Building {target}");

            let mut command = Command::new("cargo");

            let dir = PathBuf::from("server/ebpf");
            command.current_dir(&dir).args([
                "build",
                "--no-default-features",
                "--target",
                target,
                "-Z",
                "build-std=core",
                "--release",
            ]);

            // Command::new creates a child process which inherits all env variables. This means env
            // vars set by the cargo xtask command are also inherited. RUSTUP_TOOLCHAIN is removed
            // so the rust-toolchain.toml file in the -ebpf folder is honored.
            command.env_remove("RUSTUP_TOOLCHAIN");

            let mut artifact_name = format!("sdp-{arch}");

            command.arg("--target-dir");
            command.arg(PathBuf::from("../../target").join(bin));

            artifact_name += &format!("-{bin}");

            let cargo_out = PathBuf::from("target")
                .join(bin)
                .join(target)
                .join("release")
                .join(bin);

            let mut rx = tx.subscribe();
            let build_envs: HashMap<&str, String> = HashMap::new();
            print_command_output!(verbose, command, build_envs, rx);

            let out = PathBuf::from("server/xdp/src/bpf")
                .join(artifact_name)
                .with_extension("ebpf");

            std::fs::create_dir_all(out.parent().expect("x"))?;
            std::fs::copy(cargo_out, &out)?;
        }
    }
    Ok(())
}
