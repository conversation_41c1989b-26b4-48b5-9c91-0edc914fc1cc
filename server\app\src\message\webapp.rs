use amq_protocol::types::FieldTable;
use base::packet::{Message, MessageType};
use deadpool_lapin::Pool;
use futures::{future::BoxFuture, FutureExt, StreamExt};
use lapin::{
    options::{
        BasicAckOptions, BasicConsumeOptions, BasicQosOptions, QueueBindOptions,
        QueueDeclareOptions,
    },
    Channel,
};
use rand::Rng;
use tlv_types::WebApp;
use tracing::{error, info, trace, warn, Instrument};

use crate::{comm::CommCommand, message::MQCommand, Error, EventSender, InternalEvent, CLIENTS};

use super::WEBAPP_EXCHANGE;

fn do_listen(
    pool: Pool,
    channel: Channel,
    event_sender: EventSender,
) -> BoxFuture<'static, Result<(), lapin::Error>> {
    async move {
        // 声明队列
        let mut queue_declare_options = QueueDeclareOptions::default();
        // 连接断开时, 自动删除队列
        queue_declare_options.auto_delete = true;
        let num: u8 = rand::rng().random_range(0..100);
        let webapp_queue = format!("{}_{}", WEBAPP_EXCHANGE, num);
        channel
            .queue_declare(&webapp_queue, queue_declare_options, FieldTable::default())
            .await?;
        let bind_options = QueueBindOptions::default();
        channel
            .queue_bind(
                &webapp_queue,
                WEBAPP_EXCHANGE,
                WEBAPP_EXCHANGE,
                bind_options,
                FieldTable::default(),
            )
            .await?;
        let qos_options = BasicQosOptions::default();
        channel.basic_qos(1, qos_options).await?;
        let mut webapp_consumer = channel
            .basic_consume(
                &webapp_queue,
                "webapp_consumer",
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await?;
        tokio::spawn(
            async move {
                while let Some(Ok(delivery)) = webapp_consumer.next().await {
                    trace!("recv mq message: {:?}", &delivery.data);

                    let payload = &delivery.data;
                    let tenant_len = payload[1] as usize;
                    if let Ok(tenant) = std::str::from_utf8(&payload[2..2 + tenant_len]) {
                        let webapp_len = u16::from_be_bytes([
                            payload[2 + tenant_len],
                            payload[2 + tenant_len + 1],
                        ]) as usize;

                        if let Ok(webapp) = serde_json::from_slice::<WebApp>(
                            &payload[2 + tenant_len + 1 + 1..2 + tenant_len + 1 + 1 + webapp_len],
                        ) {
                            let message = if payload[0] == 0 {
                                InternalEvent::MQMessage(MQCommand::DelWebapp {
                                    tenant: tenant.to_owned(),
                                    webapp,
                                })
                            } else {
                                InternalEvent::MQMessage(MQCommand::AddWebapp {
                                    tenant: tenant.to_owned(),
                                    webapp,
                                })
                            };
                            event_sender.send(message).await;

                            let mut webapp_bytes = payload
                                [2 + tenant_len + 1 + 1..2 + tenant_len + 1 + 1 + webapp_len]
                                .to_vec();

                            webapp_bytes.insert(0, b'[');
                            webapp_bytes.push(b']');
                            tlv::utils::insert_tag(&mut webapp_bytes, tlv::Tag::RawBytes);

                            let mut payload = vec![tlv::Tag::Boolean as u8, 1, payload[0]];
                            payload.extend(webapp_bytes);
                            let message = Message::new(MessageType::WebApps, &payload);
                            let command = CommCommand::Message(message);

                            let clients = CLIENTS.read().await;
                            for client in clients.values().filter(|client| client.tenant == tenant)
                            {
                                client.handle.send(command.clone()).await;
                            }
                        }
                    }

                    delivery
                        .ack(BasicAckOptions::default())
                        .await
                        .expect("basic_ack");
                }

                error!("reconnecting...");

                tokio::spawn(async move {
                    let mut timeout = 1;

                    loop {
                        let channel = get_channel!(pool, timeout);

                        if let Err(err) =
                            do_listen(pool.clone(), channel, event_sender.clone()).await
                        {
                            error!("listen error: {err}");

                            tokio::time::sleep(std::time::Duration::from_millis(
                                2 << std::cmp::max(12, timeout),
                            ))
                            .await;
                            timeout += 1;
                            continue;
                        }
                        info!("reconnected");
                        break;
                    }
                });
            }
            .instrument(tracing::error_span!(parent: None, "WebappConsumer")),
        );

        Ok(())
    }
    .boxed()
}

/// 监听WEB应用变化
pub(super) async fn listen(pool: Pool, event_sender: EventSender) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;
    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("webapp channel status is abnormal: {error}");
    });

    do_listen(pool, channel, event_sender)
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;
    Ok(())
}
