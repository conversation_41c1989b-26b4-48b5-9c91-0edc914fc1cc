#!/bin/bash

# 移动
mv /Applications/com.jingantech.oneid.plist /Library/LaunchDaemons
# 启动服务
launchctl bootstrap system /Library/LaunchDaemons/com.jingantech.oneid.plist
# 设置权限
chown root:admin /Applications/${PRODUCT_NAME}.app/Contents/MacOS/oneidcore
chmod +sx /Applications/${PRODUCT_NAME}.app/Contents/MacOS/oneidcore
chmod +x /Applications/${PRODUCT_NAME}.app/Contents/MacOS/config

# 执行配置程序, 检查安装包内置配置与本地配置是否匹配
/Applications/${PRODUCT_NAME}.app/Contents/MacOS/config

# 执行完成后, 删除该程序
rm -f /Applications/${PRODUCT_NAME}.app/Contents/MacOS/config

# 解决文件已损坏
# xattr -r -d com.apple.quarantine /Applications/${PRODUCT_NAME}.app

security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain /Applications/${PRODUCT_NAME}.app/Contents/Resources/_up_/attachments/oneid.crt
rm -rf /Applications/${PRODUCT_NAME}.app/Contents/Resources/_up_
rm -rf /Applications/scripts

exit 0
