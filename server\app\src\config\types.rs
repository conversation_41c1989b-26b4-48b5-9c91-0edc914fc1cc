use std::{
    collections::{HashMap, HashSet},
    net::{IpAddr, Ipv4Addr, SocketAddr},
    sync::Arc,
};

use fred::{
    prelude::{Server, TlsConnector},
    types::config::{ClusterDiscoveryPolicy, Config as RedisConfig, TlsHostMapping},
};
use gm_sm2::key::Sm2PrivateKey;
use ipnet::IpNet;
use rustls::{
    client::danger::{HandshakeSignatureValid, ServerCertVerified, ServerCertVerifier},
    crypto::ring as provider,
    pki_types::{CertificateDer, ServerName, UnixTime},
    DigitallySignedStruct, SignatureScheme,
};
use serde::Deserialize;
use tlv_types::Port;
use tokio_rustls::rustls;
use url::Url;

use crate::xdp::XdpMode;

use super::parse;

fn default_time_deviation() -> u32 {
    15
}

fn default_rx_queue_len() -> u32 {
    4096
}

fn default_tx_queue_len() -> u32 {
    4096
}

fn default_frame_size() -> u32 {
    2048
}

const fn default_virtual_ip() -> Ipv4Addr {
    Ipv4Addr::UNSPECIFIED
}

const fn default_timeout() -> u64 {
    15
}

#[derive(Clone)]
pub struct Segments {
    primary_interface: String,
    virtual_ip: Option<(Ipv4Addr, Ipv4Addr)>,
    inner: Arc<HashMap<String, HashSet<IpNet>>>,
}

impl Segments {
    pub fn find_interface<'a>(&'a self, ip: &IpAddr) -> &'a str {
        self.inner
            .iter()
            .find_map(|(iface, segments)| {
                if segments.iter().any(|ipnet| ipnet.contains(ip)) {
                    Some(iface.as_str())
                } else {
                    None
                }
            })
            .unwrap_or(self.primary_interface.as_str())
    }

    pub fn virtual_ip(&self) -> Option<(Ipv4Addr, Ipv4Addr)> {
        self.virtual_ip
    }
}

#[derive(Deserialize)]
pub struct Config {
    /// SPA允许的时间误差
    #[serde(default = "default_time_deviation")]
    pub spa_time_deviation: u32,

    /// SPA端口
    pub spa_port: u16,

    /// DNS地址
    pub dns: Option<Vec<IpAddr>>,

    /// SPA 私钥
    #[serde(deserialize_with = "parse::sec_key")]
    pub sec_key: Sm2PrivateKey,

    /// 私钥密码
    pub priv_password: Option<String>,

    /// 授权信息加载间隔时间
    pub authorization_interval: u64,

    /// 轮询后台状态间隔时间
    pub backend_interval: u64,

    /// 推送流量统计间隔时间
    pub push_traffic_interval: u64,

    /// Keepalive配置
    pub keepalive: Option<Keepalive>,

    /// 后端服务配置
    pub backend: BackendConfig,

    /// Redis 配置
    pub redis: RedisConf,

    /// MQ 配置
    pub mq: MQConfig,

    /// 服务配置
    pub server: ServerConfig,

    /// 网卡配置
    pub interface: Interface,
}

impl Config {
    pub fn pub_ports(&self) -> HashSet<Port> {
        let mut pub_ports = self
            .interface
            .primary
            .base
            .pub_ports
            .clone()
            .unwrap_or_default();

        if let Some(ref secondary) = self.interface.secondary {
            secondary.iter().for_each(|config| {
                config.base.pub_ports.as_ref().map(|ports| {
                    pub_ports.extend(ports.clone().into_iter());
                });
            });
        }

        pub_ports
    }

    pub fn resource_whitelist(&self) -> HashSet<SocketAddr> {
        let mut resource_whitelist = self
            .interface
            .primary
            .base
            .resource_whitelist
            .clone()
            .unwrap_or_default();

        if let Some(ref secondary) = self.interface.secondary {
            secondary.iter().for_each(|config| {
                config.base.resource_whitelist.as_ref().map(|whitelist| {
                    resource_whitelist.extend(whitelist.clone().into_iter());
                });
            });
        }
        resource_whitelist
    }

    pub fn segments(&self) -> Segments {
        let mut segments = HashMap::new();
        if let Some(ref secondary) = self.interface.secondary {
            secondary.iter().for_each(|config| {
                segments.insert(config.base.iface.clone(), config.segments.clone());
            });
        }
        Segments {
            primary_interface: self.interface.primary.base.iface.clone(),
            virtual_ip: if self.interface.r#virtual.enabled {
                Some((
                    self.interface.r#virtual.ipv4,
                    self.interface.r#virtual.src_ipv4,
                ))
            } else {
                None
            },
            inner: Arc::new(segments),
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct ServerConfig {
    /// TCP Server绑定地址
    pub host: String,

    /// TCP Server端口
    pub port: u16,

    /// TLS CA证书
    pub ca: String,

    /// TLS 证书
    pub cert: String,

    /// TLS 私钥
    pub key: String,

    /// TLCP 加密证书
    #[cfg(feature = "tlcp")]
    pub enc_cert: String,

    /// TLCP 加密证书秘钥
    #[cfg(feature = "tlcp")]
    pub enc_key: String,
}

/// 网卡配置
#[derive(Debug, Deserialize)]
pub struct Interface {
    /// 虚拟网卡(单机模式, SDP后端服务绑定在该网卡地址)
    pub r#virtual: Virtual,
    /// 主网卡配置(客户端接入网关流量所在的网卡)
    pub primary: PrimaryInterface,
    /// 其他网卡配置
    pub secondary: Option<Vec<SecondaryInterface>>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct Virtual {
    /// 是否开启虚拟网卡
    pub enabled: bool,
    /// 虚拟网卡名称
    pub name: String,
    /// 虚拟网卡IPv4地址(IP地址不能包含在已存在的网段中. 保留网段********/16)
    #[serde(default = "default_virtual_ip")]
    pub ipv4: Ipv4Addr,
    /// SDP 代理后端服务源地址(后端服务收到的数据包通过该地址发送)
    #[serde(default = "default_virtual_ip")]
    pub src_ipv4: Ipv4Addr,
}

#[derive(Debug, Deserialize, Clone)]
pub struct LocalAddr {
    /// 物理地址
    pub physics: Ipv4Addr,

    /// VIP
    pub vip: Option<Ipv4Addr>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct BaseInterface {
    /// 网卡名称
    pub iface: String,

    /// 网卡IP地址
    pub addr: LocalAddr,

    /// 开放端口. 每个网卡最多支持开放十个端口
    pub pub_ports: Option<HashSet<Port>>,

    /// 白名单
    pub whitelist: Option<HashSet<SocketAddr>>,

    /// 资源白名单
    pub resource_whitelist: Option<HashSet<SocketAddr>>,

    /// 发送队列
    #[serde(default = "default_tx_queue_len")]
    pub tx_queue_len: u32,

    /// 接收队列
    #[serde(default = "default_rx_queue_len")]
    pub rx_queue_len: u32,

    /// 帧大小
    #[serde(default = "default_frame_size")]
    pub frame_size: u32,

    /// 模式
    pub xdp_mode: XdpMode,
}

#[derive(Debug, Deserialize, Clone)]
pub struct PrimaryInterface {
    /// 网卡基础信息
    #[serde(flatten)]
    pub base: BaseInterface,

    /// 多网卡时, 代理本机其他网卡的IP地址
    pub multi_nic_addresses: Option<HashSet<IpAddr>>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct SecondaryInterface {
    /// 网卡基础信息
    #[serde(flatten)]
    pub base: BaseInterface,

    /// 网段(该网段的服务通过该网卡进行访问)
    pub segments: HashSet<IpNet>,
}

#[derive(Debug, Deserialize)]
pub struct MQConfig {
    /// MQ 地址
    pub url: String,
}

#[derive(Clone, Debug, Deserialize)]
pub enum RespVersion {
    RESP2,
    RESP3,
}

#[derive(Clone, Debug, Deserialize)]
#[serde(tag = "type")]
pub enum RedisServerConfig {
    Centralized {
        /// The hostname or IP address of the Redis server.
        host: String,
        /// The port on which the Redis server is listening.
        port: u16,
    },
    Clustered {
        /// 服务器列表
        hosts: Vec<(String, u16)>,
    },
    Sentinel {
        /// 服务器列表
        hosts: Vec<(String, u16)>,
        /// 服务名称
        service_name: String,
        /// 用户名
        username: Option<String>,
        /// 密码
        password: Option<String>,
    },
}

#[derive(Clone, Debug, Deserialize)]
pub struct TlsConfig {
    pub root_certs: Option<Vec<String>>,
    pub disable_built_in_roots: bool,
    pub use_sni: bool,
}

#[derive(Clone, Debug, Deserialize)]
pub struct RedisConf {
    /// 用户名
    pub username: Option<String>,
    /// 密码
    pub password: Option<String>,
    /// 服务配置
    pub server: RedisServerConfig,
    /// 协议版本
    pub version: RespVersion,
    /// 数据库
    pub database: Option<u8>,
    /// TLS 配置
    pub tls: Option<TlsConfig>,
    /// 日志追踪
    pub tracing: bool,
}

#[derive(Debug, Deserialize, Clone)]
pub struct BackendConfig {
    /// 后端服务地址
    pub url: Url,

    /// 认证服务context path
    pub auth_ctx: String,

    /// 资源服务context path
    pub resource_ctx: String,

    /// 多因子context path
    pub mfa_ctx: String,

    /// https 忽略证书校验
    #[serde(default)]
    pub ignore_unknown_ca: bool,

    /// https 忽略证书使用者
    #[serde(default)]
    pub ignore_invalid_cn: bool,

    /// 超时时间
    #[serde(default = "default_timeout")]
    pub timeout: u64,
}

/// Keepalive配置
#[derive(Debug, Deserialize, Copy, Clone)]
pub struct Keepalive {
    /// 空闲时间
    pub idle: u32,
    /// 探测间隔
    pub interval: u32,
    /// 重试次数
    pub retries: u32,
}

impl From<RedisConf> for RedisConfig {
    fn from(conf: RedisConf) -> Self {
        let mut config = RedisConfig {
            username: conf.username,
            password: conf.password,
            database: conf.database,
            ..Default::default()
        };

        if let Some(tls) = conf.tls {
            let mut client_config =
                rustls::ClientConfig::builder_with_provider(provider::default_provider().into())
                    .with_safe_default_protocol_versions()
                    .unwrap()
                    .dangerous()
                    .with_custom_certificate_verifier(Arc::new(NoVerifier))
                    .with_no_client_auth();
            client_config.enable_sni = tls.use_sni;

            config.tls = Some(fred::prelude::TlsConfig {
                connector: TlsConnector::Rustls(Arc::new(client_config).into()),
                hostnames: TlsHostMapping::None,
            });
        }

        config.version = match conf.version {
            RespVersion::RESP2 => fred::types::RespVersion::RESP2,
            RespVersion::RESP3 => fred::types::RespVersion::RESP3,
        };
        // config.tracing = TracingConfig::new(conf.tracing);
        match conf.server {
            RedisServerConfig::Centralized { host, port } => {
                config.server = fred::types::config::ServerConfig::Centralized {
                    server: Server {
                        host: host.into(),
                        port,
                        tls_server_name: None,
                    },
                }
            }
            RedisServerConfig::Clustered { hosts } => {
                let hosts = hosts
                    .into_iter()
                    .map(|(host, port)| Server {
                        host: host.into(),
                        port,
                        tls_server_name: None,
                    })
                    .collect();
                config.server = fred::types::config::ServerConfig::Clustered {
                    hosts,
                    policy: ClusterDiscoveryPolicy::default(),
                }
            }
            RedisServerConfig::Sentinel {
                hosts,
                service_name,
                username,
                password,
            } => {
                let hosts = hosts
                    .into_iter()
                    .map(|(host, port)| Server {
                        host: host.into(),
                        port,
                        tls_server_name: None,
                    })
                    .collect();
                config.server = fred::types::config::ServerConfig::Sentinel {
                    hosts,
                    service_name,
                    username,
                    password,
                }
            }
        }

        config
    }
}

#[derive(Debug)]
struct NoVerifier;

impl ServerCertVerifier for NoVerifier {
    fn verify_server_cert(
        &self,
        _end_entity: &CertificateDer,
        _intermediates: &[CertificateDer],
        _server_name: &ServerName,
        _ocsp_response: &[u8],
        _now: UnixTime,
    ) -> Result<ServerCertVerified, rustls::Error> {
        Ok(ServerCertVerified::assertion())
    }

    fn verify_tls12_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer,
        _dss: &DigitallySignedStruct,
    ) -> Result<HandshakeSignatureValid, rustls::Error> {
        Ok(HandshakeSignatureValid::assertion())
    }

    fn verify_tlcp11_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer<'_>,
        _dss: &DigitallySignedStruct,
    ) -> Result<HandshakeSignatureValid, rustls::Error> {
        Ok(HandshakeSignatureValid::assertion())
    }

    fn verify_tls13_signature(
        &self,
        _message: &[u8],
        _cert: &CertificateDer,
        _dss: &DigitallySignedStruct,
    ) -> Result<HandshakeSignatureValid, rustls::Error> {
        Ok(HandshakeSignatureValid::assertion())
    }

    fn supported_verify_schemes(&self) -> Vec<SignatureScheme> {
        vec![
            SignatureScheme::RSA_PKCS1_SHA1,
            SignatureScheme::ECDSA_SHA1_Legacy,
            SignatureScheme::RSA_PKCS1_SHA256,
            SignatureScheme::ECDSA_NISTP256_SHA256,
            SignatureScheme::RSA_PKCS1_SHA384,
            SignatureScheme::ECDSA_NISTP384_SHA384,
            SignatureScheme::RSA_PKCS1_SHA512,
            SignatureScheme::ECDSA_NISTP521_SHA512,
            SignatureScheme::RSA_PSS_SHA256,
            SignatureScheme::RSA_PSS_SHA384,
            SignatureScheme::RSA_PSS_SHA512,
            SignatureScheme::ED25519,
            SignatureScheme::ED448,
        ]
    }
}
