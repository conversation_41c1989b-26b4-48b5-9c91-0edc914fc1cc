use std::sync::Arc;

use base::packet::{Message, MessageType};
use tlv::Serialize;
use tracing::{error, info, Instrument};

use crate::{comm::CommCommand, enums::Notification, CLIENTS};

use super::client::BackendCommonClient;

pub async fn spawn(interval: u64, backend_client: Arc<BackendCommonClient>) {
    tokio::spawn(
        async move {
            info!("start scan backend state task.");
            // 状态
            let mut state = Ok(());
            let mut count = 0;
            loop {
                let duration = if count == 0 {
                    std::time::Duration::from_secs(interval)
                } else {
                    let secs = 2i32.pow(if count >= 7 { 7 } else { count }) as u64;
                    std::time::Duration::from_secs(if secs < interval { interval } else { secs })
                };
                tokio::time::sleep(duration).await;

                macro_rules! notify_server_health {
                    ($state: expr) => {
                        let clients = CLIENTS.read().await;

                        for (_, client) in clients.iter() {
                            let message = Message::new(
                                MessageType::Notification,
                                &($state as u8).serialize(),
                            );
                            let command = CommCommand::Message(message);
                            client.handle.send(command).await;
                        }
                    };
                }

                if backend_client.get_backend_state().await.is_none() {
                    error!("backend state: abnormal");
                    count += 1;
                    if (count + 1) % 8 == 0 {
                        state = Err(());
                        notify_server_health!(Notification::ServerException);
                    }
                } else {
                    count = 0;
                    if state.is_err() {
                        state = Ok(());
                        notify_server_health!(Notification::ServerNormal);
                    }
                }
            }
        }
        .in_current_span(),
    );
}
