use once_cell::sync::Lazy;
use std::{
    env,
    ffi::OsString,
    sync::{
        atomic::{AtomicUsize, Ordering},
        Arc,
    },
    time::Duration,
};

use oneidservice::{runtime::new_runtime_builder, ErrorExt};
use windows_service::{
    define_windows_service,
    service::{
        Service, ServiceAccess, ServiceAction, ServiceActionType, ServiceControl,
        ServiceControlAccept, ServiceDependency, ServiceErrorControl, ServiceExitCode,
        ServiceFailureActions, ServiceFailureResetPeriod, ServiceInfo, ServiceStartType,
        ServiceState, ServiceStatus, ServiceType,
    },
    service_control_handler::{self, ServiceControlHandlerResult, ServiceStatusHandle},
    service_dispatcher,
    service_manager::{ServiceManager, ServiceManagerAccess},
};

pub const SERVICE_NAME: &str = if let Some(service_name) = option_env!("SERVICE_NAME") {
    service_name
} else {
    "oneid"
};
pub const SERVICE_DISPLAY_NAME: &str =
    if let Some(display_name) = option_env!("SERVICE_DISPLAY_NAME") {
        display_name
    } else {
        "安全令"
    };
pub const SERVICE_DESCRIPTION: &str = if let Some(description) = option_env!("SERVICE_DESCRIPTION")
{
    description
} else {
    "此服务用于计算客户端标识"
};
static SERVICE_TYPE: ServiceType = ServiceType::OWN_PROCESS;

const SERVICE_RECOVERY_LAST_RESTART_DELAY: Duration = Duration::from_secs(60 * 10);
const SERVICE_FAILURE_RESET_PERIOD: Duration = Duration::from_secs(60 * 15);

static SERVICE_ACCESS: Lazy<ServiceAccess> = Lazy::new(|| {
    ServiceAccess::QUERY_CONFIG
        | ServiceAccess::CHANGE_CONFIG
        | ServiceAccess::START
        | ServiceAccess::DELETE
});

pub fn run() -> Result<(), String> {
    // Start the service dispatcher.
    // This will block current thread until the service stopped and spawn `service_main` on a
    // background thread.
    service_dispatcher::start(SERVICE_NAME, service_main)
        .map_err(|e| e.display_chain_with_msg("Failed to start a service dispatcher"))
}

define_windows_service!(service_main, handle_service_main);

pub fn handle_service_main(_arguments: Vec<OsString>) {
    // 停止服务
    let (event_tx, event_rx) = flume::bounded(1);
    // 事件处理
    let event_handler = move |control_event| -> ServiceControlHandlerResult {
        log::trace!("received event: {:?}", control_event);
        match control_event {
            // Notifies a service to report its current status information to the service
            // control manager. Always return NoError even if not implemented.
            ServiceControl::Interrogate => ServiceControlHandlerResult::NoError,

            // Handle stop
            ServiceControl::Stop => {
                event_tx.send(()).unwrap();
                ServiceControlHandlerResult::NoError
            }
            _ => ServiceControlHandlerResult::NotImplemented,
        }
    };

    log::trace!("Register service...");
    let status_handle = match service_control_handler::register(SERVICE_NAME, event_handler) {
        Ok(handle) => handle,
        Err(error) => {
            log::error!(
                "{}",
                error.display_chain_with_msg("Failed to register a service control handler")
            );
            return;
        }
    };
    let mut persistent_service_status = PersistentServiceStatus::new(status_handle);

    persistent_service_status
        .set_pending_start(Duration::from_secs(1))
        .unwrap();

    let runtime = new_runtime_builder().build();
    let runtime = match runtime {
        Err(error) => {
            log::error!("{}", error.display_chain());
            persistent_service_status
                .set_stopped(ServiceExitCode::ServiceSpecific(1))
                .unwrap();
            return;
        }
        Ok(runtime) => runtime,
    };

    persistent_service_status.set_running().unwrap();

    let result = runtime.block_on(oneidservice::service::run(event_rx));

    let exit_code = match result {
        Ok(()) => {
            log::info!("Stopping service");
            ServiceExitCode::default()
        }
        Err(error) => {
            log::error!("{}", error);
            ServiceExitCode::ServiceSpecific(1)
        }
    };

    persistent_service_status.set_stopped(exit_code).unwrap();
}

#[derive(Debug, Clone)]
struct PersistentServiceStatus {
    status_handle: ServiceStatusHandle,
    checkpoint_counter: Arc<AtomicUsize>,
}

impl PersistentServiceStatus {
    fn new(status_handle: ServiceStatusHandle) -> Self {
        PersistentServiceStatus {
            status_handle,
            checkpoint_counter: Arc::new(AtomicUsize::new(1)),
        }
    }

    /// Tell the system that the service is pending start and provide the time estimate until
    /// initialization is complete.
    fn set_pending_start(&mut self, wait_hint: Duration) -> windows_service::Result<()> {
        self.report_status(
            ServiceState::StartPending,
            wait_hint,
            ServiceExitCode::default(),
        )
    }

    /// Tell the system that the service is running.
    fn set_running(&mut self) -> windows_service::Result<()> {
        self.report_status(
            ServiceState::Running,
            Duration::default(),
            ServiceExitCode::default(),
        )
    }

    /// Tell the system that the service is pending stop and provide the time estimate until the
    /// service is stopped.
    #[allow(dead_code)]
    fn set_pending_stop(&mut self, wait_hint: Duration) -> windows_service::Result<()> {
        self.report_status(
            ServiceState::StopPending,
            wait_hint,
            ServiceExitCode::default(),
        )
    }

    /// Tell the system that the service is stopped and provide the exit code.
    fn set_stopped(&mut self, exit_code: ServiceExitCode) -> windows_service::Result<()> {
        self.report_status(ServiceState::Stopped, Duration::default(), exit_code)
    }

    /// Private helper to report the service status update.
    fn report_status(
        &mut self,
        next_state: ServiceState,
        wait_hint: Duration,
        exit_code: ServiceExitCode,
    ) -> windows_service::Result<()> {
        // Automatically bump the checkpoint when updating the pending events to tell the system
        // that the service is making a progress in transition from pending to final state.
        // `wait_hint` should reflect the estimated time for transition to complete.
        let checkpoint = match next_state {
            ServiceState::StartPending
            | ServiceState::StopPending
            | ServiceState::ContinuePending
            | ServiceState::PausePending => self.checkpoint_counter.fetch_add(1, Ordering::SeqCst),
            _ => 0,
        };

        let service_status = ServiceStatus {
            service_type: SERVICE_TYPE,
            current_state: next_state,
            controls_accepted: accepted_controls_by_state(next_state),
            exit_code,
            checkpoint: checkpoint as u32,
            wait_hint,
            process_id: None,
        };

        log::debug!(
            "Update service status: {:?}, checkpoint: {}, wait_hint: {:?}",
            service_status.current_state,
            service_status.checkpoint,
            service_status.wait_hint
        );

        self.status_handle.set_service_status(service_status)
    }
}

/// Returns the list of accepted service events at each stage of the service lifecycle.
fn accepted_controls_by_state(state: ServiceState) -> ServiceControlAccept {
    let always_accepted = ServiceControlAccept::POWER_EVENT | ServiceControlAccept::SESSION_CHANGE;
    match state {
        ServiceState::StartPending | ServiceState::PausePending | ServiceState::ContinuePending => {
            ServiceControlAccept::empty()
        }
        ServiceState::Running => {
            always_accepted | ServiceControlAccept::STOP | ServiceControlAccept::PRESHUTDOWN
        }
        ServiceState::Paused => {
            always_accepted | ServiceControlAccept::STOP | ServiceControlAccept::PRESHUTDOWN
        }
        ServiceState::StopPending | ServiceState::Stopped => ServiceControlAccept::empty(),
    }
}

#[derive(thiserror::Error, Debug)]
pub enum InstallError {
    #[error("Unable to connect to service manager")]
    ConnectServiceManager(#[source] windows_service::Error),

    #[error("Unable to create a service")]
    CreateService(#[source] windows_service::Error),
}

pub fn install_service() -> Result<(), InstallError> {
    let manager_access = ServiceManagerAccess::CONNECT | ServiceManagerAccess::CREATE_SERVICE;
    let service_manager = ServiceManager::local_computer(None::<&str>, manager_access)
        .map_err(InstallError::ConnectServiceManager)?;

    let service = service_manager
        .create_service(&get_service_info(), *SERVICE_ACCESS)
        .and_then(|service| {
            _ = service.set_description(SERVICE_DESCRIPTION);
            service.start(&[""])?;
            Ok(service)
        })
        .or(open_update_service(&service_manager))
        .map_err(InstallError::CreateService)?;

    let recovery_actions = vec![
        ServiceAction {
            action_type: ServiceActionType::Restart,
            delay: Duration::from_secs(3),
        },
        ServiceAction {
            action_type: ServiceActionType::Restart,
            delay: Duration::from_secs(30),
        },
        ServiceAction {
            action_type: ServiceActionType::Restart,
            delay: SERVICE_RECOVERY_LAST_RESTART_DELAY,
        },
    ];

    let failure_actions = ServiceFailureActions {
        reset_period: ServiceFailureResetPeriod::After(SERVICE_FAILURE_RESET_PERIOD),
        reboot_msg: None,
        command: None,
        actions: Some(recovery_actions),
    };

    service
        .update_failure_actions(failure_actions)
        .map_err(InstallError::CreateService)?;
    service
        .set_failure_actions_on_non_crash_failures(true)
        .map_err(InstallError::CreateService)?;

    // Change how the service SID is added to the service process token.
    // WireGuard needs this.
    // service
    //     .set_config_service_sid_info(ServiceSidType::Unrestricted)
    //     .map_err(InstallError::CreateService)?;

    Ok(())
}

fn open_update_service(
    service_manager: &ServiceManager,
) -> Result<Service, windows_service::Error> {
    let service = service_manager.open_service(SERVICE_NAME, *SERVICE_ACCESS)?;
    service.change_config(&get_service_info())?;
    Ok(service)
}

fn get_service_info() -> ServiceInfo {
    ServiceInfo {
        name: OsString::from(SERVICE_NAME),
        display_name: OsString::from(SERVICE_DISPLAY_NAME),
        service_type: SERVICE_TYPE,
        start_type: ServiceStartType::AutoStart,
        error_control: ServiceErrorControl::Normal,
        executable_path: env::current_exe().unwrap(),
        launch_arguments: vec![
            OsString::from("--run-as-service"),
            OsString::from("-v"),
            OsString::from("0"),
        ],
        dependencies: vec![
            // Base Filter Engine
            ServiceDependency::Service(OsString::from("BFE")),
            // Network Store Interface Service
            // This service delivers network notifications (e.g. interface addition/deleting etc).
            ServiceDependency::Service(OsString::from("NSI")),
        ],
        account_name: None, // run as System
        account_password: None,
    }
}
