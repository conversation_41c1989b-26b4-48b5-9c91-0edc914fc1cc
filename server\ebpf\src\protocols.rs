//        These are the defined Ethernet Protocol ID's.

pub const ETH_P_LOOP: u16 = 0x0060; // Ethernet Loopback packet
pub const ETH_P_PUP: u16 = 0x0200; // Xerox PUP packet
pub const ETH_P_PUPAT: u16 = 0x0201; // Xerox PUP Addr Trans packet
pub const ETH_P_IP: u16 = 0x0800; // Internet Protocol packet
pub const ETH_P_X25: u16 = 0x0805; // CCITT X.25
pub const ETH_P_ARP: u16 = 0x0806; // Address Resolution packet
pub const ETH_P_BPQ: u16 = 0x08FF; // G8BPQ AX.25 Ethernet Packet        [ NOT AN OFFICIALLY REGISTERED ID ]
pub const ETH_P_IEEEPUP: u16 = 0x0a00; // Xerox IEEE802.3 PUP packet
pub const ETH_P_IEEEPUPAT: u16 = 0x0a01; // Xerox IEEE802.3 PUP Addr Trans packet
pub const ETH_P_DEC: u16 = 0x6000; // DEC Assigned proto
pub const ETH_P_DNA_DL: u16 = 0x6001; // DEC DNA Dump/Load
pub const ETH_P_DNA_RC: u16 = 0x6002; // DEC DNA Remote Console
pub const ETH_P_DNA_RT: u16 = 0x6003; // DEC DNA Routing
pub const ETH_P_LAT: u16 = 0x6004; // DEC LAT
pub const ETH_P_DIAG: u16 = 0x6005; // DEC Diagnostics
pub const ETH_P_CUST: u16 = 0x6006; // DEC Customer use
pub const ETH_P_SCA: u16 = 0x6007; // DEC Systems Comms Arch
pub const ETH_P_TEB: u16 = 0x6558; // Trans Ether Bridging
pub const ETH_P_RARP: u16 = 0x8035; // Reverse Addr Res packet
pub const ETH_P_ATALK: u16 = 0x809B; // Appletalk DDP
pub const ETH_P_AARP: u16 = 0x80F3; // Appletalk AARP
pub const ETH_P_8021Q: u16 = 0x8100; // 802.1Q VLAN Extended Header
pub const ETH_P_IPX: u16 = 0x8137; // IPX over DIX
pub const ETH_P_IPV6: u16 = 0x86DD; // IPv6 over bluebook
pub const ETH_P_PAUSE: u16 = 0x8808; // IEEE Pause frames. See 802.3 31B
pub const ETH_P_SLOW: u16 = 0x8809; // Slow Protocol. See 802.3ad 43B
pub const ETH_P_WCCP: u16 = 0x883E; // Web-cache coordination protocol
                                    // defined in draft-wilson-wrec-wccp-v2-00.txt
pub const ETH_P_PPP_DISC: u16 = 0x8863; // PPPoE discovery messages
pub const ETH_P_PPP_SES: u16 = 0x8864; // PPPoE session messages
pub const ETH_P_MPLS_UC: u16 = 0x8847; // MPLS Unicast traffic
pub const ETH_P_MPLS_MC: u16 = 0x8848; // MPLS Multicast traffic
pub const ETH_P_ATMMPOA: u16 = 0x884c; // MultiProtocol Over ATM
pub const ETH_P_ATMFATE: u16 = 0x8884; // Frame-based ATM Transport
                                       // over Ethernet
pub const ETH_P_PAE: u16 = 0x888E; // Port Access Entity (IEEE 802.1X)
pub const ETH_P_AOE: u16 = 0x88A2; // ATA over Ethernet
pub const ETH_P_TIPC: u16 = 0x88CA; // TIPC
pub const ETH_P_1588: u16 = 0x88F7; // IEEE 1588 Timesync
pub const ETH_P_FCOE: u16 = 0x8906; // Fibre Channel over Ethernet
pub const ETH_P_FIP: u16 = 0x8914; // FCoE Initialization Protocol
pub const ETH_P_EDSA: u16 = 0xDADA; // Ethertype DSA [ NOT AN OFFICIALLY REGISTERED ID ]

//        Non DIX types. Won't clash for 1500 types.

pub const ETH_P_802_3: u16 = 0x0001; // Dummy type for 802.3 frames
pub const ETH_P_AX25: u16 = 0x0002; // Dummy protocol id for AX.25
pub const ETH_P_ALL: u16 = 0x0003; // Every packet (be careful!!!)
pub const ETH_P_802_2: u16 = 0x0004; // 802.2 frames
pub const ETH_P_SNAP: u16 = 0x0005; // Internal only
pub const ETH_P_DDCMP: u16 = 0x0006; // DEC DDCMP: Internal only
pub const ETH_P_WAN_PPP: u16 = 0x0007; // Dummy type for WAN PPP frames
pub const ETH_P_PPP_MP: u16 = 0x0008; // Dummy type for PPP MP frames
pub const ETH_P_LOCALTALK: u16 = 0x0009; // Localtalk pseudo type
pub const ETH_P_CAN: u16 = 0x000C; // Controller Area Network
pub const ETH_P_PPPTALK: u16 = 0x0010; // Dummy type for Atalk over PPP
pub const ETH_P_TR_802_2: u16 = 0x0011; // 802.2 frames
pub const ETH_P_MOBITEX: u16 = 0x0015; // Mobitex (<EMAIL>)
pub const ETH_P_CONTROL: u16 = 0x0016; // Card specific control frames
pub const ETH_P_IRDA: u16 = 0x0017; // Linux-IrDA
pub const ETH_P_ECONET: u16 = 0x0018; // Acorn Econet
pub const ETH_P_HDLC: u16 = 0x0019; // HDLC frames
pub const ETH_P_ARCNET: u16 = 0x001A; // 1A for ArcNet :-)
pub const ETH_P_DSA: u16 = 0x001B; // Distributed Switch Arch.
pub const ETH_P_TRAILER: u16 = 0x001C; // Trailer switch tagging
pub const ETH_P_PHONET: u16 = 0x00F5; // Nokia Phonet frames
pub const ETH_P_IEEE802154: u16 = 0x00F6; // IEEE802.15.4 frame

// Standard well-defined IP protocols.
pub const IPPROTO_IP: u8 = 0; // Dummy protocol for TCP
pub const IPPROTO_ICMP: u8 = 1; // Internet Control Message Protocol
pub const IPPROTO_IGMP: u8 = 2; // Internet Group Management Protocol
pub const IPPROTO_IPIP: u8 = 4; // IPIP tunnels (older KA9Q tunnels use 94)
pub const IPPROTO_TCP: u8 = 6; // Transmission Control Protocol
pub const IPPROTO_EGP: u8 = 8; // Exterior Gateway Protocol
pub const IPPROTO_PUP: u8 = 12; // PUP protocol
pub const IPPROTO_UDP: u8 = 17; // User Datagram Protocol
pub const IPPROTO_IDP: u8 = 22; // XNS IDP protocol
pub const IPPROTO_DCCP: u8 = 33; // Datagram Congestion Control Protocol
pub const IPPROTO_RSVP: u8 = 46; // RSVP protocol
pub const IPPROTO_GRE: u8 = 47; // Cisco GRE tunnels (rfc 1701,1702)

pub const IPPROTO_IPV6: u8 = 41; // IPv6-in-IPv4 tunnelling

pub const IPPROTO_ESP: u8 = 50; // Encapsulation Security Payload protocol
pub const IPPROTO_AH: u8 = 51; // Authentication Header protocol
pub const IPPROTO_BEETPH: u8 = 94; // IP option pseudo header for BEET
pub const IPPROTO_PIM: u8 = 103; // Protocol Independent Multicast

pub const IPPROTO_COMP: u8 = 108; // Compression Header protocol
pub const IPPROTO_SCTP: u8 = 132; // Stream Control Transport Protocol
pub const IPPROTO_UDPLITE: u8 = 136; // UDP-Lite (RFC 3828)

pub const IPPROTO_RAW: u8 = 255; // Raw IP packets
