use std::{env, fs};

fn main() {
    let args: Vec<String> = env::args().collect();
    if args.len() < 3 {
        panic!("Program requires at least 3 arguments.");
    }

    let file = &args[1];
    let key = &args[2];

    if !key.starts_with('/') {
        panic!("Key must start with `/`.");
    }

    let content =
        fs::read_to_string(file).unwrap_or_else(|error| panic!("Failed to read file: {error}"));

    let mut json_content =
        serde_json::from_str::<serde_json::Value>(&content).unwrap_or_else(|error| {
            panic!("Only support json file: {}", error);
        });
    // println!("content: {:?}", json_content);

    let old_value = json_content.pointer_mut("/package/version");
    if old_value.is_none() {
        panic!("Key not found. {}", key);
    }

    let old_value = old_value.unwrap();
    *old_value = serde_json::Value::String(version::PRODUCT_VERSION.to_owned());

    let content = serde_json::to_string_pretty(&json_content).unwrap();
    fs::write(file, content.as_bytes()).unwrap();
}
