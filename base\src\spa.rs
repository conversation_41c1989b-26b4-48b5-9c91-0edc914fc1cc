use gm_sm2::key::{Sm2Model, Sm2PublicKey};
use gm_sm3::sm3_hash;
use std::net::Ipv4Addr;

use rand::Rng;

use crate::hotp::Hotp;

/// SPA最小长度
/// 原始SPA最小长度 45 = 8(timestamp) + 16(device id) + 4(nonce) + 4(client ip) + 4(hotp) +
/// 8(counter) + 1(type)
pub const MIN_SPA_PACKET_LEN: usize = 45;

/// SPA最大长度
/// 原始SPA最大长度 144 = 8(timestamp) + 16(device id) + 4(nonce) + 4(client ip) + 4(hotp) +
/// 8(counter) + 1(type) 加上最大的`单位|用户名`字段 144 = 45 + 99
pub const MAX_SPA_PACKET_LEN: usize = 144;

/// 单包认证类型
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq)]
#[repr(u8)]
pub enum SPAType {
    /// 认证
    Auth = 0,
    /// 修改密码
    ChangePwd = 1,
    /// 代理访问
    Proxy = 2,
}

impl TryFrom<u8> for SPAType {
    type Error = ();

    fn try_from(value: u8) -> Result<Self, Self::Error> {
        match value {
            0 => Ok(SPAType::Auth),
            1 => Ok(SPAType::ChangePwd),
            2 => Ok(SPAType::Proxy),
            _ => Err(()),
        }
    }
}

#[derive(Debug)]
pub struct SPA {
    buf: [u8; 44],
    r#type: SPAType,
    payload: Option<Vec<u8>>,
}

impl Default for SPA {
    fn default() -> Self {
        Self::new(SPAType::Auth)
    }
}

impl SPA {
    pub fn new(r#type: SPAType) -> Self {
        let mut buf = [0; 44];
        let nonce = rand::thread_rng().gen::<[u8; 4]>();
        buf[24..28].copy_from_slice(&nonce);
        SPA {
            buf,
            r#type,
            payload: None,
        }
    }

    pub fn from(mut buf: Vec<u8>) -> Result<Self, String> {
        if buf.len() < MIN_SPA_PACKET_LEN || buf.len() > MAX_SPA_PACKET_LEN {
            return Err(format!(
                "invalid spa packet: expect length between {} and {}, actual length {}",
                MIN_SPA_PACKET_LEN,
                MAX_SPA_PACKET_LEN,
                buf.len()
            ));
        }
        if buf.len() > MIN_SPA_PACKET_LEN {
            Ok(SPA {
                buf: buf[..MIN_SPA_PACKET_LEN - 1].try_into().unwrap(),
                r#type: buf[MIN_SPA_PACKET_LEN - 1].try_into().map_err(|_| {
                    format!(
                        "invalid spa packet: invalid spa type {}",
                        buf[MIN_SPA_PACKET_LEN - 1]
                    )
                })?,
                payload: Some(buf.split_off(MIN_SPA_PACKET_LEN)),
            })
        } else {
            Ok(SPA {
                buf: buf[..MIN_SPA_PACKET_LEN - 1].try_into().unwrap(),
                r#type: buf[MIN_SPA_PACKET_LEN - 1].try_into().map_err(|_| {
                    format!(
                        "invalid spa packet: invalid spa type {}",
                        buf[MIN_SPA_PACKET_LEN - 1]
                    )
                })?,
                payload: None,
            })
        }
    }

    pub fn r#type(&self) -> SPAType {
        self.r#type
    }

    pub fn timestamp(&self) -> u64 {
        u64::from_be_bytes(self.buf[..8].try_into().unwrap())
    }

    fn set_timestamp(&mut self, timestamp: u64) {
        self.buf[..=7].copy_from_slice(&timestamp.to_be_bytes());
    }

    pub fn device(&self) -> &[u8; 16] {
        self.buf[8..24].try_into().unwrap()
    }

    pub fn set_device(&mut self, device_id: &[u8; 16]) {
        self.buf[8..=23].copy_from_slice(device_id);
    }

    pub fn nonce(&self) -> &[u8; 4] {
        self.buf[24..28].try_into().unwrap()
    }

    pub fn client_ip(&self) -> Ipv4Addr {
        let ip: [u8; 4] = self.buf[28..32].try_into().unwrap();
        Ipv4Addr::from(ip)
    }

    pub fn set_client_ip(&mut self, ip: &Ipv4Addr) {
        self.buf[28..=31].copy_from_slice(&ip.octets());
    }

    pub fn password(&self) -> u32 {
        u32::from_be_bytes(self.buf[32..36].try_into().unwrap())
    }

    pub fn set_password(&mut self, password: u32) {
        self.buf[32..=35].copy_from_slice(&password.to_be_bytes());
    }

    pub fn counter(&self) -> u64 {
        u64::from_be_bytes(self.buf[36..44].try_into().unwrap())
    }

    pub fn set_counter(&mut self, counter: u64) {
        self.buf[36..44].copy_from_slice(&counter.to_be_bytes());
    }

    pub fn payload(&self) -> Option<&Vec<u8>> {
        self.payload.as_ref()
    }

    pub fn set_payload(&mut self, payload: &[u8]) {
        let payload = Vec::from(payload);
        self.payload = Some(payload);
    }

    pub fn buffer(&self) -> Vec<u8> {
        if let Some(payload) = &self.payload {
            let mut buf = Vec::with_capacity(MIN_SPA_PACKET_LEN + payload.len());
            buf.extend_from_slice(&self.buf);
            buf.push(self.r#type as u8);
            buf.extend_from_slice(payload);
            buf
        } else {
            let mut buf = self.buf.to_vec();
            buf.push(self.r#type as u8);
            buf
        }
    }
}

/// 生成SPA包
pub fn new_packet(pk: Sm2PublicKey, mut spa: SPA) -> Vec<u8> {
    let timestamp = coarsetime::Clock::now_since_epoch().as_millis();
    spa.set_timestamp(timestamp);

    let buf = spa.buffer();
    let signature = sm3_hash(&buf);

    let mut cipher_text = pk.encrypt(&buf, false, Sm2Model::C1C2C3).unwrap();

    cipher_text.extend_from_slice(&signature);
    cipher_text
}

/// 校验SPA
///
/// # Arguments
///
/// - `deviation`: 允许的时间误差, 为0时, 不校验时间.
pub fn verify_packet(spa: &SPA, signature: &[u8; 32], secret: &[u8], deviation: u32) -> bool {
    // 验证签名
    let hash = sm3_hash(&spa.buffer());
    let verify = signature.iter().zip(hash.iter()).all(|(a, b)| a == b);

    if !verify {
        return false;
    }

    // 验证时间
    if deviation > 0 {
        let timestamp = spa.timestamp() as i64;
        let local_stamp = coarsetime::Clock::now_since_epoch().as_millis() as i64;
        if (local_stamp - timestamp).abs() > deviation as i64 * 1000 {
            let device = hex::to_string!(spa.device());
            log::error!(
                "time difference is more than {} seconds. device.id = {}",
                deviation,
                device
            );
            return false;
        }
    }

    // 验证 HOTP
    let otp = spa.password();
    let counter = spa.counter();
    let new_otp = Hotp::new(secret).generate(counter);
    let result = otp == new_otp;
    if !result {
        let payload = spa
            .payload
            .as_ref()
            .map(|payload| String::from_utf8(payload.to_owned()));
        log::error!("binding code does not match. {:?}", &payload);
    }
    result
}

#[cfg(test)]
mod test {
    use gm_sm2::key::{gen_keypair, Sm2Model, Sm2PrivateKey};
    use gm_sm3::sm3_hash;
    use pkcs8::DecodePrivateKey;
    use std::net::Ipv4Addr;

    use rand::Rng;

    use crate::{
        hotp::Hotp,
        spa::{MIN_SPA_PACKET_LEN, SPA},
    };

    use super::{new_packet, verify_packet};

    #[test]
    fn test_spa_packet() {
        let (pk, sk) = gen_keypair().unwrap();

        let aid: [u8; 16] = rand::thread_rng().gen::<[u8; 16]>();
        let ip = Ipv4Addr::new(192, 168, 1, 1);
        let secret: [u8; 4] = [0x01, 0x02, 0x03, 0x04];
        let counter = 1;

        let mut spa = SPA::new(crate::spa::SPAType::Auth);
        spa.set_client_ip(&ip);
        spa.set_device(&aid);
        spa.set_counter(counter);

        let hotp = Hotp::new(&secret);
        let password = hotp.generate(counter);
        spa.set_password(password);
        spa.set_payload(&[1, 3, 4]);

        let packet = new_packet(pk, spa);
        assert_eq!(MIN_SPA_PACKET_LEN + 3 + 97 + 32, packet.len());

        let spa_pkt = sk
            .decrypt(&packet[..packet.len() - 32], false, Sm2Model::C1C2C3)
            .unwrap();

        let spa = SPA::from(spa_pkt[..].into()).unwrap();

        assert_eq!(Some(&vec![1u8, 3, 4]), spa.payload());

        let verify = verify_packet(
            &spa,
            &packet[packet.len() - 32..].try_into().unwrap(),
            &secret,
            0,
        );

        assert!(verify);
    }

    #[test]
    fn test_spa() {
        let packet = &[
            22, 3, 1, 0, 234, 1, 0, 0, 230, 3, 3, 195, 194, 93, 53, 60, 173, 167, 225, 131, 136,
            120, 194, 61, 16, 41, 169, 161, 59, 41, 139, 180, 85, 169, 150, 20, 47, 112, 21, 230,
            104, 31, 219, 32, 3, 122, 98, 23, 64, 221, 139, 49, 253, 153, 180, 84, 52, 109, 27, 96,
            125, 24, 116, 244, 245, 5, 223, 219, 47, 40, 68, 113, 242, 133, 67, 139, 0, 38, 192,
            43, 192, 47, 192, 44, 192, 48, 204, 169, 204, 168, 192, 9, 192, 19, 192, 10, 192, 20,
            0, 156, 0, 157, 0, 47, 0, 53, 192, 18, 0, 10, 19, 1, 19, 2, 19, 3, 1, 0, 0, 119, 0, 5,
            0, 5, 1, 0, 0, 0, 0, 0, 10, 0, 10, 0, 8, 0, 29, 0, 23, 0, 24, 0, 25, 0, 11, 0, 2, 1, 0,
            0, 13, 0, 26, 0, 24, 8, 4, 4, 3, 8, 7, 8, 5, 8, 6, 4, 1, 5, 1, 6, 1, 5, 3, 6, 3,
        ];

        let prikey = "-----BEGIN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgTITLdgYONqYEc6YW
E4H+/JPCVJNeyshHbDryCkFYIPugCgYIKoEcz1UBgi2hRANCAAR7NuNVoiyVhWTh
peEk139qk5xz2G6IdvTGt0MeqGmBzW+04Us2FDiNLULuryDRpM3L+kkZ4oMmXJX3
q0LIeD/k
-----END PRIVATE KEY-----";

        let privkey = Sm2PrivateKey::from_pkcs8_pem(prikey).unwrap();
        let spa = match privkey.decrypt(&packet[..packet.len() - 32], false, Sm2Model::C1C2C3) {
            Ok(spa) => spa,
            Err(err) => {
                println!("failed to decrypt spa packet: {err}");
                return;
            }
        };
        let spa = SPA::from(spa);
        // let spa = SPA::from(packet[..packet.len() - 32].to_vec());
        assert!(spa.is_ok());

        let spa = spa.unwrap();
        // 验证签名
        let hash = sm3_hash(&spa.buffer());
        let verify = packet[packet.len() - 32..]
            .iter()
            .zip(hash.iter())
            .all(|(a, b)| a == b);
        assert!(verify);

        println!("{:?}", spa.timestamp());

        if let Some(username) = spa.payload() {
            println!("{:?}", String::from_utf8(username.to_vec()));
        }

        let secret = hex::decode!("80aa34da255247919543405f157a089b".as_bytes());
        let hotp = Hotp::new(&secret);
        let otp = hotp.generate(spa.counter());
        assert_eq!(spa.password(), otp);
    }

    #[test]
    fn test_sm3() {
        let c_hash = "207cf410532f92a47dee245ce9b11ff71f578ebd763eb3bbea44ebd043d018fb";
        let msg = "123456";
        let hash = sm3_hash(msg.as_bytes());

        let hash = hex::to_string!(&hash);

        assert_eq!(c_hash, hash);
    }
}
