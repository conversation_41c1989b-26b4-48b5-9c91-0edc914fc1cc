use std::process::Command;

/// 防火墙
/// sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate
pub fn is_enable() -> bool {
    // let cmd = duct::cmd!("/sbin/pfctl", "-s", "info").stderr_null().stdout_capture();
    // const EXPECTED_OUTPUT: &'static [u8] = b"Status: Enabled";
    // match cmd.run() {
    //     Ok(output) => output.stdout.as_slice().find(&EXPECTED_OUTPUT).is_some(),
    //     Err(err) => {
    //         log::error!("Failed to execute pfctl, assuming pf is not enabled: {}", err);
    //         false
    //     }
    // }
    // Create a PfCtl instance to control PF with:
    // let mut pf = pfctl::PfCtl::new().unwrap();
    // pf.is_enabled()
    //     .map_err(|err| {
    //         log::error!("{err}");
    //     })
    //     .unwrap_or_default()

    match Command::new("/usr/libexec/ApplicationFirewall/socketfilterfw")
        .arg("--getglobalstate")
        .output()
    {
        Ok(output) => {
            if output.status.success() {
                if let Ok(result) = String::from_utf8(output.stdout) {
                    return result.contains("enabled");
                }
            }
        }
        Err(err) => {
            log::warn!("Failed to get firewall state. {:?}", err);
        }
    }
    false
}
