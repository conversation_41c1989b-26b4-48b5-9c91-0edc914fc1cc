use ipnet::{Ipv4AddrRange, Ipv4Net, Ipv6AddrRange, Ipv6Net};
use std::{
    collections::HashSet,
    net::IpAddr,
    ops::{AddAssign, SubAssign},
};

use tlv::Serialize;
use tlv_types::{Resource, ResourceList};

#[derive(serde::Serialize, serde::Deserialize, Default, Debug, Clone)]
pub struct Servers {
    pub ips: HashSet<IpAddr>,
    pub range: HashSet<(IpAddr, IpAddr)>,
    pub cidr: HashSet<(u8, IpAddr)>,
}

impl AddAssign for Servers {
    fn add_assign(&mut self, rhs: Self) {
        self.ips.extend(rhs.ips.into_iter());
        self.cidr.extend(rhs.cidr.into_iter());
        self.range.extend(rhs.range.into_iter());
    }
}

impl SubAssign for Servers {
    fn sub_assign(&mut self, rhs: Self) {
        self.ips.retain(|ip| !rhs.ips.contains(ip));
        self.cidr.retain(|cidr| !rhs.cidr.contains(cidr));
        self.range.retain(|range| !rhs.range.contains(range));
    }
}

impl Servers {
    pub fn as_bytes(&self) -> Vec<u8> {
        let mut resources = vec![];
        for ip in &self.ips {
            let resource = match ip {
                IpAddr::V4(ip) => Resource::Ipv4(*ip),
                IpAddr::V6(ip) => Resource::Ipv6(*ip),
            };
            resources.push(resource);
        }
        for (start, end) in &self.range {
            match (start, end) {
                (IpAddr::V4(start), IpAddr::V4(end)) => {
                    resources.push(Resource::Ipv4Range(Ipv4AddrRange::new(*start, *end)));
                }
                (IpAddr::V6(start), IpAddr::V6(end)) => {
                    resources.push(Resource::Ipv6Range(Ipv6AddrRange::new(*start, *end)));
                }
                (..) => {}
            }
        }
        for (prefix_len, ip) in &self.cidr {
            match ip {
                IpAddr::V4(ip) => {
                    resources.push(Resource::Ipv4Net(Ipv4Net::new(*ip, *prefix_len).unwrap()));
                }
                IpAddr::V6(ip) => {
                    resources.push(Resource::Ipv6Net(Ipv6Net::new(*ip, *prefix_len).unwrap()));
                }
            }
        }

        let resources = ResourceList {
            resources: Some(resources),
        };
        resources.serialize()
    }
}
