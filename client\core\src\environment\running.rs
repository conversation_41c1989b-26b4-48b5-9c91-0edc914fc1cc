use sysinfo::{ProcessRefresh<PERSON><PERSON>, Refresh<PERSON><PERSON>, System};

/// 运行软件
#[cfg_attr(not(windows), allow(unused_variables))]
pub fn processes(need_dot: bool) -> Vec<String> {
    let sys = System::new_with_specifics(
        RefreshKind::default().with_processes(ProcessRefreshKind::default()),
    );

    let mut processes: Vec<String> = sys
        .processes()
        .values()
        .map(|process| {
            let process_name = process.name().to_str().unwrap_or_default();
            if cfg!(windows) && !need_dot {
                process_name
                    .rsplit_once('.')
                    .map(|(name, _)| name.to_string())
                    .unwrap_or(process_name.to_string())
            } else {
                process_name.to_owned()
            }
        })
        .collect();
    processes.sort();
    processes
}
