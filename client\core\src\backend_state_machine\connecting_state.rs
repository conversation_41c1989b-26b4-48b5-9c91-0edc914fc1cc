use std::time::Duration;

use futures::{SinkExt, StreamExt};
use serde_json::Value;
use tokio::net::TcpStream;
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use base::packet::MessageCodec;
use tlv_types::{AuthResult, ModePayload};
use types::backend::{BackendStateTransition, ErrorStateCause, Login};

use crate::{
    backend_state_machine::{
        connected_state::ConnectedState, disconnected_state::DisconnectedState, AfterDisconnect,
        BackendCommand, BackendCommandReceiver, BackendState, BackendStateWrapper, ResponseTx,
        SharedBackendStateValues,
    },
    EventListener,
};

use super::{error_state::ErrorState, EventConsequence};

pub struct ConnectingState {
    pub(super) framed: Framed<TlsStream<TcpStream>, MessageCodec>,
}

impl ConnectingState {
    async fn disconnect<L: EventListener>(
        mut self,
        shared_values: &mut SharedBackendStateValues<L>,
        _after_disconnect: AfterDisconnect,
    ) -> EventConsequence {
        if self.framed.close().await.is_err() {
            log::error!("Controller stream already closed");
        }

        EventConsequence::NewState(DisconnectedState::enter(shared_values, None).await)
    }
}

#[async_trait::async_trait]
impl<L> BackendState<L> for ConnectingState
where
    L: EventListener + Send + Clone + 'static,
{
    type Bootstrap = (ResponseTx<AuthResult, crate::Error>, Value, Login);

    async fn enter(
        shared_values: &mut SharedBackendStateValues<L>,
        args: Self::Bootstrap,
    ) -> (BackendStateWrapper, BackendStateTransition) {
        match tokio::time::timeout(
            Duration::from_secs(5),
            shared_values.login(args.0, args.1, args.2),
        )
        .await
        {
            Ok(Some((framed, mode_type, dns, ip))) => {
                let local_addr = framed.get_ref().get_ref().0.local_addr().unwrap();

                match mode_type {
                    ModePayload::Mix { .. } => {
                        ConnectedState::enter(shared_values, (framed, dns, ip.unwrap())).await
                    }
                    ModePayload::Standard { .. } => {
                        let connecting_state = Self { framed };
                        (
                            BackendStateWrapper::from(connecting_state),
                            BackendStateTransition::Connecting(local_addr),
                        )
                    }
                }
            }
            _ => ErrorState::enter(shared_values, ErrorStateCause::AuthFailed).await,
        }
    }

    async fn handle_event(
        self,
        commands: &mut BackendCommandReceiver,
        shared_values: &mut SharedBackendStateValues<L>,
    ) -> EventConsequence {
        use EventConsequence::*;

        let command = commands.next().await;
        match command {
            Some(BackendCommand::Disconnect(tx)) => {
                let result = self
                    .disconnect(shared_values, AfterDisconnect::Nothing)
                    .await;
                if let Some(tx) = tx {
                    _ = tx.send(());
                }
                result
            }
            Some(BackendCommand::Login { .. }) => {
                self.disconnect(shared_values, AfterDisconnect::Nothing)
                    .await
            }
            Some(BackendCommand::ChangePwdByCode(tx, system_info, payload)) => {
                shared_values
                    .change_password_by_verify_code(tx, system_info, payload)
                    .await;
                self.disconnect(shared_values, AfterDisconnect::Nothing)
                    .await
            }
            None => Finished,
            Some(_) => SameState(self.into()),
        }
    }
}
