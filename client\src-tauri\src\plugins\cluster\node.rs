use crate::{
    cluster,
    plugins::{self, ErrorResponse},
    state::AppState,
};
use communicate_interface::ControlServiceClient;
use config::UrlWrapper;
use reqwest::Url;
use serde::{Deserialize, Serialize};
use tauri::{AppHandle, Runtime, State};
use tokio::sync::{Mutex, RwLock};

#[derive(Deserialize)]
pub(super) struct ClusterConfigUrl {
    internal: Option<Url>,
    external: Option<Url>,
}

/// 加载节点后的下一步
#[derive(Serialize)]
#[serde(tag = "type", content = "payload")]
pub(super) enum NextStep {
    /// 执行下一步
    ExecuteNextAction,
    /// 设置地址
    ConfigClusterUrl {
        internal: Option<UrlWrapper>,
        external: Option<UrlWrapper>,
    },
}

/// 加载节点信息
#[tauri::command(async)]
pub(super) async fn load<R: Runtime>(
    app_handle: AppHandle<R>,
    app_config: State<'_, Mutex<config::AppConfig>>,
) -> plugins::Result<NextStep> {
    let config_lock = app_config.lock().await;
    let mut cloned_config = config_lock.clone();
    drop(config_lock);

    // 未配置地址
    if cloned_config.cluster_config_url.is_none()
        && cloned_config.cluster_external_config_url.is_none()
    {
        return Ok(NextStep::ConfigClusterUrl {
            internal: None,
            external: None,
        });
    }

    if cluster::load_nodes_and_update_cluster_config_url(
        &app_handle,
        cloned_config.cluster_config_url.clone().map(Into::into),
        cloned_config
            .cluster_external_config_url
            .clone()
            .map(Into::into),
        &mut cloned_config,
    )
    .await
    {
        return Ok(NextStep::ExecuteNextAction);
    }

    // 若上次加载有配置, 则放弃在此加载
    let has_nodes = cloned_config.cluster_nodes.is_some();
    if has_nodes {
        return Ok(NextStep::ExecuteNextAction);
    }

    Ok(NextStep::ConfigClusterUrl {
        internal: cloned_config.cluster_config_url,
        external: cloned_config.cluster_external_config_url,
    })
}

/// 使用之前的地址重新加载集群节点配置信息
#[tauri::command(async)]
pub(super) async fn reload<R: Runtime>(app_handle: AppHandle<R>) -> plugins::Result<()> {
    tokio::spawn(cluster::reload(app_handle));
    Ok(())
}

/// 使用指定地址重新加载集群节点
#[tauri::command(async)]
pub(super) async fn reload_cluster_nodes<R: Runtime>(
    config: ClusterConfigUrl,
    app_handle: AppHandle<R>,
    app_config: State<'_, Mutex<config::AppConfig>>,
    rpc: State<'_, Mutex<ControlServiceClient>>,
    app_state: State<'_, RwLock<AppState>>,
) -> plugins::Result<config::AppConfig> {
    // 断开连接
    _ = plugins::sdp::auth::logout(rpc, app_state).await;

    let config_lock = app_config.lock().await;
    let mut cloned_config = config_lock.clone();
    drop(config_lock);

    if cluster::load_nodes_and_update_cluster_config_url(
        &app_handle,
        config.internal,
        config.external,
        &mut cloned_config,
    )
    .await
    {
        return Ok(cloned_config);
    }
    Err(ErrorResponse::new(618, None))
}
