use crate::{config::User, RemoteNode};
use futures_channel::{
    mpsc::{self, UnboundedReceiver},
    oneshot,
};
use futures_util::StreamExt;
use libcore::{
    backend_state_machine::BackendCommand,
    proxy_request::{Pro<PERSON>H<PERSON><PERSON>, ProxyHttp, ProxyHttpResult, REQUESTS, REQUEST_SEQUENCE},
    CoreCommandSender, InternalCoreEvent,
};
use proxy_request::ResponseData;
use serde_json::Value;
use std::{collections::HashMap, slice::Iter, sync::atomic::Ordering};
use tracing::{debug, error, info};
use types::backend::{ConnectionConfig, Login, SPAAuthorization};
use url::Url;

pub fn spawn(
    core_tx: CoreCommandSender,
    nodes: Vec<RemoteNode>,
    proxy_handle: ProxyHandle,
    system_info: Value,
    user: User,

    mut reconnect_rx: UnboundedReceiver<()>,
) {
    tokio::spawn(async move {
        // 重试次数
        let retry_times = 3;

        login_with_retrys(
            core_tx.clone(),
            proxy_handle.clone(),
            nodes.iter(),
            system_info.clone(),
            &user,
            retry_times,
        )
        .await;

        loop {
            while let Some(_) = reconnect_rx.next().await {
                info!("Reconnect");

                login_with_retrys(
                    core_tx.clone(),
                    proxy_handle.clone(),
                    nodes.iter(),
                    system_info.clone(),
                    &user,
                    retry_times,
                )
                .await;
            }
        }
    });
}

async fn do_login(core_tx: CoreCommandSender, system_info: Value, payload: Login) -> bool {
    let (tx, rx) = oneshot::channel();
    _ = core_tx.send_to_backend(BackendCommand::Login {
        tx,
        system_info: system_info.clone(),
        payload: payload.clone(),
    });

    match rx.await.unwrap() {
        Ok(result) => {
            info!("Login successful, response: {:?}", result);
            false
        }
        Err(libcore::Error::ConnectError(error)) => {
            error!("Login failed: {}", error);
            true
        }
        Err(error) => {
            error!("Login failed: {}", error);
            _ = core_tx.send(InternalCoreEvent::TriggerShutdown);
            false
        }
    }
}

fn payload_construct(remote_node: &RemoteNode, user: &User, secret: &str) -> Login {
    Login {
        connection_config: ConnectionConfig {
            ip: remote_node.ip,
            host: remote_node.host.clone(),
            port: remote_node.port,
            spa_port: remote_node.spa_port,
            authorization: Some(SPAAuthorization {
                tenant: user.unit.clone(),
                secret: secret.to_owned(),
                username: user.username.clone(),
            }),
        },
        payload: serde_json::json!({
            "type": "pwd",
            "tenant": user.unit,
            "password": user.password,
        }),
    }
}

async fn login_with_retrys(
    core_tx: CoreCommandSender,
    proxy_handle: ProxyHandle,
    mut node_iter: Iter<'_, RemoteNode>,
    system_info: Value,
    user: &User,
    retry_times: u32,
) {
    let mut previous_node = None;
    let mut previous_secret: Option<String> = None;

    for _ in 0..retry_times {
        // 获取到了安全码, 则后续重试也使用该节点
        let payload = if let Some(secret) = &previous_secret {
            let node = previous_node.as_ref().unwrap();
            payload_construct(node, &user, secret.as_str())
        } else if let Some(remote_node) = node_iter.next() {
            previous_node = Some(remote_node.clone());
            // 获取绑定码
            info!("Get the user security code through node {:?}", remote_node);
            let Some(secret) =
                get_user_secret(proxy_handle.clone(), remote_node, user.username.clone()).await
            else {
                error!("Failed to obtain user security code, exit");
                _ = core_tx.send(InternalCoreEvent::TriggerShutdown);
                return;
            };

            previous_secret = Some(secret.clone());
            payload_construct(remote_node, &user, secret.as_str())
        } else if let Some(remote_node) = &previous_node {
            // 获取绑定码
            info!("Get the user security code through node {:?}", remote_node);
            let Some(secret) =
                get_user_secret(proxy_handle.clone(), remote_node, user.username.clone()).await
            else {
                error!("Failed to obtain user security code, exit");
                _ = core_tx.send(InternalCoreEvent::TriggerShutdown);
                return;
            };

            previous_secret = Some(secret.clone());

            payload_construct(remote_node, &user, secret.as_str())
        } else {
            return;
        };

        if !do_login(core_tx.clone(), system_info.clone(), payload).await {
            break;
        }
    }
}

async fn get_user_secret(
    proxy_handle: ProxyHandle,
    node: &RemoteNode,
    username: String,
) -> Option<String> {
    const PRIVATE_KEY: &str = "3146d44927b057e26f90adf3ee51e3c2297c1cf632b2744fa18ebf0b351ae129";
    let private_key = gm_sm2::key::Sm2PrivateKey::from_hex_string(PRIVATE_KEY).unwrap();

    let mut inner_request = proxy_request::HttpRequest {
        method: "GET".to_string(),
        url: Url::parse("https://sso.jingantech.com/mfa/v8/web/mfa/client/request/generate")
            .unwrap(),
        query: Some({
            let mut q = HashMap::new();
            q.insert("participantTypes".to_owned(), "03".to_owned());
            q.insert("participantKeyword".to_owned(), username);
            q.insert("method".to_owned(), "pwd".to_owned());
            q
        }),
        headers: None,
        body: None,
        timeout: None,
        response_type: Some(proxy_request::ResponseType::Json),
        stream: None,
    };

    // 获取requestID
    let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
    let request = ProxyHttp {
        connection_config: Some(ConnectionConfig {
            ip: node.ip,
            host: node.host.clone(),
            port: node.port,
            spa_port: node.spa_port,
            authorization: None,
        }),
        seq,
        request: serde_json::to_vec(&inner_request).unwrap(),
    };

    let (tx, mut rx) = mpsc::channel(1);
    let (chunk_tx, mut chunk_rx) = mpsc::channel(1);

    REQUESTS.insert(seq, (tx, chunk_tx)).await;
    proxy_handle.send_request(request);

    let request_id = match rx.next().await {
        Some(ProxyHttpResult::Ok(result, is_chunk)) => {
            let response = serde_json::from_slice::<ResponseData>(&result).unwrap();
            debug!("Response code of requestID: {}", response.status);

            if response.status / 100 != 2 {
                error!("Invalid response: {}", response.status);
                return None;
            }

            let data = if is_chunk {
                let mut chunk_bytes = vec![];
                while let Some(chunk) = chunk_rx.next().await {
                    chunk_bytes.extend(chunk);
                }

                serde_json::from_slice(&chunk_bytes).unwrap()
            } else {
                response.data
            };

            let Some(request_id) = data["data"].as_str() else {
                error!("Invalid response: {}", data);
                return None;
            };

            request_id.to_owned()
        }
        result => {
            error!("Request error: {:?}", result);
            return None;
        }
    };

    info!("Got requestID: {}", request_id);

    // 获取用户绑定码
    let seq = REQUEST_SEQUENCE.fetch_add(1, Ordering::Acquire);
    inner_request.url =
        Url::parse("https://sso.jingantech.com/identity-sso/v8/web/client/userSecretKey/web")
            .unwrap();
    inner_request.method = "POST".to_owned();
    inner_request.body = Some(proxy_request::Body::Json(serde_json::json!({
        "requestId": request_id,
    })));
    let request = ProxyHttp {
        connection_config: Some(ConnectionConfig {
            ip: node.ip,
            host: node.host.clone(),
            port: node.port,
            spa_port: node.spa_port,
            authorization: None,
        }),
        seq,
        request: serde_json::to_vec(&inner_request).unwrap(),
    };

    let (tx, mut rx) = mpsc::channel(1);
    let (chunk_tx, mut chunk_rx) = mpsc::channel(1);
    REQUESTS.insert(seq, (tx, chunk_tx)).await;
    proxy_handle.send_request(request);

    match rx.next().await {
        Some(ProxyHttpResult::Ok(result, is_chunk)) => {
            let response = serde_json::from_slice::<ResponseData>(&result).unwrap();
            debug!("Response code of secret: {}", response.status);

            if response.status / 100 != 2 {
                error!("Invalid response: {}", response.status);
                return None;
            }

            let data = if is_chunk {
                let mut chunk_bytes = vec![];
                while let Some(chunk) = chunk_rx.next().await {
                    chunk_bytes.extend(chunk);
                }

                serde_json::from_slice(&chunk_bytes).unwrap()
            } else {
                response.data
            };

            let Some(secret) = data["data"].as_str() else {
                error!("Invalid response: {}", data);
                return None;
            };

            info!("Got user secret: {}", secret);
            let secret_bytes = if secret.starts_with("04") {
                secret.as_bytes().to_vec()
            } else {
                [b"04", secret.as_bytes()].concat()
            };

            let secret = private_key
                .decrypt(
                    &hex::decode!(&secret_bytes),
                    false,
                    gm_sm2::key::Sm2Model::C1C2C3,
                )
                .unwrap();
            String::from_utf8(secret).ok()
        }
        result => {
            error!("Request error: {:?}", result);
            None
        }
    }
}
