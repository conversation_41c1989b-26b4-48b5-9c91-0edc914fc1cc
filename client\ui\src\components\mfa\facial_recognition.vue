<template>
  <div class="mfa-content">
    <h3 v-if="type">Windows人脸识别</h3>
    <div v-if="firstStep && type === 'login'">
      <div class="inputItem">
        <div class="inputContent">
          <el-input
            ref="username"
            v-model="user.username"
            placeholder="请输入用户名"
          />
        </div>
      </div>
      <el-button
        ref="nextStepBtn"
        class="Btn"
        :loading="loading"
        :disabled="!user.username"
        @click="generateRequestId"
      >
        下一步
      </el-button>
      <userAgreement />
    </div>
    <div v-else>
      <img
        :src="src"
        :class="type ? 'mfa-content-img' : 'mfa-content-imgmode'"
        alt=""
      />
      <p v-if="type" style="font-size: 12px">
        用户:
        <span @click="cancel" style="color: #f9780c; cursor: pointer"
          >&nbsp;{{ user.username }}&nbsp;
          <el-icon color="#F9780C" style="vertical-align: middle">
            <EditPen />
          </el-icon>
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import { getRequestId, cancelAuth, facialCompleteAudit } from "@/api/service";
import faceAuthImg from "/assets/images/mfa/facial.png";
import userAgreement from "../../views/userAgreement.vue";
import { invoke } from "@tauri-apps/api/tauri";

export default {
  name: "facial_recognition",
  components: {
    userAgreement,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
    tenant: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      polling: undefined,
      requestId: "",
      src: faceAuthImg,
      code: "facial_recognition",
      user: {
        username: "" || this.name,
      },
      firstStep: true,
    };
  },
  created() {
    if (this.type !== "login") {
      this.generateRequestId();
    }
  },
  methods: {
    // 获取requestId
    generateRequestId() {
      this.loading = true;
      invoke("plugin:bio|is_support_facial_recognition").then((support) => {
        // 不支持面部识别
        if (!support) {
          this.loading = false;
          this.alert.error("检测到设备不支持人脸识别，请换其他方式认证");
          return;
        }

        let data = {
          participantTypes: this.type === "login" ? "03" : "01",
          participantKeyword: this.userId || this.user.username,
        };
        getRequestId(
          this.participantGroupId,
          data.participantTypes,
          data.participantKeyword,
          this.code
        )
          .then((result) => {
            this.requestId = result.data;
            this.$emit("requestId", result.data);
            // this.startPolling();
            this.doFacialRecognition();
            this.firstStep = false;
          })
          .catch((error) => {
            this.$emit("generateRequestIdFailCallbackFunc", error);
          });
      });
    },
    // 开始面部识别认证
    doFacialRecognition() {
      invoke("plugin:bio|facial_recognition", {
        username: this.user.username,
      })
        .then(async (status) => {
          /// 认证成功, 记录日志
          if (status) {
            await facialCompleteAudit(this.requestId, null);
            this.Complete();
          }
        })
        .catch(async (msg) => {
          // 认证失败/超时处理
          await facialCompleteAudit(this.requestId, msg);
          this.alert.error(msg);

          this.cancel();
          this.$emit("cancelMfaCallbackFunc");
        });
    },
    Complete() {
      this.$emit("mfaCallbackFunc", {
        requestId: this.requestId,
        type: this.code,
      });
    },
    cancel() {
      invoke("plugin:bio|cancel_facial_recognition");
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
      this.firstStep = true;
    },
  },
};
</script>

<style scoped lang="less">
.inputItem {
  text-indent: 0.6em;
  position: relative;
  margin-top: 1.8rem;
  border-bottom: 1px solid #d1d3db;
  margin-bottom: 10px;
  // border-radius: 30px;
  &:hover {
    border-bottom: 1px solid #f9780c;
  }
}

.login_icon {
  color: #bfbfbf;
  font-size: 22px;
}

.inputContent {
  padding: 0.4rem;
  width: 240px;
}

:deep(.el-input__wrapper) {
  border: 0 !important;
  padding: 1px 1px;
}

.Btn {
  margin: 15px 0 10px;
  width: 100%;
  color: #f59547;
  background: #fff;
  border: 1px solid #f59547;
  outline: none;
  height: 45px;
  border-radius: 30px;
}
</style>
