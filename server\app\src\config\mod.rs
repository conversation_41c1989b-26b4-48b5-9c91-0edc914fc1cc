mod parse;
mod types;

use std::{
    fs::File,
    io::{self, Read},
    path::Path,
    process,
};

use lapin::uri::AMQPUri;
use tracing::{debug, error};
pub use types::*;

#[derive(Debug, thiserror::Error)]
pub enum Error {
    #[error("Failed to read config file. {0}")]
    ReadError(#[from] io::Error),

    #[error("Syntax error in config file. {0}")]
    FormatError(#[from] toml::de::Error),

    #[error("Syntax error in MQ address. {0}")]
    AMQPError(String),

    #[error("Invalid virtual IP address.")]
    VirtualIpError,

    #[error("Invalid xdp mode value. {0}")]
    XdpModeError(String),
}

pub fn load(config: impl AsRef<Path>) -> Result<Config, Error> {
    let config_file = config.as_ref().to_str().unwrap();
    debug!("reading config file: {}", config_file);

    let mut file = File::open(config).map_err(|error| Error::ReadError(error))?;
    let mut conf = String::new();

    file.read_to_string(&mut conf)
        .map_err(|error| Error::ReadError(error))?;

    // Parse configuration
    let config: Config = toml::from_str(&conf).map_err(|error| Error::FormatError(error))?;

    // 验证开放端口个数
    if let Some(ports) = &config.interface.primary.base.pub_ports {
        if ports.len() > 10 {
            error!(
                "Network interface `{}` allows a maximum of ten open ports",
                config.interface.primary.base.iface
            );
            process::exit(-1);
        }
    }

    if let Some(ref secondary) = config.interface.secondary {
        secondary.iter().for_each(|config| {
            config.base.pub_ports.as_ref().map(|ports| {
                if ports.len() > 10 {
                    error!(
                        "Network interface `{}` allows a maximum of ten open ports",
                        config.base.iface
                    );
                    process::exit(-1);
                }
            });
        });
    }

    // 验证虚拟IP地址
    let r#virtual = &config.interface.r#virtual;
    if r#virtual.enabled && (r#virtual.ipv4.is_unspecified() || r#virtual.src_ipv4.is_unspecified())
    {
        return Err(Error::VirtualIpError);
    }

    // validate(&config, mode)?;
    // 验证MQ地址
    config
        .mq
        .url
        .parse::<AMQPUri>()
        .map_err(|err| Error::AMQPError(err))?;

    Ok(config)
}
