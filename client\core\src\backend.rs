use std::time::Duration;

use futures::{
    channel::{mpsc, oneshot},
    stream::{SplitSink, SplitStream},
    SinkExt, StreamExt,
};

use socket2::{SockRef, TcpKeepalive};
use tokio::{net::TcpStream, sync::broadcast};
use tokio_rustls::client::TlsStream;
use tokio_util::codec::Framed;

use crate::{
    mpsc::Sender, routing::RouteManagerHandle, CoreEventSender, Direction, EventListener,
    InternalCoreEvent,
};
use base::packet::{Message, MessageCodec, MessageType};
use types::backend::TicketType;

pub(crate) mod common;
pub(crate) mod server;
mod spa;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("{0}")]
    IoError(#[from] std::io::Error),

    #[error("missing bind code")]
    MissingSecret,

    #[error("missing unit code")]
    MissingUnit,

    #[error("missing username")]
    MissingUsername,
}

pub struct Backend<L: EventListener + Clone + Send> {
    pub core_tx: CoreEventSender,
    pub event_listener: L,
    pub route_manager_handle: RouteManagerHandle,
    pub interface_name: String,

    pub ctl_framed: Framed<TlsStream<TcpStream>, MessageCodec>,

    // 断开连接
    pub backend_close_rx: oneshot::Receiver<()>,
    // 连接断开通知
    pub backend_close_tx: oneshot::Sender<Option<i8>>,

    pub ctl_packet_rx: mpsc::UnboundedReceiver<Message>,
    pub ctl_auth_packet_rx: mpsc::UnboundedReceiver<(Message, oneshot::Sender<bool>)>,
}

impl<L> Backend<L>
where
    L: EventListener + Clone + Send + 'static,
{
    pub(crate) async fn run(mut self) {
        // 将清理工作提前到消息处理前, 消息发送比启动HTTP代理服务更快, 导致数据丢失
        #[cfg(feature = "traceability")]
        crate::traceability::clear_cache().await;

        Self::config_stream(self.ctl_framed.get_ref().get_ref().0);

        let (tx, _rx) = broadcast::channel(1);
        let (sink, stream) = self.ctl_framed.split();
        let rx_ctl_read = tx.subscribe();
        let args = CtlServeReadArgs {
            core_tx: self.core_tx.clone(),
            interface_name: self.interface_name,
            event_listener: self.event_listener.clone(),
            stream,
            route_manager_handle: self.route_manager_handle,
            close_rx: rx_ctl_read,
        };
        let ctl_read_handle = tokio::spawn(async move { Self::tcp_read(args).await });

        let rx_ctl_write = tx.subscribe();
        let args = CtlServeWriteArgs {
            sink,
            close_rx: rx_ctl_write,
            message_rx: self.ctl_packet_rx,
            auth_message_rx: self.ctl_auth_packet_rx,
            core_tx: self.core_tx.clone(),
        };
        let ctl_write_handle = tokio::spawn(async move { Self::tcp_write(args).await });

        tokio::select! {
            _ = &mut self.backend_close_rx => {}
            reason = ctl_read_handle => {
                _ = self.backend_close_tx.send(reason.unwrap_or_default());
            }
            reason = ctl_write_handle => {
                _ = self.backend_close_tx.send(reason.unwrap_or_default());
            }
        }
        if tx.send(()).is_err() {
            log::debug!("Backend already closed");
        }
        log::debug!("Backend closed");
    }

    fn config_stream(stream: &TcpStream) {
        let socket = SockRef::from(stream);
        let keepalive = TcpKeepalive::new()
            .with_time(Duration::from_secs(180))
            .with_interval(Duration::from_secs(15));

        socket.set_tcp_keepalive(&keepalive).unwrap();
    }

    async fn tcp_read(mut args: CtlServeReadArgs<L>) -> Option<i8> {
        let reason = loop {
            tokio::select! {
                _ = args.close_rx.recv() => {
                    break None;
                }
                result = args.stream.next() => match result {
                    None => {
                        log::debug!("Lost connection with server");
                        break None;
                    }
                    Some(Err(error)) => {
                        if let Some(error_code) = error.raw_os_error() {
                            log::error!("Lost connection with server. os error {error_code}");
                        } else {
                            log::error!("Lost connection with server. {}", error);
                        }
                        break Some(-1);
                    }
                    Some(Ok(message)) => match message.r#type() {
                        // 白名单
                        MessageType::ResourceWhitelist => {
                            common::handle_whitelist_resources(
                                args.core_tx.clone(),
                                message.payload(),
                                args.interface_name.to_owned(),
                                args.route_manager_handle.clone()).await;
                        }
                        // 资源列表
                        MessageType::ServerList => {
                            common::handle_server_list(
                                args.core_tx.clone(),
                                message.payload(),
                                args.interface_name.to_owned(),
                                args.route_manager_handle.clone()).await;
                        }
                        // 增删服务器
                        MessageType::IncrementalServer => {
                            common::handle_increment_server_list(
                                args.core_tx.clone(),
                                message.payload(),
                                args.interface_name.to_owned(),
                                args.route_manager_handle.clone()).await;
                        }
                        // 修改密码结果
                        MessageType::ChangePwdResult => {
                            if let Some(reason) =
                                common::handle_change_pwd_result(
                                    args.event_listener.clone(),
                                    message.payload()).await {
                                break Some(reason);
                            }
                        }
                        // 票据结果
                        MessageType::SSOTicketResult => {
                            log::debug!("Received sso ticket");
                            let input = message.payload();
                            let result: Result<Vec<u8>, &'static str> = tlv::read_to_struct!(input);
                            let Ok(bytes) = result else {
                                log::error!("bad message. type = {:?}", MessageType::SSOTicketResult);
                                continue;
                            };
                            args.event_listener.notify_ticket_result(&bytes, TicketType::SSO)
                        }
                        // 客户端策略
                        MessageType::DeliveryStrategy => {
                            if let Some(reason) = common::handle_client_strategy(
                                args.core_tx.clone(),
                                message.payload()).await {
                                break Some(reason);
                            }
                        }

                        MessageType::RefreshTokenResult => {
                            if let Some(reason) = common::handle_refresh_token(
                                args.core_tx.clone(),
                                message.payload()).await {
                                break Some(reason);
                            }
                        }
                        // ----------------------
                        MessageType::ServerPacket => {
                            let input = message.payload();
                            if args
                                .core_tx
                                .send(InternalCoreEvent::TunnelPacket(
                                    Direction::In,
                                    input.as_slice_less_safe().to_vec(),
                                ))
                                .is_err()
                                {
                                    log::error!("Core already down or thread panicked");
                                    break Some(-1);
                                }
                        }
                        // 需要进行多因子认证
                        MessageType::RequiredMfa => {
                            if let Some(reason) =
                                common::handle_require_mfa(
                                    args.core_tx.clone(),
                                    args.event_listener.clone(),
                                    message.payload()).await {
                                break Some(reason);
                            }
                        }
                        // 断开连接的原因
                        MessageType::CloseReason => {
                            let input = message.payload();
                            let result: Result<u8, &'static str> = tlv::read_to_struct!(input);
                            let Ok(reason) = result else {
                                log::error!("bad message. type = {:?}", MessageType::CloseReason);
                                continue;
                            };
                            break Some(reason as i8);
                        }
                        // 通知信息
                        MessageType::Notification => {
                            common::handle_notification(
                                args.core_tx.clone(),
                                message.payload()).await;
                        }
                        MessageType::WebApps => {
                            let input = message.payload();
                            if common::handle_web_apps(#[cfg(feature = "dns_server")]args.core_tx.clone(),input).await.is_err() {
                                break None;
                            }
                        }
                        MessageType::WebAppsVirtualIps => {
                            common::handle_web_apps_virtual_ips(
                                args.core_tx.clone(),
                                message.payload(),
                                args.interface_name.to_owned(),
                                args.route_manager_handle.clone()).await;
                        }
                        MessageType::ProxyResponse => {
                            let input = message.payload();
                            if common::handle_proxy_response(input).await.is_err() {
                                break None;
                            }
                        }
                        MessageType::ProxyResponseChunk => {
                            let input = message.payload();
                            if common::handle_proxy_response_chunk(input).await.is_err() {
                                break None;
                            }
                        }
                        MessageType::ProxyFail => {
                            let input = message.payload();
                            if common::handle_proxy_fail(input).await.is_err() {
                                break None;
                            }
                        }
                        _ => {
                            log::error!(
                                "Received unexpected message. type = {:?}",
                                message.as_bytes()[0]
                            );
                        }
                    },
                }
            }
        };
        log::debug!("Backend read loop exit");
        reason
    }

    async fn tcp_write(mut args: CtlServeWriteArgs) -> Option<i8> {
        let reason = loop {
            tokio::select! {
                _ = args.close_rx.recv() => break None,
                message = args.message_rx.next() => match message {
                    Some(message) => {
                        let ty = message.r#type();
                        if let Err(error) = args.sink.send(message).await {
                            log::error!("Failed to send message  {:?} {}", ty, error);
                            break Some(-1);
                        }
                    }
                    None => break None,
                },
                message = args.auth_message_rx.next() => match message {
                    Some((message, tx)) => {
                        let typ = message.r#type();

                        let back_message = message.clone();
                        match args.sink.send(message).await {
                            Err(error) => {
                                log::error!("Failed to send message to server. {:?} {}", typ, error);
                                break Some(-1);
                            }
                            Ok(_) => {
                                // 从队列中移除正在认证的数据
                                if let Some(reason) =
                                    common::cancel_or_complete_auth_post_process(
                                        typ,
                                        back_message.payload(),
                                        args.core_tx.clone(),
                                        tx).await {
                                    break Some(reason);
                                }
                            }
                        }
                    }
                    None => break None,
                },
            }
        };
        if let Err(error) = args.sink.close().await {
            log::error!("Failed to close server. {error}");
        }
        log::debug!("Backend write loop exit");
        reason
    }
}

struct CtlServeReadArgs<L: EventListener> {
    core_tx: CoreEventSender,
    interface_name: String,
    event_listener: L,
    stream: SplitStream<Framed<TlsStream<TcpStream>, MessageCodec>>,
    route_manager_handle: RouteManagerHandle,
    close_rx: broadcast::Receiver<()>,
}

struct CtlServeWriteArgs {
    sink: SplitSink<Framed<TlsStream<TcpStream>, MessageCodec>, Message>,
    close_rx: broadcast::Receiver<()>,
    message_rx: mpsc::UnboundedReceiver<Message>,
    auth_message_rx: mpsc::UnboundedReceiver<(Message, oneshot::Sender<bool>)>,
    core_tx: CoreEventSender,
}
