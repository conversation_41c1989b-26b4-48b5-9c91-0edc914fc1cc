use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

use amq_protocol::types::FieldTable;
use deadpool_lapin::Pool;
use futures::{future::BoxFuture, FutureExt, StreamExt};
use lapin::{
    options::{
        BasicAckOptions, BasicConsumeOptions, BasicQosOptions, QueueBindOptions,
        QueueDeclareOptions,
    },
    Channel,
};
use rand::Rng;
use tracing::{error, info, trace, warn, Instrument};

use crate::{message::MQCommand, types::Servers, Error, EventSender, InternalEvent};

use super::{SERVER_LIST_EXCHANGE, SERVER_LIST_QUEUE};

fn do_listen(
    pool: Pool,
    channel: Channel,
    event_sender: EventSender,
) -> BoxFuture<'static, Result<(), lapin::Error>> {
    async move {
        // 声明队列
        let mut queue_declare_options = QueueDeclareOptions::default();
        // 连接断开时, 自动删除队列
        queue_declare_options.auto_delete = true;
        let num: u8 = rand::rng().random_range(0..100);
        let server_list_queue = format!("{}_{}", SERVER_LIST_QUEUE, num);
        channel
            .queue_declare(
                &server_list_queue,
                queue_declare_options,
                FieldTable::default(),
            )
            .await?;
        let bind_options = QueueBindOptions::default();
        channel
            .queue_bind(
                &server_list_queue,
                SERVER_LIST_EXCHANGE,
                SERVER_LIST_QUEUE,
                bind_options,
                FieldTable::default(),
            )
            .await?;
        let qos_options = BasicQosOptions::default();
        channel.basic_qos(1, qos_options).await?;
        let mut resources_consumer = channel
            .basic_consume(
                &server_list_queue,
                "server_list_consumer",
                BasicConsumeOptions::default(),
                FieldTable::default(),
            )
            .await?;
        tokio::spawn(
            async move {
                while let Some(Ok(delivery)) = resources_consumer.next().await {
                    trace!("recv mq message: {:?}", &delivery.data);

                    // 消息标记 0: 删除资源, 1: 新增资源, 2: 新增伪IP资源
                    let tag = delivery.data[0];

                    // 中间IP数据长度
                    let data_len =
                        u16::from_be_bytes([delivery.data[1], delivery.data[2]]) as usize;

                    // 租户长度
                    let tenant_len = delivery.data[3 + data_len] as usize;

                    if let Ok(tenant) =
                        std::str::from_utf8(&delivery.data[4 + data_len..4 + data_len + tenant_len])
                    {
                        let data = &delivery.data[3..3 + data_len];
                        let message = match tag {
                            // 新增/删除 资源
                            0 | 1 => {
                                let mut servers = Servers::default();

                                let mut i = 0;

                                loop {
                                    let ip_type = data[i + 1];
                                    match data[i] {
                                        // 单个IP
                                        0 => {
                                            let ip = match ip_type {
                                                4 => {
                                                    let ip = IpAddr::V4(Ipv4Addr::from(
                                                        <&[u8] as TryInto<[u8; 4]>>::try_into(
                                                            &data[i + 2..i + 6],
                                                        )
                                                        .unwrap(),
                                                    ));
                                                    i += 6;
                                                    ip
                                                }
                                                6 => {
                                                    let ip = IpAddr::V6(Ipv6Addr::from(
                                                        <&[u8] as TryInto<[u8; 16]>>::try_into(
                                                            &data[i + 2..i + 18],
                                                        )
                                                        .unwrap(),
                                                    ));
                                                    i += 18;
                                                    ip
                                                }
                                                // never match
                                                _ => continue,
                                            };
                                            servers.ips.insert(ip);
                                        }
                                        // IP段
                                        1 => {
                                            let range = match ip_type {
                                                4 => {
                                                    let start = IpAddr::V4(Ipv4Addr::from(
                                                        <&[u8] as TryInto<[u8; 4]>>::try_into(
                                                            &data[i + 2..i + 6],
                                                        )
                                                        .unwrap(),
                                                    ));
                                                    let end = IpAddr::V4(Ipv4Addr::from(
                                                        <&[u8] as TryInto<[u8; 4]>>::try_into(
                                                            &data[i + 6..i + 10],
                                                        )
                                                        .unwrap(),
                                                    ));
                                                    i += 10;
                                                    (start, end)
                                                }
                                                6 => {
                                                    let start = IpAddr::V6(Ipv6Addr::from(
                                                        <&[u8] as TryInto<[u8; 16]>>::try_into(
                                                            &data[i + 2..i + 18],
                                                        )
                                                        .unwrap(),
                                                    ));
                                                    let end = IpAddr::V6(Ipv6Addr::from(
                                                        <&[u8] as TryInto<[u8; 16]>>::try_into(
                                                            &data[i + 18..i + 34],
                                                        )
                                                        .unwrap(),
                                                    ));
                                                    i += 34;
                                                    (start, end)
                                                }
                                                // never match
                                                _ => continue,
                                            };
                                            servers.range.insert(range);
                                        }
                                        // CIDR
                                        2 => {
                                            let (prefix_len, ip) = match ip_type {
                                                4 => {
                                                    let cidr = (
                                                        data[i + 2],
                                                        IpAddr::V4(Ipv4Addr::from(
                                                            <&[u8] as TryInto<[u8; 4]>>::try_into(
                                                                &data[i + 3..i + 7],
                                                            )
                                                            .unwrap(),
                                                        )),
                                                    );
                                                    i += 7;
                                                    cidr
                                                }
                                                6 => {
                                                    let cidr = (
                                                        data[i + 2],
                                                        IpAddr::V6(Ipv6Addr::from(
                                                            <&[u8] as TryInto<[u8; 16]>>::try_into(
                                                                &data[i + 3..i + 19],
                                                            )
                                                            .unwrap(),
                                                        )),
                                                    );
                                                    i += 19;
                                                    cidr
                                                }
                                                // never match
                                                _ => continue,
                                            };
                                            servers.cidr.insert((prefix_len, ip));
                                        }
                                        _ => {}
                                    }
                                    if i >= data_len {
                                        break;
                                    }
                                }

                                Some(if tag == 0 {
                                    InternalEvent::MQMessage(MQCommand::DelResource {
                                        tenant: tenant.to_owned(),
                                        servers,
                                    })
                                } else {
                                    InternalEvent::MQMessage(MQCommand::AddResource {
                                        tenant: tenant.to_owned(),
                                        servers,
                                    })
                                })
                            }
                            // 新增伪IP资源
                            2 => {
                                let ip_type = data[0];
                                let (virtual_ip, resource) = match ip_type {
                                    4 => {
                                        let virtual_ip = IpAddr::V4(Ipv4Addr::from(
                                            <&[u8] as TryInto<[u8; 4]>>::try_into(&data[1..5])
                                                .unwrap(),
                                        ));
                                        let resource = IpAddr::V4(Ipv4Addr::from(
                                            <&[u8] as TryInto<[u8; 4]>>::try_into(&data[5..9])
                                                .unwrap(),
                                        ));
                                        (virtual_ip, resource)
                                    }
                                    6 => {
                                        let virtual_ip = IpAddr::V6(Ipv6Addr::from(
                                            <&[u8] as TryInto<[u8; 16]>>::try_into(&data[1..17])
                                                .unwrap(),
                                        ));
                                        let resource = IpAddr::V6(Ipv6Addr::from(
                                            <&[u8] as TryInto<[u8; 16]>>::try_into(&data[17..33])
                                                .unwrap(),
                                        ));
                                        (virtual_ip, resource)
                                    }
                                    // never match
                                    _ => continue,
                                };
                                Some(InternalEvent::MQMessage(MQCommand::AddVirtualResource {
                                    tenant: tenant.to_owned(),
                                    resource,
                                    virtual_ip,
                                }))
                            }
                            _ => None,
                        };
                        if let Some(message) = message {
                            event_sender.send(message).await;
                        }
                    }

                    delivery
                        .ack(BasicAckOptions::default())
                        .await
                        .expect("basic_ack");
                }

                error!("reconnecting...");

                let retry_future = async move {
                    let mut timeout = 1;

                    loop {
                        let channel = get_channel!(pool, timeout);

                        if let Err(err) =
                            do_listen(pool.clone(), channel, event_sender.clone()).await
                        {
                            error!("listen error: {err}");

                            tokio::time::sleep(std::time::Duration::from_millis(
                                2 << std::cmp::max(12, timeout),
                            ))
                            .await;
                            timeout += 1;
                            continue;
                        }
                        info!("reconnected");
                        break;
                    }
                };

                tokio::spawn(retry_future);
            }
            .instrument(tracing::error_span!(parent: None, "ResourceConsumer")),
        );

        Ok(())
    }
    .boxed()
}

/// 监听资源变化
pub(super) async fn listen(pool: Pool, event_sender: EventSender) -> Result<(), Error> {
    let conn = pool
        .get()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;

    let channel = conn
        .create_channel()
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;
    channel.on_error(|error| {
        warn!("resource channel status is abnormal: {error}");
    });

    do_listen(pool, channel, event_sender)
        .await
        .map_err(|error| Error::MqConsumerError(error.to_string()))?;

    Ok(())
}
