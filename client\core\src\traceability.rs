use base::net::IpPacket;
use moka::future::Cache;

#[cfg(any(windows, target_os = "linux"))]
use std::net::Ipv6Addr;

use once_cell::sync::Lazy;
use std::{
    net::{IpAddr, Ipv4Addr, SocketAddr},
    sync::atomic::{AtomicU16, Ordering},
};
use tokio::task::Join<PERSON><PERSON>le;

mod constants;
mod handler;
mod inner;
mod logs;
mod utils;

pub use constants::PROXY_TIMEOUT_SEC;
pub use handler::CLIENTS;
use inner::Proxy;
pub use inner::{add_app, get_http_scheme, remove_app, SNI_SERVER_CRYPTO_MAP, WEB_APPS};
pub use utils::cert::init;

pub static TRACING_LISTEN_PORT: AtomicU16 = AtomicU16::new(0);

/// 虚拟源地址与真实目标地址的映射
pub static FORWARD_PROXY_TARGETS: Lazy<Cache<SocketAddr, SocketAddr>> =
    Lazy::new(|| Cache::builder().build());

/// WEB应用虚拟IP地址映射
pub static WEBAPPS_VIRTUAL_IP_MAPPING: Lazy<Cache<IpAddr, IpAddr>> =
    Lazy::new(|| Cache::builder().build());

#[derive(Debug, thiserror::Error)]
pub enum ProxyError {
    #[error("Config builder error: {0}")]
    ConfigBuild(&'static str),

    #[error("Http Message Handler Error: {0}")]
    Handler(&'static str),

    #[error("Http Request Message Error: {0}")]
    Request(&'static str),

    #[error("TCP/UDP Proxy Layer Error: {0}")]
    Proxy(String),

    #[error("I/O Error")]
    Io(#[from] std::io::Error),

    #[error("rustls Connection Error")]
    Rustls(#[source] rustls::Error),

    #[error("Url Parse error")]
    Url(#[from] url::ParseError),

    #[error("Reqwest Error")]
    Reqwest(#[from] reqwest::Error),

    #[error("Hyper Http Error")]
    HyperHttp(#[from] hyper::http::Error),

    #[error("Hyper Http HeaderValue Error")]
    HyperHeaderValue(#[from] hyper::header::InvalidHeaderValue),

    #[error("Hyper Http HeaderName Error")]
    HyperHeaderName(#[from] hyper::header::InvalidHeaderName),

    // hyper errors
    #[error("hyper body manipulation error")]
    HyperBodyManipulationError(String),
    #[error("New closed in incoming-like")]
    HyperIncomingLikeNewClosed,
    #[error("New body write aborted")]
    HyperNewBodyWriteAborted,
    #[error("Hyper error in serving request or response body type")]
    HyperBodyError(#[from] hyper::Error),
}

#[allow(dead_code)]
#[derive(Debug, thiserror::Error, Clone)]
pub enum ClientCertsError {
    #[error("TLS Client Certificate is Required for Given SNI: {0}")]
    ClientCertRequired(String),

    #[error("Inconsistent TLS Client Certificate for Given SNI: {0}")]
    InconsistentClientCert(String),
}

/// 开启溯源代理服务
///
/// - `ip`: 可以是本机虚拟IP. (目前是客户端连接控制器的出网IP地址)
pub fn start_trace_proxy(device_id: String, ip: IpAddr) -> JoinHandle<()> {
    // HTTP代理
    tokio::spawn(async move {
        // 重复使用之前已经申请的端口
        let listening_port = TRACING_LISTEN_PORT.load(Ordering::Relaxed);

        let proxy = Proxy {
            // 通过操作系统分配端口
            listening_on: SocketAddr::new(IpAddr::V4(Ipv4Addr::new(10, 6, 7, 7)), listening_port),
            msg_handler: handler::MessageHandler::new(ip, device_id.clone()),
        };

        proxy.start().await;
    })
}

/// 清理缓存数据
pub async fn clear_cache() {
    let mut webapps = WEB_APPS.write().await;
    webapps.clear();
    drop(webapps);
    let mut sni_map = SNI_SERVER_CRYPTO_MAP.write().await;
    sni_map.clear();
    drop(sni_map);

    FORWARD_PROXY_TARGETS.invalidate_all();

    CLIENTS.invalidate_all();
}

/// 解析数据包
///
/// - 如果流量是从转发代理服务回来的流量, 则将源地址改为真正的目标地址
/// - 如果是访问WEB应用的流量, 则将目标地址改为本地HTTP转发代理地址
///    - 在Windows和Linux下, 需要将源地址改为同网段的虚拟地址
pub async fn parse_if_match_and_change_target_or_source(
    tunnel_ip: IpAddr,
    packet: &mut IpPacket,
) -> bool {
    // 只处理TCP协议
    if packet.next_protocol != 6 {
        return false;
    }

    let (src_port, dst_port) = packet.ports.unwrap();
    // 转发代理回来的流量
    // log::trace!(
    //     "src/{}:{}, dest/{}:{}",
    //     packet.src,
    //     src_port,
    //     packet.dest,
    //     dst_port
    // );
    let listening_port = TRACING_LISTEN_PORT.load(Ordering::Relaxed);
    if src_port == listening_port && tunnel_ip == packet.src {
        let Some(origin_address) = FORWARD_PROXY_TARGETS
            .get(&SocketAddr::new(packet.dest, dst_port))
            .await
        else {
            return false;
        };

        // log::trace!("origin: {}", origin_address);

        #[cfg(any(windows, target_os = "linux"))]
        {
            // 将虚拟目标地址还原成真实目标地址
            let virtual_dst_ip = match packet.dest {
                IpAddr::V4(ip) => {
                    let mut ip = ip.octets();
                    if ip[3] + 1 == 255 {
                        ip[3] += 1;
                    } else {
                        ip[3] -= 1;
                    }
                    IpAddr::V4(Ipv4Addr::from(ip))
                }
                IpAddr::V6(ip) => {
                    let mut ip = ip.octets();
                    if ip[15] + 1 == 255 {
                        ip[15] += 1;
                    } else {
                        ip[15] -= 1;
                    }
                    IpAddr::V6(Ipv6Addr::from(ip))
                }
            };
            packet.set_dest(virtual_dst_ip);
        }

        packet.set_src(origin_address.ip());
        packet.update_ip_checksum();
        packet.set_src_port(origin_address.port());
        packet.update_next_protocol_checksum();

        return true;
    }

    // 访问WEB应用的流量
    match get_http_scheme(packet.dest, dst_port).await {
        Some("http" | "https") => {
            // log::trace!("change target to: {}:{}", tunnel_ip, listening_port);

            #[cfg(any(windows, target_os = "linux"))]
            // 构建一个虚拟源地址, IP为源IP段最后一段+1, 端口为源端口
            let virtual_src_ip = match packet.src {
                IpAddr::V4(ip) => {
                    let mut ip = ip.octets();
                    if ip[3] + 1 == 0 {
                        ip[3] -= 1;
                    } else {
                        ip[3] += 1;
                    }
                    IpAddr::V4(Ipv4Addr::from(ip))
                }
                IpAddr::V6(ip) => {
                    let mut ip = ip.octets();
                    if ip[15] + 1 == 0 {
                        ip[15] -= 1;
                    } else {
                        ip[15] += 1;
                    }
                    IpAddr::V6(Ipv6Addr::from(ip))
                }
            };
            #[cfg(any(windows, target_os = "linux"))]
            packet.set_src(virtual_src_ip);
            #[cfg(target_os = "macos")]
            let virtual_src_ip = packet.src;

            FORWARD_PROXY_TARGETS
                .insert(
                    SocketAddr::new(virtual_src_ip, src_port),
                    SocketAddr::new(packet.dest, dst_port),
                )
                .await;

            packet.set_dest(tunnel_ip);
            packet.update_ip_checksum();
            packet.set_dest_port(listening_port);
            packet.update_next_protocol_checksum();

            true
        }
        _ => false,
    }
}
