[package]
edition = "2021"
name = "server-cli"
version = "0.0.0"

[build-dependencies]
built = { version = "0.7", features = ["git2"] }
time = { version = "0.3", features = ["formatting", "local-offset"] }

[dependencies]
clap = { version = "4.5.20", features = ["derive", "string"] }
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.132"
time = { version = "0.3", features = ["formatting", "macros"] }
version = { path = "../../version" }
