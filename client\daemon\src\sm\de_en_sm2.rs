use base64::prelude::*;

use libsm::{
    sm2::{
        encrypt::DecryptCtx,
        signature::{SigCtx, Signature},
    },
    sm3::hash::Sm3Hash,
};
use log::{error, info};
use num_bigint::BigUint;

use serde::{Deserialize, Serialize};

use super::cryptor_factory::PubkeyInfo;

const PUB_KEY:&str = "04645629ffb19130f9a8a5338c64148ee819fd273d9b69b5aba97ae25a9f917d10549c62e81c1c87a91067174fb1603dfa9113e526c85cded7a6fdd9c6bab9c5c5";
const TYPE_SDP: &str = "SDP";
#[cfg(windows)]
const TYPE_TFA: &str = "TFA";
// sm2私钥Hex== ：83b6d82072e97ad7ea4badf78a19e50f939010016a70656686b664960860b170
// sm2公钥Hex==：
// 04b9a1a8b20024bb1dd863ec866d19b90eae635340405beddf7ce91daf84c8a7a55c5da9e0bdb1480de6ce812ec1f8869226ca737bc99c27b4201feee8ed1ae8c2

// const PUB_KEY_SIGNATURE:&str =
// "04fc05c30ec46efa722aea1437ba646f0364d81220fcf5f74ad1e48ccfcaff1c3127843df5dee0ae84496066547d379e2e790bf2d16b945a32863de86fdd6b9ac5"
// ; const SECRET_KEY_SIGNATURE:&str =
// "48871990384963113685471154436724540366714011052322222004549110084141739700255";

const SECRET_KEY_HEX: &str = "83b6d82072e97ad7ea4badf78a19e50f939010016a70656686b664960860b170";
const PUB_KEY_HEX:&str = "04b9a1a8b20024bb1dd863ec866d19b90eae635340405beddf7ce91daf84c8a7a55c5da9e0bdb1480de6ce812ec1f8869226ca737bc99c27b4201feee8ed1ae8c2";

#[cfg(windows)]
const SUBKEY_CREDENTIAL:&str = "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\Credential Providers\\{E8881F18-2CCB-4251-9F88-FEB046AA2563}";

#[derive(Debug, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct TicketInfo {
    signature: String,
    ticket: String,
    device_id: String,
    tenant: String,
    username: String,
    r#type: String,
}

impl TicketInfo {
    #[cfg(windows)]
    pub async fn new_from_registry() -> Result<TicketInfo, String> {
        // let user_id = crate::registry::getregvalue(SUBKEY_CREDENTIAL, "CURRENTLOGONUSERID")
        //    .unwrap_or(String::from(""));
        let tenant =
            crate::registry::getregvalue(SUBKEY_CREDENTIAL, "tenantname").unwrap_or(String::new());
        let device_id = uniqueid::device_id().unwrap_or(String::new());
        let username = crate::logon_user::get_lastssousername();
        if username.clone().is_empty() {
            return Err(String::from("The username is empty"));
        }

        let is_local_signin = crate::logon_user::is_localuser_logon();
        info!("is_local_signin:{}", is_local_signin);
        if is_local_signin {
            let ticket = TicketInfo {
                signature: String::new(),
                ticket: String::new(),
                device_id,
                tenant,
                username: String::new(),
                r#type: TYPE_TFA.to_string(),
            };
            return Ok(ticket);
        }

        let json_value = serde_json::json!({
            "username":username.clone()
        });
        let json_str = json_value.to_string();
        println!("json_str:{}", &json_str);
        let pubkey_ret = crate::sm::cryptor_factory::fetch_pubkey_info(&tenant, &device_id).await;
        let encrypted_str = match pubkey_ret {
            Ok(pubkey) => {
                info!("data_transmit_type:{}", pubkey.data_transmit_type);
                if pubkey.data_transmit_type == 1 {
                    sm2_encrypt_base64key(
                        &pubkey.public_key_str.unwrap_or(String::new()),
                        &json_str,
                    )
                    .0
                } else {
                    sm2_encrypt(&json_str)
                }
            }
            Err(_err) => {
                error!("error in pubkey:{:?}", &_err);
                sm2_encrypt(&json_str)
            }
        };
        let signed_ret = sign(&encrypted_str, SECRET_KEY_HEX);
        let signatrue = match signed_ret {
            Ok(signature) => signature,
            Err(_err) => {
                let err_msg = format!("{}", _err);
                error!("{}", &err_msg);
                return Err(err_msg);
            }
        };
        let ticket = TicketInfo {
            signature: signatrue,
            ticket: encrypted_str,
            device_id,
            tenant,
            username,
            r#type: TYPE_TFA.to_string(),
        };
        Ok(ticket)
    }

    pub async fn new_from_userinfo(
        tenant: &str,
        username: &str,
        pubkey: &PubkeyInfo,
    ) -> Result<TicketInfo, String> {
        let json_value = serde_json::json!({
            "username":username
        });
        let device_id = uniqueid::device_id().unwrap_or(String::new());
        let json_str = json_value.to_string();
        println!("json_str:{}", &json_str);

        // let pubkey_ret = crate::sm::cryptor_factory::fetch_pubkey_info(&tenant,&device_id).await;
        // let encrypted_str = match pubkey_ret {
        //     Ok(pubkey) => {
        //         info!("data_transmit_type:{}",pubkey.data_transmit_type);
        //         if pubkey.data_transmit_type == 1 {
        //             sm2_encrypt_base64key(&pubkey.public_key_str.unwrap_or(String::new()),
        // &json_str).0         }else{
        //             sm2_encrypt(&json_str)
        //         }
        //     },
        //     Err(_err)=>{
        //         error!("error in pubkey:{:?}",&_err);
        //         sm2_encrypt(&json_str)
        //     }
        // };
        info!("data_transmit_type:{}", pubkey.data_transmit_type);
        println!("data_transmit_type:{}", pubkey.data_transmit_type);
        let encrypted_str = if pubkey.data_transmit_type == 1 {
            let public_key_str = pubkey.public_key_str.clone().unwrap_or(String::new());
            sm2_encrypt_base64key(&public_key_str, &json_str).0
        } else {
            sm2_encrypt(&json_str)
        };

        let signed_ret = sign(&encrypted_str, SECRET_KEY_HEX);
        let signatrue = match signed_ret {
            Ok(signature) => signature,
            Err(_err) => {
                let err_msg = format!("{}", _err);
                error!("{}", &err_msg);
                return Err(err_msg);
            }
        };
        let ticket = TicketInfo {
            signature: signatrue,
            ticket: encrypted_str,
            device_id,
            tenant: tenant.to_string(),
            username: username.to_string(),
            r#type: TYPE_SDP.to_string(),
        };
        Ok(ticket)
    }
    pub fn to_json(&self) -> String {
        match serde_json::to_string(self) {
            Ok(json) => json,
            Err(err) => {
                error!("Failed to serialize UserInfo to JSON: {:?}", err);
                String::new()
            }
        }
    }
}

pub fn convert_pubkey() -> Vec<u8> {
    // let mut pubkey_vec = Vec::new();
    // let zero:u8 = b'0';
    // for i in PUB_KEY.as_bytes() {
    //     let value = *i-zero;
    //     pubkey_vec.push(value);
    // }
    // pubkey_vec
    let public_key_bytes = hex::decode(PUB_KEY).expect("Invalid hex for public key");
    public_key_bytes
}

pub fn sm2_encrypt(message: &str) -> String {
    let content_base64 = BASE64_STANDARD.encode(message);
    let klen = content_base64.len();
    println!("content_base64 is {},klen is {}", &content_base64, klen);
    let public_key_bytes = convert_pubkey();
    println!("public_key_bytes length:{}", klen);
    let ctx = SigCtx::new();
    let pubkey_result = ctx.load_pubkey(&public_key_bytes);
    let public_key = match pubkey_result {
        Ok(pkey) => pkey,
        Err(_err) => {
            println!("Error in getting public key {:?}", &_err);
            return String::new();
        }
    };

    println!("EncryptCtx::new with public_key");
    let encrypt_ctx = libsm::sm2::encrypt::EncryptCtx::new(klen, public_key);
    // Encrypt the message
    println!("encrypt_ctx.encrypt content_base64 {}", &content_base64);
    let encrypted_message = encrypt_ctx
        .encrypt(content_base64.as_bytes())
        .expect("Encryption failed");
    println!("encrypted_message is done");
    // Convert the encrypted message to a hex string or another format as needed
    let encrypted_hex = hex::encode(encrypted_message);
    encrypted_hex
}

pub fn sm2_encrypt_base64key(base64_key: &str, message: &str) -> (String, usize) {
    let content_base64 = BASE64_STANDARD.encode(message);
    let klen = content_base64.len();
    println!("content_base64 is {},klen is {}", &content_base64, klen);
    let public_key_bytes_ret = crate::sm::keymgr::get_public_key(base64_key);
    let public_key_bytes_ret = match public_key_bytes_ret {
        Ok(public_key_bytes) => hex::decode(public_key_bytes),
        Err(_err) => {
            println!("error is {:?}", &_err);
            return (String::new(), klen);
        }
    };

    let public_key_bytes = match public_key_bytes_ret {
        Ok(public_key_bytes) => public_key_bytes,
        Err(_err) => {
            println!("error is {:?} in hex", &_err);
            return (String::new(), klen);
        }
    };

    println!(
        "public_key_bytes length in base64:{},bytes:{:?}",
        public_key_bytes.len(),
        public_key_bytes
    );
    let ctx = SigCtx::new();
    let pubkey_result = ctx.load_pubkey(&public_key_bytes);
    let public_key = match pubkey_result {
        Ok(pkey) => pkey,
        Err(_err) => {
            println!("Error in getting public key {:?}", &_err);
            return (String::new(), klen);
        }
    };

    println!("EncryptCtx::new with public_key");
    let encrypt_ctx = libsm::sm2::encrypt::EncryptCtx::new(klen, public_key);
    // Encrypt the message
    println!("encrypt_ctx.encrypt content_base64 {}", &content_base64);
    let encrypted_message = encrypt_ctx
        .encrypt(content_base64.as_bytes())
        .expect("Encryption failed");
    println!("encrypted_message is done");
    // Convert the encrypted message to a hex string or another format as needed
    let encrypted_hex = hex::encode(encrypted_message);
    (encrypted_hex, klen)
}

pub fn sm2_encrypt2(message: &str, pubkey: &str) -> (String, usize) {
    let content_base64 = BASE64_STANDARD.encode(message);
    let klen = content_base64.len();
    println!("content_base64 is {},klen is {}", &content_base64, klen);
    println!("public_key_bytes length:{}", klen);
    let ctx = SigCtx::new();
    let public_key_bytes = hex::decode(pubkey).unwrap();
    let pubkey_result = ctx.load_pubkey(&public_key_bytes);
    let public_key = match pubkey_result {
        Ok(pkey) => pkey,
        Err(_err) => {
            let err_msg = format!("Error in getting public key {:?}", &_err);
            return (err_msg, klen);
        }
    };

    println!("EncryptCtx::new with public_key");
    let encrypt_ctx = libsm::sm2::encrypt::EncryptCtx::new(klen, public_key);
    // Encrypt the message
    println!("encrypt_ctx.encrypt content_base64 {}", &content_base64);
    let encrypted_message = encrypt_ctx
        .encrypt(content_base64.as_bytes())
        .expect("Encryption failed");
    println!("encrypted_message is done");
    // Convert the encrypted message to a hex string or another format as needed
    let encrypted_hex = hex::encode(encrypted_message);
    (encrypted_hex, klen)
}

pub fn sm2_decrypt(encrypted_content: &str, sk: &str, klen: usize) -> Result<String, String> {
    println!("sm2_decrypt:{},{}", encrypted_content, sk);
    let sk_num_ret = BigUint::parse_bytes(sk.as_bytes(), 16);
    let sk_num = match sk_num_ret {
        Some(sk_num) => sk_num,
        None => {
            return Err(String::from("sk is empty"));
        }
    };
    let string_hex_ret = hex::decode(encrypted_content);
    let encrypted_message = match string_hex_ret {
        Ok(string_hex) => string_hex,
        Err(_err) => {
            let err_msg = format!("Error in hex decode {:?}", &_err);
            return Err(err_msg);
        }
    };
    // let klen = encrypted_message.len();
    println!("klen is {}", klen);
    let decrypt_ctx = DecryptCtx::new(klen, sk_num);
    println!("start decrypt ");
    match decrypt_ctx.decrypt(&encrypted_message) {
        Ok(message) => {
            let base64_decode_bytes = match BASE64_STANDARD.decode(message) {
                Ok(base64_decode) => base64_decode,
                Err(_err) => {
                    let err_msg = format!("Error in base64 decode {:?}", &_err);
                    return Err(err_msg);
                }
            };
            let message: String = String::from_utf8(base64_decode_bytes).expect("字符串解码失败");
            Ok(message)
        }
        Err(_err) => {
            let err_msg = format!("Error in decrypt {:?}", &_err);
            Err(err_msg)
        }
    }
}

pub fn generate_keypair() -> Result<(String, String), String> {
    //(publickey,secretkey)
    let ctx = SigCtx::new();
    let result = ctx.new_keypair();
    match result {
        Ok((pk, sk)) => {
            // let radix_num = sk.to_str_radix(16);
            let result_sk = format!("{:x}", sk);
            let pubkey_serialized = ctx.serialize_pubkey(&pk, false).unwrap();
            let hex_str = hex::encode(pubkey_serialized);
            Ok((hex_str, result_sk))
        }
        Err(_err) => {
            let err_msg = format!("error in new keypair {:?}", &_err);
            return Err(err_msg);
        }
    }
}

pub fn get_keys() -> Result<(String, String), String> {
    Ok((PUB_KEY_HEX.to_string(), SECRET_KEY_HEX.to_string()))
}

pub fn generate_keypair_base64() -> Result<(String, String), String> {
    //(publickey,secretkey)
    let ctx = SigCtx::new();
    let result = ctx.new_keypair();
    match result {
        Ok((pk, sk)) => {
            // let result_sk = format!("{}",sk);
            let pubkey_serialized = ctx.serialize_pubkey(&pk, false).unwrap();
            // let hex_str = hex::encode(pubkey_serialized);
            let base_64_pubkey = BASE64_STANDARD.encode(pubkey_serialized);
            let secret_key = sk.to_bytes_be();
            let base_64_secretkey = BASE64_STANDARD.encode(secret_key);
            Ok((base_64_pubkey, base_64_secretkey))
        }
        Err(_err) => {
            let err_msg = format!("error in new keypair {:?}", &_err);
            return Err(err_msg);
        }
    }
}

// https://github.com/luoffei/libsm/blob/master/docs/sm2.md

pub fn sign(msg: &str, sk: &str) -> Result<String, String> {
    let ctx = SigCtx::new();

    let sk_num_ret = BigUint::parse_bytes(sk.as_bytes(), 16);
    let sk_num = match sk_num_ret {
        Some(sk_num) => sk_num,
        None => {
            return Err(String::from("sk is empty"));
        }
    };
    // let public_key_bytes = match hex::decode(pk){
    //         Ok(public_key_bytes) => public_key_bytes,
    //         Err(_err) =>{
    //             let err_msg = format!("Error in hex decode {:?}",&_err);
    //             return Err(err_msg);
    //         }
    // };

    // //println!("public_key_bytes size is {},public_key_bytes is
    // {:?}",public_key_bytes.len(),&public_key_bytes); let pubkey_result =
    // ctx.load_pubkey(&public_key_bytes); let pubkey = match pubkey_result {
    //     Ok(pubkey) => pubkey,
    //     Err(_err) => {
    //         let err_msg = format!("load pubkey error:{:?}",_err);
    //         return Err(err_msg);
    //     }
    // };
    let mut hash = Sm3Hash::new(msg.as_bytes());
    let digest: [u8; 32] = hash.get_hash();
    let signature_ret = ctx.sign_raw(&digest, &sk_num);
    let signature = match signature_ret {
        Ok(signature) => signature,
        Err(_err) => {
            let err_msg = format!("error is {:?}", &_err);
            return Err(err_msg);
        }
    };
    let decode_signature: Vec<u8> = signature.der_encode();
    let signature_hex = hex::encode(decode_signature);
    Ok(format!("{}", signature_hex))
}

pub fn verify(msg: &str, pk: &str, signature: &str) -> Result<bool, String> {
    let ctx = SigCtx::new();
    let public_key_bytes = match hex::decode(pk) {
        Ok(public_key_bytes) => public_key_bytes,
        Err(_err) => {
            let err_msg = format!("Error in hex decode {:?}", &_err);
            return Err(err_msg);
        }
    };
    let pubkey_result = ctx.load_pubkey(&public_key_bytes);
    let pubkey = match pubkey_result {
        Ok(pubkey) => pubkey,
        Err(_err) => {
            let err_msg = format!("{:?}", _err);
            return Err(err_msg);
        }
    };

    let signature_hex_decoded_ret = hex::decode(signature);
    let signature_hex_decoded = match signature_hex_decoded_ret {
        Ok(signature_hex_decoded) => signature_hex_decoded,
        Err(_err) => {
            let err_msg = format!("Error in hex decode signature {:?}", &_err);
            println!("{}", &err_msg);
            return Err(err_msg);
        }
    };

    let signature_ret = Signature::der_decode(&signature_hex_decoded);
    let signature = match signature_ret {
        Ok(signature) => signature,
        Err(_err) => {
            let err_msg = format!("Error in der_decode is {:?}", &_err);
            println!("{}", &err_msg);
            return Err(err_msg);
        }
    };
    // sm3
    let verified_ret = ctx.verify(msg.as_bytes(), &pubkey, &signature);
    match verified_ret {
        Ok(verified_result) => Ok(verified_result),
        Err(_err) => {
            let err_msg = format!("Error in verification {:?}", &_err);
            Err(err_msg)
        }
    }
}

//     Signature::

//     ctx.verify(msg.as_bytes(),&pubkey, sig);
// }

#[cfg(test)]
mod test_sm2 {
    use super::sm2_encrypt;

    /// cargo test -- test_sm2_encrypt --nocapture
    #[test]
    fn test_sm2_encrypt() {
        let str_encrypted = sm2_encrypt("Changeme123!@#");
        println!("encrypted str:{}", str_encrypted);
    }

    /// cargo test -- test_bytes --nocapture
    #[test]
    fn test_bytes() {
        let bytes = super::convert_pubkey();
        println!("size of pubkey:{},bytes {:?}", bytes.len(), bytes);
    }

    /// cargo test -- test_generate_keypair --nocapture
    #[test]
    fn test_generate_keypair() {
        let result = super::generate_keypair();
        match result {
            Ok(r) => {
                println!("pubkey is {},sk is {}", r.0, r.1);
            }
            Err(_err) => {
                println!("error is {}", _err);
            }
        }
    }

    /// cargo test -- test_sign_verify --nocapture
    #[test]
    fn test_sign_verify() {
        let msg = "{\"userId\":\"\",\"tenant\":\"DEFAULT\",\"deviceId\":\"00d06fc1c56a06c22d5fa3b48f592abb\"}";
        //{content:"",signautre:""}
        let result = super::generate_keypair();
        match result {
            Ok(r) => {
                println!("pubkey is {},sk is {}", r.0, r.1);
                let sign_ret = super::sign(msg, &r.1);
                match sign_ret {
                    Ok(signature) => {
                        println!("signature is {}", signature);

                        let verify_ret = super::verify(msg, &r.0, &signature);
                        match verify_ret {
                            Ok(verified_ret) => {
                                println!("verified ret:{}", verified_ret);
                                assert!(verified_ret);
                                let message= "{\"userId\":\"45646498496464\",\"tenant\":\"DEFAULT\",\"deviceId\":\"00d06fc1c56a06c22d5fa3b48f592abb\"}";
                                let encrypted_message = super::sm2_encrypt2(message, &r.0);
                                println!("encrypted:{}", &encrypted_message.0);
                                let decrypted_message_ret = super::sm2_decrypt(
                                    &encrypted_message.0,
                                    &r.1,
                                    encrypted_message.1,
                                );
                                match decrypted_message_ret {
                                    Ok(decrypted_message) => {
                                        println!("decrypted_message is {}", &decrypted_message);
                                        assert!(decrypted_message.eq(message));
                                    }
                                    Err(_err) => {
                                        println!("{}", _err);
                                        assert!(false);
                                    }
                                };
                            }
                            Err(_err) => {
                                println!("{}", _err);
                                assert!(false);
                            }
                        }
                    }
                    Err(_err) => {
                        println!("sign err is {}", _err);
                    }
                };
            }
            Err(_err) => {
                println!("error is {}", _err);
            }
        }
    }

    //  /**
    // *
    // cargo test -- test_newkeys_verify --nocapture
    // *
    // */
    // #[test]
    // fn test_newkeys_verify(){
    //     let msg =
    // "{\"userId\":\"\",\"tenant\":\"DEFAULT\",\"deviceId\":\"00d06fc1c56a06c22d5fa3b48f592abb\"}";
    //     //{content:"",signautre:""}
    //     let result = super::get_keys();
    //     match result {
    //         Ok(r)=>{
    //             println!("pubkey is {},sk is {}",r.0,r.1);
    //         },
    //         Err(_err)=>{
    //             println!("error is {}",_err);
    //         }
    //     }
    // }

    //           /**
    // *
    // cargo test -- test_signature --nocapture
    // *
    // */
    // #[test]
    // fn test_signature(){
    //     let msg =
    // "0438bd4f7e498d3b25284c4f8b641ba1e4597352b69751af372f3f7b63652c0e1b041aa3443f41b4dc61dff5e14ef1d352cd23c366f57899c02655e1ca6f4a743684f3d2e032b6dc052411082b28a498fd94ccd35bb086b56e7ff1f75cb3142bf62348d127033c30156a6c3790e4f94fed8d8c14f639a8727ba3c2c50ee6ad4888fdfe7e101352e9be650b228908d30742baff8bf2b21d68f6af3e96d58698a1d3d6ac8d8312f1273969af919158a69db466d3695999c0c209df364c364eb53bbc74069f2e8b1e997bbfdbc05523a96693e0071876da4b1697121b00cc"
    // ;     let sign_ret = super::sign(msg,SECRET_KEY_SIGNATURE ,PUB_KEY_SIGNATURE);
    //     match sign_ret {
    //         Ok(signature)=>{
    //             println!("signature:{}",signature);
    //             assert!(true);
    //         },
    //         Err(_err) => {
    //             println!("{}",_err);
    //             assert!(false);
    //         }
    //     }
    // }

    // cargo test -- test_generate_keypair_base64 --nocapture
    #[test]
    fn test_generate_keypair_base64() {
        let result = super::generate_keypair_base64();
        match result {
            Ok(r) => {
                println!("pubkey:{}", r.0);
                println!("secretkey:{}", r.1);
            }
            Err(_err) => {
                println!("{}", _err);
            }
        }
    }

    // cargo test -- test_new_from_registry --nocapture
    #[tokio::test]
    async fn test_new_from_registry() {
        let ret = super::TicketInfo::new_from_registry().await;
        println!("{:?}", &ret);
        assert!(ret.is_ok());
    }
}
