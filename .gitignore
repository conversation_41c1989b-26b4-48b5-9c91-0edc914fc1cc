# Generated by Cargo
# will have compiled files and executables
debug/
target/
build/

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
# Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

.idea
*.iml
logs/
.lh/
*.out
.history/
node_modules
.fleet
~
*.exe

.VSCodeCounter
.vscode/settings.json
client/src-tauri/bin/oneidcore-aarch64-apple-darwin
client/src-tauri/bin/oneidcore-x86_64-apple-darwin
client/src-tauri/bin/oneidcore-x86_64-pc-windows-msvc.exe
client/src-tauri/bin/oneidcore-x86_64-unknown-linux-gnu
client/src-tauri/bin/config-aarch64-apple-darwin
client/src-tauri/bin/config-x86_64-apple-darwin
client/src-tauri/bin/config-x86_64-pc-windows-msvc.exe
client/src-tauri/bin/oneidconfig-x86_64-unknown-linux-gnu
client/src-tauri/src/bin/
.DS_Store