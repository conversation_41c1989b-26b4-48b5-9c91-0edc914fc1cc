use actix_web::{web::Data, App, HttpResponse, HttpServer};
#[cfg(feature = "iam")]
use once_cell::sync::Lazy;
#[cfg(feature = "iam")]
use rust_embed::RustEmbed;
use serde_json::json;
use std::{convert::Into, net::TcpListener};
use tauri::{<PERSON>pp<PERSON><PERSON><PERSON>, Manager, Wry};
#[cfg(feature = "iam")]
use tera::{Context, Tera};

#[cfg(feature = "iam")]
const INDEX_TEMPLATE: &str = include_str!("../../templates/index.html.tera");
#[cfg(feature = "iam")]
const INDEX_TEMPLATE_NAME: &str = "index.html.tera";

#[cfg(feature = "iam")]
static TEMPLATES: Lazy<Tera> = Lazy::new(|| {
    let mut tera = Tera::default();
    tera.add_raw_template(INDEX_TEMPLATE_NAME, INDEX_TEMPLATE)
        .unwrap_or_else(|err| {
            println!("Parsing error(s): {}", err);
            ::std::process::exit(1);
        });
    tera
});

#[cfg(feature = "iam")]
#[derive(RustEmbed)]
#[folder = "static"]
pub struct Asset;

use config::AppConfig;
use tokio::sync::{Mutex, RwLock};

use crate::state::AppState;

#[cfg(windows)]
pub struct AppPort(pub u16);

pub async fn start(app_handle: AppHandle<Wry>) {
    #[cfg(windows)]
    let (listener, port) = {
        let listener = TcpListener::bind("127.0.0.1:0").unwrap();
        let socket_addr = listener.local_addr().unwrap();
        let local_port = socket_addr.port();

        // 缓存到应用中
        app_handle.manage(AppPort(local_port));

        // 将端口存储到注册表中
        let hklm = winreg::RegKey::predef(winreg::enums::HKEY_LOCAL_MACHINE);
        let software = hklm.open_subkey("SOFTWARE").unwrap();
        let product = match software
            .open_subkey_with_flags(paths::PRODUCT_NAME, winreg::enums::KEY_SET_VALUE)
        {
            Ok(product) => product,
            Err(error) => {
                if error.kind() == std::io::ErrorKind::NotFound {
                    match software.create_subkey(paths::PRODUCT_NAME) {
                        Ok((product, _)) => product,
                        Err(error) => {
                            log::error!(target: "app", "Failed to create registry key `{}`. {error}", paths::PRODUCT_NAME);
                            std::process::exit(1);
                        }
                    }
                } else {
                    log::error!(target: "app", "Failed to read registry key `{}`. {error}", paths::PRODUCT_NAME);
                    std::process::exit(1);
                }
            }
        };
        if let Err(error) = product.set_value("port", &(local_port as u32)) {
            log::error!(target: "app", "Failed to set registry value. {error}");
        }

        (listener, local_port)
    };

    #[cfg(not(windows))]
    let (listener, port) = {
        let listener = TcpListener::bind("127.0.0.1:50031").unwrap();
        (listener, 50031u16)
    };

    log::info!(target: "app", "Start HTTP service on port {}", port);

    tokio::spawn(async move {
        if let Err(error) = HttpServer::new(move || {
            let app = App::new().app_data(Data::new(app_handle.clone()));
            #[cfg(feature = "iam")]
            let app = app.service(single_sign_on).service(dist);

            #[cfg(target_os = "macos")]
            let app = app.service(wifi);

            app.service(screen_saver).service(web_login_ticket)
        })
        .listen(listener)
        .unwrap()
        .run()
        .await
        {
            log::error!(target: "app", "{error}");
        }
    });
}

#[cfg(feature = "iam")]
fn handle_embedded_file(path: &str) -> HttpResponse {
    match Asset::get(path) {
        Some(content) => HttpResponse::Ok()
            .content_type(mime_guess::from_path(path).first_or_octet_stream().as_ref())
            .body(content.data.into_owned()),
        None => HttpResponse::NotFound().finish(),
    }
}

#[cfg(feature = "iam")]
#[actix_web::get("/static/{_:.*}")]
async fn dist(path: actix_web::web::Path<String>) -> HttpResponse {
    handle_embedded_file(path.as_str())
}

#[cfg(feature = "iam")]
#[actix_web::get("/")]
async fn single_sign_on(app_handle: Data<AppHandle<Wry>>) -> HttpResponse {
    let app_state = app_handle.state::<RwLock<AppState>>();
    let mut state = app_state.write().await;
    let sso_result = state.sso_result.take();
    drop(state);
    if let Some(sso_result) = sso_result {
        let mut context = Context::new();
        context.insert("result", &sso_result);

        match TEMPLATES.render(INDEX_TEMPLATE_NAME, &context) {
            Ok(html) => HttpResponse::Ok().content_type("text/html").body(html),
            Err(error) => HttpResponse::InternalServerError().body(format!("Error: {error}")),
        }
    } else {
        HttpResponse::NotFound().finish()
    }
}

#[actix_web::get("/screen_saver")]
async fn screen_saver() -> HttpResponse {
    HttpResponse::Ok().json(crate::environment::screen_saver::screen_saver_active())
}

#[cfg(target_os = "macos")]
#[actix_web::get("/wifi")]
async fn wifi() -> HttpResponse {
    HttpResponse::Ok().json(crate::environment::wifi::network_ssid())
}

#[actix_web::get("/web_login_ticket")]
async fn web_login_ticket(app_handle: Data<AppHandle<Wry>>) -> HttpResponse {
    let app_config = app_handle.state::<Mutex<AppConfig>>();
    let app_config_guard = app_config.lock().await;
    let mut secret = app_config_guard.secret_key.clone().unwrap_or_default();
    drop(app_config_guard);

    let app_state = app_handle.state::<RwLock<AppState>>();
    let app_state = app_state.read().await;

    let Some(user) = &app_state.user else {
        return HttpResponse::Unauthorized().finish();
    };
    let result = json!({
        "username": user.username,
        "tenant": user.unit,
    });
    drop(app_state);
    json_patch::merge(&mut secret, &result);
    HttpResponse::Ok().json(result)
}
