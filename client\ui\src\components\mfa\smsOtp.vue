<template>
  <div class="mfa-content" :class="type ? 'sms_login' : 'sms_auth'">
    <div class="auth-code-content">
      <div v-if="type">
        <div class="inputItem">
          <div class="inputContent">
            <el-input
              ref="username"
              v-model="username"
              placeholder="请输入手机号"
            />
          </div>
        </div>
      </div>
      <div class="otp-item">
        <el-input
          v-model="smsCode"
          placeholder="请输入验证码"
          :class="type === 'login' ? 'auth-input' : 'auth-code-input'"
          maxlength="6"
        >
          <template #append>
            <el-button
              :disabled="disabled"
              class="send-code-button"
              size="small"
              style="font-size: 12px"
              @click="getSMSCode"
              :loading="btnLoading"
            >
              {{ sendCode }}
            </el-button>
          </template>
        </el-input>
      </div>

      <el-button
        ref="confirmBtn"
        type="primary"
        :class="{
          loginBtn: type === 'login',
          'mfa-confirm-btn': type !== 'login',
        }"
        :disabled="
          (type === 'login' && !username) ||
          !requestId ||
          !smsCode ||
          subbmitting
        "
        @click="submitAuth"
      >
        {{ type === "login" ? "登录" : "确定" }}
      </el-button>
      <!-- <span v-if="type" class="loginBtn_title">登录即默认接受 <strong class="str">用户协议</strong></span> -->
      <userAgreement v-if="type" />
    </div>
  </div>
</template>

<script>
import { getSMSCode, verifySMSCode, cancelAuth } from "@/api/service";
import userAgreement from "../../views/userAgreement.vue";

export default {
  name: "SMSOTP",
  components: {
    userAgreement,
  },
  props: {
    participantGroupId: {
      type: String,
      default: "",
    },
    userId: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    name: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      requestId: "",
      smsCode: "",
      sendCode: "获取验证码",
      countdown: undefined,
      disabled: false,
      defaultTime: 60,
      reg: /^\d+$/,
      username: "",
      btnLoading: false,
      subbmitting: false,
    };
  },
  created() {
    // this.getRequestId();
  },
  methods: {
    getSMSCode() {
      if (!this.username && this.type === "login") {
        this.alert.error("请输入手机号");
        return;
      }
      this.btnLoading = true;
      let data = {
        participantGroupId: this.participantGroupId,
        participantTypes: this.type === "login" ? "04" : "01",
        participantKeyword: this.userId || this.username,
      };

      getSMSCode(data)
        .then((result) => {
          this.requestId = result.data.requestId;
          this.$emit("requestId", this.requestId);
          this.alert.success(
            "短信已发送至手机" + result.data.phone + "，请注意查收。"
          );
          this.countdown = setInterval(this.timer, 1000);
        })
        .catch((error) => {
          this.btnLoading = false;
          this.$emit("childEventCancel", this.requestId);
          this.$emit("generateRequestIdFailCallbackFunc", error);
        });
    },
    timer() {
      this.btnLoading = false;
      this.disabled = true;
      this.sendCode =
        this.type === "login"
          ? "重新获取(" + this.defaultTime + ")"
          : this.defaultTime + "s";
      this.defaultTime--;
      if (this.defaultTime < 0) {
        clearInterval(this.countdown);
        this.disabled = false;
        this.sendCode = "重新获取";
        this.defaultTime = 60;
      }
    },
    /**
     * 提交认证
     */
    async submitAuth() {
      try {
        this.subbmitting = true;
        await this.Complete();
      } finally {
        this.subbmitting = false;
      }
    },
    Complete() {
      if (!this.username && this.type === "login") {
        this.alert.error("请输入手机号码");
        return;
      }

      if (!this.smsCode) {
        this.alert.error("请输入短信验证码");
        return;
      }
      if (this.smsCode && !this.reg.test(this.smsCode)) {
        this.alert.error("验证码格式错误");
        return;
      }
      if (!this.requestId) {
        this.alert.error("请发送短信验证码");
        return;
      }
      return verifySMSCode({ requestId: this.requestId, code: this.smsCode })
        .then(() => {
          this.$emit("mfaCallbackFunc", {
            requestId: this.requestId,
            type: this.smsCode,
          });
        })
        .catch((err) => {
          let msg = JSON.parse(err.message);
          if (
            msg.data.messageKey === "MFA.MFA.AUTH.CANCELED" ||
            msg.data.messageKey === "MFA.MFA.AUTH.ALREADY"
          ) {
            this.alert.error("认证已失效，请重新发送验证码");
            clearInterval(this.countdown);
            this.disabled = false;
            this.sendCode = "重新获取";
            this.defaultTime = 60;
          }
        });
    },
    cancel() {
      if (this.requestId) {
        let requestId = this.requestId;
        this.requestId = "";
        cancelAuth(requestId);
      }
    },
  },
  watch: {
    username(newValue, oldValue) {
      console.log(newValue);
      if (newValue) {
        this.smsCode = "";
      }
    },
  },
};
</script>

<style scoped lang="less">
// // #if [platform=macos]
// .sms_login {
//   margin: 56px 0 43px !important;
// }
// // #else
// .sms_login {
//   margin: 54px 0 42px !important;
// }
// // #endif

.sms_auth {
  margin-top: 61px;
  padding-bottom: 61px;
}

// .mfa-content{
//     margin: 0 !important;
// }
.auth-code-content {
  width: 100%;
  margin: auto;
  text-align: center;

  .auth-code-input {
    border: 1px solid #f9780c;
    width: 60%;
    margin-bottom: 30px;
    height: 40px;
    border-radius: 30px;
    padding-left: 20px;
  }

  .auth-input {
    width: 100%;
    margin-bottom: 30px;
    height: 46px;
    padding-left: 16px;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #d1d3db;
    &:hover {
      border-bottom: 1px solid #f9780c;
    }
  }

  .inputItem {
    margin-bottom: 30px;
    text-indent: 0.6em;
    position: relative;
    margin-top: 2rem;
    border-bottom: 1px solid #d1d3db;
    &:hover {
      border-bottom: 1px solid #f9780c;
    }
  }

  .login_icon {
    color: #bfbfbf;
    font-size: 22px;
  }

  .inputContent {
    padding: 0.4rem;
    width: 240px;
    border: 0;
  }

  :deep(.el-input__wrapper) {
    border: 0 !important;
    padding: 1px;
    // border-radius: 30px;
  }

  // .boder{
  //     border-bottom:1px solid #ddd ;
  // }
  // .mfa-content-footer{
  //     background:#f59547;
  //     color: #fff;
  //     font-size: 14px;
  // }
  :deep(.el-input-group__append) {
    border: 0;
    // background: #fbece1;
  }

  .send-code-button,
  .send-code-button:disabled,
  .send-code-button:hover {
    border: none;
    background-color: unset;
  }
}

.otp-item {
  position: relative;
}

.icon_password {
  position: absolute;
  left: 2px;
  top: 7px;
  z-index: 999999;
}
</style>
