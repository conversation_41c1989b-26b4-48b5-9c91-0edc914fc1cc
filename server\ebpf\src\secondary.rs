#![no_std]
#![no_main]

#[allow(non_upper_case_globals)]
#[allow(non_snake_case)]
#[allow(non_camel_case_types)]
#[allow(dead_code)]
mod protocols;

#[allow(non_upper_case_globals)]
#[allow(non_snake_case)]
#[allow(non_camel_case_types)]
#[allow(dead_code)]
mod vmlinux;

mod options;

use core::mem;

use aya_ebpf::{
    bindings::xdp_action,
    macros::{map, xdp},
    maps::{Array, HashMap, XskMap},
    programs::XdpContext,
};
use options::*;
use protocols::{IPPROTO_ICMP, IPPROTO_TCP, IPPROTO_UDP};
use share::Addr;
use vmlinux::{arphdr, ethhdr, icmphdr, iphdr, tcphdr, udphdr};

/// 本机对外开放端口
#[map(name = "PUB_PORTS")]
static mut PUB_PORTS: Array<u32> = Array::with_max_entries(10, 0);

/// 白名单
#[map(name = "WHITELIST")]
static mut WHITELIST: HashMap<Addr, u8> = HashMap::with_max_entries(WHITE_LIST_SIZE, 0);

/// 将包转到用户程序处理
#[map(name = "SOCKETS")]
static mut PACKETS: XskMap = XskMap::with_max_entries(1024, 0);

/// NAT表
#[map(name = "NAT")]
static mut NAT_TABLE: HashMap<Addr, u8> = HashMap::with_max_entries(CONCURRENCY_ACCESS, 0);

/// VRRP 组播地址. **********
const VRRP_BROADCAST: u32 = 3758096402;

/// 本机IP
#[no_mangle]
static LOCAL_IP: u32 = 0;

/// VIP
#[no_mangle]
static VIP: u32 = 0;

#[xdp]
pub fn sdp_s(ctx: XdpContext) -> u32 {
    let local_ip = unsafe { core::ptr::read_volatile(&LOCAL_IP) };

    let vip = unsafe { core::ptr::read_volatile(&VIP) };

    try_sdp_xdp(ctx, local_ip, vip).unwrap_or(xdp_action::XDP_DROP)
}

fn try_sdp_xdp(ctx: XdpContext, local_ip: u32, vip: u32) -> Result<u32, ()> {
    // info!(&ctx, "received a packet");

    let ethhdr: *const ethhdr = unsafe { ptr_at(&ctx, 0)? };
    let ether_type = u16::from_be(unsafe { *ethhdr }.h_proto);
    match ether_type {
        protocols::ETH_P_IP => {
            let ipv4hdr: *const iphdr = unsafe { ptr_at(&ctx, 14)? };
            let dst_ip = u32::from_be(unsafe { (*ipv4hdr).__bindgen_anon_1.addrs.daddr });

            // 只允许发给本机的流量通过
            if dst_ip != local_ip && dst_ip != vip && dst_ip != VRRP_BROADCAST {
                return Ok(xdp_action::XDP_DROP);
            }

            return unsafe { handle_ipv4_packet(ctx, ipv4hdr, vip) };
        }
        protocols::ETH_P_ARP => {
            let arphdr: *const arphdr = unsafe { ptr_at(&ctx, 14)? };

            if u16::from_be(unsafe { *arphdr }.ar_pro) == protocols::ETH_P_IP as u16 {
                let dst_ip: *const u32 = unsafe { ptr_at(&ctx, 14 + 24)? };
                let dst_ip = u32::from_be(unsafe { *dst_ip });
                if dst_ip == local_ip || dst_ip == vip {
                    return Ok(xdp_action::XDP_PASS);
                }
            }
        }
        _ => {}
    }

    Ok(xdp_action::XDP_DROP)
}

fn is_pub_port(dst_port: u16) -> bool {
    for index in 0..10 {
        match unsafe { PUB_PORTS.get(index) } {
            Some(&port) => {
                let start = (port >> 16) as u16;
                if start == 0 {
                    if port as u16 == dst_port {
                        return true;
                    }
                } else {
                    let end = (port & 0xffff) as u16;
                    if dst_port >= start && dst_port <= end {
                        return true;
                    }
                }
            }
            None => return false,
        }
    }
    false
}

unsafe fn in_whitelist(src_ip: u32, src_port: u16) -> bool {
    if WHITELIST.get(&Addr::new(src_ip, 0)).is_some() {
        return true;
    }

    if WHITELIST.get(&Addr::new(src_ip, src_port)).is_some() {
        return true;
    }
    false
}

#[inline(always)]
unsafe fn ptr_at<T>(ctx: &XdpContext, offset: usize) -> Result<*const T, ()> {
    let start = ctx.data();
    let end = ctx.data_end();
    let len = mem::size_of::<T>();

    if start + offset + len > end {
        return Err(());
    }

    let ptr = (start + offset) as *const T;
    Ok(&*ptr)
}

#[panic_handler]
fn panic(_info: &core::panic::PanicInfo) -> ! {
    unsafe { core::hint::unreachable_unchecked() }
}

#[inline]
unsafe fn handle_ipv4_packet(ctx: XdpContext, ipv4hdr: *const iphdr, vip: u32) -> Result<u32, ()> {
    let protocol = unsafe { *ipv4hdr }.protocol;
    let src_ip = u32::from_be(unsafe { (*ipv4hdr).__bindgen_anon_1.addrs.saddr });
    let addr = 14 + (unsafe { *ipv4hdr }.ihl() * 4) as usize;
    match protocol {
        IPPROTO_UDP => {
            let udphdr: *const udphdr = unsafe { ptr_at(&ctx, addr)? };
            let src_port = u16::from_be(unsafe { (*udphdr).source });
            let dst_port = u16::from_be(unsafe { (*udphdr).dest });

            // 本机开放端口
            if is_pub_port(dst_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            // 白名单
            if in_whitelist(src_ip, src_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            if NAT_TABLE.get(&Addr::new(src_ip, src_port)).is_some() {
                let queue_id = unsafe { (*ctx.ctx).rx_queue_index };
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }

            Ok(xdp_action::XDP_DROP)
        }
        IPPROTO_TCP => {
            let tcphdr: *const tcphdr = unsafe { ptr_at(&ctx, addr)? };
            let src_port = u16::from_be(unsafe { (*tcphdr).source });
            let dst_port = u16::from_be(unsafe { (*tcphdr).dest });

            // 本机开放端口
            if is_pub_port(dst_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            // 白名单
            if in_whitelist(src_ip, src_port) {
                return Ok(xdp_action::XDP_PASS);
            }

            if NAT_TABLE.get(&Addr::new(src_ip, src_port)).is_some() {
                let queue_id = unsafe { (*ctx.ctx).rx_queue_index };
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }

            Ok(xdp_action::XDP_DROP)
        }
        IPPROTO_ICMP => {
            let icmphdr: *const icmphdr = unsafe { ptr_at(&ctx, addr)? };
            let identifier = u16::from_be(unsafe { (*icmphdr).un.echo.id });

            if WHITELIST.get(&Addr::new(src_ip, 0)).is_some() {
                return Ok(xdp_action::XDP_PASS);
            }

            if NAT_TABLE.get(&Addr::new(src_ip, identifier)).is_some() {
                let queue_id = unsafe { (*ctx.ctx).rx_queue_index };
                let not_found_action = xdp_action::XDP_DROP as _;
                return PACKETS.redirect(queue_id, not_found_action).map_err(|_| ());
            }
            Ok(xdp_action::XDP_DROP)
        }
        // VRRP, 虚拟路由器冗余协议 https://protocol.aymar.cn/cd_feature_vrrp_message_format.html
        112 => {
            // 取第一个虚拟IP地址
            let first_vip: *const u32 = ptr_at(&ctx, addr + 8)?;
            let first_vip = u32::from_be(*first_vip);
            if first_vip != vip {
                return Ok(xdp_action::XDP_DROP);
            }
            Ok(xdp_action::XDP_PASS)
        }
        _ => Ok(xdp_action::XDP_DROP),
    }
}
