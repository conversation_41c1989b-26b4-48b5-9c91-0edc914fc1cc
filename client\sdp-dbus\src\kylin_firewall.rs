use dbus::blocking::{Proxy, SyncConnection};
use std::{sync::Arc, time::Duration};

type Result<T> = std::result::Result<T, Error>;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Failed to create a DBus connection")]
    ConnectError(#[source] dbus::Error),

    #[error("Failed to read defender firewall property")]
    ReadDefenderFirewallStateError(#[source] dbus::Error),
}

/// Destination
const FIREWALL_BUS: &str = "com.ksc.defender";
const FIREWALL_PATH: &str = "/firewall";
const FIREWALL_INTERFACE: &str = "com.ksc.defender.firewall";
/// 网络接口列表
const NETWORK_NAME_LIST: &str = "get_networkNameList";
/// 网络模式接口
const NETWORK_MODE: &str = "get_networkModeConfig";
/// 防火墙状态
const FIREWALL_MODE: &str = "get_firewall_mode";

/// 公用网络
const NETWORK_MODE_PUBLIC: i32 = 0;
/// 专用网络
const NETWORK_MODE_PRIVATE: i32 = 1;

// 共用 & 专用 = 0  !共用 !专用 = 3  专用= 2 共用 = 1
/// 防火墙公用网络
const FIREWALL_MODE_PUBLIC: i32 = 1;
/// 防火墙专用网络
const FIREWALL_MODE_PRIVATE: i32 = 2;

const RPC_TIMEOUT: Duration = Duration::from_secs(1);

/// Returns true if the firewall is on.
pub fn is_open() -> Result<bool> {
    Defender::new()?.is_open()
}

struct Defender {
    pub dbus_connection: Arc<SyncConnection>,
}

impl Defender {
    fn new() -> Result<Self> {
        Ok(Self {
            dbus_connection: crate::get_connection().map_err(Error::ConnectError)?,
        })
    }

    fn is_open(&self) -> Result<bool> {
        let name_list = match self
            .as_manager_object()
            .method_call(FIREWALL_INTERFACE, NETWORK_NAME_LIST, ())
            .map(|(names,): (String,)| names)
        {
            Ok(names) => Ok(names),
            Err(error) => {
                if let Some(name) = error.name() {
                    if name.ends_with("ServiceUnknown") {
                        return Ok(false);
                    }
                }
                Err(Error::ReadDefenderFirewallStateError(error))
            }
        }?;

        if let Some((uuid, _last)) = name_list.split_once("::") {
            let network_mode = self
                .as_manager_object()
                .method_call(FIREWALL_INTERFACE, NETWORK_MODE, (uuid,))
                .map(|(mode,): (i32,)| mode)
                .map_err(Error::ReadDefenderFirewallStateError)?;

            let firewall_mode = self
                .as_manager_object()
                .method_call(FIREWALL_INTERFACE, FIREWALL_MODE, ())
                .map(|(mode,): (i32,)| mode)
                .map_err(Error::ReadDefenderFirewallStateError)?;

            match network_mode {
                NETWORK_MODE_PUBLIC => {
                    Ok(firewall_mode | FIREWALL_MODE_PUBLIC == FIREWALL_MODE_PUBLIC)
                }
                NETWORK_MODE_PRIVATE => {
                    Ok(firewall_mode | FIREWALL_MODE_PRIVATE == FIREWALL_MODE_PRIVATE)
                }
                _ => {
                    return Ok(false);
                }
            }
        } else {
            Ok(false)
        }
    }

    fn as_manager_object(&self) -> Proxy<'_, &SyncConnection> {
        Proxy::new(
            FIREWALL_BUS,
            FIREWALL_PATH,
            RPC_TIMEOUT,
            &self.dbus_connection,
        )
    }
}
