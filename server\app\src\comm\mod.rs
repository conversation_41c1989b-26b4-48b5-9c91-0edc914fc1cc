use std::{
    collections::HashMap,
    io,
    net::{IpAddr, SocketAddr},
};

use base::{net::IpPacket, packet::Message};
use fred::{
    bytes::Buf,
    error::{Error as RedisError, <PERSON><PERSON><PERSON><PERSON><PERSON> as RedisErrorK<PERSON>},
    prelude::*,
    types::Value as RedisValue,
};
use serde::Deserialize;
use serde_json::Value;
use std::collections::HashSet;
use tracing::error;
mod auth;
pub mod listen;
mod server;
mod server_handler;

mod common;

pub use common::VIRTUAL_IP_MAPPING;
use tlv_types::{Cidr, Port, Range};

/// 执行动作
pub const ALLOW: &str = "ALLOW";
pub const DENY: &str = "DENY";
pub const AUTH_REQUIRED: &str = "AUTH_REQUIRED";
pub const DENY_AND_DISABLE_ACCOUNT: &str = "DENY_AND_DISABLE_ACCOUNT";
pub const DENY_AND_QUIT: &str = "DENY_AND_QUIT";

#[derive(Debug, thiserror::Error)]
enum Error {
    #[error("Missing environment information")]
    EnvMissing,

    #[error("Environment information missing field: {0}")]
    EnvMissingField(&'static str),

    #[error("Authentication failed")]
    AuthenticationFailed,

    #[error("Stream error. {0}")]
    StreamError(#[from] io::Error),

    #[error("The authentication is successful, but the password needs to be changed")]
    NeedChangePwd,
}

/// 消息处理后的动作
#[derive(PartialEq)]
pub enum NextAction {
    /// 拒绝访问需要退出等..
    Break,
    /// 中断, 处理下一条消息
    Continue,
}

#[derive(Clone)]
pub enum CommCommand {
    /// 强制断开客户端
    Kill,
    /// 在其他地方登录后, MQ发送的通知消息, 其中IpAddr为最后连接的服务端地址
    Close(IpAddr),
    Message(Message),
    Packet(IpPacket),
}

enum InternalAuthCommand {
    /// 取消认证
    CancelAuth(tlv_types::AccessResource),
    /// 完成认证
    CompleteAuth(tlv_types::AccessResource),
}

#[allow(dead_code)]
/// 已认证客户端
#[derive(Debug, Clone)]
pub struct Session {
    pub id: String,
    pub peer_addr: SocketAddr,
    pub local_addr: SocketAddr,
    pub device_id: String,
    pub tenant: String,
    pub user_id: String,
    pub username: String,
    pub env: Value,
}

#[allow(dead_code)]
/// 认证用户信息
#[derive(Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct AuthUser {
    pub session_id: String,
    pub username: String,
    pub device_id: String,
    pub ticket: String,
    pub tenant: String,
    pub user_id: String,
    pub expire: u64,
}

impl FromValue for AuthUser {
    fn from_value(v: RedisValue) -> FredResult<Self> {
        match v {
            RedisValue::Boolean(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: bool",
            )),
            RedisValue::Integer(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: int",
            )),
            RedisValue::Double(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: double",
            )),
            RedisValue::String(data) => {
                let result = serde_json::from_str::<AuthUser>(data.trim());
                match result {
                    Ok(user) => Ok(user),
                    Err(_) => Err(RedisError::new(
                        RedisErrorKind::InvalidArgument,
                        "institution mismatch: string",
                    )),
                }
            }
            RedisValue::Bytes(data) => {
                let result = serde_json::from_slice::<AuthUser>(data.chunk());
                match result {
                    Ok(user) => Ok(user),
                    Err(_) => Err(RedisError::new(
                        RedisErrorKind::InvalidArgument,
                        "institution mismatch: bytes",
                    )),
                }
            }
            RedisValue::Null => Err(RedisError::new(RedisErrorKind::NotFound, "none")),
            RedisValue::Queued => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: queued",
            )),
            RedisValue::Map(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: map",
            )),
            RedisValue::Array(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: array",
            )),
        }
    }
}

#[derive(Debug, Default, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AccessRule {
    /// 所有资源
    pub(crate) all: bool,

    /// 资源列表-IP(支持v4/v6)
    #[serde(default)]
    pub(crate) servers: HashMap<IpAddr, Option<HashSet<Port>>>,

    /// 资源列表-范围
    pub(crate) ranges: Vec<Range>,

    /// 资源列表-CIDR
    pub(crate) cidrs: Vec<Cidr>,
}

impl AccessRule {
    fn r#match(&self, target: IpAddr, port: Option<u16>) -> bool {
        if self.all {
            return true;
        }

        match port {
            Some(dst_port) => {
                self.servers.get(&target).map_or(false, |ports| {
                    ports.as_ref().map_or(true, |ports| {
                        ports.iter().any(|port| port.contains(dst_port))
                    })
                }) || self
                    .ranges
                    .iter()
                    .filter(|range| range.contains(&target))
                    .any(|range| {
                        range.ports.as_ref().map_or(true, |ports| {
                            ports.iter().any(|port| port.contains(dst_port))
                        })
                    })
                    || self
                        .cidrs
                        .iter()
                        .filter(|cidr| {
                            let ip_net = ipnet::IpNet::new(cidr.ip, cidr.prefix).unwrap();
                            ip_net.contains(&target)
                        })
                        .any(|cidr| {
                            cidr.ports.as_ref().map_or(true, |ports| {
                                ports.iter().any(|port| port.contains(dst_port))
                            })
                        })
            }
            None => {
                self.servers.contains_key(&target)
                    || self.ranges.iter().any(|range| range.contains(&target))
                    || self.cidrs.iter().any(|cidr| {
                        let ip_net = ipnet::IpNet::new(cidr.ip, cidr.prefix).unwrap();
                        ip_net.contains(&target)
                    })
            }
        }
    }
}

#[derive(Debug, Default, Deserialize)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub(crate) struct AccessInfo {
    /// 执行动作
    pub(crate) action: String,
    /// 策略ID
    pub(crate) strategy_id: Option<String>,
    /// 排序号
    pub(crate) order: u32,
    /// 授权规则
    pub(crate) rules: Vec<AccessRule>,
}

impl FromValue for AccessInfo {
    fn from_value(value: RedisValue) -> Result<Self, RedisError> {
        match value {
            RedisValue::Boolean(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: bool",
            )),
            RedisValue::Integer(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: int",
            )),
            RedisValue::Double(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: double",
            )),
            RedisValue::String(data) => {
                let result = serde_json::from_str::<AccessInfo>(data.trim());
                match result {
                    Ok(user) => Ok(user),
                    Err(_) => Err(RedisError::new(
                        RedisErrorKind::InvalidArgument,
                        "institution mismatch: string",
                    )),
                }
            }
            RedisValue::Bytes(data) => {
                let result = serde_json::from_slice::<AccessInfo>(data.chunk());
                match result {
                    Ok(user) => Ok(user),
                    Err(_) => Err(RedisError::new(
                        RedisErrorKind::InvalidArgument,
                        "institution mismatch: bytes",
                    )),
                }
            }
            RedisValue::Null => Err(RedisError::new(RedisErrorKind::NotFound, "none")),
            RedisValue::Queued => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: queued",
            )),
            RedisValue::Map(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: map",
            )),
            RedisValue::Array(_) => Err(RedisError::new(
                RedisErrorKind::InvalidArgument,
                "type mismatch: array",
            )),
        }
    }
}

pub struct ClientHandle {
    username: String,
    command_tx: flume::Sender<CommCommand>,
}

impl ClientHandle {
    pub async fn send(&self, command: CommCommand) {
        if self.command_tx.send_async(command).await.is_err() {
            error!(
                username = %self.username,
                "client already close or thread panicked."
            );
        }
    }
}
