//! 根据域名生成证书

use once_cell::sync::Lazy;
use rcgen::{CertificateParams, DnType, Ia5String, IsCa, KeyPair, KeyUsagePurpose, SanType};
use rustls::{
    crypto::ring::{self as provider},
    pki_types::{pem::PemObject, PrivateKeyDer},
    RootCertStore, ServerConfig,
};
use std::{net::IpAddr, str::FromStr, sync::Arc};
use time::{Duration, OffsetDateTime};
use tonic::transport::CertificateDer;

/// 根证书
const ROOT_CA: &str = include_str!("../../../../attachments/ca.crt");
const ROOT_KEY: &str = include_str!("../../../../attachments/ca.key");

static ISSUER: Lazy<CertificateParams> =
    Lazy::new(|| CertificateParams::from_ca_cert_pem(ROOT_CA).unwrap());

static ISSUER_KEY: Lazy<KeyPair> = Lazy::new(|| KeyPair::from_pem(ROOT_KEY).unwrap());

static ROOTS: Lazy<Vec<CertificateDer<'static>>> = Lazy::new(|| base::load_pem_certs(ROOT_CA));

/// 初始化
pub fn init() {
    Lazy::force(&ISSUER);
    Lazy::force(&ISSUER_KEY);

    Lazy::force(&ROOTS);
}

pub fn generate(host: &str) -> Option<Arc<ServerConfig>> {
    let key_pair = KeyPair::generate().unwrap();
    let mut params = CertificateParams::new(vec![host.to_string()]).unwrap();

    params.not_before = OffsetDateTime::now_local().unwrap();
    params.not_after = OffsetDateTime::now_local().unwrap() + Duration::days(7);
    let subject_alt_name = host
        .parse::<IpAddr>()
        .map(|ip| Some(SanType::IpAddress(ip)))
        .unwrap_or_else(|_| {
            Ia5String::from_str(host)
                .ok()
                .map(|dns_name| SanType::DnsName(dns_name))
        });
    if let Some(subject_alt_name) = subject_alt_name {
        params.subject_alt_names.push(subject_alt_name);
    }
    params.key_usages = vec![
        KeyUsagePurpose::DigitalSignature,
        KeyUsagePurpose::KeyEncipherment,
    ];
    params.distinguished_name = {
        let mut dn = ISSUER.distinguished_name.clone();
        dn.push(DnType::CommonName, host);
        dn
    };
    params.is_ca = IsCa::ExplicitNoCa;

    let cert = match params.signed_by(&key_pair, ISSUER.as_ref(), Lazy::force(&ISSUER_KEY)) {
        Ok(cert) => cert,
        Err(error) => {
            log::error!("Failed to generate certificate for `{host}`: {error}");
            return None;
        }
    };

    let mut client_auth_roots = RootCertStore::empty();
    client_auth_roots.add_parsable_certificates(ROOTS.clone());

    let mut config =
        rustls::ServerConfig::builder_with_provider(provider::default_provider().into())
            .with_safe_default_protocol_versions()
            .expect("inconsistent cipher-suites/versions specified")
            .with_no_client_auth()
            .with_single_cert(
                vec![cert.der().clone()],
                PrivateKeyDer::from_pem_slice(key_pair.serialize_pem().as_bytes()).unwrap(),
            )
            .expect("bad certificates/private key");

    config.alpn_protocols.push(b"h2".to_vec());
    config.alpn_protocols.push(b"http/1.1".to_vec());
    Some(Arc::new(config))
}

#[cfg(test)]
mod tests {

    #[test]
    fn test_create_certificate() {
        let server_config = super::generate("************");
        dbg!(server_config);
    }
}
