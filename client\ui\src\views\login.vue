<template>
  <div class="app-content" style="display: flex">
    <headerBar ref="inswitch">
      <el-dropdown>
        <el-icon v-if="!isSettings" class="title-icon" :size="20">
          <Setting />
        </el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- #if [includeCluster] -->
            <el-dropdown-item @click="openClusterConfigDialog"
              >服务器设置
            </el-dropdown-item>
            <!-- #elif [includeSDP] -->
            <el-dropdown-item @click="openNodeDialog"
              >设置控制器地址
            </el-dropdown-item>
            <!-- #endif -->
            <el-dropdown-item @click="resetSettings"
              >恢复出厂设置
            </el-dropdown-item>
            <el-dropdown-item @click="openAboutUsDialog"
              >关于我们
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </headerBar>

    <div class="login_left">
      <div class="login_logo">
        <img
          src="/assets/oneid_logo.png"
          width="30"
          height="30"
          alt=""
          class="icon"
        />
        <h1>欢迎使用{{ productName }}</h1>
      </div>
      <div class="carouselClass">
        <el-carousel arrow="never" :indicator-position="position">
          <el-carousel-item v-for="item in AllBanners" :key="item">
            <img
              v-banner="item"
              style="width: 100%; height: 100%"
              onerror="javascript:this.src='/assets/images/defaultLogo.png';this.onerror=null;"
            />
            <!-- <img src="/assets/login_Logo.png" alt="" style="width: 100%;height: 100%;"> -->
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <div
      class="flex w-full h-full justify-center items-center text-6xl login_nav"
    >
      <div class="select-tenant">
        <div
          class="tenant-img"
          v-if="show_change_tenant_tip && tenants.length > 1"
        >
          <span style="margin-right: 3px">切换单位</span>
          <el-icon style="cursor: pointer" @click="closeChangeTenantTip">
            <Close />
          </el-icon>
        </div>
        <div
          class="remould_select"
          v-if="tenants.length > 1"
          :title="tenant.name"
        >
          <!-- <el-select :title="tenant.name" v-model="tenant.name" :no-data-text="'暂无数据'" value-key="code"
            placeholder="选择单位" visible-change="false" @change="changeTenant">
            <el-option v-for="item in tenants" :key="item.code" :title="item.name.length > 10 ? item.name : ''" :label="
                item.name.length > 10 ? item.name.slice(0, 10) + '...' : item.name
              " :value="item">
            </el-option>
          </el-select> -->
          <el-dropdown
            class="dropdown-link"
            trigger="click"
            @command="handleCommand($event, row)"
          >
            <span class="el-dropdown-link">
              <input
                class="tenant_input"
                v-model="tenant.name"
                type="text"
                readonly
              />
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  class="dropdownStyle"
                  v-for="res in tenants"
                  :key="res.id"
                  :command="res"
                  :class="{ selected: tenant.name == res.name }"
                >
                  <span class="dropdownClass">{{ res.name }}&nbsp;</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <!-- 只有二维码认证时 -->
      <div class="nav_right">
        <div class="nav_right_box">
          <div
            v-if="
              mfaContains('qrcode') ||
              mfaContains('sms_otp') ||
              mfaContains('pwd')
            "
          >
            <el-icon
              v-if="
                user.login_method !== 'qrcode' &&
                user.login_method !== 'sms_otp' &&
                user.login_method !== 'pwd'
              "
              class="nav_right_icon"
              :size="20"
              @click="closeEvent"
            >
              <Close />
            </el-icon>
          </div>
          <img
            class="nav_right_qrcode"
            v-if="
              mfaContains('qrcode') &&
              (user.login_method == 'pwd' || user.login_method == 'sms_otp')
            "
            @click="cutMethods('qrcode')"
            src=" /assets/QrCodeLogin.png"
            alt=""
            width="50"
          />
          <img
            class="nav_right_pwd"
            v-if="
              user.login_method === 'qrcode' &&
              (mfaContains('pwd') || mfaContains('sms_otp'))
            "
            @click="cutMethods('pwd')"
            src="/assets/computer.png"
            alt=""
            width="50"
          />
          <div
            v-if="user.login_method === 'qrcode'"
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              height: 414px;
              justify-content: center;
            "
          >
            <p
              style="
                font-size: 18px;
                font-weight: 600;
                text-align: center;
                margin-bottom: 20px;
              "
            >
              安全令APP扫码登录
            </p>
            <qrcode
              ref="qrcode"
              :participant-group-id="participantGroupId"
              type="login"
              @mfaCallbackFunc="mfaLoginCallback"
              @childEventCancel="errorLog"
              @generateRequestIdFailCallbackFunc="generateRequestIdFailCallback"
            />
          </div>
          <div
            v-else
            v-loading.fullscreen.lock="loading"
            class="login"
            element-loading-text="loading_text"
            element-loading-background="rgba(0, 0, 0, 0.5)"
          >
            <el-alert
              v-if="connectState.state"
              class="alert_error"
              :style="
                connectState.title.length > 7
                  ? 'width: 328px;'
                  : 'width: 230px;'
              "
              :title="connectState.title"
              type="error"
              close-text="点击刷新"
              @close="refresh"
              show-icon
            />
            <el-tabs
              style="flex: 1; width: 100%; height: 54px"
              v-model="user.login_method"
              @tab-change="changeLoginMethod"
            >
              <el-tab-pane
                label="账号登录"
                name="pwd"
                v-if="
                  (mfaMethods.length === 0 || mfaContains('pwd')) &&
                  (user.login_method == 'sms_otp' || user.login_method == 'pwd')
                "
              >
                <div v-if="mfaMethods.length === 0">
                  <div class="input_wrapper" style="margin: 65px 0"></div>
                  <div class="input_wrapper">
                    <div class="inputItem">
                      <div class="inputContent">
                        <input
                          ref="username"
                          v-model="user.username"
                          type="text"
                          placeholder="请输入用户名"
                        />
                      </div>
                    </div>
                    <div class="inputItem">
                      <div class="inputContent" style="padding: 6px 14px">
                        <el-input
                          ref="password"
                          v-model="user.password"
                          placeholder="请输入密码"
                          show-password
                          class="input-pwd"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="login-form-bottom">
                    <el-checkbox
                      v-model="user.remember_me"
                      class="pull-left"
                      label="记住密码"
                      @change="changeRemember"
                    />
                    <el-checkbox
                      v-model="user.auto_connect"
                      class="pull-right"
                      label="自动登录"
                      @change="changeAutoConnect"
                    />

                    <el-link
                      class="login-bottom-item"
                      :underline="false"
                      @click="openForgetPwdDialog"
                    >
                      忘记密码?
                    </el-link>
                  </div>
                  <div style="margin: 30px 0 15px">
                    <button
                      ref="confirmBtn"
                      :class="{
                        opt_button: !disableLoginButton,
                        forbidden: disableLoginButton,
                      }"
                      :disabled="disableLoginButton"
                      @click="clickLogin(user.username, 'normal', true)"
                    >
                      登录
                    </button>
                  </div>
                  <userAgreement />
                </div>
                <div v-else>
                  <div v-if="user.login_method === 'pwd'">
                    <mfaPwd
                      ref="mfapwd"
                      :participant-group-id="participantGroupId"
                      type="login"
                      :user="user"
                      :tenant="tenants"
                      :disable-login-button="disableLoginButton"
                      :show-forget-pwd-dialog="showForgetPwdDialog"
                      @loading="startLoading"
                      @mfaCallbackFunc="mfaLoginCallback"
                      @openForgetPwdDialog="openForgetPwdDialog"
                      @closeForgetPwdDialog="closeForgetPwdDialog"
                      @forgetPwdCallback="resetOrForgetPwdCallback"
                      @generateRequestIdFailCallbackFunc="
                        generateRequestIdFailCallback
                      "
                    />
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane
                label="短信登录"
                name="sms_otp"
                v-if="
                  mfaContains('sms_otp') &&
                  (user.login_method == 'sms_otp' || user.login_method == 'pwd')
                "
              >
                <SMSOTP
                  ref="smsOtp"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="face" v-if="mfaContains('face')">
                <FACE
                  ref="pollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="fingerprint" v-if="mfaContains('fingerprint')">
                <FINGERPRINT
                  ref="pollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="email_otp" v-if="mfaContains('email_otp')">
                <EMAILOTP
                  ref="unPollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="voice" v-if="mfaContains('voice')">
                <VOICE
                  ref="pollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="nativepass" v-if="mfaContains('nativepass')">
                <NATIVEPASS
                  ref="pollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="auth_code" v-if="mfaContains('auth_code')">
                <AUTHCODE
                  ref="unPollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="mobileclick" v-if="mfaContains('mobileclick')">
                <MOBILECLICK
                  ref="pollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="ft_c200" v-if="mfaContains('ft_c200')">
                <TFC200
                  ref="unPollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="anshu_a5" v-if="mfaContains('anshu_a5')">
                <ANSHUA5
                  ref="unPollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="escuotp1" v-if="mfaContains('escuotp1')">
                <ESCUOTP1
                  ref="unPollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="et_z203" v-if="mfaContains('et_z203')">
                <ETZ203
                  ref="unPollLoginMethod"
                  type="login"
                  :participant-group-id="participantGroupId"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="ukey_bjca" v-if="user.login_method === 'ukey_bjca'">
                <UKEY
                  ref="unPollLoginMethod"
                  :participant-group-id="participantGroupId"
                  type="login"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="ukey_wq" v-if="user.login_method === 'ukey_wq'">
                <UKEYWQ
                  ref="unPollLoginMethod"
                  :participant-group-id="participantGroupId"
                  type="login"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane name="ukey" v-if="user.login_method === 'ukey'">
                <UKEYPKI
                  ref="unPollLoginMethod"
                  :participant-group-id="participantGroupId"
                  type="login"
                  :name="user.username"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
              <el-tab-pane
                name="facial_recognition"
                v-if="mfaContains('facial_recognition')"
              >
                <FACIAL
                  ref="unPollLoginMethod"
                  :participant-group-id="participantGroupId"
                  type="login"
                  :name="user.username"
                  :tenant="tenant"
                  @mfaCallbackFunc="mfaLoginCallback"
                  @generateRequestIdFailCallbackFunc="
                    generateRequestIdFailCallback
                  "
                  @childEventCancel="errorLog"
                />
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="login_title" v-if="carouselMfaGroupMethods.length">
            <p class="login_title_el">
              <span class="login_title_el_eles"> 更多方式 </span>
            </p>
            <div class="block" style="flex-grow: 1">
              <el-carousel
                trigger="click"
                class="approveTab"
                height="80px"
                :autoplay="false"
                :loop="false"
                :arrow="carouselMfaGroupMethods.length > 1 ? 'always' : 'never'"
                :initial-index="carouselMethodIndex"
              >
                <el-carousel-item
                  v-for="groupMethods in carouselMfaGroupMethods"
                  :key="groupMethods"
                  class=""
                >
                  <div
                    v-for="additionalMethod in groupMethods"
                    :key="additionalMethod.method.code"
                    class="carouselCode"
                  >
                    <img
                      width="40"
                      height="40"
                      alt=""
                      :src="
                        user.login_method !== additionalMethod.method.code
                          ? 'assets/images/defaultlogin/' +
                            additionalMethod.method.code +
                            '.png'
                          : '/assets/images/mfalogin/' +
                            additionalMethod.method.code +
                            '.png'
                      "
                      @click="changeLoginMethod(additionalMethod.method.code)"
                      :style="
                        user.login_method === additionalMethod.method.code &&
                        'pointer-events: none;'
                      "
                    />
                    <span :title="additionalMethod.method.name">{{
                      additionalMethod.method.name
                    }}</span>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- #if [includeCluster] -->
    <el-dialog
      v-model="showClusterConfig"
      title="服务器设置"
      width="60%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="endpointUrl">
        <el-input
          v-model="nodeConfig.internal.host"
          class="input-text"
          placeholder="服务器内网连接地址"
          clearable
        />
      </div>
      <div class="endpointUrl">
        <el-input
          v-model="nodeConfig.external.host"
          class="input-text"
          placeholder="服务器外网连接地址"
          clearable
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            round
            style="padding: 20px 35px; line-height: 0"
            v-if="showClusterConfigDialogCancelBtn()"
            @click="closeClusterConfigDialog"
            >取消</el-button
          >
          <el-button
            ref="clusterConfigBtn"
            round
            style="padding: 20px 35px; line-height: 0"
            type="primary"
            :disabled="!nodeConfig.internal.host && !nodeConfig.external.host"
            @click="updateClusterConfigUrl"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- #elif [includeSDP] -->
    <el-dialog
      v-model="showNodeConfig"
      title="设置控制器地址"
      width="60%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="endpointUrl">
        <el-input
          v-model="node.host"
          class="input-text"
          placeholder="请输入域名"
          clearable
        />
      </div>
      <div class="endpointUrl">
        <el-input
          v-model="node.ip"
          class="input-text"
          placeholder="请输入IP (可选)"
          clearable
        />
      </div>
      <div class="endpointUrl">
        <el-input
          v-model="node.port"
          class="input-text"
          placeholder="请输入端口"
          clearable
        />
      </div>
      <div class="endpointUrl">
        <el-input
          v-model="node.spa_port"
          class="input-text"
          placeholder="请输入单包认证端口"
          clearable
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            round
            style="padding: 20px 35px; line-height: 0"
            v-if="showNodeDialogCancelBtn()"
            @click="closeNodeDialog"
            >取消</el-button
          >
          <el-button
            ref="nodeConfigBtn"
            round
            style="padding: 20px 35px; line-height: 0"
            type="primary"
            :disabled="!node.host || !node.port || !node.spa_port"
            @click="updateNode(node)"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- #endif -->
    <el-dialog
      v-model="showAboutUs"
      width="50%"
      :before-close="closeAboutUsDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="AboutUs"
    >
      <h1 style="font-weight: normal; margin-top: 0">关于我们</h1>
      <p>
        <span v-if="hasNewVersion">发现新版本 : {{ productVersion }}</span>
        <span v-else>当前已是最新版本 : {{ productVersion }}</span>
        &nbsp;<el-button
          plain
          @click="check"
          style="border: 1px solid #f87f1a; color: #f87f1a"
        >
          {{ hasNewVersion ? "立即更新" : "检查更新" }}
        </el-button>
      </p>
      <p>
        设备ID : {{ deviceId }} &nbsp;
        <el-icon
          style="color: #f9780c; cursor: pointer"
          @click="copyDeviceId()"
        >
          <DocumentCopy />
        </el-icon>
      </p>
    </el-dialog>
    <reset-pwd
      ref="resetPwd"
      @successCallback="resetOrForgetPwdCallback"
      :show="showResetPwdDialog"
      @close="closeResetPwdDialog"
    />
    <forget-pwd
      ref="forgetPwd"
      @successCallback="resetOrForgetPwdCallback"
      :show="showForgetPwdDialog"
      @close="closeForgetPwdDialog"
    />
    <updater ref="updater" />
  </div>
</template>
<script>
import { invoke } from "@tauri-apps/api/tauri";
import { computed, inject } from "vue";
import { useStore } from "vuex";
import headerBar from "@/components/Header.vue";
import SliderCheck from "@/components/SliderCheck.vue";
import {
  authStrategy,
  mfaRequestId,
  getLinkedLoginToken,
  tenants,
  getRequestId,
  userSecretKeyResultId,
  userSecretKeyauthResultId,
  getSlideshow,
  getPublicKey,
  msgrequestId,
} from "../api/service.js";
import qrcode from "../components/mfa/qrcode.vue";
import mfaPwd from "../components/mfa/pwd.vue";
import FINGERPRINT from "@/components/mfa/fingerprint.vue";
import SMSOTP from "@/components/mfa/smsOtp.vue";
import EMAILOTP from "@/components/mfa/emailOtp.vue";
import VOICE from "@/components/mfa/voice.vue";
import NATIVEPASS from "@/components/mfa/nativepass.vue";
import AUTHCODE from "@/components/mfa/authCode.vue";
import MOBILECLICK from "@/components/mfa/mobileClick.vue";
import TFC200 from "@/components/mfa/ftc200.vue";
import ANSHUA5 from "@/components/mfa/anshuA5.vue";
import ESCUOTP1 from "@/components/mfa/escuotp1.vue";
import ETZ203 from "@/components/mfa/etz203.vue";
import UKEY from "@/components/mfa/ukey.vue";
import UKEYWQ from "@/components/mfa/ukey_wq.vue";
import UKEYPKI from "@/components/mfa/ukey_pki.vue";
import FACE from "@/components/mfa/face.vue";
import FACIAL from "@/components/mfa/facial_recognition.vue";
import { emit } from "@tauri-apps/api/event";
import { VOffline } from "v-offline";
import { sm2 } from "sm-crypto";
import userAgreement from "./userAgreement.vue";
import resetPwd from "./resetPwd.vue";
import forgetPwd from "./forgetPwd.vue";
import updater from "./updater.vue";
import { ElMessageBox } from "element-plus";
import { executeLogin } from "../global.js";

export default {
  name: "Login",
  components: {
    headerBar,
    SliderCheck,
    qrcode,
    mfaPwd,
    FINGERPRINT,
    SMSOTP,
    EMAILOTP,
    VOICE,
    NATIVEPASS,
    AUTHCODE,
    MOBILECLICK,
    TFC200,
    ANSHUA5,
    ESCUOTP1,
    ETZ203,
    VOffline,
    UKEY,
    FACE,
    FACIAL,
    UKEYWQ,
    UKEYPKI,
    userAgreement,
    resetPwd,
    forgetPwd,
    updater,
  },
  beforeRouteLeave(to, from, next) {
    // 设置下一个路由的 meta
    to.meta.keepalive = false;
    next();
  },
  setup() {
    const store = useStore();
    store.commit("updateIsLogin", false);
    const checkUpdate = inject("checkUpdate");

    return {
      checkUpdate,
      productVersion: window.PRODUCT_VERSION,
      deviceId: window.DEVICE_ID,
      connectState: computed(() => store.getters.connectState),
      hasNewVersion: computed(() => store.getters.hasNewVersion),
      // #if [includeCluster]
      showClusterConfig: computed(() => store.getters.showClusterConfig),
      // #elif [includeSDP]
      showNodeConfig: computed(() => store.getters.showNodeConfig),
      // #endif
      showAboutUs: computed(() => store.getters.showAboutUs),
      isSettings: false,
    };
  },
  inject: ["reload"],
  computed: {
    // 轮播认证方式
    carouselMfaMethods() {
      let excluded = ["qrcode", "pwd", "sms_otp"];
      return this.mfaMethods.filter(
        (item) => !excluded.includes(item.method.code)
      );
    },
    // 轮播认证方式分组-3个一组
    carouselMfaGroupMethods() {
      return this.groupOfThrees(this.carouselMfaMethods);
    },
    // 选中的方式在轮播的哪个组中
    carouselMethodIndex() {
      // 认证方式中, 不包含当前选择的登录方式
      if (!this.mfaContains(this.user.login_method)) {
        // 若密码登录可用
        if (this.mfaContains("pwd")) {
          this.user.login_method = "pwd";
        }
        // 若短信登录可用
        else if (this.mfaContains("sms_otp")) {
          this.user.login_method = "sms_otp";
        } else if (this.mfaContains("qrcode")) {
          this.user.login_method = "qrcode";
        } else {
          this.mfaMethods.length &&
            (this.user.login_method = this.mfaMethods[0].method.code);
        }
        return 0;
      }
      for (let i = 0; i < this.carouselMfaGroupMethods.length; i++) {
        let groupMethods = this.carouselMfaGroupMethods[i];
        for (const index in groupMethods) {
          if (groupMethods[index].method.code === this.user.login_method) {
            return i;
          }
        }
      }
      return 0;
    },
  },
  data() {
    return {
      productName: PRODUCT_NAME,
      tenant: CONFIG.selected_tenant ? CONFIG.selected_tenant : {},
      secret_key: CONFIG.secret_key ? CONFIG.secret_key : {},
      show_change_tenant_tip: !!CONFIG.show_change_tenant_tip,
      showForgetPwdDialog: false, // 忘记密码标记
      showResetPwdDialog: false, // 重置密码标记
      loading: false, // 加载中...
      tenants: [], // 租户列表
      mfaMethods: [], // MFA 认证方式
      user: {
        username: "",
        password: "",
        remember_me: false,
        auto_connect: false,
        secret: "",
        login_method: "pwd",
      }, // 用户信息
      // #if [includeSDP]
      node: {
        host: "",
        ip: null,
        port: "",
        spa_port: "",
      }, // 节点信息
      nodeConfig: {
        internal: {
          host: "",
          uri: "/cluster_inside",
        },
        external: {
          host: "",
          uri: "/cluster",
        },
      },
      // #endif
      authStrategyTask: null, // 定时加载策略任务
      disableLoginButton: true,
      linkedLoginToken: null, // 联动登录票据信息
      linkedLoginSignature: null, // 联动登录票据信息
      authResultId: "",
      participantGroupId: "",
      loading_text: "加载中...",
      //节流
      throttle: false,

      // 重试信息
      retryTimes: 1,
      currentTimes: 1,
      // 全部轮播图
      AllBanners: [],
      position: "",
    };
  },
  watch: {
    //监听输入框输入内容
    user: {
      deep: true,
      handler: function (newValue, oldValue) {
        if (newValue) {
          this.disableLoginButton = !(
            newValue.password !== "" && newValue.username !== ""
          );
        } else if (oldValue) {
          this.disableLoginButton = false;
        }
      },
    },
    $route(to) {
      if (to.path != "/login") {
        this.$refs.qrcode && this.$refs.qrcode.cancel();
        this.$refs.pollLoginMethod && this.$refs.pollLoginMethod.cancel();
      }
    },
    "user.username"(newValue, oldValue) {
      // 若是持久化的信息, 则不用清空密码
      if (!this.user.persistent) {
        if (oldValue) {
          this.user.password = "";
          this.user.secret = "";
        }
      } else {
        this.user.persistent = false;
      }
    },
    "connectState.state"(state) {
      // 网络断开或连接异常
      if (state === true) {
        if (this.$refs.qrcode) {
          this.$refs.qrcode.close();
        }
      }
    },
    "mfaMethods"() {
      // 认证方式中, 不包含当前选择的登录方式
      if (!this.mfaContains(this.user.login_method)) {
        // 若密码登录可用
        if (this.mfaContains("pwd")) {
          this.user.login_method = "pwd";
        }
        // 若短信登录可用
        else if (this.mfaContains("sms_otp")) {
          this.user.login_method = "sms_otp";
        } else if (this.mfaContains("qrcode")) {
          this.user.login_method = "qrcode";
        } else {
          this.mfaMethods.length &&
            (this.user.login_method = this.mfaMethods[0].method.code);
        }
      }
    }
  },
  async created() {
    let that = this;

    // #if [includeCluster]
    if (!CONFIG.cluster_config_url && !CONFIG.cluster_external_config_url) {
      this.$store.commit("setConnectState", { state: false, title: "" });
      let that = this;
      that.$alert("服务器连接地址不能为空", "提示", {
        confirmButtonText: "确定",
        showClose: false,
        roundButton: true,
        center: true,
        callback: () => {
          that.$store.commit("setClusterConfigVisible", true);
        },
      });
      return;
    }
    // #elif [includeSDP]
    let nullOfNode = !CONFIG.cluster_nodes || CONFIG.cluster_nodes.length === 0;
    if (nullOfNode) {
      this.$store.commit("setConnectState", { state: false, title: "" });
      this.$alert("控制器地址不能为空", "提示", {
        confirmButtonText: "确定",
        showClose: false,
        roundButton: true,
        center: true,
        callback: () => {
          this.$store.commit("setNodeVisible", true);
        },
      });
      return;
    }
    // #endif

    that.loading = true;
    await that.initData(1, 1);
    that.loading = false;
  },
  mounted() {
    console.log("mounted......");

    // #if [includeCluster]
    window.addEventListener("showClusterConfig", this.openClusterConfigDialog);
    // #elif [includeSDP]
    window.addEventListener("showNodeConfig", this.openNodeDialog);
    // #endif

    // 注册回车登录事件
    window.addEventListener("keydown", this.handleEnterEvent);
  },
  // 离开该页面触发
  beforeUnmount() {
    console.log("leave mounted......");

    // #if [includeCluster]
    window.removeEventListener(
      "showClusterConfig",
      this.openClusterConfigDialog,
      false
    );
    // #elif [includeSDP]
    window.removeEventListener("showNodeConfig", this.openNodeDialog, false);
    // #endif

    window.removeEventListener("keydown", this.handleEnterEvent, false);

    this.$refs.qrcode && this.$refs.qrcode.cancel();
    this.$refs.pollLoginMethod && this.$refs.pollLoginMethod.cancel();

    // 清理策略加载任务
    if (this.authStrategyTask) {
      clearInterval(this.authStrategyTask);
      this.authStrategyTask = null;
    }
  },
  methods: {
    startLoading() {
      this.loading = true;
    },
    setCurrentTenant() {
      this.tenant = CONFIG.selected_tenant;
    },
    setCloseTenantTip() {
      this.show_change_tenant_tip = CONFIG.show_change_tenant_tip;
    },
    // 重置状态
    resetState() {
      this.loading = false;
      this.mfaMethods = [];
    },
    // 判断策略里面是否包含当前认证方式
    mfaContains(methodCode) {
      return !!this.mfaMethods.find((item) => item.method.code === methodCode);
    },
    /**
     * 回车事件
     */
    handleEnterEvent(event) {
      if (event.key !== "Enter") {
        return;
      }

      // 只要有弹窗, 就不执行任何操作
      if (ElMessageBox.count && ElMessageBox.count() > 0) {
        event.preventDefault();
        return;
      }

      console.log(event);

      // #if [includeCluster]
      // 当前打开了设置集群地址, 回车保存
      if (this.showClusterConfig) {
        // 出发保存按钮的点击事件
        this.$refs.clusterConfigBtn.$el.click();
        return;
      }
      // #elif [includeSDP]
      // 当前打开了设置节点地址, 回车保存
      if (this.showNodeConfig) {
        // 出发保存按钮的点击事件
        this.$refs.nodeConfigBtn.$el.click();
        return;
      }
      // #endif

      // 关于我们
      if (this.showAboutUs) {
        return;
      }

      if (this.throttle) return;
      this.throttle = true;
      setTimeout(() => {
        this.throttle = false;
      }, 1000);

      // 忘记密码
      if (this.showForgetPwdDialog) {
        // MFA的密码登录
        if (this.mfaMethods.length > 0 && this.user.login_method === "pwd") {
          this.$refs.mfapwd.$refs.forgetPwd.$refs.forgetPwdBtn.$el.click();
        }
        // 密码登录
        if (this.mfaMethods.length === 0) {
          this.$refs.forgetPwd.$refs.forgetPwdBtn.$el.click();
        }
        return;
      }

      // 重置密码
      if (this.showResetPwdDialog) {
        this.$refs.resetPwd.$refs.resetPwdBtn.$el.click();
        return;
      }

      // MFA密码登录
      if (this.mfaMethods.length > 0 && this.user.login_method === "pwd") {
        // 判断登录按钮是否被禁用
        if (this.$refs.mfapwd.$refs.confirmBtn.disabled) {
          console.log("mfa pwd login disabled");
          return;
        }

        this.$refs.mfapwd.$refs.confirmBtn.$el.click();
        return;
      }

      // MFA 短信登录
      if (this.user.login_method === "sms_otp") {
        // 判断登录按钮是否被禁用
        if (this.$refs.smsOtp.$refs.confirmBtn.disabled) {
          console.log("mfa sms login disabled");
          return;
        }

        this.$refs.smsOtp.$refs.confirmBtn.$el.click();
        return;
      }

      // MFA 需要手动点击确认的认证方式
      if (this.$refs.unPollLoginMethod) {
        // 处于第一阶段
        if (this.$refs.unPollLoginMethod.$refs.nextStepBtn) {
          if (this.$refs.unPollLoginMethod.$refs.nextStepBtn.disabled) {
            console.log(`mfa ${this.user.login_method} login disabled`);
            return;
          }
          this.$refs.unPollLoginMethod.$refs.nextStepBtn.$el.click();
          return;
        }

        // 确认按钮
        if (this.$refs.unPollLoginMethod.$refs.confirmBtn) {
          if (this.$refs.unPollLoginMethod.$refs.confirmBtn.disabled) {
            console.log(`mfa ${this.user.login_method} login disabled`);
            return;
          }
          this.$refs.unPollLoginMethod.$refs.confirmBtn.$el.click();
          return;
        }
        return;
      }

      // MFA 轮询登录方式
      if (this.$refs.pollLoginMethod) {
        // 处于第一阶段
        if (this.$refs.pollLoginMethod.$refs.nextStepBtn) {
          if (this.$refs.pollLoginMethod.$refs.nextStepBtn.disabled) {
            console.log(`mfa ${this.user.login_method} login disabled`);
            return;
          }
          this.$refs.pollLoginMethod.$refs.nextStepBtn.$el.click();
          return;
        }
        return;
      }

      // 没有MFA时, 密码登录
      this.clickLogin(this.user.username, "normal", true);
    },
    /**
     * 初始化数据
     *
     * 0. 根据存储中的数据, 填充用户信息
     * 1. 加载租户信息
     * 2. 尝试联动登录
     * 3. 加载认证策略
     * 4. 执行自动登录
     *
     * @param retryTimes 重试次数
     * @param currentTimes 当前处在第几次
     * @returns {Promise<void>}
     */
    async initData(retryTimes, currentTimes) {
      this.retryTimes = typeof retryTimes === "number" ? retryTimes : 1;
      this.currentTimes = typeof currentTimes === "number" ? currentTimes : 1;
      // 0. 根据存储中的数据, 填充用户信息
      // 若是临时存储了用户信息, 则加载临时用户信息
      if (
        window.TMP_USERS &&
        CONFIG.selected_tenant &&
        CONFIG.selected_tenant.code &&
        window.TMP_USERS[CONFIG.selected_tenant.code]
      ) {
        this.user = window.TMP_USERS[CONFIG.selected_tenant.code];
        this.user.persistent = true;
      } else {
        if (
          CONFIG.selected_tenant &&
          CONFIG.selected_tenant.code &&
          CONFIG.users &&
          CONFIG.users[CONFIG.selected_tenant.code]
        ) {
          this.user = CONFIG.users[CONFIG.selected_tenant.code];
          this.user.persistent = true;
        } else {
          this.user = {
            username: "",
            password: "",
            remember_me: false,
            auto_connect: false,
            secret: "",
            login_method: "pwd",
          };
        }
      }

      /**
       * 网络错误回调函数
       *
       * @param self 指向this
       * @param error 错误
       * @returns {Promise<void>}
       */
      let network_error_callback = async (self, error) => {
        console.log("network_error_callback " + error);
        if (error === "Connection failed" || error === "Internal Server Error") {
          if (await self.changeToNextClusterNode("initData")) {
            await self.initData(retryTimes, currentTimes);
          } else {
            // 无节点可用
            !self.connectState.state &&
            self.$store.commit("setConnectState", {
              state: true,
              title: "服务器连接异常",
            });
            // #if [includeCluster]
            invoke("plugin:cluster|reload");
            // #endif
          }
          return;
        }

        if (self.currentTimes < self.retryTimes) {
          setTimeout(async () => {
            await self.initData(self.retryTimes, self.currentTimes + 1);
          }, 500 * Math.pow(2, self.currentTimes));
        } else if (self.currentTimes >= self.retryTimes) {
          self.$router.push({
            path: "/login",
            query: { date: new Date().getTime() },
          });
        }
        !self.connectState.state &&
          self.$store.commit("setConnectState", {
            state: true,
            title: "服务器连接异常",
          });
      };

      this.loading = true;
      emit("log", { level: "Trace", message: "initdata: true" });
      try {
        // 1. 加载租户信息
        this.tenants = await this.loadTenants();
        this.$store.commit("setConnectState", { state: false, title: "" });
        if (!CONFIG.selected_tenant || !CONFIG.selected_tenant.name) {
          if (this.tenants.length > 0) {
            let start = this.tenants.filter((item) => item.code === "DEFAULT");
            window.CONFIG = await invoke("patch", {
              value: {
                selected_tenant: {
                  code: start[0].code,
                  name: start[0].name,
                },
              },
            });
          }
        }
        // 后台改变租户名称/客户端同步修改
        else if (CONFIG.selected_tenant && CONFIG.selected_tenant.name) {
          this.tenants.forEach(async (res) => {
            if (res.code === CONFIG.selected_tenant.code) {
              CONFIG.selected_tenant.name = res.name;
              window.CONFIG = await invoke("patch", {
                value: {
                  selected_tenant: {
                    code: res.code,
                    name: res.name,
                  },
                },
              });
            }
          });
        }

        this.setCurrentTenant();

        // 2. 尝试联动登录
        await this.linkedLogin(network_error_callback);
        // 3. 获取轮播图
        this.slideshowFun();
      } catch (error) {
        this.loading = false;
        emit("log", {
          level: "Error",
          message: "tenant list: " + JSON.stringify(error),
        });
        emit("log", { level: "Trace", message: "initdata catch: false" });
        await network_error_callback(this, error);
      }
    },
    /**
     * 尝试联动登录
     *
     * 0. 未执行过联动登录
     * 1. 加载认证策略
     * 2. 执行自动登录
     *
     * @param network_error_callback 网络错误回调函数
     * @returns {Promise<void>}
     */
    async linkedLogin(network_error_callback) {
      let hasLoggedIn = await invoke("has_logged_in");

      emit("log", {
        level: "Trace",
        message:
          "login with: " +
          location.hash +
          ", alreadyExecutedLogin: " +
          hasLoggedIn,
      });

      // 正常登录
      if (hasLoggedIn) {
        await this.loadAuthStrategy(network_error_callback);
      }
      // 联动登录
      else {
        let fallback = async (self) => {
          self.linkedLoginToken = null;
          await self.loadAuthStrategy(network_error_callback);
        };
        getLinkedLoginToken()
          .then(async (res) => {
            if (res && res.data && res.data.type === "TFA") {
              this.linkedLoginToken = res.data.ticket;
              this.linkedLoginSignature = res.data.signature;
              if (this.tenants.length > 0) {
                let tenant = this.tenants.find(
                  (tenant) => tenant.code === res.data.tenant
                );

                if (tenant) {
                  // 双因素用户与SDP用户不匹配时, 清空绑定码
                  if (
                    tenant.code !== CONFIG.selected_tenant.code ||
                    this.user.username !== res.data.username
                  ) {
                    this.user = {
                      secret: null,
                      username: res.data.username,
                      password: null,
                      login_method: "pwd",
                      remember_me: false,
                      auto_connect: false,
                    };

                    let value = {};
                    value["users"] = {};
                    value["users"][res.data.tenant] = this.user;

                    value["selected_tenant"] = {
                      code: tenant.code,
                      name: tenant.name,
                    };

                    window.CONFIG = await invoke("patch", {
                      value: value,
                    });

                    this.setCurrentTenant();
                  }

                  await this.clickLogin(res.data.username, "link", false);
                } else {
                  await fallback(this);
                }
              } else {
                await fallback(this);
              }
            } else {
              await fallback(this);
            }
          })
          .catch(async () => {
            await fallback(this);
          })
          .finally(async () => {
            await invoke("set_logged_in");
          });
      }
    },
    // 加载租户
    async loadTenants() {
      return new Promise((resolve, reject) => {
        const hasTenants = CONFIG.tenants && CONFIG.tenants.length > 0;
        if (hasTenants) {
          tenants()
            .then(async (res) => {
              this.secret_key = await this.PublicKeyInit();
              invoke("patch", {
                value: {
                  tenants: res.data,
                  secret_key: this.secret_key,
                },
              }).then((config) => {
                window.CONFIG = config;
              });
              resolve(res.data);
            })
            .catch((e) => {
              reject(e);
            });
        } else {
          tenants()
            .then(async (res) => {
              this.secret_key = await this.PublicKeyInit();
              invoke("patch", {
                value: {
                  tenants: res.data,
                  secret_key: this.secret_key,
                },
              }).then((config) => {
                window.CONFIG = config;
              });
              resolve(res.data);
            })
            .catch((e) => {
              reject(e);
            });
        }
      });
    },
    // 动态加密
    async PublicKeyInit() {
      return new Promise((resolve, reject) => {
        getPublicKey()
          .then((res) => {
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 捕捉错误信息
    errorLog(res) {
      msgrequestId(res).then((res) => {
        console.log("res:认证失败错误日志 ", res);
      });
    },

    /**
     * 加载认证策略
     *
     * 加载完成后根据是否执行过登录, 若没有登录过, 则执行自动登录
     *
     * @param network_error_callback 网络错误回调函数
     * @returns {Promise<void>}
     */
    async loadAuthStrategy(network_error_callback) {
      this.loading = true;
      emit("log", { level: "Trace", message: "loadAuthStrategy: true" });
      try {
        this.user.authResultId = "";
        let res = await authStrategy();
        console.log(res, "策略");
        if (!res || !res.data) {
          this.mfaMethods = [];
          await this.autoLogin();
        } else if (res.code !== "SUCCESS") {
          this.alert.error("系统错误");
        } else if (!res.data.enabled || res.data.auth) {
          await this.autoLogin();
        } else {
          let decision = res.data.decision;
          switch (decision) {
            case "PERMIT":
              this.alert.error(res.data);
              break;
            case "DENY":
              // this.alert.error('禁止操作')
              this.mfaDenyLogin();
              break;
            case "MFA":
              this.authResultId = res.data.id;
              this.participantGroupId = res.data.participantGroups[0].id;

              let methods =
                res.data.participantGroups[0].participants[0].mfaMethods;
              // Windows人脸/北京CA/握奇 只支持Windows平台
              // #if [platform!=windows]
              let excluded = ["ukey_bjca", "ukey_wq", "facial_recognition"];
              methods = methods.filter(
                (item) => !excluded.includes(item.method.code)
              );
              // #endif

              this.mfaMethods = [...methods];

              await this.autoLogin("mfa");
          }
        }
      } catch (error) {
        if (error.messageKey === "MFA.CHECK.DENY") {
          await this.autoLogin();
        }
        emit("log", {
          level: "Error",
          message: "login strategy: " + JSON.stringify(error),
        });
        if (typeof network_error_callback === "function") {
          await network_error_callback(this, error);
        } else {
          this.$store.commit("setConnectState", {
            state: true,
            title: "服务器连接异常",
          });
        }
      }
      emit("log", { level: "Trace", message: "loadAuthStrategy: false" });

      let that = this;
      if (!that.authStrategyTask) {
        emit("log", {
          level: "Trace",
          message: "Enable the scheduled strategy loading task",
        });
        that.authStrategyTask = setInterval(async () => {
          await that.silenceLoadAuthStrategy(function (self, error) {
            emit("log", {
              level: "Error",
              message:
                "Authentication policy loading failed: " +
                JSON.stringify(error),
            });
          });
        }, 10 * 60 * 1000);
      }
      this.loading = false;
    },
    /**
     * 静默加载认证策略
     *
     * @param network_error_callback 网络错误回调函数
     * @returns {Promise<void>}
     */
    async silenceLoadAuthStrategy(network_error_callback) {
      emit("log", { level: "Trace", message: "silenceLoadAuthStrategy: true" });
      try {
        let res = await authStrategy();
        console.log(res, "静默加载策略");
        if (!res || !res.data) {
        } else if (res.code !== "SUCCESS") {
        } else if (!res.data.enabled || res.data.auth) {
        } else {
          let decision = res.data.decision;
          switch (decision) {
            case "PERMIT":
              this.alert.error(res.data);
              break;
            case "DENY":
              // this.alert.error('禁止操作')
              this.mfaDenyLogin();
              break;
            case "MFA":
              this.authResultId = res.data.id;
              this.participantGroupId = res.data.participantGroups[0].id;

              let methods =
                res.data.participantGroups[0].participants[0].mfaMethods;
              // Windows人脸/北京CA/握奇 只支持Windows平台
              // #if [platform!=windows]
              let excluded = ["ukey_bjca", "ukey_wq", "facial_recognition"];
              methods = methods.filter(
                (item) => !excluded.includes(item.method.code)
              );
              // #endif

              this.mfaMethods = methods;
          }
        }
      } catch (error) {
        emit("log", {
          level: "Error",
          message: "silence login strategy: " + JSON.stringify(error),
        });
        if (typeof network_error_callback === "function") {
          await network_error_callback(this, error);
        }
      }
      emit("log", {
        level: "Trace",
        message: "silenceLoadAuthStrategy: false",
      });
    },
    // 切换租户
    async changeTenant(tenant) {
      if (this.tenant && this.tenant.code === tenant.code) {
        return;
      }

      if (CONFIG.selected_tenant && CONFIG.selected_tenant.code) {
        // 未勾选记住密码, 删除密码属性
        if (!this.user.remember_me) {
          this.user.password = null;
        }
        if (!window.TMP_USERS) {
          window.TMP_USERS = {};
        }
        window.TMP_USERS[CONFIG.selected_tenant.code] = this.user;
      }

      // 取消轮询
      this.$refs.qrcode && this.$refs.qrcode.cancel();
      this.$refs.pollLoginMethod && this.$refs.pollLoginMethod.cancel();

      // 更新租户信息
      window.CONFIG = await invoke("patch", {
        value: {
          selected_tenant: {
            code: tenant.code,
            name: tenant.name,
          },
        },
      });

      this.setCurrentTenant();
      this.resetState();

      this.loading = true;
      await this.initData(1, 1);
      this.loading = false;
    },
    changeAutoConnect() {
      this.user.remember_me = true;
    },
    changeRemember() {
      if (this.user.remember_me) {
        this.user.auto_connect = false;
      }
    },

    // 下拉菜单选中ID
    handleCommand(res) {
      this.changeTenant(res);
    },
    /**
     * 自动登录
     *
     * @param type mfa或其他
     */
    async autoLogin(type) {
      let hasLoggedIn = await invoke("has_logged_in");
      if (this.user.auto_connect && !hasLoggedIn) {
        emit("log", { level: "Info", message: "Auto login" });
        let that = this;
        setTimeout(function () {
          type === "mfa"
            ? that.$refs.mfapwd.login()
            : that
                .clickLogin(that.user.username, "normal", false)
                .then(() => {});
        }, 500);
      }
    },
    /**
     * 登录
     *
     * @param {*} username 用户名
     * @param {*} type 登录类型. link: 双因素联动登录 normal: 普通登录
     * @param {*} click 是否是手动点击登录按钮登录
     */
    async clickLogin(username, type, click) {
      if (click && this.loading) {
        return;
      }
      if (!this.tenant || !this.tenant.code) {
        this.alert.error("请选择单位");
        return;
      }
      if (!username && type !== "link") {
        this.alert.error("请输入用户名");
        return;
      }
      if (
        !this.user.password &&
        this.user.login_method === "pwd" &&
        type === "normal"
      ) {
        this.alert.error("请输入密码");
        return;
      }
      this.loading = true;
      await emit("log", { level: "Trace", message: "clicklogin: true" });
      let t = this;

      // #if [includeSDP]
      // 是否有绑定码
      if (!this.user.secret) {
        // 有策略
        if (this.user.authResultId) {
          try {
            // 获取绑定码
            let data = await userSecretKeyauthResultId({
              authResultId: this.user.authResultId,
            });
            const privateKey =
              "3146d44927b057e26f90adf3ee51e3c2297c1cf632b2744fa18ebf0b351ae129";
            this.user.secret = sm2.doDecrypt(data.data, privateKey, 0);
          } catch (error) {
            this.loading = false;
            await emit("log", {
              level: "Trace",
              message: "clicklogin strategy failed: false",
            });
            this.alert.error(
              (error.data && error.data.errorMsg) ||
                "绑定码获取失败, 请稍后再试"
            );
            await this.loadAuthStrategy();
            return;
          }
          // 无策略
        } else {
          try {
            let result = await getRequestId("", "03", username, "pwd");
            try {
              // 获取绑定码
              let data = await userSecretKeyResultId({
                requestId: result.data,
              });
              if (data.data) {
                const privateKey =
                  "3146d44927b057e26f90adf3ee51e3c2297c1cf632b2744fa18ebf0b351ae129";
                this.user.secret = sm2.doDecrypt(data.data, privateKey, 0);
              } else {
                this.loading = false;
                emit("log", {
                  level: "Trace",
                  message: "clicklogin no requestid: false",
                });
                this.user.password = "";
                // 双因素联动登录失败, 执行正常加载数据(策略等)逻辑
                if (type === "link") {
                  await this.loadAuthStrategy();
                } else {
                  this.alert.error("用户名或密码错误");
                }
                return;
              }
            } catch (error) {
              this.loading = false;
              emit("log", {
                level: "Trace",
                message: "clicklogin request failed: false",
              });
              this.alert.error(
                (error.data && error.data.errorMsg) ||
                  "绑定码获取失败, 请稍后再试"
              );
              return;
            }
          } catch (error) {
            this.alert.error("网络连接异常, 请稍后再试");
            this.loading = false;
            emit("log", {
              level: "Trace",
              message: "clicklogin request catch: false",
            });
            // 双因素联动登录失败, 执行正常加载数据(策略等)逻辑
            if (type === "link") {
              await this.loadAuthStrategy();
            }
            return;
          }
        }
      }

      /*secret: "aed39160d46965780db79f4837ec488d"*/
      executeLogin(
        username,
        this.user.secret,
        this.user.password,
        this.tenant.code,
        type === "normal" ? this.user.login_method : type,
        type === "link" ? this.linkedLoginToken : null,
        type === "link" ? this.linkedLoginSignature : null,
        this.user.authResultId || ""
      )
        .then((data) => {
          // 标记已登录
          t.$store.commit("updateIsLogin", true);
          let doLoginSuccess = function () {
            t.$store.commit("setUsername", username);
            if (type !== "link") {
              // 更新登录方式, 用户及绑定码信息
              let users = {};
              // 未勾选记住密码, 删除密码属性
              if (!t.user.remember_me) {
                t.user.password = null;
              }
              users[t.tenant.code] = t.user;
              invoke("patch", {
                value: {
                  users: users,
                },
              }).then((config) => {
                window.CONFIG = config;
              });
            }
            t.$store.commit("setConnectState", {
              state: false,
              title: "服务器连接异常",
            });
            t.$router.push("/");
            // localStorage.setItem( 'logstart', location.hash)
            t.loading = false;
          };
          if (
            data.payload.expire_day != 2147483647 &&
            data.payload.expire_day <= data.payload.pwd_tip_day
          ) {
            let msg =
              data.payload.expire_day === 0
                ? "您的密码将在今天过期"
                : "您的密码还有" +
                  data.payload.expire_day +
                  "天过期!" +
                  "，" +
                  "请及时修改密码";

            t.$confirm(msg, "过期提示", {
              confirmButtonText: "修改密码",
              cancelButtonText: "跳过",
              class: "logout",
              roundButton: true,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              center: true,
            })
              .then(() => {
                t.$nextTick(() => {
                  t.$refs.resetPwd.init({
                    secret: t.user.secret,
                    verifyCode: "",
                    username: t.user.username,
                  });
                  t.openResetPwdDialog();
                });
                t.loading = false;
              })
              .catch(() => {
                doLoginSuccess();
              });
          } else {
            doLoginSuccess();
          }
        })
        .catch(async (res) => {
          console.log(res, "登录失败");
          this.loading = false;
          emit("log", {
            level: "Trace",
            message: "clicklogin login failed: false",
          });
          type !== "link" && (await this.throwError(res));
        });
      // #endif
    },
    async throwError(res) {
      if (res.code === 613) {
        let msg = JSON.parse(res.msg);
        if (msg.data.type === "NEED_FORCE_PASSWORD") {
          let t = this;
          this.$alert(
            '<div style="padding: 0 50px">' + msg.data.tip + "</div>",
            "重置密码",
            {
              confirmButtonText: "确定",
              roundButton: true,
              center: true,
              dangerouslyUseHTMLString: true,
              callback: (action) => {
                if (action === "confirm") {
                  t.$nextTick(() => {
                    t.$refs.resetPwd.init({
                      secret: t.user.secret,
                      verifyCode: msg.data.verifyCode,
                      username: t.user.username,
                    });
                    t.openResetPwdDialog();
                  });
                }
              },
            }
          );
          return;
        }
      } else if (res.code == 602) {
        this.alert.error("服务器地址为空");
        this.$router.push({
          name: "setting",
          query: { date: new Date().getTime() },
          params: { item: "endpoint" },
        });
      } else if (res.code == 604) {
        this.alert.error("绑定码为空");
        this.$router.push({
          name: "setting",
          query: { date: new Date().getTime() },
          params: { item: "client" },
        });
      } else if (res.code == 603) {
        this.alert.error("域名解析失败");
      } else if (res.code == 607 || res.code == 608 || res.code == 609) {
        this.alert.error("认证失败");
      } else if (res.code == 601) {
        let msg = JSON.parse(res.msg);
        if (msg.code === "APP_FAILED") {
          this.user.remember_me = false;
          this.user.auto_connect = false;
          this.user.password = "";
        }
        this.alert.error(msg.data.errorMsg);
      } else if (res.code === 606 || res.code === 610) {
        this.alert.error("连接超时");
      } else {
        // 绑定码不匹配,登录失败,清空绑定码
        this.user.secret = null;
        let users = {};
        users[this.tenant.code] = this.user;
        invoke("patch", {
          value: {
            users: users,
          },
        }).then((config) => {
          window.CONFIG = config;
        });
        this.alert.error("网络连接异常, 请稍后再试");
        // 如果是集群模式, 则切换节点
        if (await this.changeToNextClusterNode("connect_ctl")) {
          await this.initData(1, 1);
        }
        return;
      }
      await this.loadAuthStrategy();
    },
    // 集群模式下, 切换下一个节点
    async changeToNextClusterNode(position) {
      await emit("log", {
        level: "Debug",
        message:
            "Call changeToNextClusterNode: " +
            position +
            ", length: " +
            CONFIG.cluster_nodes.length +
            ", " +
            CONFIG.cluster_node_changed_times,
      });
      if (CONFIG.cluster_nodes && CONFIG.cluster_nodes.length > 1) {
        let length = CONFIG.cluster_nodes.length;
        // 所有节点都试过了
        if (CONFIG.cluster_node_changed_times > length) {
          return false;
        }

        let index = 0;
        for (const i in CONFIG.cluster_nodes) {
          index += 1;
          let node = CONFIG.cluster_nodes[i];
          if (
              CONFIG.node.ip === node.ip &&
              CONFIG.node.port === node.port &&
              CONFIG.node.spa_port === node.spa_port
          ) {
            break;
          }
        }
        let cluster_node_changed_times = CONFIG.cluster_node_changed_times + 1;
        window.CONFIG = await invoke("patch", {
          value: {
            cluster_node_changed_times: cluster_node_changed_times,
            node: CONFIG.cluster_nodes[index % length],
          },
        });
        await emit("log", {
          level: "Debug",
          message: "Change to next node: " + JSON.stringify(CONFIG.node),
        });
        return true;
      }
      return false;
    },
    // #if [includeCluster]
    // 是否显示集群设置取消按钮
    showClusterConfigDialogCancelBtn() {
      return CONFIG.cluster_config_url || CONFIG.cluster_external_config_url;
    },
    // 打开集群设置框
    openClusterConfigDialog() {
      // ['url','scheme','slash','host','port','path','query','hash']
      var parse_url =
        /^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;

      if (CONFIG.cluster_config_url) {
        var result = parse_url.exec(CONFIG.cluster_config_url);
        this.nodeConfig.internal.host = result[1] + "://" + result[3];
        // port
        if (result[4]) {
          this.nodeConfig.internal.host += ":" + result[4];
        }
      }

      if (CONFIG.cluster_external_config_url) {
        var result = parse_url.exec(CONFIG.cluster_external_config_url);
        this.nodeConfig.external.host = result[1] + "://" + result[3];
        // port
        if (result[4]) {
          this.nodeConfig.external.host += ":" + result[4];
        }
      }

      this.$store.commit("setClusterConfigVisible", true);
    },
    // 关闭集群设置框
    closeClusterConfigDialog() {
      this.$store.commit("setClusterConfigVisible", false);
    },
    // 更新集群地址
    async updateClusterConfigUrl() {
      this.loading = true;
      var config = {};
      if (this.nodeConfig.internal.host) {
        config["internal"] =
          this.nodeConfig.internal.host + this.nodeConfig.internal.uri;
      }

      if (this.nodeConfig.external.host) {
        config["external"] =
          this.nodeConfig.external.host + this.nodeConfig.external.uri;
      }

      invoke("plugin:cluster|reload_cluster_nodes", {
        config: config,
      })
        .then(async (config) => {
          this.loading = false;
          // 修改地址/恢复出厂设置删除原租户
          delete config.selected_tenant;
          window.CONFIG = config;
          this.$message({
            message: "保存成功",
            type: "success",
          });
          this.$store.commit("setClusterConfigVisible", false);

          await this.initData(1, 1);
        })
        .catch(() => {
          this.loading = false;
          this.$message({
            message: "服务器连接失败，请稍后重试",
            type: "error",
          });
        });
    },
    // #elif [includeSDP]
    showNodeDialogCancelBtn() {
      return (
        CONFIG.node &&
        CONFIG.node.host &&
        CONFIG.node.port &&
        CONFIG.node.spa_port
      );
    },
    // 打开节点设置框
    openNodeDialog() {
      if (CONFIG.cluster_nodes) {
        var ips = [];
        var ports = [];
        var spa_ports = [];
        for (const node of CONFIG.cluster_nodes) {
          this.node.host = node.host;
          if (node.ip) {
            if (ips.length === 0 || node.ip !== ips[0]) {
              ips.push(node.ip);
            }
          }
          ports.push(node.port);
          spa_ports.push(node.spa_port);
        }

        if (ips.length >= 1) {
          this.node.ip = ips.join(',');
        }
        this.node.port = ports.join(',');
        this.node.spa_port = spa_ports.join(',');
      } else {
        this.node = {
          host: "",
          ip: null,
          port: "",
          spa_port: "",
        };
      }

      this.$store.commit("setNodeVisible", true);
    },
    // 关闭节点设置框
    closeNodeDialog() {
      this.$store.commit("setNodeVisible", false);
    },
    // 更新节点信息
    updateNode() {
      let hostRegex = /^[\w-]+(\.[\w-]+)*\.[a-zA-Z]{2,}$/;
      let portRegex = /^(?:0|[1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])(?:,(?:0|[1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))*$/;
      let ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])(?:,(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9]))*$/;
      if (!this.node.host) {
        this.alert.error("请输入域名");
        return;
      }
      if (!hostRegex.test(this.node.host)) {
        this.alert.error("请输入正确的域名");
        return;
      }
      if (!this.node.port) {
        this.alert.error("请输入端口");
        return;
      }
      if (!this.node.spa_port) {
        this.alert.error("请输入单包认证端口");
        return;
      }
      if (!portRegex.test(this.node.port)) {
        this.alert.error("请输入正确的端口");
        return;
      }
      if (!portRegex.test(this.node.spa_port)) {
        this.alert.error("请输入正确的单包认证端口");
        return;
      }
      if (this.node.ip && !ipRegex.test(this.node.ip)) {
        this.alert.error("请输入正确的IP");
        return;
      }

      let nodes = [];
      let ports = this.node.port.split(",");
      let spa_ports = this.node.spa_port.split(",");
      if (this.node.ip) {
        let ips = this.node.ip.split(",");
        let ports = this.node.port.split(",");
        let spa_ports = this.node.spa_port.split(",");

        if (ports.length !== spa_ports.length) {
          this.alert.error("端口与SPA端口数量不匹配");
          return;
        }

        // 只有一个IP
        if (ips.length === 1) {
          for (let index in ports) {
            nodes.push({
              host: this.node.host,
              ip: ips[0],
              port: parseInt(ports[index]),
              spa_port: parseInt(spa_ports[index]),
            });
          }
        } else {
          if (ips.length !== ports.length) {
            this.alert.error("端口与IP地址数量不匹配");
            return;
          }
          for (let index in ips) {
            nodes.push({
              host: this.node.host,
              ip: ips[index],
              port: parseInt(ports[index]),
              spa_port: parseInt(spa_ports[index]),
            });
          }
        }
      } else {
        if (ports.length !== spa_ports.length) {
          this.alert.error("端口与SPA端口数量不匹配");
          return;
        }
        for (let index in ports) {
          nodes.push({
            host: this.node.host,
            ip: null,
            port: parseInt(ports[index]),
            spa_port: parseInt(spa_ports[index]),
          });
        }
      }

      invoke("plugin:sdp|set_nodes", {
        clusterNodes: nodes,
      })
        .then(async (config) => {
          window.CONFIG = config;
          this.$message({
            message: "保存成功",
            type: "success",
          });
          await invoke("plugin:sdp|logout");
          this.$store.commit("setNodeVisible", false);
          this.reload();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // #endif
    openForgetPwdDialog() {
      this.showForgetPwdDialog = true;
      this.$refs.forgetPwd.init();
    },
    closeForgetPwdDialog() {
      this.showForgetPwdDialog = false;
    },
    openResetPwdDialog() {
      this.showResetPwdDialog = true;
    },
    closeResetPwdDialog() {
      this.showResetPwdDialog = false;
    },
    // 重置/修改密码回调函数
    resetOrForgetPwdCallback(successMsg) {
      this.alert.success(successMsg);
      // 清理密码
      let users = {};
      // 未勾选记住密码, 删除密码属性
      this.user.password = null;
      this.user.remember_me = false;
      this.user.auto_connect = false;
      users[this.tenant.code] = { ...this.user };
      invoke("patch", {
        value: {
          users: users,
        },
      })
        .then((config) => {
          window.CONFIG = config;
        })
        .finally(() => {
          this.reload();
        });
    },
    // 切换登录方式
    changeLoginMethod(type) {
      this.$refs.pollLoginMethod && this.$refs.pollLoginMethod.cancel();
      this.$refs.unPollLoginMethod &&
        this.$refs.unPollLoginMethod.cancel &&
        this.$refs.unPollLoginMethod.cancel();
      this.$refs.qrcode && this.$refs.qrcode.cancel();
      this.user.login_method = type;
    },

    // 点击图标切换扫码
    cutMethods(res) {
      res == "pwd" && this.$refs.qrcode && this.$refs.qrcode.cancel();
      this.user.login_method = res;
    },
    // 获取轮播图
    slideshowFun() {
      getSlideshow().then((result) => {
        console.log("result:获取轮播图 ", result);
        this.AllBanners = result.data?.loginBanners.split(",");
        if (this.AllBanners && this.AllBanners.length === 1) {
          this.position = "none";
        } else {
          this.position = "";
        }
      });
    },
    // 点击关闭回到首个认证方式
    closeEvent() {
      this.$refs.pollLoginMethod && this.$refs.pollLoginMethod.cancel();
      this.$refs.unPollLoginMethod &&
        this.$refs.unPollLoginMethod.cancel &&
        this.$refs.unPollLoginMethod.cancel();
      if (this.mfaContains("pwd")) {
        this.user.login_method = "pwd";
      } else if (this.mfaContains("sms_otp")) {
        this.user.login_method = "sms_otp";
      } else if (this.mfaContains("qrcode")) {
        this.user.login_method = "qrcode";
      } else {
        this.mfaMethods.length &&
          (this.user.login_method = this.mfaMethods[0].method.code);
      }
    },
    /**
     * MFA登录回调函数
     *
     * @param request {requestId: "<请求ID>", type: "<认证类型>"}
     * @param user 用户信息, 可选
     */
    mfaLoginCallback(request, user) {
      if (request.requestId) {
        mfaRequestId(request.requestId)
          .then((res) => {
            this.user.username = res.data.username;
            this.user.authResultId = this.authResultId;
            this.clickLogin(this.user.username, "mfa", false);
          })
          .catch((error) => {
            emit("log", {
              level: "Error",
              message: "mfa request result: " + JSON.stringify(error),
            });
          });
      } else {
        this.user.username = user.username;
        this.user.password = user.password;
        // this.user.remember_me=name.remember_me
        // this.user.auto_connect=name.auto_connect
        this.user.authResultId = this.authResultId;
        this.clickLogin(this.user.username, "normal", false);
      }
    },
    /**
     * 认证请求获取失败回调函数
     */
    generateRequestIdFailCallback(error) {
      console.log(error, "generateRequestFailed");

      // 取消加载状态
      this.loading = false;
      // 给出提示
      this.alert.error(error?.data?.errorMsg || "登录失败，请重试");

      // 认证策略请求过期
      if (
        error &&
        error.data &&
        error.data.messageKey === "MFA.MFA.AUTH.TIMEOUT"
      ) {
        // this.$alert("策略已过期,请重新获取!", "过期提示", {
        //   confirmButtonText: "确定",
        //   roundButton: true,
        //   center: true,
        //   callback: () => {
        //     this.reload();
        //   },
        // });
        this.reload();
      }
      // 密码错误
      else if (
        error &&
        error.data &&
        error.data.messageKey === "MFA.USERNAME.OR.PASSWORD.ERROR"
      ) {
        this.user.password = "";
        this.user.remember_me = false;
        this.user.auto_connect = false;
      }
    },
    /**
     * MFA策略禁止登录
     */
    mfaDenyLogin() {
      this.$alert("禁止操作", "错误提示", {
        confirmButtonText: "确定",
        roundButton: true,
        showClose: false,
        center: true,
        callback: () => {
          this.reload();
        },
      });
    },
    /**
     * 刷新页面
     */
    refresh() {
      // #if [includeCluster]
      // 刷新一次节点列表
      var config = {};
      if (this.nodeConfig.internal.host) {
        config["internal"] =
          this.nodeConfig.internal.host + this.nodeConfig.internal.uri;
      }

      if (this.nodeConfig.external.host) {
        config["external"] =
          this.nodeConfig.external.host + this.nodeConfig.external.uri;
      }

      invoke("plugin:cluster|reload_cluster_nodes", {
        config: config,
      }).finally(() => {
        this.reload();
      });
      // #else
      this.reload();
      // #endif
    },
    // 将认证方式转为3个一组的二维数组
    groupOfThrees(array) {
      const result = [];
      for (let i = 0; i < array.length; i += 3) {
        result.push(array.slice(i, i + 3));
      }
      return result;
    },
    // 恢复出厂设置
    resetSettings() {
      this.$confirm("是否恢复出厂设置？", "恢复出厂设置", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        roundButton: true,
        center: true,
        closeOnClickModal: false,
        closeOnPressEscape: false,
      })
        .then(() => {
          delete window.TMP_USERS;
          invoke("reset")
            .then((config) => {
              window.CONFIG = config;
              this.resetState();
              this.$message({
                type: "success",
                message: "恢复出厂设置成功",
              });
              this.reload();
            })
            .catch(() => {
              this.$message({
                type: "error",
                message: "恢复出厂设置失败",
              });
            });
        })
        .catch(() => {});
    },
    // 打开关于我们
    openAboutUsDialog() {
      this.$store.commit("setAboutUsVisible", true);
    },
    // 关闭关于我们
    closeAboutUsDialog() {
      this.$store.commit("setAboutUsVisible", false);
    },
    //检查更新
    async check() {
      this.loading_text = "检查中";
      this.loading = true;
      let response = await this.checkUpdate(true, 5000);

      if (response && response.shouldUpdate) {
        this.$nextTick(() => {
          this.$refs.updater.init(response);
        });
      }

      this.loading = false;
      this.loading_text = "正在退出...";
    },
    // 复制ID
    copyDeviceId() {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard
          .writeText(window.DEVICE_ID)
          .then(() => {
            this.alert.success("已复制到剪贴板");
          })
          .catch((error) => {
            this.alert.error("复制失败：" + error);
          });
      } else {
        // 如果不支持 navigator.clipboard，使用 document.execCommand
        const textarea = document.createElement("textarea");
        textarea.value = window.DEVICE_ID;
        textarea.style.position = "absolute";
        textarea.style.left = "-9999px"; // 隐藏在视图之外
        document.body.appendChild(textarea);
        textarea.select(); // 选中内容

        try {
          document.execCommand("copy");
          this.alert.success("已复制到剪贴板");
        } catch (error) {
          this.alert.error("复制失败：" + error);
        } finally {
          // 移除临时创建的 textarea
          document.body.removeChild(textarea);
        }
      }
    },
    // 关闭切换租户提示
    closeChangeTenantTip() {
      invoke("patch", {
        value: {
          show_change_tenant_tip: false,
        },
      }).then((config) => {
        window.CONFIG = config;
        this.setCloseTenantTip();
      });
    },
  },
};
</script>

<style lang="less" scoped>
@mainColor: #f9780c;

.app-content {
  background: url("/assets/login-bg.png") no-repeat;
  height: 652px;
}

.gradient {
  background-image: linear-gradient(
    to right,
    @mainColor,
    lighten(@mainColor, 18%)
  );
}

//:deep(.el-tabs__item:nth-child(n + 4)) {
//  display: none;
//}

//:deep(#tab-fingerprint,
//#tab-email_otp,
//#tab-voice,
//#tab-nativepass,
//#tab-auth_code,
//#tab-mobileclick,
//#tab-ft_c200,
//#tab-anshu_a5,
//#tab-escuotp1,
//#tab-et_z203,
//#tab-ukey,
//#tab-ukey_wq) {
//  padding: 0;
//}

:deep(#tab-pwd, #tab-sms_otp) {
  padding: 0 20px 0 0;
}

.login_nav {
  width: 487px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  // box-shadow: 2px 2px 4px rgba(42, 41, 41, 0.5);
  // border-radius: 15px 0 0 15px;

  .select-tenant {
    width: max-content;
    height: 60px;
    margin-top: 20px;
    font-size: 15px;
    z-index: 1;
    display: flex;
    align-items: center;

    .tenant-img {
      background: url("/assets/change_tenant.png") no-repeat center center;
      width: 80px;
      height: 30px;
      text-align: right;
      padding-left: 15px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
  }
}

.login_left {
  width: 487px;
  height: 654px;
  // background: url("/assets/login_Logo.png") no-repeat;

  .login_logo {
    padding-top: 20px;
    padding-left: 38px;
    display: flex;
    align-items: center;

    h1 {
      margin-left: 10px;
      font-size: 23px;
      font-weight: normal;
      line-height: 30px;
    }
  }
}

:deep(.is-active) {
  color: #343539 !important;
  font-size: 18px;
}

:deep(.el-tabs__nav-wrap::after) {
  width: 0;
}

:deep(.el-tabs__active-bar) {
  background-color: #f9780c;
  max-width: 72px !important;
}

:deep(.el-tabs__item) {
  color: #939497;
  padding: 0;
}

:deep(.el-tabs) {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

:deep(.el-tabs__header) {
  height: 39px;
}

:deep(.el-tabs__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login {
  flex: 1;
  padding: 40px 65px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 354px;

  .h4 {
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 1px;
    font-family: Microsoft YaHei;
    color: rgba(0, 0, 0, 0.75);
    margin: 0;
  }

  .icon {
    width: 100px;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .login-form-bottom {
    margin: 15px 0 0 3px;
    clear: both;
    overflow: visible;
    // height: 40px;
    // background: #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .pull-left {
    //     // float: left;

    //     label {
    //         font-weight: bold;
    //     }
    // }

    // .pull-right {
    //     float: right;
    // }
    :deep(.el-checkbox) {
      margin-right: 0;
    }
  }

  .login-bottom-item {
    // margin-top: 20px;
    // vertical-align: top;
    color: @mainColor;
  }

  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: @mainColor;
    border-color: @mainColor;
  }

  :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: @mainColor;
  }

  //用户名、密码输入框
  .input_wrapper {
    // margin-top: 46px;

    .login_icon {
      color: #bfbfbf;
      font-size: 22px;
    }

    .inputItem {
      text-indent: 0.6em;
      position: relative;
      margin-top: 1.42rem;
      // margin-top: 1rem;
      // border-radius: 30px;
      border-bottom: 1px solid #d1d3db;

      .clud {
        display: flex;

        span {
          width: 40px;
          font-size: 14px;
          cursor: pointer;
          color: @mainColor;
        }
      }

      &:hover {
        border-bottom: 1px solid #f9780c;
      }

      .inputVCode {
        width: 140px;
        height: 42px;
        border: none;
        // border-radius: 3px;
        outline: none;

        &::placeholder {
          color: #aaaaaa;
        }
      }

      .input-pwd {
        width: 290px;
        padding: 0 !important;

        :deep(.el-input__wrapper) {
          box-shadow: none;
          padding: 1px;
        }
      }

      .verifyImg {
        width: 100px;
        height: 40px;
        margin-left: 40px;
        cursor: pointer;
      }

      &:last-child {
        // margin-bottom: 1.42rem;
      }

      // &::after {
      //     content: "";
      //     position: absolute;
      //     height: 1px;
      //     width: 100%;
      //     bottom: 0;
      //     transform: scaleY(0.5);
      //     background: #bfbfbf;
      // }

      &.last::after {
        height: 0;
      }

      img {
        width: 1.2rem;
        height: 1.2rem;
      }

      .inputContent {
        padding: 0.7rem;
        width: 240px;

        img {
          width: 1.2rem;
          height: 1.2rem;
        }

        input {
          font-size: 14px;
          border: none;
          outline: none;
          width: 240px;
          letter-spacing: 0;
          background-color: #fff;

          &::placeholder {
            color: #aaaaaa;
          }
        }

        .inputContent-select {
          font-size: 14px;
          border: none;
          outline: none;
          width: 240px;
          letter-spacing: 0;

          :deep(.el-input__wrapper) {
            font-size: 14px;
            box-shadow: none !important;
            outline: none;
            width: 240px;
            letter-spacing: 0;
            padding: 0;
          }
        }
      }
    }

    .focus {
      color: @mainColor;
    }

    .focus::after {
      background: @mainColor;
    }

    .username_error::after {
      content: "用户名不能为空";
      background: red;
      transform: scaleY(1);
      font-size: 0.72rem;
      color: red;
      text-align: right;
      line-height: 1.6rem;
    }

    .password_error::after {
      content: "密码不能为空";
      background: red;
      transform: scaleY(1);
      font-size: 0.72rem;
      color: red;
      text-align: right;
      line-height: 1.6rem;
    }
  }

  //登录按钮
  button {
    .gradient;
    border: none;
    outline: none;
    width: 100%;
    position: relative;
    color: white;
    height: 40px;
    line-height: 40px;
    margin-top: 5%;
    font-size: 1rem;
    overflow: hidden;
    letter-spacing: 0.3rem;

    text-align: center;
    outline: 0;
    border-radius: 5px;
  }

  .opt_button {
    height: 45px;
    cursor: pointer;
    border-radius: 30px;
    // margin-top: 20px;
  }

  .forbidden {
    border-radius: 30px;
    height: 45px;
    background: tint(@mainColor, 70%);
    color: fade(white, 80%);
    cursor: not-allowed;
  }

  .auto_quickLogin {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 0.72rem;
    margin-top: 10%;
    color: #333333;

    .autoLogin_wrapper {
      .autoLogin {
        display: inline-block;
        width: 12px;
        height: 12px;
        background-image: url("/assets/images/unchosen.png");
        background-size: 12px 12px;
        background-repeat: no-repeat;
        background-position: center;
        vertical-align: middle;
      }

      input[type="checkbox"]:checked + .autoLogin {
        background-image: url("/assets/images/chosen.png");
      }

      span {
        margin-left: 0.2rem;
      }
    }

    .quickLogin {
      margin-right: 0.2rem;
      color: #41484e;
    }
  }

  //注册按钮
  .registerBtn {
    background: white;
    color: @mainColor;
    border: 1px solid @mainColor;
    margin-top: 5%;

    &:active {
      background: fade(@mainColor, 8%);
    }
  }

  //短信验证码登录按钮
  .phoneLogin {
    .registerBtn();
    letter-spacing: 0.1rem;
  }

  /************************忘记密码/注册/用户协议*****************************/

  .forgetPwd_register_protocol {
    display: flex;
    flex-direction: column;
    width: 11.2rem;
    margin: 3.6rem auto;
    font-size: 0.7rem;

    .forgetPwd_register {
      display: flex;
      flex-direction: row;
      justify-content: center;
      font-weight: 600;

      .separator {
        color: #aaaaaa;
      }
    }

    .space_between {
      justify-content: space-between;
    }

    .protocol {
      color: #aaaaaa;
      font-size: 0.7rem;
      margin-top: 0.6rem;
      position: static;

      b {
        color: black;
      }
    }
  }

  //没有第三方登录时
  .footer {
    position: fixed;
    bottom: 1.4%;
    left: 24.3%;
    right: 24.3%;
  }

  /*********************第三方登录******************/

  .otherLoginWays {
    width: 80%;
    position: fixed;
    bottom: 6.2%;
    left: 10%;
    right: 10%;
    text-align: center;

    .otherWayTextWrapper {
      line-height: 0;
      color: #666666;

      &:before,
      &:after {
        position: absolute;
        background: #cccccc;
        content: "";
        height: 1px;
        width: 30%;
      }

      &:before {
        left: 0;
      }

      &:after {
        right: 0;
      }

      .otherWayText {
        font-size: 0.8rem;
        color: #bbbbbb;
      }
    }

    .icons {
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      margin-top: 1.2em;

      img {
        width: 1.88rem;
        height: 1.88em;
      }
    }
  }

  .protocol {
    position: fixed;
    bottom: 2%;
    left: 2%;
    right: 2%;
    font-size: 0.72em;
    margin-top: 8%;
    color: #333333;

    span {
      color: #2986de;
    }
  }

  .effect {
    color: rgb(244, 165, 106);
  }

  .text {
    font-size: 14px;
  }

  &_title {
    height: 100px;
    text-align: center;
    display: flex;
    padding: 0 65px;
    align-items: center;

    &_el {
      font-weight: 600;
      font-size: 13px;
      color: #d1d3db;
      // margin-bottom: 2px;
      width: 80px;
      // text-align: right;

      &_eles {
        font-weight: normal;
        color: #000;
        font-size: 14px;
      }
    }

    &_img {
      margin-top: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  :deep(.mfa-content) {
    margin: 21px 0;
    // height: 184px;
  }
}

.for {
  padding: 0 30px;

  &_title {
    font-weight: 600;
    padding: 0 10px 10px;
    width: 100%;
    border-bottom: 1px solid rgb(96, 96, 96);
    display: flex;
    flex-direction: column;
  }

  &_title:hover {
    color: #f9780c;
  }

  .ipstyle {
    font-weight: normal;
    font-size: 13px;
    color: #aaaaaa;
  }
}

.activeso {
  color: #f9780c;
}

:deep(.el-carousel__button) {
  display: none;
}

.el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-carousel__arrow) {
  width: 30px;
  height: 30px;
  background-color: #fff;
  color: #8b8b8f;

  &:hover {
    color: #f9780c;
  }
}

.approveTab {
  :deep(.el-carousel__indicators--horizontal) {
    display: none;
  }
}

.alert_error {
  position: fixed;
  z-index: 999;
  font-size: 14px;
  border: 1px solid #f54a45;
  left: 50%;
  top: 23px;
  transform: translate(-50%, 0%);
  height: 54px;

  :deep(.el-alert__title) {
    color: #5c5b60;
  }
}

:deep(.is-customed) {
  color: #0e97ff !important;
  top: 18px !important;
}

.el-select .el-input {
  border-color: transparent;
  box-shadow: none;
}

.endpointUrl {
  padding: 15px 20px;

  :deep(.el-input__wrapper) {
    box-shadow: none !important;
    outline: none;
    border-bottom: 1px solid #ddd;
    border-radius: inherit;
  }
}

:deep(.el-dialog__title) {
  color: #000;
  font-size: 18px;
  font-weight: 500;
}

:deep(.el-carousel__arrow--right) {
  right: 0;
  top: 36px;
}

:deep(.el-carousel__arrow--left) {
  left: 0;
  top: 36px;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: none !important;
  border-radius: 20px;
}

// /deep/.el-input .el-input__wrapper.is-focus{
//     box-shadow: none!important;
// }
:deep(.el-select .el-input.is-focus .el-input__wrapper) {
  border-color: #dcdfe6 !important;
  box-shadow: none !important;
}

.el-select {
  width: 95%;
}

.carouselCode {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 280px;
  cursor: pointer;

  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
    width: 100%;
    margin-top: 5px;
    text-align: center;
  }
}

.carouselCode:nth-child(2) {
  margin: 0 10px;
}

.carouselClass {
  height: 530px;
  padding: 6px 0 0 38px;
  box-sizing: border-box;

  .el-carousel {
    height: 100%;
    border-radius: 8px 0 0 8px;
  }

  :deep(.el-carousel__container) {
    height: 100%;
  }

  :deep(.el-carousel__indicators--horizontal) {
    bottom: 10px;

    .el-carousel__indicator {
      background: #ffffff;
      border-radius: 50%;
      width: 10px;
      height: 10px;
      padding: 0;
      margin-right: 10px;
    }

    // .el-carousel__indicators--horizontal{
    //   bottom: 24px;
    // }
    .el-carousel__indicator.is-active {
      background: #de3a3a;
    }
  }
}

.nav_right {
  height: 530px;
  padding: 8px 38px 0 0;
  box-sizing: border-box;
  // box-shadow: 2px 2px 6px 4px rgba(42, 41, 41, 0.5);
  // border-radius: 15px 0 0 15px;
  &_box {
    height: 100%;
    box-shadow: 2px 2px 12px 2px rgba(42, 41, 41, 0.1);
    // border-radius: 15px 0 0 15px;
    background: #fff;
    position: relative;
    border-radius: 0 8px 8px 0;
  }

  &_qrcode,
  &_pwd {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
  }

  &_icon {
    position: absolute;
    top: 20px;
    left: 20px;
    cursor: pointer;
  }
}

.remould_select {
  cursor: pointer;
}

.dropdownClass {
  max-width: 150px;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
}

.is_active {
  display: inline-block;
  width: 19px;
  height: 22px;
}

.tenant_input {
  border: 0;
  width: 80px;
  outline: none;
  box-shadow: none;
  color: #606266;
  white-space: nowrap; /* 指定不换行 */
  overflow: hidden; /* 指定超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */

  pointer-events: none;
}
</style>
