use winreg::{
    enums::{RegType, HKEY_LOCAL_MACHINE},
    types::FromRegValue,
    RegKey,
};

#[allow(dead_code)]
#[derive(Debug, Default)]
pub struct ShareFolder {
    ca_timeout: u32,
    csc_flags: u32,
    max_uses: u64,
    permissions: u32,
    path: String,
    remark: Option<String>,
    share_name: String,
    share_type: u32,
}

#[allow(dead_code)]
pub fn share_folders() -> Vec<ShareFolder> {
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let Ok(shares) = hklm.open_subkey("SYSTEM\\CurrentControlSet\\Services\\LanmanServer\\Shares")
    else {
        return vec![];
    };

    shares
        .enum_values()
        .filter(|result| {
            if let Ok((_, value)) = result {
                return value.vtype == RegType::REG_MULTI_SZ;
            }
            false
        })
        .map(|result| {
            let (_, value) = result.unwrap();
            let values: Vec<String> = FromRegValue::from_reg_value(&value).unwrap();
            let mut share_folder = ShareFolder::default();
            for entry in values {
                if let Some((key, value)) = entry.split_once('=') {
                    match key.trim() {
                        "CATimeout" => {
                            share_folder.ca_timeout = value.trim().parse().unwrap_or_default();
                        }
                        "CSCFlags" => {
                            share_folder.csc_flags = value.trim().parse().unwrap_or_default();
                        }
                        "MaxUses" => {
                            share_folder.max_uses = value.trim().parse().unwrap_or_default();
                        }
                        "Path" => share_folder.path = value.trim().to_owned(),
                        "Permissions" => {
                            share_folder.permissions = value.trim().parse().unwrap_or_default();
                        }
                        "Remark" if !value.trim().is_empty() => {
                            share_folder.remark = Some(value.trim().to_owned());
                        }
                        "ShareName" => {
                            share_folder.share_name = value.trim().to_owned();
                        }
                        "Type" => {
                            share_folder.share_type = value.trim().parse().unwrap_or_default();
                        }
                        _ => {}
                    }
                }
            }
            share_folder
        })
        .collect()
}
