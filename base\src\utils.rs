pub(crate) mod ua {
    use serde_json::Value;

    #[inline]
    pub fn generate_ua(env: &Value) -> String {
        // 定义UA
        format!(
            "Mozilla/5.0 ({}; {} Build/{}) {}/{} ",
            env["operatingSystem"].as_str().unwrap().to_lowercase(),
            env["name"].as_str().unwrap(),
            env["systemVersion"].as_str().unwrap(),
            "SDP",
            env["clientVersion"].as_str().unwrap()
        )
    }
}

pub mod net {
    use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

    use pnet_packet::ip::IpNextHeaderProtocol;

    #[derive(Clone, Copy)]
    pub enum Version {
        V4,
        V6,
    }

    #[derive(PartialEq, Eq, Copy, Clone, Debug, Hash)]
    pub struct Addr(pub IpAddr, pub u16);

    /// 仅包含上层协议为 ICMP, ICMPv6, TCP, UDP
    #[derive(Clone)]
    pub struct IpPacket {
        pub version: Version,
        pub next_protocol: u8,
        pub src: IpAddr,
        pub dest: IpAddr,
        /// 端口或标识
        pub identifier: Option<u16>,
        pub ports: Option<(u16, u16)>,
        pub ihl: usize,

        pub data: Vec<u8>,
    }

    impl IpPacket {
        pub fn payload(&self) -> &[u8] {
            &self.data[self.ihl..]
        }

        pub fn address(&self) -> Option<(Addr, Addr)> {
            match self.next_protocol {
                1 | 58 => {
                    let identifier = self.identifier.clone().unwrap();
                    // 访问目标地址
                    let access_addr = Addr(self.dest, identifier);

                    // 客户端访问服务器本地地址
                    let client_local_addr = Addr(self.src, identifier);

                    Some((access_addr, client_local_addr))
                }
                6 | 17 => {
                    let (src_port, dest_port) = self.ports.clone().unwrap();
                    // 访问目标地址
                    let access_addr = Addr(self.dest, dest_port);

                    // 客户端访问服务器本地地址
                    let client_local_addr = Addr(self.src, src_port);

                    Some((access_addr, client_local_addr))
                }
                _ => None,
            }
        }

        pub fn set_dest(&mut self, dest: IpAddr) {
            self.dest = dest;
            match self.version {
                Version::V4 => self.data[16..20].copy_from_slice(&ip_to_bytes(dest)),
                Version::V6 => self.data[24..40].copy_from_slice(&ip_to_bytes(dest)),
            }
        }

        pub fn set_src(&mut self, src: IpAddr) {
            self.src = src;
            match self.version {
                Version::V4 => self.data[12..16].copy_from_slice(&ip_to_bytes(src)),
                Version::V6 => self.data[8..24].copy_from_slice(&ip_to_bytes(src)),
            }
        }

        pub fn update_ip_checksum(&mut self) {
            if let Version::V4 = self.version {
                let checksum = pnet_packet::util::checksum(&self.data[..self.ihl], 5);
                self.data[10..=11].copy_from_slice(&checksum.to_be_bytes());
            }
        }

        pub fn set_src_port(&mut self, port: u16) {
            match self.next_protocol {
                1 | 58 => {
                    self.data[self.ihl + 4..self.ihl + 6].copy_from_slice(&port.to_be_bytes());
                }
                6 => {
                    self.data[self.ihl..self.ihl + 2].copy_from_slice(&port.to_be_bytes());
                }
                17 => {
                    self.data[self.ihl..self.ihl + 2].copy_from_slice(&port.to_be_bytes());
                }
                _ => {}
            }
        }

        pub fn set_dest_port(&mut self, port: u16) {
            match self.next_protocol {
                1 | 58 => {
                    self.data[self.ihl + 4..self.ihl + 6].copy_from_slice(&port.to_be_bytes());
                }
                6 => {
                    self.data[self.ihl + 2..self.ihl + 4].copy_from_slice(&port.to_be_bytes());
                }
                17 => {
                    self.data[self.ihl + 2..self.ihl + 4].copy_from_slice(&port.to_be_bytes());
                }
                _ => {}
            }
        }

        pub fn update_next_protocol_checksum(&mut self) {
            match self.version {
                Version::V4 => match self.next_protocol {
                    1 => {
                        let checksum = pnet_packet::util::checksum(&self.data[self.ihl..], 1);
                        self.data[self.ihl + 2..=self.ihl + 3]
                            .copy_from_slice(&checksum.to_be_bytes());
                    }
                    6 => {
                        if let (IpAddr::V4(src), IpAddr::V4(dest)) = (self.src, self.dest) {
                            let checksum = pnet_packet::util::ipv4_checksum(
                                self.payload(),
                                8,
                                &[],
                                &src,
                                &dest,
                                IpNextHeaderProtocol(self.next_protocol),
                            );
                            self.data[self.ihl + 16..self.ihl + 18]
                                .copy_from_slice(&checksum.to_be_bytes());
                        }
                    }
                    17 => {
                        if let (IpAddr::V4(src), IpAddr::V4(dest)) = (self.src, self.dest) {
                            let checksum = pnet_packet::util::ipv4_checksum(
                                self.payload(),
                                3,
                                &[],
                                &src,
                                &dest,
                                IpNextHeaderProtocol(self.next_protocol),
                            );
                            self.data[self.ihl + 6..self.ihl + 8]
                                .copy_from_slice(&checksum.to_be_bytes());
                        }
                    }
                    _ => {}
                },
                Version::V6 => {
                    if let (IpAddr::V6(src), IpAddr::V6(dest)) = (self.src, self.dest) {
                        let skipword = match self.next_protocol {
                            58 => 1,
                            6 => 9,
                            17 => 4,
                            _ => 0,
                        };
                        let checksum = pnet_packet::util::ipv6_checksum(
                            self.payload(),
                            skipword,
                            &[],
                            &src,
                            &dest,
                            IpNextHeaderProtocol(self.next_protocol),
                        );
                        match self.next_protocol {
                            58 => {
                                self.data[self.ihl + 2..self.ihl + 4]
                                    .copy_from_slice(&checksum.to_be_bytes());
                            }
                            6 => {
                                self.data[self.ihl + 16..self.ihl + 18]
                                    .copy_from_slice(&checksum.to_be_bytes());
                            }
                            17 => {
                                self.data[self.ihl + 6..self.ihl + 8]
                                    .copy_from_slice(&checksum.to_be_bytes());
                            }
                            _ => {}
                        }
                    }
                }
            }
        }
    }

    /// 解析IP数据包
    pub fn parse_ip_packet(mut buf: Vec<u8>) -> Option<IpPacket> {
        if buf.len() < 4 {
            log::error!("Invalid IP header length");
            return None;
        }
        let version = buf[0] >> 4;
        match version {
            4 => {
                if buf.len() < 20 {
                    log::error!("Invalid IPv6 packet length. Length less than 20. {:?}", buf);
                    return None;
                }

                // IP包总长度
                let total_length = u16::from_be_bytes([buf[2], buf[3]]) as usize;
                if total_length > buf.len() {
                    log::error!(
                        "Invalid IP packet length. expect: {}, actual: {}. {:?}",
                        total_length,
                        buf.len(),
                        buf
                    );
                    return None;
                }
                let next_protocol = buf[9];

                let src = IpAddr::V4(Ipv4Addr::new(buf[12], buf[13], buf[14], buf[15]));
                let dest = IpAddr::V4(Ipv4Addr::new(buf[16], buf[17], buf[18], buf[19]));

                let total_len = u16::from_be_bytes([buf[2], buf[3]]);
                buf.truncate(total_len as usize);
                let ihl = (buf[0] & 0x0f) as usize * 4;
                match next_protocol {
                    1 => {
                        if buf.len() < ihl + 4 {
                            log::error!("Invalid ICMP packet length {}. {:?}", buf.len(), buf);
                            return None;
                        }

                        let identifier = u16::from_be_bytes([buf[ihl + 4], buf[ihl + 5]]);
                        Some(IpPacket {
                            version: Version::V4,
                            next_protocol,
                            src,
                            dest,
                            identifier: Some(identifier),
                            ports: None,
                            ihl,
                            data: buf,
                        })
                    }
                    6 | 17 => {
                        let (protocol_min_len, protocol_name) = if next_protocol == 6 {
                            (20, "TCP")
                        } else {
                            (8, "UDP")
                        };
                        if buf.len() < ihl + protocol_min_len {
                            log::error!(
                                "Invalid {protocol_name} packet length {}. {:?}",
                                buf.len(),
                                buf
                            );
                            return None;
                        }

                        let dest_port = u16::from_be_bytes([buf[ihl + 2], buf[ihl + 3]]);
                        let src_port = u16::from_be_bytes([buf[ihl], buf[ihl + 1]]);
                        Some(IpPacket {
                            version: Version::V4,
                            next_protocol,
                            src,
                            dest,
                            identifier: None,
                            ports: Some((src_port, dest_port)),
                            ihl,
                            data: buf,
                        })
                    }
                    _ => {
                        log::warn!("Unsupported protocol: {}", next_protocol);
                        None
                    }
                }
            }
            6 => {
                if buf.len() < 40 {
                    log::error!("Invalid IPv6 packet length. Length less than 40. {:?}", buf);
                    return None;
                }
                // 负载长度
                let payload_length = u16::from_be_bytes([buf[4], buf[5]]) as usize;
                if buf.len() < payload_length + 40 {
                    log::error!(
                        "Invalid IPv6 packet length. expect: {}, actual: {}. {:?}",
                        payload_length + 40,
                        buf.len(),
                        buf
                    );
                    return None;
                }

                let next_protocol = buf[6];
                match next_protocol {
                    58 => {
                        if buf.len() < 40 + 4 {
                            log::error!("Invalid ICMP packet length {}. {:?}", buf.len(), buf);
                            return None;
                        }

                        let identifier = u16::from_be_bytes([buf[40 + 4], buf[40 + 5]]);

                        let src = slice_to_ip(&buf[8..24]).unwrap();
                        let dest = slice_to_ip(&buf[24..40]).unwrap();

                        Some(IpPacket {
                            version: Version::V6,
                            next_protocol,
                            src,
                            dest,
                            identifier: Some(identifier),
                            ports: None,
                            ihl: 40,
                            data: buf,
                        })
                    }
                    6 | 17 => {
                        let (protocol_min_len, protocol_name) = if next_protocol == 6 {
                            (20, "TCP")
                        } else {
                            (8, "UDP")
                        };
                        if buf.len() < 40 + protocol_min_len {
                            log::error!(
                                "Invalid {protocol_name} packet length {}. {:?}",
                                buf.len(),
                                buf
                            );
                            return None;
                        }

                        let dest_port = u16::from_be_bytes([buf[40 + 2], buf[40 + 3]]);
                        let src_port = u16::from_be_bytes([buf[40], buf[40 + 1]]);

                        let src = slice_to_ip(&buf[8..24]).unwrap();
                        let dest = slice_to_ip(&buf[24..40]).unwrap();

                        Some(IpPacket {
                            version: Version::V6,
                            next_protocol,
                            src,
                            dest,
                            identifier: None,
                            ports: Some((src_port, dest_port)),
                            ihl: 40,
                            data: buf,
                        })
                    }
                    _ => {
                        log::warn!("Unsupported protocol: {}", next_protocol);
                        None
                    }
                }
            }
            _ => {
                log::error!("Invalid IP packet length. unkown version: {}", version);
                None
            }
        }
    }

    pub fn slice_to_ip(buf: &[u8]) -> Option<IpAddr> {
        match buf.len() {
            4 => Some(IpAddr::V4(Ipv4Addr::from(
                <&[u8] as TryInto<[u8; 4]>>::try_into(buf).unwrap(),
            ))),
            16 => Some(IpAddr::V6(Ipv6Addr::from(
                <&[u8] as TryInto<[u8; 16]>>::try_into(buf).unwrap(),
            ))),
            _ => None,
        }
    }

    pub fn ip_to_bytes(ip: IpAddr) -> Vec<u8> {
        match ip {
            IpAddr::V4(ip) => ip.octets().to_vec(),
            IpAddr::V6(ip) => ip.octets().to_vec(),
        }
    }
}

pub(crate) fn extract_len(buf: &[u8]) -> Option<(u8, u64)> {
    if buf.len() < 2 {
        return None;
    }

    let len = buf[1];

    if len & 0x80 == 0x80 {
        let len_len = len ^ 0x80;
        let mut len_bytes = [0; 8];
        if buf.len() < 2 + len_len as usize {
            return None;
        }

        len_bytes[8 - len_len as usize..].copy_from_slice(&buf[2..2 + len_len as usize]);

        Some((len_len + 1, u64::from_be_bytes(len_bytes)))
    } else {
        Some((1, len as u64))
    }
}
