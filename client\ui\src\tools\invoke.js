import { invoke } from "@tauri-apps/api/tauri";
import { alert } from "./alert";
import { listen } from "@tauri-apps/api/event";

export const request = (eventName, params) => {
  return new Promise((resolve, reject) => {
    invoke(eventName, params)
      .then((res) => {
        if (!res) {
          resolve(res);
          return;
        }
        if (res.code !== 200) {
          throw res.msg;
        }
        resolve(res);
      })
      .catch((e) => {
        alert.error(e.toString());
        reject(e);
      });
  });
};

// const cache = {}
export const rustRequestByEventResponse = (
  eventName,
  responseEventName,
  params
) => {
  return new Promise((resolve, reject) => {
    !(async () => {
      let unlisten = await listen(responseEventName || eventName, (event) => {
        unlisten();
        let data = JSON.parse(event.payload).data;
        resolve(data);
      });
      setTimeout(function () {
        unlisten();
        reject("timeout");
      }, 3000);
    })();
    invoke(eventName, params).catch((e) => {
      alert.error(e.toString());
      reject(e);
    });
  });
};
