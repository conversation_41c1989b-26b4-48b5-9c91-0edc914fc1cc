#!/bin/bash

echo "installing daemon service"
/usr/bin/oneidservice --register-service

echo "grant files permissions"
chown root:root /usr/bin/oneidcore
chmod +sx /usr/bin/oneidcore
chmod +x /usr/bin/oneidconfig
chmod +x /usr/bin/oneid_auth.sh

# 执行配置程序, 检查安装包内置配置与本地配置是否匹配
echo "prepare config"
/usr/bin/oneidconfig

# 执行完成后, 删除该程序
rm -f /usr/bin/oneidconfig

# 添加系統信任证书
echo "add system trusted root certificate"
cp /usr/lib/${PRODUCT_NAME}/_up_/attachments/oneid.crt /usr/local/share/ca-certificates
update-ca-certificates

# 添加浏览器信任证书
# https://superuser.com/questions/1772957/how-to-trust-a-self-signed-ssl-root-ca-in-chrome-on-debian-via-terminal
echo "add browser trusted root certificate"
add_browser_trusted_root_certificate() {
  for FILENAME in /home/<USER>
    USERNAME=$(echo ${FILENAME} | awk '{print substr($1, 7, 32)}')
    su - $USERNAME -c "mkdir -p ~/.pki/nssdb"
    su - $USERNAME -c "certutil -d \"sql:\$(getent passwd $USERNAME | cut -d: -f6)/.pki/nssdb\" -A -t \"CP,CP,\" -n OneID -i /usr/local/share/ca-certificates/oneid.crt"
  done
}

add_browser_trusted_root_certificate

echo "remove tmp files"
rm -rf /usr/lib/${PRODUCT_NAME}/_up_

install_svg_icons() {
  if [ -d "/usr/share/icons/hicolor/scalable/apps/" ]; then
    SVG_ICON="product_logo_256.svg"
    if [ -f "/opt/jingantech/oneid/${SVG_ICON}" ]; then
      cp "/opt/jingantech/oneid/${SVG_ICON}" "/usr/share/icons/hicolor/scalable/apps/oneid.svg"
    fi
    # Ubuntu 20.04.3(some machines) don't support 256x
    SVG_ICON="product_logo_128.svg"
    if [ -f "/opt/jingantech/oneid/${SVG_ICON}" ]; then
      cp "/opt/jingantech/oneid/${SVG_ICON}" "/usr/share/icons/hicolor/scalable/apps/oneid.svg"
    fi
  fi
}

copy_entry_file_to_desktop() {
  cp -f /usr/share/applications/com.jingantech.oneid.desktop "$1/jingantech-oneid.desktop"
  if [ $? -eq 0 ]; then
    chmod a+x "$1/jingantech-oneid.desktop"
    if [ ! -z "$2" ]; then
      TUID=$(id -u "$2")
      TGID=$(id -g "$2")
      if [ ! -z $TUID ] && [ ! -z $TGID ]; then
        chown "${TUID}:${TGID}" "$1/jingantech-oneid.desktop"
      fi
    fi
    # xdg-desktop-menu install "$1/jingantech-oneid.desktop" || true
  fi
}

install_entry_file_to_desktop() {
  if [ "$1" = "configure" ]; then
    if [ -d "/root/桌面" ]; then
      copy_entry_file_to_desktop "/root/桌面"
    elif [ -d "/root/Desktop" ]; then
      copy_entry_file_to_desktop "/root/Desktop"
    fi

    for FILENAME in /home/<USER>
      USERNAME=$(echo ${FILENAME} | awk '{print substr($1, 7, 32)}')
      if [ -f "${FILENAME}/.config/user-dirs.dirs" ]; then
        HOME=${FILENAME}
        . "${FILENAME}/.config/user-dirs.dirs"
        if [ ! -d "${XDG_DESKTOP_DIR}" ]; then
          mkdir -p "${XDG_DESKTOP_DIR}" >/dev/null 2>&1 || true
        fi
        copy_entry_file_to_desktop ${XDG_DESKTOP_DIR} "${USERNAME}"
      else
        if [ -d "${FILENAME}/桌面" ]; then
          copy_entry_file_to_desktop "${FILENAME}/桌面" "${USERNAME}"
        elif [ -d "${FILENAME}/Desktop" ]; then
          copy_entry_file_to_desktop "${FILENAME}/Desktop" "${USERNAME}"
        fi
      fi
    done
  fi
}

if [ -f "/etc/os-release" ]; then
  OS_PRETTY_NAME="$(cat /etc/os-release | grep PRETTY_NAME)"
  if [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i Kylin)" ] || [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i UnionTech)" ] || [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i UOS)" ]; then
    install_entry_file_to_desktop $1 || true
  fi
else
  install_entry_file_to_desktop $1 || true
fi

# Update cache of .desktop file MIME types. Non-fatal since it's just a cache.
update-desktop-database >/dev/null 2>&1 || true

install_svg_icons

# Kylin
if [ -f "/etc/os-release" ]; then
  LIMIT_FILE="/etc/dbus-1/conf/com.ksc.defender.limit"
  OS_PRETTY_NAME="$(cat /etc/os-release | grep PRETTY_NAME)"
  if [ ! -z "$(echo "${OS_PRETTY_NAME}" | grep -i Kylin)" ]; then
    if [ -f "$LIMIT_FILE" ]; then
      if ! grep -qE "key[0-9]+=/usr/bin/oneidcore" "$LIMIT_FILE"; then
        LAST_KEY=$(grep -oP '^key\K[0-9]+' "$LIMIT_FILE" | sort -n | tail -1)
        if [[ -n "$LAST_KEY" ]]; then
          NEW_KEY=$((LAST_KEY + 1))
        else
          NEW_KEY=1
        fi

        NEW_ENTRY="key${NEW_KEY}=/usr/bin/oneidcore"
        echo "$NEW_ENTRY" | sudo tee -a "$LIMIT_FILE" >/dev/null
      fi
    fi
  fi
fi

# Kill running instance to avoid crash during installation.
# We don't re-launch the app because it's difficult to set up correct
# environment variables.
kill_running_package() {
  PROCESS_NAME="$1"
  PACKAGE_PID="$(pidof ${PROCESS_NAME} || echo '')"

  if [ -n "${PACKAGE_PID}" ]; then
    PACKAGE_PID_KILL=$(kill ${PACKAGE_PID} || echo 'kill failed')
  fi
}
kill_running_package oneidcore
kill_running_package oneid
