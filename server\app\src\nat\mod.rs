use std::{collections::HashSet, net::IpAddr};

pub use light::LightNat;
use rand::{rngs::OsRng, TryRngCore};
pub use standard::Nat;
use tracing::error;

/// 轻量版NAT实现
mod light;
/// NAT实现
mod standard;

pub type VirtualIp = IpAddr;

#[derive(std::hash::Hash, PartialEq, Eq, Copy, Clone, Debug)]
pub struct VirtualAddr(pub VirtualIp, pub u16);

pub type Port = u16;

pub enum NatState {
    Created(u16),
    Already(u16),
}

impl NatState {
    pub fn port(&self) -> u16 {
        match self {
            NatState::Created(port) => *port,
            NatState::Already(port) => *port,
        }
    }
}

/// 随机端口
///
/// - `used_ports`: 已使用的端口
///
/// ## Results
///
/// if return `None`, it means no port is available
fn rand_port(used_ports: &HashSet<u16>) -> Option<Port> {
    // 没有剩余端口可用
    if used_ports.len() >= 65535 {
        error!("no remaining ports are available.");
        return None;
    }

    let mut ports = [0u8; 2];
    OsRng.try_fill_bytes(&mut ports[..]).ok()?;
    let port = u16::from_be_bytes(ports);
    if used_ports.contains(&port) {
        rand_port(used_ports)
    } else {
        Some(port)
    }
}
