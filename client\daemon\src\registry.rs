use winreg::{enums::*, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};

fn get_root_key(subkey: &str) -> HKEY {
    if subkey.starts_with("HKEY_CLASSES_ROOT") {
        return HKEY_CLASSES_ROOT;
    }
    if subkey.starts_with("HKEY_CURRENT_USER") {
        return HKEY_CURRENT_USER;
    }
    if subkey.starts_with("HKEY_LOCAL_MACHINE") {
        return HKEY_LOCAL_MACHINE;
    }
    if subkey.starts_with("HKEY_USERS") {
        return HKEY_USERS;
    }

    HKEY_CURRENT_USER_LOCAL_SETTINGS
}

pub fn getregvalue(subkey: &str, name: &str) -> Result<String, String> {
    let vkeys: Vec<&str> = subkey.split('\\').collect();
    let mut tmpkeys: Vec<&str> = Vec::new();
    let mut i: usize = 0;
    let mut tmp_root_key: String = String::new();
    loop {
        if i == 0 {
            tmp_root_key = vkeys[i].to_string();
        } else {
            tmpkeys.push(vkeys[i]);
        }
        if i == vkeys.len() - 1 {
            break;
        }
        i = i + 1;
    }
    let subkeyvalue = tmpkeys.join("\\");
    let rootkey = get_root_key(&tmp_root_key);
    let current_key = RegKey::predef(rootkey);
    let wv2 = current_key.open_subkey(subkeyvalue);
    if let Ok(key) = wv2 {
        let value = key.get_value(name).unwrap_or(String::new());
        return Ok(value);
    };
    Err(String::from("打开注册表的项出错了"))
}
pub fn get_lastlogin() -> String {
    // HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\LogonUI\\
    // LastLoggedOnUser
    let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
    let open_ret =
        hklm.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\LogonUI");
    let mut username = String::new();
    match open_ret {
        Ok(rk) => {
            let value_ret = rk.get_value::<String, &str>("LastLoggedOnUser");
            match value_ret {
                Ok(v) => {
                    username = v.clone();
                }
                Err(err) => {
                    log::error!("error is {}", &err);
                }
            }
        }
        Err(err) => {
            log::error!("error2 is {}", &err);
        }
    }
    log::info!("username is {}", &username);
    if username.contains("\\") {
        let arr: Vec<&str> = username.split('\\').collect();
        if arr.len() > 1 {
            username = arr[1].to_owned();
        }
    }
    username
}
