use super::Result;
use tauri::{
    plugin::{Builder, TauriPlugin},
    Runtime,
};

/// 是否支持面部识别
#[tauri::command]
pub(super) async fn is_support_facial_recognition() -> Result<bool> {
    Ok(biometric::is_support_facial_recognition())
}

/// 面部识别
#[tauri::command]
pub(super) async fn facial_recognition(
    _username: String,
) -> std::result::Result<bool, &'static str> {
    biometric::facial_recognition_verify().map(|user| user.is_some())
}

/// 取消面部识别
#[tauri::command]
pub(super) async fn cancel_facial_recognition() {
    biometric::cancel_facial_recognition();
}

pub fn init<R: Runtime>() -> TauriPlugin<R> {
    Builder::new("bio")
        .invoke_handler(tauri::generate_handler![
            is_support_facial_recognition,
            facial_recognition,
            cancel_facial_recognition,
        ])
        .build()
}
