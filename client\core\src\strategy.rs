//! 客户端策略

use std::collections::{HashMap, HashSet};

use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use tokio::fs;

use base::enums::{AttrType, Operator, ValueType};

use crate::{environment, http};

/// 补丁
#[cfg(windows)]
const PATCH: &str = "PATCH";
///  指定文件
const SPECIFIED_FILE: &str = "SPECIFIED_FILE";
/// 安装软件
const INSTALL_SOFTWARE: &str = "INSTALL_SOFTWARE";

/// 运行软件
const RUN_SOFTWARE: &str = "RUN_SOFTWARE";

const TELECOM: &str = "TELECOM";
#[cfg(windows)]
const DOMAIN_ENVIRONMENT: &str = "DOMAIN_ENVIRONMENT";
const SCREEN_SAVER: &str = "SCREEN_SAVER";
const DOMAIN_FIREWALL: &str = "DOMAIN_FIREWALL";
const PRIVATE_NETWORK_FIREWALL: &str = "PRIVATE_NETWORK_FIREWALL";
const PUBLIC_NETWORK_FIREWALL: &str = "PUBLIC_NETWORK_FIREWALL";

/// 条件
#[derive(Serialize, Deserialize, Clone, Debug)]
#[serde(rename_all = "camelCase")]
pub struct Condition {
    // 属性源
    pub attr: String,
    // 属性类型
    pub attr_type: AttrType,
    // 操作符
    pub operator: Operator,
    // 值
    pub value: String,
    // 单值/多值
    pub value_type: ValueType,
    // 版本
    pub version: Option<String>,
}

pub async fn exec(conditions: &HashMap<String, Vec<Condition>>) -> Value {
    let mut strategy_exec_result = vec![];
    for (scene_id, conditions) in conditions {
        let mut it = conditions.iter();
        let r#match = loop {
            if let Some(condition) = it.next() {
                let r#match = execute_condition(condition).await;
                if !r#match {
                    break false;
                }
            } else {
                break true;
            }
        };
        let version = &conditions.first().unwrap().version;
        strategy_exec_result.push(json!({
            "sceneId": &scene_id,
            "version": version,
            "result": r#match,
        }));
    }

    Value::Array(strategy_exec_result)
}

async fn execute_condition(condition: &Condition) -> bool {
    match condition.attr.as_str() {
        #[cfg(windows)]
        PATCH => self::check_patch(condition).await,
        SPECIFIED_FILE => self::check_file(condition).await,
        INSTALL_SOFTWARE => self::check_software(condition).await,
        RUN_SOFTWARE => self::check_processes(condition).await,
        TELECOM => self::check_telecom(condition).await,
        #[cfg(windows)]
        DOMAIN_ENVIRONMENT => self::check_domain_environment(condition).await,
        SCREEN_SAVER => self::check_screen_saver_active(condition).await,
        PRIVATE_NETWORK_FIREWALL | DOMAIN_FIREWALL | PUBLIC_NETWORK_FIREWALL => {
            self::check_firewall(condition).await
        }
        _ => true,
    }
}

#[cfg(windows)]
async fn check_patch(condition: &Condition) -> bool {
    let patchs = environment::patch::list().await;
    match condition.operator {
        Operator::EQ => patchs.contains(&condition.value),
        Operator::NE => !patchs.contains(&condition.value),
        Operator::IN | Operator::NOT_IN => {
            match serde_json::from_str::<HashSet<String>>(&condition.value) {
                Ok(patch_value) => match condition.operator {
                    Operator::IN => patchs.intersection(&patch_value).next().is_some(),
                    Operator::NOT_IN => patchs.intersection(&patch_value).next().is_none(),
                    _ => false,
                },
                _ => false,
            }
        }
        _ => true,
    }
}

async fn check_file(condition: &Condition) -> bool {
    match condition.operator {
        Operator::EQ => fs::File::open(condition.value.as_str()).await.is_ok(),
        Operator::NE => fs::File::open(condition.value.as_str()).await.is_err(),
        Operator::IN | Operator::NOT_IN => {
            match serde_json::from_str::<Vec<String>>(&condition.value) {
                Ok(files) => match condition.operator {
                    Operator::IN => {
                        for file in files {
                            if fs::File::open(file.as_str()).await.is_ok() {
                                return true;
                            }
                        }
                        false
                    }
                    Operator::NOT_IN => {
                        for file in files {
                            if fs::File::open(file.as_str()).await.is_ok() {
                                return false;
                            }
                        }
                        true
                    }
                    _ => false,
                },
                _ => false,
            }
        }
        _ => true,
    }
}

async fn check_software(condition: &Condition) -> bool {
    let softwares: HashSet<String> = environment::software::list()
        .into_iter()
        .map(|x| x.software_name)
        .collect();
    log::trace!("Execute install softwares . {:?}", &softwares);
    match serde_json::from_str::<HashSet<String>>(&condition.value) {
        Ok(specified_softwares) => match condition.operator {
            Operator::EQ => {
                for ele in specified_softwares.into_iter() {
                    if !softwares.contains(&ele) {
                        return false;
                    }
                }
                true
            }
            Operator::NE => {
                for ele in specified_softwares.into_iter() {
                    if softwares.contains(&ele) {
                        return false;
                    }
                }
                true
            }
            Operator::IN => softwares
                .intersection(&specified_softwares)
                .next()
                .is_some(),
            Operator::NOT_IN => softwares
                .intersection(&specified_softwares)
                .next()
                .is_none(),

            _ => false,
        },
        _ => false,
    }
}
async fn check_processes(condition: &Condition) -> bool {
    let softwares: HashSet<String> = environment::running::processes(false).into_iter().collect();
    log::trace!("Execute run softwares . {:?}", &softwares);
    match serde_json::from_str::<HashSet<String>>(&condition.value) {
        Ok(specified_softwares) => match condition.operator {
            Operator::EQ => {
                for ele in specified_softwares.into_iter() {
                    if !softwares.contains(&ele) {
                        return false;
                    }
                }
                true
            }
            Operator::NE => {
                for ele in specified_softwares.into_iter() {
                    if softwares.contains(&ele) {
                        return false;
                    }
                }
                true
            }
            Operator::IN => {
                log::trace!("Software list: {:#?}", &specified_softwares);
                softwares
                    .intersection(&specified_softwares)
                    .next()
                    .is_some()
            }
            Operator::NOT_IN => softwares
                .intersection(&specified_softwares)
                .next()
                .is_none(),
            _ => false,
        },
        _ => false,
    }
}

async fn check_screen_saver_active(condition: &Condition) -> bool {
    let client = http::client().await;

    #[cfg(windows)]
    let port = crate::windows::get_gui_port();
    #[cfg(not(windows))]
    let port = 50031u16;

    let response = client
        .get(format!("http://localhost:{port}/screen_saver"))
        .send()
        .await;
    let screen_saver = if let Ok(response) = response {
        response.json::<bool>().await.unwrap_or_default()
    } else {
        false
    };

    condition.value.parse::<bool>().unwrap_or_default() == screen_saver
}

async fn check_telecom(condition: &Condition) -> bool {
    match environment::ip::public_ip().await {
        Some((_, value, _)) => match condition.operator {
            Operator::EQ => value.eq(&condition.value),
            Operator::NE => value.ne(&condition.value),
            Operator::IN | Operator::NOT_IN => {
                match serde_json::from_str::<HashSet<String>>(&condition.value) {
                    Ok(specified_net_operator) => match condition.operator {
                        Operator::IN => specified_net_operator.contains(&value),
                        Operator::NOT_IN => !specified_net_operator.contains(&value),
                        _ => false,
                    },
                    _ => false,
                }
            }
            _ => true,
        },
        None => true,
    }
}

#[cfg(windows)]
async fn check_domain_environment(condition: &Condition) -> bool {
    let domain_name = environment::domain::get_domain_name();
    condition.value.parse::<bool>().unwrap_or_default() == domain_name.is_some()
}

#[cfg(windows)]
async fn check_firewall(condition: &Condition) -> bool {
    let firewall_enabled = environment::firewall::is_enable();
    match condition.attr.as_str() {
        DOMAIN_FIREWALL => match condition.operator {
            Operator::EQ => {
                condition.value.parse::<bool>().unwrap_or_default()
                    == firewall_enabled.domain_firewall
            }
            _ => true,
        },
        PRIVATE_NETWORK_FIREWALL => match condition.operator {
            Operator::EQ => {
                condition.value.parse::<bool>().unwrap_or_default()
                    == firewall_enabled.private_network_firewall
            }
            _ => true,
        },
        PUBLIC_NETWORK_FIREWALL => match condition.operator {
            Operator::EQ => {
                condition.value.parse::<bool>().unwrap_or_default()
                    == firewall_enabled.public_network_firewall
            }
            _ => true,
        },
        _ => true,
    }
}

#[cfg(not(windows))]
async fn check_firewall(condition: &Condition) -> bool {
    let firewall_enabled = environment::firewall::is_enable();
    match condition.attr.as_str() {
        DOMAIN_FIREWALL => match condition.operator {
            Operator::EQ => condition.value.parse::<bool>().unwrap_or_default() == firewall_enabled,
            _ => true,
        },
        PRIVATE_NETWORK_FIREWALL => match condition.operator {
            Operator::EQ => condition.value.parse::<bool>().unwrap_or_default() == firewall_enabled,
            _ => true,
        },
        PUBLIC_NETWORK_FIREWALL => match condition.operator {
            Operator::EQ => condition.value.parse::<bool>().unwrap_or_default() == firewall_enabled,
            _ => true,
        },
        _ => true,
    }
}
