use objc2::rc::Retained;
use objc2_app_kit::{NSWindow, NSWindowButton, NSWindowTitleVisibility};
use objc2_foundation::NSRect;
use tauri::{Runtime, Window};

#[allow(dead_code)]
pub trait WindowExt {
    fn set_transparent_titlebar(&self, transparent: bool) -> anyhow::Result<()>;
    fn position_traffic_lights(&self, x: f64, y: f64) -> anyhow::Result<()>;
    fn set_button_visible(&self, button: u64, visible: bool) -> anyhow::Result<()>;
}

impl<R: Runtime> WindowExt for Window<R> {
    fn set_transparent_titlebar(&self, transparent: bool) -> anyhow::Result<()> {
        let ns_window = self.ns_window().unwrap();
        let ns_window = unsafe { Retained::retain_autoreleased(ns_window as *mut NSWindow) }
            .expect("failed to retain window");

        ns_window.setTitleVisibility(NSWindowTitleVisibility::NSWindowTitleHidden);
        ns_window.setTitlebarAppearsTransparent(transparent);

        Ok(())
    }

    fn position_traffic_lights(&self, x: f64, y: f64) -> anyhow::Result<()> {
        let ns_window = self.ns_window().unwrap();
        let ns_window = unsafe { Retained::retain_autoreleased(ns_window as *mut NSWindow) }
            .expect("failed to retain window");

        let close = ns_window
            .standardWindowButton(NSWindowButton::NSWindowCloseButton)
            .ok_or(anyhow::anyhow!("failed to get close button"))?;
        let miniaturize = ns_window
            .standardWindowButton(NSWindowButton::NSWindowMiniaturizeButton)
            .ok_or(anyhow::anyhow!("failed to get miniaturize button"))?;
        let zoom = ns_window
            .standardWindowButton(NSWindowButton::NSWindowZoomButton)
            .ok_or(anyhow::anyhow!("failed to get zoom button"))?;

        let title_bar_container_view = unsafe {
            close
                .superview()
                .and_then(|view| view.superview())
                .ok_or(anyhow::anyhow!("failed to get title bar container view"))?
        };

        let close_rect = close.frame();
        let button_height = close_rect.size.height;

        let title_bar_frame_height = button_height + y;
        let mut title_bar_rect = title_bar_container_view.frame();
        title_bar_rect.size.height = title_bar_frame_height;
        title_bar_rect.origin.y = ns_window.frame().size.height - title_bar_frame_height;
        unsafe {
            title_bar_container_view.setFrame(title_bar_rect);
        }

        let space_between = miniaturize.frame().origin.x - close.frame().origin.x;
        let window_buttons = vec![close, miniaturize, zoom];

        for (i, button) in window_buttons.into_iter().enumerate() {
            let mut rect: NSRect = button.frame();
            rect.origin.x = x + (i as f64 * space_between);
            unsafe {
                button.setFrameOrigin(rect.origin);
            }
        }

        Ok(())
    }

    fn set_button_visible(&self, button: u64, visible: bool) -> anyhow::Result<()> {
        let ns_window = self.ns_window().unwrap();
        let ns_window = unsafe { Retained::retain_autoreleased(ns_window as *mut NSWindow) }
            .expect("failed to retain window");

        let button = match button {
            0 => NSWindowButton::NSWindowCloseButton,
            1 => NSWindowButton::NSWindowMiniaturizeButton,
            2 => NSWindowButton::NSWindowZoomButton,
            _ => return Ok(()),
        };

        let button = ns_window
            .standardWindowButton(button)
            .ok_or(anyhow::anyhow!("failed to get {:?} button", button))?;

        button.setHidden(!visible);

        Ok(())
    }
}
