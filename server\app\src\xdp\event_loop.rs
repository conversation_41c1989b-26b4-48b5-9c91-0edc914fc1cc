use sdpcore::io::{rx::Rx, tx::Tx};

pub mod select;
use select::Select;

use sdpcore::io::{rx::Queue as RxQueue, tx::Queue as TxQueue};

use crate::{packet::server::ServerPacketHandle, utils};

pub struct EventLoop<R, T, OnPacket: FnMut(&mut [u8])> {
    pub rx: R,
    pub tx: T,
    pub packet_sender: ServerPacketHandle,
}

impl<R, T, OnPacket> EventLoop<R, T, OnPacket>
where
    R: Rx,
    T: Tx,
    OnPacket: FnMut(&mut [u8]),
{
    /// Starts running the endpoint event loop in an async task
    pub async fn start(self) {
        let Self {
            mut rx,
            mut tx,
            packet_sender,
        } = self;

        loop {
            // Poll for RX readiness
            let rx_ready = rx.ready();

            match rx_ready.await {
                Some(Ok(())) => {
                    // we received some packets. give them to the endpoint.
                    rx.queue(|queue| {
                        queue.for_each(|packet| {
                            let mut data = Vec::with_capacity(packet.len() - 14);
                            data.extend_from_slice(&packet[14..]);

                            if let Some(packet) = utils::parse_ip_packet(data) {
                                packet_sender.send(packet);
                            }
                        });
                    });
                }
                Some(Err(error)) => {
                    // The RX provider has encountered an error. shut down the event loop
                    // let mut publisher = publisher!(clock.get_time());
                    // rx.handle_error(error, &mut publisher);
                    return;
                }
                None => {
                    // We didn't receive any packets; nothing to do
                }
            }
        }
    }
}
