use std::{fs::File, io};
use time::{
    macros::{format_description, offset},
    UtcOffset,
};
use tracing_rolling::{Checker, Daily, Token};
use tracing_subscriber::{
    fmt::{self, time::OffsetTime},
    prelude::__tracing_subscriber_SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};

const COMMIT_DATE: &str = include_str!(concat!(env!("OUT_DIR"), "/git-commit-date.txt"));

pub fn init(dir: &str, filter: &str, name: &str) -> Token<File> {
    let (writer, token) = Daily::new::<&str>(format!("{}/{name}.log", dir), None, offset!(+8))
        .build()
        .unwrap();

    let timer = OffsetTime::new(
        UtcOffset::from_hms(8, 0, 0).unwrap(),
        format_description!("[year]-[month]-[day] [hour]:[minute]:[second].[subsecond digits:3]"),
    );

    let std_logger = fmt::layer()
        .with_timer(timer.clone())
        .with_line_number(true)
        .with_file(true)
        .with_thread_ids(false)
        .with_thread_names(false)
        .with_target(true)
        .with_ansi(true)
        .with_writer(io::stdout)
        .with_filter(EnvFilter::new(filter));

    let log_layer = fmt::layer()
        .compact()
        .with_timer(timer.clone())
        .with_line_number(false)
        .with_file(false)
        .with_thread_ids(false)
        .with_thread_names(false)
        .with_target(true)
        .with_ansi(false)
        .with_writer(writer)
        .with_filter(EnvFilter::new(filter));

    tracing_subscriber::registry()
        .with(std_logger)
        .with(log_layer)
        .init();

    log_panics::init();

    #[cfg(not(feature = "tlcp"))]
    let tlcp = "";
    #[cfg(feature = "tlcp")]
    let tlcp = " With TLCP";
    // banner
    tracing::info!(
        "Headless Client\nVersion: {} {}{}\n",
        version::FILE_VERSION,
        COMMIT_DATE,
        tlcp
    );

    token
}
