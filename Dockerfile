### RUST项目基础镜像
FROM ubuntu

# 设置工作目录
WORKDIR  /opt/sdp_server/bin

COPY  target/debug/server /opt/sdp_server/bin
COPY  tools/gm_cert/138/*   /opt/sdp_server/cert/
COPY  config/server_tlcp.toml   /opt/sdp_server/config
RUN  export RUST_BACKTRACE=1 \
     &&  mkdir /opt/sdp_server/logs

CMD ["server", "--config","/opt/sdp_server/config/server_tlcp.toml", "--filter","info,server=info","--log-dir","logs"]
