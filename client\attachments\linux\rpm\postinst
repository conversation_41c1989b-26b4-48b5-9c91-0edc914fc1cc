#!/bin/bash

/usr/bin/oneidservice --register-service

chown root:root /usr/bin/oneidcore
chmod +sx /usr/bin/oneidcore
chmod +x /usr/bin/oneidconfig
chmod +x /usr/bin/oneid_auth.sh

# 执行配置程序, 检查安装包内置配置与本地配置是否匹配
/usr/bin/oneidconfig

# 执行完成后, 删除该程序
rm -f /usr/bin/oneidconfig

# 添加系統信任证书
cp /usr/lib/${PRODUCT_NAME}/_up_/attachments/oneid.crt /usr/local/share/ca-certificates
update-ca-certificates

rm -rf /usr/lib/${PRODUCT_NAME}/_up_

exit 0