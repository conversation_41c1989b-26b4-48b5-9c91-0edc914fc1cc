use std::{fs, io, path::PathBuf};

use config::CORE_CONFIG;
use serde_json::json;
use tauri::{A<PERSON><PERSON><PERSON><PERSON>, <PERSON>ry};

use encrypt_files::help;

#[derive(thiserror::Error, Debug)]
pub enum Error {
    #[error("Missing file: {0}")]
    MissingFile(String),

    #[error("Unable to read config file {0}")]
    Read(String, #[source] io::Error),

    #[error("Unable to parse config file")]
    Parse(#[from] serde_json::Error),
}

pub fn init(app_handle: &AppHandle<Wry>, resource_dir: PathBuf) -> Result<(), Error> {
    let resolver = app_handle.path_resolver();

    #[cfg(all(feature = "sdp", not(feature = "tlcp")))]
    let dir = "certs/tls";
    #[cfg(feature = "tlcp")]
    let dir = "certs/tlcp";

    let certs_dir = resolver
        .resolve_resource(dir)
        .ok_or(Error::MissingFile(dir.to_owned()))?;

    let certs_dir = certs_dir
        .read_dir()
        .map_err(|error| Error::Read(dir.to_owned(), error))?;

    let mut core = json!({});
    for entry in certs_dir {
        let entry = entry.map_err(|error| Error::Read(dir.to_owned(), error))?;
        let domain = entry.file_name().into_string().unwrap();

        let path = entry.path();
        let path_string = path.to_string_lossy().into_owned();
        let cert_dir = path
            .read_dir()
            .map_err(|error| Error::Read(path_string.clone(), error))?;

        let mut domain_config = json!({});
        for entry in cert_dir {
            let entry = entry.map_err(|error| Error::Read(path_string.clone(), error))?;

            let file_name = entry.file_name().into_string().unwrap();
            match file_name.as_str() {
                "ca.crt" => {
                    domain_config["ca"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                "client.crt" => {
                    domain_config["cert"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                "client.key" => {
                    domain_config["key"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                #[cfg(feature = "tlcp")]
                "client_enc.crt" => {
                    domain_config["enc_cert"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                #[cfg(feature = "tlcp")]
                "client_enc.key" => {
                    domain_config["enc_key"] =
                        serde_json::Value::String(fs::read_to_string(entry.path()).unwrap());
                }
                _ => (),
            }
        }

        core[domain.clone()] = domain_config;
    }

    let path = resource_dir.join(CORE_CONFIG);
    help::save_yaml(&path, &core, Some("# Generated by SDP Client")).unwrap();

    Ok(())
}
