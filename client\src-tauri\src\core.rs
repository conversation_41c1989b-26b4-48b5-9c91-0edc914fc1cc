use std::{env, fs, io::Write, path::PathBuf, sync::Arc};

use anyhow::{Context, Result};
use once_cell::sync::OnceCell;
use parking_lot::Mutex;
use sysinfo::{Pid, System};
use tauri::{
    api::process::{Command, CommandChild, CommandEvent},
    AppH<PERSON><PERSON>, Wry,
};
use tokio::sync::oneshot::Sender;

use crate::{log_err, notify};

pub struct CoreManager {
    sidecar: Arc<Mutex<Option<CommandChild>>>,
}

impl CoreManager {
    pub fn global() -> &'static CoreManager {
        static CORE_MANAGER: OnceCell<CoreManager> = OnceCell::new();

        CORE_MANAGER.get_or_init(|| CoreManager {
            sidecar: Arc::new(Mutex::new(None)),
        })
    }

    pub fn init(&self, app_handle: AppHandle<Wry>, app_name: String, tx: Sender<()>) -> Result<()> {
        // kill old oneidcore process
        let pid_path = paths::core_pid_path();
        _ = fs::read(pid_path)
            .map(|p| p.to_vec())
            .map(|pid| String::from_utf8_lossy(&pid).parse::<u32>())
            .map(|pid| {
                pid.map(|pid| {
                    let mut system = System::new();
                    system.refresh_all();
                    if let Some(proc) = system.process(Pid::from_u32(pid)) {
                        let process_name = proc.name().to_str().unwrap_or_default();
                        if process_name.contains("oneidcore") {
                            log::debug!(target: "app", "Kill old core process");
                            proc.kill();
                        }
                    }
                })
            });

        tauri::async_runtime::spawn(async move {
            // 启动core
            log_err!(Self::global().run_core(app_handle, app_name, tx).await);
        });

        Ok(())
    }

    #[cfg_attr(not(target_os = "macos"), allow(unused_variables))]
    pub async fn run_core(
        &self,
        app_handle: AppHandle<Wry>,
        app_name: String,
        tx: Sender<()>,
    ) -> Result<()> {
        #[allow(unused_mut, unused_variables)]
        let mut should_kill = match self.sidecar.lock().take() {
            Some(child) => {
                log::debug!(target: "app", "Stop the core by sidecar");
                let _ = child.kill();
                true
            }
            None => false,
        };

        // 授权
        #[cfg(any(target_os = "macos", target_os = "linux"))]
        if let Err(err) = grant_permission(&app_handle, "oneidcore".to_owned(), app_name.clone()) {
            log::error!(target: "app", "Failed to grant core. {:?}", err);
            // 授权失败或不予授权时, 直接退出程序
            std::process::exit(-249);
        }

        // 启动核心服务
        #[cfg(debug_assertions)]
        let filter = env::var("ONEID_LOG").unwrap_or(String::from("error,oneidcore=trace"));
        #[cfg(not(debug_assertions))]
        let filter = env::var("ONEID_LOG").unwrap_or(String::from("error,oneidcore=info"));

        fn path_to_str(path: &PathBuf) -> Result<&str> {
            let path_str = path
                .as_os_str()
                .to_str()
                .ok_or(anyhow::anyhow!("Failed to get path from {:?}", path))?;
            Ok(path_str)
        }

        log::info!(target: "app", r#"Starting the core using the log filter "{filter}"."#);

        // 已经校验过, unwrap是安全的
        let resources_dir = paths::resource_dir().unwrap();
        let resources_dir = path_to_str(&resources_dir).unwrap();

        let (mut rx, cmd_child) = {
            let sidecar = Command::new_sidecar("oneidcore")?;
            sidecar
                .args(vec!["-f", &filter, "-m", "embed", "-r", resources_dir])
                .spawn()?
        };

        // 将pid写入文件中
        log_err!((|| {
            let pid = cmd_child.pid();
            let path = paths::core_pid_path();
            fs::File::create(path)
                .context("failed to create the pid file")?
                .write(format!("{pid}").as_bytes())
                .context("failed to write pid to the file")?;
            <Result<()>>::Ok(())
        })());

        let mut sidecar = self.sidecar.lock();
        *sidecar = Some(cmd_child);
        drop(sidecar);

        tauri::async_runtime::spawn(async move {
            macro_rules! output {
                ($level: expr, $line: expr) => {
                    match $level {
                        Some("TRACE") => {
                            log::trace!(target: "core", "{}", $line.trim_start_matches("TRACE").trim());
                            Some("TRACE")
                        }
                        Some("DEBUG") => {
                            log::debug!(target: "core", "{}", $line.trim_start_matches("DEBUG").trim());
                            Some("DEBUG")
                        }
                        Some("INFO") => {
                            log::info!(target: "core", "{}", $line.trim_start_matches("INFO").trim());
                            Some("INFO")
                        }
                        Some("WARN") => {
                            log::warn!(target: "core", "{}", $line.trim_start_matches("WARN").trim());
                            Some("WARN")
                        }
                        Some("ERROR") => {
                            log::error!(target: "core", "{}", $line.trim_start_matches("ERROR").trim());
                            Some("ERROR")
                        }
                        _ => None,
                    }
                };
            }

            let mut tx = Some(tx);
            let mut latest_level = None;
            loop {
                if let Some(event) = rx.recv().await {
                    match event {
                        CommandEvent::Stderr(err) => {
                            log::error!(target: "core", "{err}");
                        }
                        CommandEvent::Stdout(line) => {
                            let line = line.trim();
                            let mut split = line.split_ascii_whitespace();
                            let level = if let Some(level) = output!(split.next(), line) {
                                latest_level = Some(level);
                                latest_level
                            } else {
                                output!(latest_level, line)
                            };
                            if level.is_none() {
                                log::info!(target: "core", "{}", line.trim());
                            }
                            if line.contains("Communicate interface listening on") {
                                log::debug!(target: "app", "Core started");
                                if let Some(tx) = tx.take() {
                                    _ = tx.send(());
                                }
                            }
                        }
                        CommandEvent::Error(err) => log::error!(target: "core", "{err}"),
                        CommandEvent::Terminated(error) => {
                            // kill
                            if error.signal.is_none() {
                                log::info!(target: "core", "Terminated");
                            } else {
                                log::info!(target: "core", "Terminated. {error:?}");
                                notify!(app_handle, "程序启动失败");
                            }

                            // _ = CoreManager::global().recover_core(appname.clone(), tx);
                            app_handle.exit(1);
                        }
                        event => {
                            log::error!(target: "app", "Unexpected event: {event:?}");
                        }
                    }
                }
            }
        });

        Ok(())
    }

    // pub fn recover_core(&'static self, app_handle: AppHandle<Wry>, app_name: String, tx:
    // Sender<()>) -> Result<()> {     // 清空原来的sidecar值
    //     if let Some(sidecar) = self.sidecar.lock().take() {
    //         let _ = sidecar.kill();
    //     }

    //     tauri::async_runtime::spawn(async move {
    //         tokio::time::sleep(Duration::from_millis(500)).await;

    //         if self.sidecar.lock().is_none() {
    //             log::info!(target: "app", "recover sdp core");

    //             // 重新启动app
    //             if let Err(err) = self.run_core(app_handle.clone(), app_name.clone(), tx).await {
    //                 log::error!(target: "app", "failed to recover sdp core");
    //                 log::error!(target: "app", "{err}");

    //                 let _ = self.recover_core(app_handle, app_name, tx);
    //             }
    //         }
    //     });

    //     Ok(())
    // }

    /// 停止核心服务
    pub fn stop_core(&self) -> Result<()> {
        let mut sidecar = self.sidecar.lock();
        if let Some(child) = sidecar.take() {
            let result = child.kill();
            log::debug!(target: "app", "Stop the core by sidecar: {:?}", result);
        }
        Ok(())
    }
}

#[allow(unused_variables)]
#[cfg(any(target_os = "macos", target_os = "linux"))]
pub fn grant_permission(
    app_handle: &AppHandle<Wry>,
    core: String,
    appname: String,
) -> anyhow::Result<()> {
    use std::os::unix::prelude::{MetadataExt, PermissionsExt};
    use tauri::utils::platform::current_exe;

    let path = current_exe()?.with_file_name(core).canonicalize()?;
    let path = path.display().to_string();

    log::debug!(target: "app", "Grant permission path: {path}");

    if let Ok(metadata) = fs::metadata(&path) {
        let uid = metadata.uid();
        let gid = metadata.gid();
        let permissions = metadata.permissions().mode();

        // admin gid 在不同系统中, 可能版本不同
        #[cfg(target_os = "macos")]
        let owner = uid == 0 && gid == 80;
        #[cfg(target_os = "linux")]
        let owner = uid == 0 && gid == 0;
        // 0o001 可执行文件权限
        // +s 0o4000 setuid 是程序执行阶段具有所有者权限 0o2000 setgid
        // 是程序执行阶段具有文件属组权限
        let sx = (permissions & 0o001) != 0
            && ((permissions & 0o4000) != 0 && (permissions & 0o2000) != 0);
        if owner && sx {
            return Ok(());
        }
    }

    #[cfg(target_os = "macos")]
    {
        // the path of oneidcore /Applications/One ID.app/Contents/MacOS/oneidcore
        // https://apple.stackexchange.com/questions/82967/problem-with-empty-spaces-when-executing-shell-commands-in-applescript
        // let path = escape(&path);
        let path = path.replace(' ', "\\\\ ");
        let shell = format!("chown root:admin {path}\nchmod +sx {path}");

        let command = format!(
            r#"do shell script "{shell}" with administrator privileges with prompt "“{appname}”想要进行更改。""#
        );
        let output = Command::new("osascript")
            .args(vec!["-e", &command])
            .output()?;

        if output.status.success() {
            Ok(())
        } else {
            anyhow::bail!("{}", output.stderr);
        }
    }

    #[cfg(target_os = "linux")]
    {
        let path = current_exe()?.with_file_name("oneid_auth").canonicalize()?;

        let kdepath = std::path::Path::new("/usr/bin/kdesudo");
        let pkpath = std::path::Path::new("/usr/bin/pkexec");
        let gksudo = std::path::Path::new("/usr/bin/gksudo");
        let command = if pkpath.exists() {
            Command::new("pkexec")
        } else if kdepath.exists() {
            Command::new("kdesudo")
        } else if gksudo.exists() {
            Command::new("gksudo")
        } else {
            unreachable!();
        };

        let output = command.args([path.display().to_string()]).output()?;

        if output.status.success() {
            Ok(())
        } else {
            anyhow::bail!("{}", output.stderr);
        }
    }
}
