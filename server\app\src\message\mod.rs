//! 消息推送和接收

use std::net::IpAddr;

use deadpool_lapin::{Manager, Pool, Runtime};
use lapin::{uri::AMQPUri, ConnectionProperties};
use tlv_types::WebApp;
use tokio::sync::mpsc;
use tracing::{error, info};

use crate::{types::Servers, Error, EventSender};

#[macro_use]
mod macros {
    macro_rules! get_channel {
        ($pool:expr, $timeout:expr) => {{
            let conn = match $pool.get().await {
                Ok(conn) => conn,
                Err(err) => {
                    tracing::error!("connection error: {err}");

                    tokio::time::sleep(std::time::Duration::from_millis(
                        2 << std::cmp::max(12, $timeout),
                    ))
                    .await;
                    $timeout += 1;
                    continue;
                }
            };

            let channel = match conn.create_channel().await {
                Ok(channel) => channel,
                Err(err) => {
                    tracing::error!("create channel error: {err}");

                    tokio::time::sleep(std::time::Duration::from_millis(
                        2 << std::cmp::max(12, $timeout),
                    ))
                    .await;
                    $timeout += 1;
                    continue;
                }
            };

            channel.on_error(|err| {
                tracing::warn!("channel error: {err}");
            });

            channel
        }};
    }
}

mod access_log;
mod online;
mod resource;
mod traffic;
mod user;
mod webapp;

/// 资源列表
const SERVER_LIST_QUEUE: &str = "server_list";
/// 用户状态
const USER_STATE_QUEUE: &str = "user_state";

/// 资源列表交换机
const SERVER_LIST_EXCHANGE: &str = "server_list_exchange";
/// SDP专用交换机
const SDP_EXCHANGE: &str = "sdp_exchange";

const LOG_EXCHANGE: &str = "access_log_exchange";

/// WEB应用交换机
const WEBAPP_EXCHANGE: &str = "web_app_exchange";

/// 流量统计交换机
const TRAFFIC_EXCHANGE: &str = "traffic_exchange";

/// 会话流量队列名称
const SESSION_TRAFFIC_QUEUE: &str = "session_traffic";

/// 会话访问资源流量队列名称
const SESSION_RESOURCE_TRAFFIC_QUEUE: &str = "session_resource_traffic";

#[derive(Debug)]
pub enum MQCommand {
    /// 添加资源
    AddResource {
        tenant: String,
        servers: Servers,
    },
    /// 添加映射资源
    AddVirtualResource {
        tenant: String,
        resource: IpAddr,
        virtual_ip: IpAddr,
    },
    /// 删除资源
    DelResource {
        tenant: String,
        servers: Servers,
    },
    /// 禁用用户
    UserState {
        reason: u8,
        tenant: String,
        username: String,
    },
    /// 用户密码变更
    UserPasswordChange {
        device_id: String,
        tenant: String,
        username: String,
    },
    /// 设备禁用
    DisableDevice(String),
    /// 租户禁用
    DisableTenant(String),
    // 结束会话
    EndSession(String),
    /// 异地登录
    LoginElseWhere {
        ip: IpAddr,
        device_id: String,
    },
    /// 添加WEB应用
    AddWebapp {
        tenant: String,
        webapp: WebApp,
    },
    /// 删除WEB应用
    DelWebapp {
        tenant: String,
        webapp: WebApp,
    },
}

#[derive(Clone)]
pub struct MqHandle {
    log_tx: mpsc::Sender<Vec<u8>>,

    online_tx: mpsc::Sender<(IpAddr, String)>,
}

impl MqHandle {
    pub async fn online(&self, event: (IpAddr, String)) {
        if self.online_tx.send(event).await.is_err() {
            error!("Broadcast thread panicked.");
        }
    }

    pub async fn log(&self, log: Vec<u8>) {
        if self.log_tx.send(log).await.is_err() {
            error!("Log thread panicked.");
        }
    }
}

pub struct MqArgs {
    pub url: String,
    pub event_sender: EventSender,

    /// 会话流量
    pub session_traffic_rx: mpsc::Receiver<Vec<u8>>,
    /// 会话访问资源流量
    pub session_resource_traffic_rx: mpsc::Receiver<Vec<u8>>,
}

pub async fn spawn(args: MqArgs) -> Result<MqHandle, Error> {
    let pool = create_pool(&args.url).await?;

    // 监听资源变化
    resource::listen(pool.clone(), args.event_sender.clone()).await?;

    // 监听用户状态变化
    user::listen(pool.clone(), args.event_sender.clone()).await?;

    // 推送访问日志
    let log_tx = {
        let (log_tx, log_rx) = mpsc::channel(1024);
        access_log::spawn_producer(pool.clone(), log_rx).await?;

        log_tx
    };

    // 广播上线通知. 集群环境下, 通知其他服务断开当前客户端连接
    let online_tx = {
        let (online_tx, online_rx) = mpsc::channel(1024);
        online::spawn_producer(pool.clone(), online_rx).await?;

        online_tx
    };

    // 监听WEB应用变化
    webapp::listen(pool.clone(), args.event_sender.clone()).await?;

    // 推送流量统计信息
    {
        traffic::spawn_producer(
            pool,
            args.session_traffic_rx,
            args.session_resource_traffic_rx,
        )
        .await?;
    }

    let uri = args.url.parse::<AMQPUri>().unwrap();
    info!(
        "connected to {}://{}:{}/{}",
        format!("{:?}", uri.scheme).to_lowercase(),
        uri.authority.host,
        uri.authority.port,
        uri.vhost
    );
    Ok(MqHandle { log_tx, online_tx })
}

async fn create_pool(url: &str) -> Result<Pool, Error> {
    let options = ConnectionProperties::default()
        .with_executor(tokio_executor_trait::Tokio::current())
        .with_reactor(tokio_reactor_trait::Tokio);
    let manager = Manager::new(url, options);

    let pool = deadpool_lapin::Pool::builder(manager)
        .runtime(Runtime::Tokio1)
        .build()
        .map_err(|error| Error::MqBuildError(error))?;

    // let cfg = deadpool_lapin::Config {
    //     url: Some(url.to_owned()),
    //     ..deadpool_lapin::Config::default()
    // };
    // let pool = cfg
    //     .create_pool(Some(Runtime::Tokio1))
    //     .map_err(|error| error.display_chain_with_msg("failed to create pool"))?;
    Ok(pool)
}
