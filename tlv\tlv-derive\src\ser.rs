use proc_macro2::TokenStream;
use quote::quote;
use syn::{spanned::Spanned, Data, Error, ExprLit, Lit};

mod em;
mod fie;

pub fn expand_derive_serialize(input: &mut syn::DeriveInput) -> syn::Result<TokenStream> {
    let type_name = &input.ident;
    match &input.data {
        Data::Struct(data_struct) => {
            if !matches!(&data_struct.fields, syn::Fields::Named(_)) {
                return Err(Error::new(
                    proc_macro2::Span::call_site(),
                    "can only work on named fields",
                ));
            }
            fie::expand_struct(&data_struct.fields, type_name)
        }
        Data::Enum(em) => {
            let untag = crate::get_tlv_value(&input.attrs)?;
            let untag = match untag.as_deref() {
                Some("untag") => true,
                None => false,
                _ => {
                    return Err(Error::new(
                        proc_macro2::Span::call_site(),
                        "#[tlv = \"untag\"] is only defined for enums",
                    ));
                }
            };

            let bytes_init = if untag {
                quote!(let mut bytes = vec![];)
            } else {
                quote!(let mut bytes = vec![tlv::Tag::RawBytes as u8, 1];)
            };

            let mut struct_fields = vec![];
            for (ordinal, variant) in em.variants.iter().enumerate() {
                let enum_name = &variant.ident;

                let ordinal = if let Some((
                    _,
                    syn::Expr::Lit(ExprLit {
                        lit: Lit::Int(lit_int),
                        ..
                    }),
                )) = &variant.discriminant
                {
                    lit_int.base10_digits().parse().unwrap()
                } else {
                    ordinal as u8
                };

                let token_stream = match variant.fields {
                    syn::Fields::Named(_) => {
                        if untag {
                            return Err(Error::new(
                                proc_macro2::Span::call_site(),
                                "#[tlv = \"untag\"] is only defined for unit enums",
                            ));
                        }
                        let (enum_fields, token_stream) = em::expand_named_fields(&variant.fields)?;
                        quote! {
                            #type_name::#enum_name{#enum_fields} => {
                                bytes.push(#ordinal);
                                #token_stream
                            }
                        }
                    }
                    syn::Fields::Unnamed(ref fields) => {
                        if fields.unnamed.len() != 1 {
                            return Err(Error::new(
                                variant.fields.span(),
                                "unnamed enums must be tuples of single values",
                            ));
                        }

                        quote! {
                            #type_name::#enum_name(var) => {
                                let field_bytes = tlv::Serialize::serialize(var);
                                bytes.extend(field_bytes);
                            }
                        }
                    }
                    _ => {
                        return Err(Error::new(
                            proc_macro2::Span::call_site(),
                            "can only work on named fields",
                        ));
                    }
                };
                struct_fields.push(token_stream);
            }

            let struct_fields = TokenStream::from_iter(struct_fields);

            Ok(quote! {
                impl tlv::Serialize for #type_name {
                    fn serialize(&self) -> Vec<u8> {
                        #bytes_init

                        match self {
                            #struct_fields
                        }
                        bytes
                    }
                }
            })
        }
        Data::Union(_) => Err(Error::new(
            input.span(),
            "#[derive(Serialize)] is only defined for structs or enums",
        )),
    }
}
