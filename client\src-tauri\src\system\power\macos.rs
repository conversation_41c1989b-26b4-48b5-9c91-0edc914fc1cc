use std::{ffi::c_void, sync::atomic::AtomicPtr};

use objc2::{
    declare_class, msg_send_id,
    mutability::InteriorMutable,
    rc::{Allocated, Retained},
    sel, ClassType, DeclaredClass,
};
use objc2_app_kit::{
    NSWorkspace, NSWorkspaceDidWakeNotification, NSWorkspaceWillSleepNotification,
};
use objc2_foundation::{NSNotification, NSObject};
use tokio::sync::broadcast;

use super::PowerManagementEvent;

pub struct PowerManagementListener {
    observer: Retained<Observer>,
    rx: broadcast::Receiver<PowerManagementEvent>,
}

impl PowerManagementListener {
    pub fn new() -> Option<Self> {
        let (tx, rx) = broadcast::channel(16);
        let tx_ptr = AtomicPtr::new(Box::leak(Box::new(tx.clone())));

        unsafe {
            let handler = *tx_ptr.as_ptr() as *mut _ as *mut c_void;
            let observer = Observer::new(handler);

            let nsworkspace = NSWorkspace::sharedWorkspace();
            let notification_center = nsworkspace.notificationCenter();

            notification_center.addObserver_selector_name_object(
                &observer,
                sel!(workspaceWillSleepNotification:),
                Some(NSWorkspaceWillSleepNotification),
                None,
            );

            notification_center.addObserver_selector_name_object(
                &observer,
                sel!(workspaceDitWakeNotification:),
                Some(NSWorkspaceDidWakeNotification),
                None,
            );

            log::info!(target: "app", "Starting power management event monitor");

            Some(Self { observer, rx })
        }
    }

    /// Returns the next power event.
    pub async fn next(&mut self) -> Option<PowerManagementEvent> {
        loop {
            match self.rx.recv().await {
                Ok(event) => break Some(event),
                Err(broadcast::error::RecvError::Closed) => {
                    log::error!(target: "app", "Sender was unexpectedly dropped");
                    break None;
                }
                Err(broadcast::error::RecvError::Lagged(num_skipped)) => {
                    log::warn!(target: "app", "Skipped {num_skipped} power broadcast events");
                }
            }
        }
    }
}

impl Drop for PowerManagementListener {
    fn drop(&mut self) {
        unsafe {
            let nsworkspace = NSWorkspace::new();
            let notification_center = nsworkspace.notificationCenter();

            notification_center.removeObserver(&self.observer);
        }
    }
}

impl Observer {
    pub fn new(handler: *mut c_void) -> Retained<Self> {
        unsafe { msg_send_id![Self::alloc(), initWithHandler:handler] }
    }
}

const OBSERVER_CLASS_NAME: &str = "OneID_WindowServerObserver";

pub struct Ivars {
    handler: *mut c_void,
}

declare_class! {
    pub struct Observer;

    // - The superclass NSObject does not have any subclassing requirements.
    // - Interior mutability is a safe default.
    // - `Observer` does not implement `Drop`.
    unsafe impl ClassType for Observer {
        type Super = NSObject;
        type Mutability = InteriorMutable;
        const NAME: &'static str = OBSERVER_CLASS_NAME;
    }

    impl DeclaredClass for Observer {
        type Ivars = Ivars;
    }

    unsafe impl Observer {
        #[method_id(initWithHandler:)]
        fn init_with_handler(this: Allocated<Self>, handler: *mut c_void) -> Option<Retained<Self>> {
            let this = this.set_ivars(Ivars {
                handler
            });
            unsafe { msg_send_id![super(this), init] }
        }

        #[method(workspaceWillSleepNotification:)]
        fn workspace_will_sleep(&self, _notification: &NSNotification) {
            unsafe {
                let sender = &*(self.ivars().handler as *mut c_void as *mut broadcast::Sender<PowerManagementEvent>);
                if sender.send(PowerManagementEvent::Suspend).is_err() {
                    log::error!(target: "app", "Stopping power management event monitor");
                }
            }
        }

        #[method(workspaceDitWakeNotification:)]
        fn workspace_did_wake(&self, _notification: &NSNotification) {
            unsafe {
                let sender = &*(self.ivars().handler as *mut c_void as *mut broadcast::Sender<PowerManagementEvent>);
                if sender.send(PowerManagementEvent::ResumeAutomatic).is_err() {
                    log::error!(target: "app", "Stopping power management event monitor");
                }
            }
        }
    }
}

// SAFETY:
// * `NSObject` (the super class) is thread-safe.
// * The ivars are Send and Sync.
// See: https://github.com/madsmtm/objc2/issues/634
unsafe impl Send for Observer {}

// SAFETY:
// * `NSObject` (the super class) is thread-safe.
// * The ivars are Send and Sync.
// See: https://github.com/madsmtm/objc2/issues/634
// TODO: review this
unsafe impl Sync for Observer {}
